# Evidence build stage
FROM node:20 AS evidence-builder
WORKDIR /app
COPY evidence-project-fresh/ ./evidence-project/
RUN cd evidence-project && \
    npm ci && \
    npm run sources && \
    npm run build

# webpack stage
FROM node:20 AS webpack-bundler
WORKDIR /ddx
# Copy real Evidence build output
COPY --from=evidence-builder /app/evidence-project/build ./evidence_build
COPY package.json webpack.config.js ./
RUN npm install
RUN npx webpack

# Stage 3: Serve the bundled files with Nginx
FROM nginx:alpine

# Copy the bundled files to Nginx's public directory
COPY --from=webpack-bundler /ddx/dist /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
