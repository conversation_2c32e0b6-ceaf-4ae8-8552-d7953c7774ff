var F=Object.defineProperty;var G=(t,e,n)=>e in t?F(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var _=(t,e,n)=>G(t,typeof e!="symbol"?e+"":e,n);function L(){}const bt=t=>t;function U(t,e){for(const n in e)t[n]=e[n];return t}function gt(t){return!!t&&(typeof t=="object"||typeof t=="function")&&typeof t.then=="function"}function J(t){return t()}function xt(){return Object.create(null)}function K(t){t.forEach(J)}function Q(t){return typeof t=="function"}function wt(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}let p;function Et(t,e){return t===e?!0:(p||(p=document.createElement("a")),p.href=e,t===p.href)}function Tt(t){return Object.keys(t).length===0}function M(t,...e){if(t==null){for(const i of e)i(void 0);return L}const n=t.subscribe(...e);return n.unsubscribe?()=>n.unsubscribe():n}function vt(t){let e;return M(t,n=>e=n)(),e}function Nt(t,e,n){t.$$.on_destroy.push(M(e,n))}function kt(t,e,n,i){if(t){const s=P(t,e,n,i);return t[0](s)}}function P(t,e,n,i){return t[1]&&i?U(n.ctx.slice(),t[1](i(e))):n.ctx}function At(t,e,n,i){if(t[2]&&i){const s=t[2](i(n));if(e.dirty===void 0)return s;if(typeof s=="object"){const o=[],r=Math.max(e.dirty.length,s.length);for(let l=0;l<r;l+=1)o[l]=e.dirty[l]|s[l];return o}return e.dirty|s}return e.dirty}function Ct(t,e,n,i,s,o){if(s){const r=P(e,n,i,o);t.p(r,s)}}function jt(t){if(t.ctx.length>32){const e=[],n=t.ctx.length/32;for(let i=0;i<n;i++)e[i]=-1;return e}return-1}function Dt(t){const e={};for(const n in t)n[0]!=="$"&&(e[n]=t[n]);return e}function St(t,e){const n={};e=new Set(e);for(const i in t)!e.has(i)&&i[0]!=="$"&&(n[i]=t[i]);return n}function Ht(t){const e={};for(const n in t)e[n]=!0;return e}function Lt(t){return t??""}function Mt(t,e,n){return t.set(n),e}function Pt(t){return t&&Q(t.destroy)?t.destroy:L}function Ot(t){const e=typeof t=="string"&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return e?[parseFloat(e[1]),e[2]||"px"]:[t,"px"]}const V=["",!0,1,"true","contenteditable"];let x=!1;function qt(){x=!0}function zt(){x=!1}function X(t,e,n,i){for(;t<e;){const s=t+(e-t>>1);n(s)<=i?t=s+1:e=s}return t}function Y(t){if(t.hydrate_init)return;t.hydrate_init=!0;let e=t.childNodes;if(t.nodeName==="HEAD"){const c=[];for(let a=0;a<e.length;a++){const f=e[a];f.claim_order!==void 0&&c.push(f)}e=c}const n=new Int32Array(e.length+1),i=new Int32Array(e.length);n[0]=-1;let s=0;for(let c=0;c<e.length;c++){const a=e[c].claim_order,f=(s>0&&e[n[s]].claim_order<=a?s+1:X(1,s,I=>e[n[I]].claim_order,a))-1;i[c]=n[f]+1;const j=f+1;n[j]=c,s=Math.max(j,s)}const o=[],r=[];let l=e.length-1;for(let c=n[s]+1;c!=0;c=i[c-1]){for(o.push(e[c-1]);l>=c;l--)r.push(e[l]);l--}for(;l>=0;l--)r.push(e[l]);o.reverse(),r.sort((c,a)=>c.claim_order-a.claim_order);for(let c=0,a=0;c<r.length;c++){for(;a<o.length&&r[c].claim_order>=o[a].claim_order;)a++;const f=a<o.length?o[a]:null;t.insertBefore(r[c],f)}}function O(t,e){t.appendChild(e)}function Z(t){if(!t)return document;const e=t.getRootNode?t.getRootNode():t.ownerDocument;return e&&e.host?e:t.ownerDocument}function Bt(t){const e=w("style");return e.textContent="/* empty */",$(Z(t),e),e.sheet}function $(t,e){return O(t.head||t,e),e.sheet}function tt(t,e){if(x){for(Y(t),(t.actual_end_child===void 0||t.actual_end_child!==null&&t.actual_end_child.parentNode!==t)&&(t.actual_end_child=t.firstChild);t.actual_end_child!==null&&t.actual_end_child.claim_order===void 0;)t.actual_end_child=t.actual_end_child.nextSibling;e!==t.actual_end_child?(e.claim_order!==void 0||e.parentNode!==t)&&t.insertBefore(e,t.actual_end_child):t.actual_end_child=e.nextSibling}else(e.parentNode!==t||e.nextSibling!==null)&&t.appendChild(e)}function et(t,e,n){t.insertBefore(e,n||null)}function nt(t,e,n){x&&!n?tt(t,e):(e.parentNode!==t||e.nextSibling!=n)&&t.insertBefore(e,n||null)}function b(t){t.parentNode&&t.parentNode.removeChild(t)}function Rt(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function w(t){return document.createElement(t)}function q(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function A(t){return document.createTextNode(t)}function Wt(){return A(" ")}function It(){return A("")}function D(t,e,n,i){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n,i)}function Ft(t){return function(e){return e.preventDefault(),t.call(this,e)}}function Gt(t){return function(e){return e.stopPropagation(),t.call(this,e)}}function C(t,e,n){n==null?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}const it=["width","height"];function st(t,e){const n=Object.getOwnPropertyDescriptors(t.__proto__);for(const i in e)e[i]==null?t.removeAttribute(i):i==="style"?t.style.cssText=e[i]:i==="__value"?t.value=t[i]=e[i]:n[i]&&n[i].set&&it.indexOf(i)===-1?t[i]=e[i]:C(t,i,e[i])}function Ut(t,e){for(const n in e)C(t,n,e[n])}function rt(t,e){Object.keys(e).forEach(n=>{ot(t,n,e[n])})}function ot(t,e,n){const i=e.toLowerCase();i in t?t[i]=typeof t[i]=="boolean"&&n===""?!0:n:e in t?t[e]=typeof t[e]=="boolean"&&n===""?!0:n:C(t,e,n)}function Jt(t){return/-/.test(t)?rt:st}function Kt(t){return t.dataset.svelteH}function Qt(t){return t===""?null:+t}function Vt(t){return Array.from(t.childNodes)}function z(t){t.claim_info===void 0&&(t.claim_info={last_index:0,total_claimed:0})}function B(t,e,n,i,s=!1){z(t);const o=(()=>{for(let r=t.claim_info.last_index;r<t.length;r++){const l=t[r];if(e(l)){const c=n(l);return c===void 0?t.splice(r,1):t[r]=c,s||(t.claim_info.last_index=r),l}}for(let r=t.claim_info.last_index-1;r>=0;r--){const l=t[r];if(e(l)){const c=n(l);return c===void 0?t.splice(r,1):t[r]=c,s?c===void 0&&t.claim_info.last_index--:t.claim_info.last_index=r,l}}return i()})();return o.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1,o}function R(t,e,n,i){return B(t,s=>s.nodeName===e,s=>{const o=[];for(let r=0;r<s.attributes.length;r++){const l=s.attributes[r];n[l.name]||o.push(l.name)}o.forEach(r=>s.removeAttribute(r))},()=>i(e))}function Xt(t,e,n){return R(t,e,n,w)}function Yt(t,e,n){return R(t,e,n,q)}function ct(t,e){return B(t,n=>n.nodeType===3,n=>{const i=""+e;if(n.data.startsWith(i)){if(n.data.length!==i.length)return n.splitText(i.length)}else n.data=i},()=>A(e),!0)}function Zt(t){return ct(t," ")}function S(t,e,n){for(let i=n;i<t.length;i+=1){const s=t[i];if(s.nodeType===8&&s.textContent.trim()===e)return i}return-1}function $t(t,e){const n=S(t,"HTML_TAG_START",0),i=S(t,"HTML_TAG_END",n+1);if(n===-1||i===-1)return new E(e);z(t);const s=t.splice(n,i-n+1);b(s[0]),b(s[s.length-1]);const o=s.slice(1,s.length-1);if(o.length===0)return new E(e);for(const r of o)r.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1;return new E(e,o)}function lt(t,e){e=""+e,t.data!==e&&(t.data=e)}function at(t,e){e=""+e,t.wholeText!==e&&(t.data=e)}function te(t,e,n){~V.indexOf(n)?at(t,e):lt(t,e)}function ee(t,e){t.value=e??""}function ne(t,e,n,i){n==null?t.style.removeProperty(e):t.style.setProperty(e,n,"")}function ie(t,e,n){for(let i=0;i<t.options.length;i+=1){const s=t.options[i];if(s.__value===e){s.selected=!0;return}}(!n||e!==void 0)&&(t.selectedIndex=-1)}function se(t){const e=t.querySelector(":checked");return e&&e.__value}let y;function ut(){if(y===void 0){y=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{y=!0}}return y}function re(t,e){getComputedStyle(t).position==="static"&&(t.style.position="relative");const i=w("iframe");i.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),i.setAttribute("aria-hidden","true"),i.tabIndex=-1;const s=ut();let o;return s?(i.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",o=D(window,"message",r=>{r.source===i.contentWindow&&e()})):(i.src="about:blank",i.onload=()=>{o=D(i.contentWindow,"resize",e),e()}),O(t,i),()=>{(s||o&&i.contentWindow)&&o(),b(i)}}function oe(t,e,n){t.classList.toggle(e,!!n)}function ft(t,e,{bubbles:n=!1,cancelable:i=!1}={}){return new CustomEvent(t,{detail:e,bubbles:n,cancelable:i})}function ce(t,e){const n=[];let i=0;for(const s of e.childNodes)if(s.nodeType===8){const o=s.textContent.trim();o===`HEAD_${t}_END`?(i-=1,n.push(s)):o===`HEAD_${t}_START`&&(i+=1,n.push(s))}else i>0&&n.push(s);return n}class _t{constructor(e=!1){_(this,"is_svg",!1);_(this,"e");_(this,"n");_(this,"t");_(this,"a");this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,n,i=null){this.e||(this.is_svg?this.e=q(n.nodeName):this.e=w(n.nodeType===11?"TEMPLATE":n.nodeName),this.t=n.tagName!=="TEMPLATE"?n:n.content,this.c(e)),this.i(i)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let n=0;n<this.n.length;n+=1)et(this.t,this.n[n],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(b)}}class E extends _t{constructor(n=!1,i){super(n);_(this,"l");this.e=this.n=null,this.l=i}c(n){this.l?this.n=this.l:super.c(n)}i(n){for(let i=0;i<this.n.length;i+=1)nt(this.t,this.n[i],n)}}function le(t,e){return new t(e)}let g;function T(t){g=t}function u(){if(!g)throw new Error("Function called outside component initialization");return g}function ae(t){u().$$.before_update.push(t)}function ue(t){u().$$.on_mount.push(t)}function fe(t){u().$$.after_update.push(t)}function _e(t){u().$$.on_destroy.push(t)}function de(){const t=u();return(e,n,{cancelable:i=!1}={})=>{const s=t.$$.callbacks[e];if(s){const o=ft(e,n,{cancelable:i});return s.slice().forEach(r=>{r.call(t,o)}),!o.defaultPrevented}return!0}}function he(t,e){return u().$$.context.set(t,e),e}function me(t){return u().$$.context.get(t)}function pe(){return u().$$.context}function ye(t,e){const n=t.$$.callbacks[e.type];n&&n.slice().forEach(i=>i.call(this,e))}const m=[],H=[];let h=[];const N=[],W=Promise.resolve();let k=!1;function dt(){k||(k=!0,W.then(mt))}function be(){return dt(),W}function ht(t){h.push(t)}function ge(t){N.push(t)}const v=new Set;let d=0;function mt(){if(d!==0)return;const t=g;do{try{for(;d<m.length;){const e=m[d];d++,T(e),pt(e.$$)}}catch(e){throw m.length=0,d=0,e}for(T(null),m.length=0,d=0;H.length;)H.pop()();for(let e=0;e<h.length;e+=1){const n=h[e];v.has(n)||(v.add(n),n())}h.length=0}while(m.length);for(;N.length;)N.pop()();k=!1,v.clear(),T(t)}function pt(t){if(t.fragment!==null){t.update(),K(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(ht)}}function xe(t){const e=[],n=[];h.forEach(i=>t.indexOf(i)===-1?e.push(i):n.push(i)),n.forEach(i=>i()),h=e}export{J as $,Pt as A,ue as B,M as C,H as D,Q as E,gt as F,u as G,T as H,mt as I,oe as J,fe as K,be as L,le as M,ne as N,vt as O,St as P,U as Q,Dt as R,Jt as S,Z as T,Bt as U,bt as V,ft as W,Tt as X,g as Y,xt as Z,xe as _,At as a,qt as a0,zt as a1,m as a2,dt as a3,he as a4,me as a5,st as a6,Ht as a7,te as a8,Mt as a9,ee as aa,ge as ab,re as ac,_e as ad,Rt as ae,Yt as af,q as ag,Lt as ah,Qt as ai,$t as aj,E as ak,de as al,ae as am,ce as an,se as ao,ie as ap,Ft as aq,Ut as ar,Et as as,pe as at,Ot as au,C as b,kt as c,b as d,tt as e,Gt as f,jt as g,Xt as h,nt as i,Vt as j,Zt as k,D as l,w as m,Wt as n,ye as o,ht as p,Kt as q,It as r,wt as s,Nt as t,Ct as u,L as v,lt as w,ct as x,A as y,K as z};
