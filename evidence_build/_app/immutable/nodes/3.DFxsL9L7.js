import{s as l,c as i,u as r,g as u,a as f}from"../chunks/scheduler.CXt6djuF.js";import{S as _,i as c,t as p,a as m}from"../chunks/index.DP2zcclO.js";function $(a){let s;const n=a[1].default,e=i(n,a,a[0],null);return{c(){e&&e.c()},l(t){e&&e.l(t)},m(t,o){e&&e.m(t,o),s=!0},p(t,[o]){e&&e.p&&(!s||o&1)&&r(e,n,t,t[0],s?f(n,t[0],o,null):u(t[0]),null)},i(t){s||(m(e,t),s=!0)},o(t){p(e,t),s=!1},d(t){e&&e.d(t)}}}function d(a,s,n){let{$$slots:e={},$$scope:t}=s;return a.$$set=o=>{"$$scope"in o&&n(0,t=o.$$scope)},[t,e]}class S extends _{constructor(s){super(),c(this,s,d,$,l,{})}}export{S as component};
