const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const fs = require('fs');

// ⬇️ Customize this if Evidence output is elsewhere
const EVIDENCE_BUILD_DIR = path.resolve(__dirname, 'evidence_build');
const EVIDENCE_INDEX = path.resolve(EVIDENCE_BUILD_DIR, 'index.html');
const DDX_TEMPLATE = path.resolve(__dirname, 'ddx-template.html');



// Create entry point for Evidence SvelteKit build
const createEntryPoint = () => {
  const entryPath = path.resolve(__dirname, 'temp-entry.js');
  let entryContent = '// Auto-generated entry point for Evidence SvelteKit build\n';

  // Check if Evidence build directory exists
  if (fs.existsSync(EVIDENCE_BUILD_DIR)) {
    // Find all CSS files in the Evidence build (including _app/immutable/assets)
    const findCssFiles = (dir) => {
      const files = [];
      if (fs.existsSync(dir)) {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          if (stat.isDirectory()) {
            files.push(...findCssFiles(fullPath));
          } else if (item.endsWith('.css')) {
            files.push(fullPath);
          }
        });
      }
      return files;
    };

    const cssFiles = findCssFiles(EVIDENCE_BUILD_DIR);
    cssFiles.forEach(cssFile => {
      const relativePath = path.relative(__dirname, cssFile);
      entryContent += `import './${relativePath.replace(/\\/g, '/')}';\n`;
    });

    // Include Domo integration script and service worker
    const domoIntegrationPath = path.join(EVIDENCE_BUILD_DIR, 'domo-duckdb-integration.js');
    const serviceWorkerPath = path.join(EVIDENCE_BUILD_DIR, 'fix-tprotocol-service-worker.js');

    if (fs.existsSync(domoIntegrationPath)) {
      const domoScript = fs.readFileSync(domoIntegrationPath, 'utf8');
      entryContent += `\n// Domo DDX Integration\n`;
      entryContent += domoScript + '\n';
    }

    if (fs.existsSync(serviceWorkerPath)) {
      const serviceWorkerScript = fs.readFileSync(serviceWorkerPath, 'utf8');
      entryContent += `\n// Service Worker functionality (inline)\n`;
      entryContent += `if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {\n`;
      entryContent += `  const swCode = \`${serviceWorkerScript}\`;\n`;
      entryContent += `  const blob = new Blob([swCode], { type: 'application/javascript' });\n`;
      entryContent += `  const swUrl = URL.createObjectURL(blob);\n`;
      entryContent += `  navigator.serviceWorker.register(swUrl).then(registration => {\n`;
      entryContent += `    console.log('Service Worker registered successfully:', registration);\n`;
      entryContent += `  }).catch(error => {\n`;
      entryContent += `    console.log('Service Worker registration failed:', error);\n`;
      entryContent += `  });\n`;
      entryContent += `}\n`;
    }

    // Also import any main JavaScript files
    const indexHtml = path.join(EVIDENCE_BUILD_DIR, 'index.html');
    if (fs.existsSync(indexHtml)) {
      entryContent += `\n// Evidence app initialization\n`;
      entryContent += `console.log('Evidence app loaded for Domo DDX');\n`;
    }
  }

  // Write the entry file
  fs.writeFileSync(entryPath, entryContent);
  return entryPath;
};

module.exports = {
  mode: 'production',

  // Create dynamic entry point
  entry: createEntryPoint(),

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'app.js', // Single consolidated JS file for DDX
    clean: true,
  },

  module: {
    rules: [
      {
        test: /\.html$/,
        loader: 'html-loader',
        options: {
          sources: false, // Disable automatic asset processing for Evidence SvelteKit build
        },
      },
      {
        test: /\.css$/i,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              url: {
                filter: (url) => {
                  // Don't process URLs that start with /_app/ - they're handled by CopyPlugin
                  return !url.startsWith('/_app/');
                },
              },
            },
          }
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg|ico|webp)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]',
        },
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/fonts/[name][ext]',
        },
      },
    ],
  },

  plugins: [
    new MiniCssExtractPlugin({
      filename: 'styles.css',
      chunkFilename: 'styles.css',
    }),
    new HtmlWebpackPlugin({
      template: DDX_TEMPLATE,
      filename: 'index.html',
      inject: 'head', // Inject CSS in head, JS at end of body
      scriptLoading: 'blocking',
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
      }
    }),

    new CopyPlugin({
      patterns: [
        // Copy all Evidence SvelteKit assets (preserving directory structure)
        {
          from: `${EVIDENCE_BUILD_DIR}/_app/**/*`,
          to: ({ context, absoluteFilename }) => {
            const evidenceBuildPath = path.resolve(context, EVIDENCE_BUILD_DIR);
            const relativePath = path.relative(evidenceBuildPath, absoluteFilename);
            return `_app/${relativePath}`;
          },
          noErrorOnMissing: true,
          filter: (resourcePath) => {
            // Don't copy CSS files (webpack handles them)
            return !resourcePath.endsWith('.css');
          },
        },
        // Copy Evidence API and data files (preserving directory structure)
        {
          from: `${EVIDENCE_BUILD_DIR}/api/**/*`,
          to: ({ context, absoluteFilename }) => {
            const evidenceBuildPath = path.resolve(context, EVIDENCE_BUILD_DIR);
            const relativePath = path.relative(evidenceBuildPath, absoluteFilename);
            return `api/${relativePath}`;
          },
          noErrorOnMissing: true,
        },
        {
          from: `${EVIDENCE_BUILD_DIR}/data/**/*`,
          to: ({ context, absoluteFilename }) => {
            const evidenceBuildPath = path.resolve(context, EVIDENCE_BUILD_DIR);
            const relativePath = path.relative(evidenceBuildPath, absoluteFilename);
            return `data/${relativePath}`;
          },
          noErrorOnMissing: true,
        },
        // Copy Evidence static assets (excluding JS files that are bundled)
        {
          from: `${EVIDENCE_BUILD_DIR}/*.{ico,png,svg,webmanifest}`,
          to: '[name][ext]',
          noErrorOnMissing: true,
        },
      ],
    }),
    // Clean up temp files after build
    {
      apply: (compiler) => {
        compiler.hooks.done.tap('CleanupTempFiles', () => {
          const tempEntry = path.resolve(__dirname, 'temp-entry.js');
          if (fs.existsSync(tempEntry)) {
            fs.unlinkSync(tempEntry);
          }
        });
      },
    },
  ],


};
