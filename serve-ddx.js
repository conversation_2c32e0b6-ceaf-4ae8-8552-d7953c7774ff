#!/usr/bin/env node

/**
 * Simple HTTP server to serve the built Evidence DDX app
 * This allows proper testing of the SPA routing and API endpoints
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3000;
const DIST_DIR = path.join(__dirname, 'dist');

// MIME types for different file extensions
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.wasm': 'application/wasm',
  '.webmanifest': 'application/manifest+json',
  '.arrow': 'application/octet-stream',
  '.parquet': 'application/octet-stream'
};

function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(res, filePath) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('File not found');
      return;
    }

    const mimeType = getMimeType(filePath);
    res.writeHead(200, { 
      'Content-Type': mimeType,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end(data);
  });
}

function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  let pathname = parsedUrl.pathname;

  // Remove leading slash
  if (pathname.startsWith('/')) {
    pathname = pathname.substring(1);
  }

  // Handle root path
  if (pathname === '' || pathname === '/') {
    pathname = 'index.html';
  }

  // Construct file path
  const filePath = path.join(DIST_DIR, pathname);

  // Security check - ensure file is within dist directory
  const resolvedPath = path.resolve(filePath);
  const resolvedDistDir = path.resolve(DIST_DIR);
  
  if (!resolvedPath.startsWith(resolvedDistDir)) {
    res.writeHead(403, { 'Content-Type': 'text/plain' });
    res.end('Forbidden');
    return;
  }

  // Check if file exists
  fs.stat(filePath, (err, stats) => {
    if (err) {
      // If file doesn't exist, try to serve index.html for SPA routing
      if (pathname.includes('.')) {
        // If it has an extension, it's probably a real file that's missing
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
      } else {
        // For routes without extensions, serve index.html (SPA routing)
        serveFile(res, path.join(DIST_DIR, 'index.html'));
      }
      return;
    }

    if (stats.isDirectory()) {
      // Try to serve index.html from directory
      const indexPath = path.join(filePath, 'index.html');
      fs.stat(indexPath, (err) => {
        if (err) {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Directory listing not allowed');
        } else {
          serveFile(res, indexPath);
        }
      });
    } else {
      // Serve the file
      serveFile(res, filePath);
    }
  });
}

// Create HTTP server
const server = http.createServer(handleRequest);

// Start server
server.listen(PORT, () => {
  console.log('🚀 Evidence DDX App Server Started');
  console.log('=====================================');
  console.log(`📍 Server running at: http://localhost:${PORT}`);
  console.log(`📁 Serving files from: ${DIST_DIR}`);
  console.log('');
  console.log('🔗 Available routes:');
  console.log(`   • Home: http://localhost:${PORT}/`);
  console.log(`   • Workflow: http://localhost:${PORT}/workflow`);
  console.log('');
  console.log('💡 This simulates how the app will work in Domo DDX');
  console.log('   Press Ctrl+C to stop the server');
  console.log('=====================================');
});

// Handle server errors
server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use. Try a different port:`);
    console.error(`   PORT=3001 node serve-ddx.js`);
  } else {
    console.error('❌ Server error:', err);
  }
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down Evidence DDX server...');
  server.close(() => {
    console.log('✅ Server stopped');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n👋 Shutting down Evidence DDX server...');
  server.close(() => {
    console.log('✅ Server stopped');
    process.exit(0);
  });
});
