# Evidence Framework for Domo DDX

This project integrates the Evidence analytics framework with Domo DDX, allowing you to create interactive dashboards and reports using your Domo datasets with the power of DuckDB and SQL.

## 🚀 Features

- **Domo Dataset Integration**: Seamlessly load Domo datasets into DuckDB for analysis
- **Interactive Workflow Picker**: User-friendly interface to select and configure data loading
- **Evidence Framework**: Leverage Evidence's powerful analytics and visualization capabilities
- **DuckDB Performance**: Fast in-memory analytics with SQL support
- **DDX Compatible**: Fully packaged for deployment in Domo's DDX environment

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Access to a Domo instance
- Domo DDX development environment (for deployment)

## 🛠️ Development Setup

1. **Clone and Install Dependencies**
   ```bash
   # Install webpack dependencies
   npm install
   
   # Install Evidence dependencies
   cd evidence-project-fresh
   npm install
   cd ..
   ```

2. **Development Mode**
   ```bash
   # Start Evidence development server
   cd evidence-project-fresh
   npm run dev
   ```
   
   This will start the Evidence development server with mock Domo data for testing.

3. **Build for Production**
   ```bash
   # Run the DDX build script
   ./build-ddx.sh
   ```

## 🏗️ Project Structure

```
├── evidence-project-fresh/          # Evidence framework project
│   ├── pages/                       # Evidence pages (Markdown + SQL)
│   │   ├── index.md                 # Main dashboard page
│   │   └── workflow.md              # Domo dataset workflow picker
│   ├── src/
│   │   ├── components/              # Svelte components
│   │   │   └── DomoWorkflowPicker.svelte
│   │   └── lib/                     # Utility libraries
│   │       ├── domo-api-client.js   # Domo API integration
│   │       └── domo-duckdb-connector.js # DuckDB integration
│   ├── static/                      # Static assets
│   │   └── domo-duckdb-integration.js # DDX integration script
│   └── evidence.config.yaml         # Evidence configuration
├── webpack.config.js                # Webpack bundling configuration
├── domo-manifest.json               # Domo DDX app manifest
├── build-ddx.sh                     # Build script for DDX deployment
└── README-DDX.md                    # This file
```

## 🔧 Configuration

### Evidence Configuration

The Evidence configuration (`evidence-project-fresh/evidence.config.yaml`) includes:

- **DuckDB Integration**: Configured for both file-based and in-memory databases
- **DDX Settings**: Special configuration for Domo DDX environment
- **Data Sources**: Support for both sample data and dynamic Domo datasets

### Domo DDX Manifest

The `domo-manifest.json` file configures:

- App metadata and permissions
- Data connection requirements
- UI sizing and features
- Configuration options for end users

## 📊 Using the Workflow Picker

1. **Access the Workflow**: Navigate to `/workflow` in your Evidence app
2. **Select Dataset**: Choose from available Domo datasets
3. **Preview Data**: Review dataset schema and sample data
4. **Configure Loading**: Set table name and refresh mode
5. **Load Data**: Import the dataset into DuckDB for analysis

## 💡 Creating Dashboards

Once data is loaded, create interactive dashboards using Evidence:

### SQL Queries
```sql my_analysis
SELECT 
  region,
  SUM(revenue) as total_revenue,
  COUNT(*) as transaction_count
FROM domo_sales_data
GROUP BY region
ORDER BY total_revenue DESC
```

### Visualizations
```markdown
<BarChart 
  data={my_analysis}
  x=region
  y=total_revenue
  title="Revenue by Region"
/>
```

### Interactive Controls
```markdown
<Dropdown data={regions} name=selected_region>
  <DropdownOption value="%" valueLabel="All Regions"/>
</Dropdown>
```

## 🚀 Deployment to Domo DDX

1. **Build the App**
   ```bash
   ./build-ddx.sh
   ```

2. **Upload to Domo**
   - Use the generated `evidence-ddx-app.zip` file
   - Upload through Domo's DDX developer portal
   - Configure app permissions and data connections

3. **Configure in Domo**
   - Set up data source connections
   - Configure user permissions
   - Customize app settings through the manifest

## 🔍 Troubleshooting

### Common Issues

**Build Failures**
- Ensure Node.js 18+ is installed
- Check that all dependencies are installed
- Verify Evidence project builds successfully in isolation

**Data Loading Issues**
- Verify Domo API permissions
- Check dataset accessibility in Domo
- Review browser console for API errors

**DDX Deployment Issues**
- Ensure manifest file is valid JSON
- Check app permissions in Domo
- Verify all required files are included in the zip

### Development Tips

- Use browser developer tools to debug Domo API calls
- Test with mock data first before connecting to real Domo datasets
- Monitor the Evidence development server logs for SQL errors

## 📚 Documentation Links

- [Evidence Documentation](https://docs.evidence.dev)
- [Domo DDX Developer Guide](https://developer.domo.com)
- [DuckDB Documentation](https://duckdb.org/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly in both development and DDX environments
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
- Evidence Framework: [Evidence Community](https://slack.evidence.dev/)
- Domo DDX: [Domo Developer Community](https://developer.domo.com/community)
- DuckDB: [DuckDB Community](https://github.com/duckdb/duckdb/discussions)
