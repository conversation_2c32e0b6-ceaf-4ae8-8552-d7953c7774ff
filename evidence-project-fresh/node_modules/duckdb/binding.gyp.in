{"targets": [{"target_name": "<(module_name)", "sources": ["src/duckdb_node.cpp", "src/database.cpp", "src/data_chunk.cpp", "src/connection.cpp", "src/statement.cpp", "src/utils.cpp", "${SOURCE_FILES}"], "include_dirs": ["<!(node -p \"require('node-addon-api').include_dir\")", "${INCLUDE_FILES}"], "defines": ["NAPI_VERSION=6", "${DEFINES}"], "cflags_cc": ["-frtti", "-fexceptions", "-Wno-redundant-move"], "cflags_cc!": ["-fno-rrti", "-fno-exceptions"], "cflags": ["-frtti", "-fexceptions", "-Wno-redundant-move", "${CFLAGS}"], "cflags!": ["-fno-rrti", "-fno-exceptions"], "xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "GCC_ENABLE_CPP_RTTI": "YES", "CLANG_CXX_LIBRARY": "libc++", "MACOSX_DEPLOYMENT_TARGET": "10.15", "CLANG_CXX_LANGUAGE_STANDARD": "c++11", "OTHER_CFLAGS": ["-fexceptions", "-frtti", "-Wno-redundant-move"]}, "msvs_settings": {"VCCLCompilerTool": {"ExceptionHandling": 1, "AdditionalOptions": ["/bigobj", "${WINDOWS_OPTIONS}"]}}, "conditions": [["OS==\"win\"", {"defines": ["DUCKDB_BUILD_LIBRARY"], "libraries": ["rstrtmgr.lib", "bcrypt.lib"]}]], "libraries": ["${LIBRARY_FILES}"]}, {"target_name": "action_after_build", "type": "none", "dependencies": ["<(module_name)"], "copies": [{"files": ["<(PRODUCT_DIR)/<(module_name).node"], "destination": "<(module_path)"}]}]}