var Eg=Object.defineProperty;var Lc=n=>{throw TypeError(n)};var Ag=(n,t,e)=>t in n?Eg(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var Ya=(n,t,e)=>Ag(n,typeof t!="symbol"?t+"":t,e),Rc=(n,t,e)=>t.has(n)||Lc("Cannot "+e);var Dr=(n,t,e)=>(Rc(n,t,"read from private field"),e?e.call(n):t.get(n)),eo=(n,t,e)=>t.has(n)?Lc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(n):t.set(n,e),Ka=(n,t,e,r)=>(Rc(n,t,"write to private field"),r?r.call(n,e):t.set(n,e),e);var Uc=(n,t,e,r)=>({set _(i){Ka(n,t,i,e)},get _(){return Dr(n,t,r)}});import{cl as Gn,cm as Wt,cn as Cs,co as Ti,cp as Mt,cq as E,cr as zr,cs as Tg,ct as Bg,b4 as xt,cu as uh,cv as Mg,cw as Pg,cx as Ng,cy as Cg,w as Xe,t as Fs,F as Vc,$ as xc,v as dn,a0 as Wn,K as or,L as Gt,p as Ct,ba as zc,O as rn,a1 as Fg,N as no,H as jg,cz as _l,a2 as Lg,n as ro,bb as sr,m as ki,_ as Wc,cA as Wr,G as js,J as $a,B as fh,l as mi,a3 as Hc,a4 as qc,a6 as Rg,S as Ug,R as Vg,U as xg,a7 as zg,V as Jl,cB as Wg,x as An,bm as bl,cb as ei,h as ue,u as dh,o as Hg,cC as Yc,A as Ql,y as Fi,am as qg,aA as Kc,g as hh,cD as Yg,cE as Kg,I as Tn,cF as $g,cG as Js,cH as Jg,f as xo,cI as Qg,cJ as Gg,cK as Xg,cL as Zg,cM as mh,cN as t_,cO as ph,cP as e_,cQ as n_,cR as r_,b1 as i_,cS as yl,aZ as $c,ch as o_,cT as s_}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.bgj4tQOV.js";import{p as Io,s as a_,c as l_,h as c_,B as u_}from"../chunks/button.D5uCIRhN.js";import{L as _s,a4 as gh,a5 as f_,s as ee,d as S,i as R,r as ft,P as ln,t as Ae,Q as me,R as ri,c as Oe,u as ke,g as De,a as Ee,o as lr,D as Kn,z as wr,S as Jc,A as Rr,l as he,h as q,j as $,m as Y,a6 as $e,p as On,w as Le,b as N,e as z,k as nt,x as vt,n as rt,y as wt,al as d_,v as Qt,as as bs,B as Pr,N as Qs,q as yr,K as h_,ae as kn,J as Nr,ad as m_,C as p_,ab as _h}from"../chunks/scheduler.CXt6djuF.js";import{S as ae,i as le,t as D,a as O,g as jt,c as Lt,f as In,h as es,j as Hi,d as st,m as at,b as lt,e as ct,k as bh}from"../chunks/index.DP2zcclO.js";import{w as qe,d as fo,b as g_,c as __}from"../chunks/entry.Ba3I0i56.js";import{p as Ma,n as yh}from"../chunks/stores.DroemqDT.js";import{d as vl}from"../chunks/index.rV6zwFgL.js";import{c as b_,h as Ge,g as y_,b as v_,r as ai,a as w_,G as S_,X as I_,B as O_,S as k_}from"../chunks/index.ClE4FWIg.js";import{r as D_}from"../chunks/scroll.CBw959Hr.js";import{A as E_,a as Ja}from"../chunks/AccordionItem.ByBk5hzy.js";const A_=new TextDecoder("utf-8"),wl=n=>A_.decode(n),T_=new TextEncoder,Gl=n=>T_.encode(n),B_=n=>typeof n=="number",M_=n=>typeof n=="boolean",Ze=n=>typeof n=="function",Ln=n=>n!=null&&Object(n)===n,zo=n=>Ln(n)&&Ze(n.then),Pa=n=>Ln(n)&&Ze(n[Symbol.iterator]),Xl=n=>Ln(n)&&Ze(n[Symbol.asyncIterator]),Sl=n=>Ln(n)&&Ln(n.schema),vh=n=>Ln(n)&&"done"in n&&"value"in n,wh=n=>Ln(n)&&Ze(n.stat)&&B_(n.fd),Sh=n=>Ln(n)&&Zl(n.body),Ih=n=>"_getDOMStream"in n&&"_getNodeStream"in n,Zl=n=>Ln(n)&&Ze(n.cancel)&&Ze(n.getReader)&&!Ih(n),Oh=n=>Ln(n)&&Ze(n.read)&&Ze(n.pipe)&&M_(n.readable)&&!Ih(n),P_=n=>Ln(n)&&Ze(n.clear)&&Ze(n.bytes)&&Ze(n.position)&&Ze(n.setPosition)&&Ze(n.capacity)&&Ze(n.getBufferIdentifier)&&Ze(n.createLong),tc=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function N_(n){const t=n[0]?[n[0]]:[];let e,r,i,o;for(let s,a,l=0,c=0,u=n.length;++l<u;){if(s=t[c],a=n[l],!s||!a||s.buffer!==a.buffer||a.byteOffset<s.byteOffset){a&&(t[++c]=a);continue}if({byteOffset:e,byteLength:i}=s,{byteOffset:r,byteLength:o}=a,e+i<r||r+o<e){a&&(t[++c]=a);continue}t[c]=new Uint8Array(s.buffer,e,r-e+o)}return t}function Qc(n,t,e=0,r=t.byteLength){const i=n.byteLength,o=new Uint8Array(n.buffer,n.byteOffset,i),s=new Uint8Array(t.buffer,t.byteOffset,Math.min(r,i));return o.set(s,e),n}function er(n,t){const e=N_(n),r=e.reduce((u,f)=>u+f.byteLength,0);let i,o,s,a=0,l=-1;const c=Math.min(t||Number.POSITIVE_INFINITY,r);for(const u=e.length;++l<u;){if(i=e[l],o=i.subarray(0,Math.min(i.length,c-a)),c<=a+o.length){o.length<i.length?e[l]=i.subarray(o.length):o.length===i.length&&l++,s?Qc(s,o,a):s=o;break}Qc(s||(s=new Uint8Array(c)),o,a),a+=o.length}return[s||new Uint8Array(0),e.slice(l),r-(s?s.byteLength:0)]}function se(n,t){let e=vh(t)?t.value:t;return e instanceof n?n===Uint8Array?new n(e.buffer,e.byteOffset,e.byteLength):e:e?(typeof e=="string"&&(e=Gl(e)),e instanceof ArrayBuffer?new n(e):e instanceof tc?new n(e):P_(e)?se(n,e.bytes()):ArrayBuffer.isView(e)?e.byteLength<=0?new n(0):new n(e.buffer,e.byteOffset,e.byteLength/n.BYTES_PER_ELEMENT):n.from(e)):new n(0)}const io=n=>se(Int32Array,n),Gc=n=>se(BigInt64Array,n),Jt=n=>se(Uint8Array,n),Il=n=>(n.next(),n);function*C_(n,t){const e=function*(i){yield i},r=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof tc?e(t):Pa(t)?t:e(t);return yield*Il(function*(i){let o=null;do o=i.next(yield se(n,o));while(!o.done)}(r[Symbol.iterator]())),new n}const F_=n=>C_(Uint8Array,n);function kh(n,t){return Gn(this,arguments,function*(){if(zo(t))return yield Wt(yield Wt(yield*Cs(Ti(kh(n,yield Wt(t))))));const r=function(s){return Gn(this,arguments,function*(){yield yield Wt(yield Wt(s))})},i=function(s){return Gn(this,arguments,function*(){yield Wt(yield*Cs(Ti(Il(function*(a){let l=null;do l=a.next(yield l==null?void 0:l.value);while(!l.done)}(s[Symbol.iterator]())))))})},o=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof tc?r(t):Pa(t)?i(t):Xl(t)?t:r(t);return yield Wt(yield*Cs(Ti(Il(function(s){return Gn(this,arguments,function*(){let a=null;do a=yield Wt(s.next(yield yield Wt(se(n,a))));while(!a.done)})}(o[Symbol.asyncIterator]()))))),yield Wt(new n)})}const j_=n=>kh(Uint8Array,n);function L_(n,t){let e=0;const r=n.length;if(r!==t.length)return!1;if(r>0)do if(n[e]!==t[e])return!1;while(++e<r);return!0}const yn={fromIterable(n){return ys(R_(n))},fromAsyncIterable(n){return ys(U_(n))},fromDOMStream(n){return ys(V_(n))},fromNodeStream(n){return ys(z_(n))},toDOMStream(n,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(n,t){throw new Error('"toNodeStream" not available in this environment')}},ys=n=>(n.next(),n);function*R_(n){let t,e=!1,r=[],i,o,s,a=0;function l(){return o==="peek"?er(r,s)[0]:([i,r,a]=er(r,s),i)}({cmd:o,size:s}=(yield null)||{cmd:"read",size:0});const c=F_(n)[Symbol.iterator]();try{do if({done:t,value:i}=Number.isNaN(s-a)?c.next():c.next(s-a),!t&&i.byteLength>0&&(r.push(i),a+=i.byteLength),t||s<=a)do({cmd:o,size:s}=yield l());while(s<a);while(!t)}catch(u){(e=!0)&&typeof c.throw=="function"&&c.throw(u)}finally{e===!1&&typeof c.return=="function"&&c.return(null)}return null}function U_(n){return Gn(this,arguments,function*(){let e,r=!1,i=[],o,s,a,l=0;function c(){return s==="peek"?er(i,a)[0]:([o,i,l]=er(i,a),o)}({cmd:s,size:a}=(yield yield Wt(null))||{cmd:"read",size:0});const u=j_(n)[Symbol.asyncIterator]();try{do if({done:e,value:o}=Number.isNaN(a-l)?yield Wt(u.next()):yield Wt(u.next(a-l)),!e&&o.byteLength>0&&(i.push(o),l+=o.byteLength),e||a<=l)do({cmd:s,size:a}=yield yield Wt(c()));while(a<l);while(!e)}catch(f){(r=!0)&&typeof u.throw=="function"&&(yield Wt(u.throw(f)))}finally{r===!1&&typeof u.return=="function"&&(yield Wt(u.return(new Uint8Array(0))))}return yield Wt(null)})}function V_(n){return Gn(this,arguments,function*(){let e=!1,r=!1,i=[],o,s,a,l=0;function c(){return s==="peek"?er(i,a)[0]:([o,i,l]=er(i,a),o)}({cmd:s,size:a}=(yield yield Wt(null))||{cmd:"read",size:0});const u=new x_(n);try{do if({done:e,value:o}=Number.isNaN(a-l)?yield Wt(u.read()):yield Wt(u.read(a-l)),!e&&o.byteLength>0&&(i.push(Jt(o)),l+=o.byteLength),e||a<=l)do({cmd:s,size:a}=yield yield Wt(c()));while(a<l);while(!e)}catch(f){(r=!0)&&(yield Wt(u.cancel(f)))}finally{r===!1?yield Wt(u.cancel()):n.locked&&u.releaseLock()}return yield Wt(null)})}class x_{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch(()=>{})}get closed(){return this.reader?this.reader.closed.catch(()=>{}):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return Mt(this,void 0,void 0,function*(){const{reader:e,source:r}=this;e&&(yield e.cancel(t).catch(()=>{})),r&&r.locked&&this.releaseLock()})}read(t){return Mt(this,void 0,void 0,function*(){if(t===0)return{done:this.reader==null,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=Jt(e)),e})}}const Qa=(n,t)=>{const e=i=>r([t,i]);let r;return[t,e,new Promise(i=>(r=i)&&n.once(t,e))]};function z_(n){return Gn(this,arguments,function*(){const e=[];let r="error",i=!1,o=null,s,a,l=0,c=[],u;function f(){return s==="peek"?er(c,a)[0]:([u,c,l]=er(c,a),u)}if({cmd:s,size:a}=(yield yield Wt(null))||{cmd:"read",size:0},n.isTTY)return yield yield Wt(new Uint8Array(0)),yield Wt(null);try{e[0]=Qa(n,"end"),e[1]=Qa(n,"error");do{if(e[2]=Qa(n,"readable"),[r,o]=yield Wt(Promise.race(e.map(d=>d[2]))),r==="error")break;if((i=r==="end")||(Number.isFinite(a-l)?(u=Jt(n.read(a-l)),u.byteLength<a-l&&(u=Jt(n.read()))):u=Jt(n.read()),u.byteLength>0&&(c.push(u),l+=u.byteLength)),i||a<=l)do({cmd:s,size:a}=yield yield Wt(f()));while(a<l)}while(!i)}finally{yield Wt(h(e,r==="error"?o:null))}return yield Wt(null);function h(d,g){return u=c=null,new Promise((m,p)=>{for(const[v,b]of d)n.off(v,b);try{const v=n.destroy;v&&v.call(n,g),g=void 0}catch(v){g=v||g}finally{g!=null?p(g):m()}})}})}var Ce;(function(n){n[n.V1=0]="V1",n[n.V2=1]="V2",n[n.V3=2]="V3",n[n.V4=3]="V4",n[n.V5=4]="V5"})(Ce||(Ce={}));var cn;(function(n){n[n.Sparse=0]="Sparse",n[n.Dense=1]="Dense"})(cn||(cn={}));var Ye;(function(n){n[n.HALF=0]="HALF",n[n.SINGLE=1]="SINGLE",n[n.DOUBLE=2]="DOUBLE"})(Ye||(Ye={}));var Dn;(function(n){n[n.DAY=0]="DAY",n[n.MILLISECOND=1]="MILLISECOND"})(Dn||(Dn={}));var yt;(function(n){n[n.SECOND=0]="SECOND",n[n.MILLISECOND=1]="MILLISECOND",n[n.MICROSECOND=2]="MICROSECOND",n[n.NANOSECOND=3]="NANOSECOND"})(yt||(yt={}));var nr;(function(n){n[n.YEAR_MONTH=0]="YEAR_MONTH",n[n.DAY_TIME=1]="DAY_TIME",n[n.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"})(nr||(nr={}));const Ga=2,$n=4,fr=4,ne=4,Br=new Int32Array(2),Xc=new Float32Array(Br.buffer),Zc=new Float64Array(Br.buffer),vs=new Uint16Array(new Uint8Array([1,0]).buffer)[0]===1;var Ol;(function(n){n[n.UTF8_BYTES=1]="UTF8_BYTES",n[n.UTF16_STRING=2]="UTF16_STRING"})(Ol||(Ol={}));let ji=class Dh{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(t){return new Dh(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return Br[0]=this.readInt32(t),Xc[0]}readFloat64(t){return Br[vs?0:1]=this.readInt32(t),Br[vs?1:0]=this.readInt32(t+4),Zc[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){Xc[0]=e,this.writeInt32(t,Br[0])}writeFloat64(t,e){Zc[0]=e,this.writeInt32(t,Br[vs?0:1]),this.writeInt32(t+4,Br[vs?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+$n+fr)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<fr;e++)t+=String.fromCharCode(this.readInt8(this.position_+$n+e));return t}__offset(t,e){const r=t-this.readInt32(t);return e<this.readInt16(r)?this.readInt16(r+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const r=this.readInt32(t);t+=$n;const i=this.bytes_.subarray(t,t+r);return e===Ol.UTF8_BYTES?i:this.text_decoder_.decode(i)}__union_with_string(t,e){return typeof t=="string"?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+$n}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(t.length!=fr)throw new Error("FlatBuffers: file identifier must be length "+fr);for(let e=0;e<fr;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+$n+e))return!1;return!0}createScalarList(t,e){const r=[];for(let i=0;i<e;++i){const o=t(i);o!==null&&r.push(o)}return r}createObjList(t,e){const r=[];for(let i=0;i<e;++i){const o=t(i);o!==null&&r.push(o.unpack())}return r}},Eh=class Ah{constructor(t){this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder;let e;t?e=t:e=1024,this.bb=ji.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,e){t>this.minalign&&(this.minalign=t);const r=~(this.bb.capacity()-this.space+e)+1&t-1;for(;this.space<r+t+e;){const i=this.bb.capacity();this.bb=Ah.growByteBuffer(this.bb),this.space+=this.bb.capacity()-i}this.pad(r)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,r){(this.force_defaults||e!=r)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,r){(this.force_defaults||e!=r)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,r){(this.force_defaults||e!=r)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,r){(this.force_defaults||e!==r)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,r){(this.force_defaults||e!=r)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,r){(this.force_defaults||e!=r)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,r){(this.force_defaults||e!=r)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,r){e!=r&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){this.vtable!==null&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(e&3221225472)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const r=e<<1,i=ji.allocate(r);return i.setPosition(r-e),i.bytes().set(t.bytes(),r-e),i}addOffset(t){this.prep($n,0),this.writeInt32(this.offset()-t+$n)}startObject(t){this.notNested(),this.vtable==null&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(this.vtable==null||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&this.vtable[e]==0;e--);const r=e+1;for(;e>=0;e--)this.addInt16(this.vtable[e]!=0?t-this.vtable[e]:0);const i=2;this.addInt16(t-this.object_start);const o=(r+i)*Ga;this.addInt16(o);let s=0;const a=this.space;t:for(e=0;e<this.vtables.length;e++){const l=this.bb.capacity()-this.vtables[e];if(o==this.bb.readInt16(l)){for(let c=Ga;c<o;c+=Ga)if(this.bb.readInt16(a+c)!=this.bb.readInt16(l+c))continue t;s=this.vtables[e];break}}return s?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,s-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,r){const i=r?ne:0;if(e){const o=e;if(this.prep(this.minalign,$n+fr+i),o.length!=fr)throw new TypeError("FlatBuffers: file identifier must be length "+fr);for(let s=fr-1;s>=0;s--)this.writeInt8(o.charCodeAt(s))}this.prep(this.minalign,$n+i),this.addOffset(t),i&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const r=this.bb.capacity()-t,i=r-this.bb.readInt32(r);if(!(e<this.bb.readInt16(i)&&this.bb.readInt16(i+e)!=0))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,r){this.notNested(),this.vector_num_elems=e,this.prep($n,t*e),this.prep(r,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(t==null)return 0;let e;return t instanceof Uint8Array?e=t:e=this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return t==null?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return t===null?0:typeof t=="string"?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let r=0;r<t.length;++r){const i=t[r];if(i!==null)e.push(this.createObjectOffset(i));else throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.")}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};var Gs;(function(n){n[n.BUFFER=0]="BUFFER"})(Gs||(Gs={}));var Xs;(function(n){n[n.LZ4_FRAME=0]="LZ4_FRAME",n[n.ZSTD=1]="ZSTD"})(Xs||(Xs={}));class Mr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Mr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+ne),(e||new Mr).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):Xs.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):Gs.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,Xs.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,Gs.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,r){return Mr.startBodyCompression(t),Mr.addCodec(t,e),Mr.addMethod(t,r),Mr.endBodyCompression(t)}}class Th{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,r){return t.prep(8,16),t.writeInt64(BigInt(r??0)),t.writeInt64(BigInt(e??0)),t.offset()}}let Bh=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,r){return t.prep(8,16),t.writeInt64(BigInt(r??0)),t.writeInt64(BigInt(e??0)),t.offset()}},cr=class kl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(t,e){return(e||new kl).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRecordBatch(t,e){return t.setPosition(t.position()+ne),(e||new kl).__init(t.readInt32(t.position())+t.position(),t)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}nodes(t,e){const r=this.bb.__offset(this.bb_pos,6);return r?(e||new Bh).__init(this.bb.__vector(this.bb_pos+r)+t*16,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const r=this.bb.__offset(this.bb_pos,8);return r?(e||new Th).__init(this.bb.__vector(this.bb_pos+r)+t*16,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Mr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}},pi=class Dl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(t,e){return(e||new Dl).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryBatch(t,e){return t.setPosition(t.position()+ne),(e||new Dl).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new cr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}};var Li;(function(n){n[n.Little=0]="Little",n[n.Big=1]="Big"})(Li||(Li={}));var Zs;(function(n){n[n.DenseArray=0]="DenseArray"})(Zs||(Zs={}));class mn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new mn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+ne),(e||new mn).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,r){return mn.startInt(t),mn.addBitWidth(t,e),mn.addIsSigned(t,r),mn.endInt(t)}}class dr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new dr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+ne),(e||new dr).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new mn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):Zs.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,Zs.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class je{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new je).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+ne),(e||new je).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,r){return je.startKeyValue(t),je.addKey(t,e),je.addValue(t,r),je.endKeyValue(t)}}let tu=class ho{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(t,e){return(e||new ho).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBinary(t,e){return t.setPosition(t.position()+ne),(e||new ho).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(t){return ho.startBinary(t),ho.endBinary(t)}},eu=class mo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(t,e){return(e||new mo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBool(t,e){return t.setPosition(t.position()+ne),(e||new mo).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(t){return mo.startBool(t),mo.endBool(t)}},Ls=class gi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(t,e){return(e||new gi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDate(t,e){return t.setPosition(t.position()+ne),(e||new gi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dn.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,Dn.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(t,e){return gi.startDate(t),gi.addUnit(t,e),gi.endDate(t)}},_i=class Tr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(t,e){return(e||new Tr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDecimal(t,e){return t.setPosition(t.position()+ne),(e||new Tr).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(t,e,r,i){return Tr.startDecimal(t),Tr.addPrecision(t,e),Tr.addScale(t,r),Tr.addBitWidth(t,i),Tr.endDecimal(t)}},Rs=class bi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(t,e){return(e||new bi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDuration(t,e){return t.setPosition(t.position()+ne),(e||new bi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static endDuration(t){return t.endObject()}static createDuration(t,e){return bi.startDuration(t),bi.addUnit(t,e),bi.endDuration(t)}},Us=class yi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(t,e){return(e||new yi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeBinary(t,e){return t.setPosition(t.position()+ne),(e||new yi).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(t,e){return yi.startFixedSizeBinary(t),yi.addByteWidth(t,e),yi.endFixedSizeBinary(t)}},Vs=class vi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(t,e){return(e||new vi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeList(t,e){return t.setPosition(t.position()+ne),(e||new vi).__init(t.readInt32(t.position())+t.position(),t)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(t,e){return vi.startFixedSizeList(t),vi.addListSize(t,e),vi.endFixedSizeList(t)}};class Jn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Jn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+ne),(e||new Jn).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ye.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,Ye.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Jn.startFloatingPoint(t),Jn.addPrecision(t,e),Jn.endFloatingPoint(t)}}class Qn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Qn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+ne),(e||new Qn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):nr.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,nr.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Qn.startInterval(t),Qn.addUnit(t,e),Qn.endInterval(t)}}let nu=class po{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(t,e){return(e||new po).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsLargeBinary(t,e){return t.setPosition(t.position()+ne),(e||new po).__init(t.readInt32(t.position())+t.position(),t)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){return t.endObject()}static createLargeBinary(t){return po.startLargeBinary(t),po.endLargeBinary(t)}},ru=class go{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(t,e){return(e||new go).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsLargeUtf8(t,e){return t.setPosition(t.position()+ne),(e||new go).__init(t.readInt32(t.position())+t.position(),t)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){return t.endObject()}static createLargeUtf8(t){return go.startLargeUtf8(t),go.endLargeUtf8(t)}},iu=class _o{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(t,e){return(e||new _o).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsList(t,e){return t.setPosition(t.position()+ne),(e||new _o).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(t){return _o.startList(t),_o.endList(t)}},xs=class wi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(t,e){return(e||new wi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMap(t,e){return t.setPosition(t.position()+ne),(e||new wi).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(t,e){return wi.startMap(t),wi.addKeysSorted(t,e),wi.endMap(t)}},ou=class bo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(t,e){return(e||new bo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNull(t,e){return t.setPosition(t.position()+ne),(e||new bo).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(t){return bo.startNull(t),bo.endNull(t)}};class ti{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new ti).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+ne),(e||new ti).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return ti.startStruct_(t),ti.endStruct_(t)}}class vn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new vn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+ne),(e||new vn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,r){return vn.startTime(t),vn.addUnit(t,e),vn.addBitWidth(t,r),vn.endTime(t)}}class wn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new wn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+ne),(e||new wn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,r){return wn.startTimestamp(t),wn.addUnit(t,e),wn.addTimezone(t,r),wn.endTimestamp(t)}}class sn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new sn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+ne),(e||new sn).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):cn.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+t*4):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,cn.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addInt32(e[r]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,r){return sn.startUnion(t),sn.addMode(t,e),sn.addTypeIds(t,r),sn.endUnion(t)}}let su=class yo{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(t,e){return(e||new yo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUtf8(t,e){return t.setPosition(t.position()+ne),(e||new yo).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(t){return yo.startUtf8(t),yo.endUtf8(t)}};var de;(function(n){n[n.NONE=0]="NONE",n[n.Null=1]="Null",n[n.Int=2]="Int",n[n.FloatingPoint=3]="FloatingPoint",n[n.Binary=4]="Binary",n[n.Utf8=5]="Utf8",n[n.Bool=6]="Bool",n[n.Decimal=7]="Decimal",n[n.Date=8]="Date",n[n.Time=9]="Time",n[n.Timestamp=10]="Timestamp",n[n.Interval=11]="Interval",n[n.List=12]="List",n[n.Struct_=13]="Struct_",n[n.Union=14]="Union",n[n.FixedSizeBinary=15]="FixedSizeBinary",n[n.FixedSizeList=16]="FixedSizeList",n[n.Map=17]="Map",n[n.Duration=18]="Duration",n[n.LargeBinary=19]="LargeBinary",n[n.LargeUtf8=20]="LargeUtf8",n[n.LargeList=21]="LargeList",n[n.RunEndEncoded=22]="RunEndEncoded"})(de||(de={}));let bn=class zs{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(t,e){return(e||new zs).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsField(t,e){return t.setPosition(t.position()+ne),(e||new zs).__init(t.readInt32(t.position())+t.position(),t)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):de.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new dr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(t,e){const r=this.bb.__offset(this.bb_pos,14);return r?(e||new zs).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,16);return r?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,de.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}},qn=class ar{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(t,e){return(e||new ar).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSchema(t,e){return t.setPosition(t.position()+ne),(e||new ar).__init(t.readInt32(t.position())+t.position(),t)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Li.Little}fields(t,e){const r=this.bb.__offset(this.bb_pos,6);return r?(e||new bn).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,8);return r?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+t*8):BigInt(0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Li.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let r=e.length-1;r>=0;r--)t.addInt64(e[r]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(t,e,r,i,o){return ar.startSchema(t),ar.addEndianness(t,e),ar.addFields(t,r),ar.addCustomMetadata(t,i),ar.addFeatures(t,o),ar.endSchema(t)}};var Zt;(function(n){n[n.NONE=0]="NONE",n[n.Schema=1]="Schema",n[n.DictionaryBatch=2]="DictionaryBatch",n[n.RecordBatch=3]="RecordBatch",n[n.Tensor=4]="Tensor",n[n.SparseTensor=5]="SparseTensor"})(Zt||(Zt={}));const W_=void 0;function Wo(n){if(n===null)return"null";if(n===W_)return"undefined";switch(typeof n){case"number":return`${n}`;case"bigint":return`${n}`;case"string":return`"${n}"`}return typeof n[Symbol.toPrimitive]=="function"?n[Symbol.toPrimitive]("string"):ArrayBuffer.isView(n)?n instanceof BigInt64Array||n instanceof BigUint64Array?`[${[...n].map(t=>Wo(t))}]`:`[${n}]`:ArrayBuffer.isView(n)?`[${n}]`:JSON.stringify(n,(t,e)=>typeof e=="bigint"?`${e}`:e)}function _e(n){if(typeof n=="bigint"&&(n<Number.MIN_SAFE_INTEGER||n>Number.MAX_SAFE_INTEGER))throw new TypeError(`${n} is not safe to convert to a number.`);return Number(n)}function Mh(n,t){return _e(n/t)+_e(n%t)/_e(t)}const H_=Symbol.for("isArrowBigNum");function Un(n,...t){return t.length===0?Object.setPrototypeOf(se(this.TypedArray,n),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(n,...t),this.constructor.prototype)}Un.prototype[H_]=!0;Un.prototype.toJSON=function(){return`"${qo(this)}"`};Un.prototype.valueOf=function(n){return Ph(this,n)};Un.prototype.toString=function(){return qo(this)};Un.prototype[Symbol.toPrimitive]=function(n="default"){switch(n){case"number":return Ph(this);case"string":return qo(this);case"default":return K_(this)}return qo(this)};function Bi(...n){return Un.apply(this,n)}function Mi(...n){return Un.apply(this,n)}function Ho(...n){return Un.apply(this,n)}Object.setPrototypeOf(Bi.prototype,Object.create(Int32Array.prototype));Object.setPrototypeOf(Mi.prototype,Object.create(Uint32Array.prototype));Object.setPrototypeOf(Ho.prototype,Object.create(Uint32Array.prototype));Object.assign(Bi.prototype,Un.prototype,{constructor:Bi,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array});Object.assign(Mi.prototype,Un.prototype,{constructor:Mi,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array});Object.assign(Ho.prototype,Un.prototype,{constructor:Ho,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const q_=BigInt(4294967296)*BigInt(4294967296),Y_=q_-BigInt(1);function Ph(n,t){const{buffer:e,byteOffset:r,byteLength:i,signed:o}=n,s=new BigUint64Array(e,r,i/8),a=o&&s.at(-1)&BigInt(1)<<BigInt(63);let l=BigInt(0),c=0;if(a){for(const u of s)l|=(u^Y_)*(BigInt(1)<<BigInt(64*c++));l*=BigInt(-1),l-=BigInt(1)}else for(const u of s)l|=u*(BigInt(1)<<BigInt(64*c++));if(typeof t=="number"){const u=BigInt(Math.pow(10,t)),f=l/u,h=l%u;return _e(f)+_e(h)/_e(u)}return _e(l)}function qo(n){if(n.byteLength===8)return`${new n.BigIntArray(n.buffer,n.byteOffset,1)[0]}`;if(!n.signed)return Xa(n);let t=new Uint16Array(n.buffer,n.byteOffset,n.byteLength/2);if(new Int16Array([t.at(-1)])[0]>=0)return Xa(n);t=t.slice();let r=1;for(let o=0;o<t.length;o++){const s=t[o],a=~s+r;t[o]=a,r&=s===0?1:0}return`-${Xa(t)}`}function K_(n){return n.byteLength===8?new n.BigIntArray(n.buffer,n.byteOffset,1)[0]:qo(n)}function Xa(n){let t="";const e=new Uint32Array(2);let r=new Uint16Array(n.buffer,n.byteOffset,n.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let o=-1;const s=r.length-1;do{for(e[0]=r[o=0];o<s;)r[o++]=e[1]=e[0]/10,e[0]=(e[0]-e[1]*10<<16)+r[o];r[o]=e[1]=e[0]/10,e[0]=e[0]-e[1]*10,t=`${e[0]}${t}`}while(i[0]||i[1]||i[2]||i[3]);return t??"0"}class ec{static new(t,e){switch(e){case!0:return new Bi(t);case!1:return new Mi(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new Bi(t)}return t.byteLength===16?new Ho(t):new Mi(t)}static signed(t){return new Bi(t)}static unsigned(t){return new Mi(t)}static decimal(t){return new Ho(t)}constructor(t,e){return ec.new(t,e)}}var Nh,Ch,Fh,jh,Lh,Rh,Uh,Vh,xh,zh,Wh,Hh,qh,Yh,Kh,$h,Jh,Qh,Gh,Xh,Zh,tm;class ut{static isNull(t){return(t==null?void 0:t.typeId)===E.Null}static isInt(t){return(t==null?void 0:t.typeId)===E.Int}static isFloat(t){return(t==null?void 0:t.typeId)===E.Float}static isBinary(t){return(t==null?void 0:t.typeId)===E.Binary}static isLargeBinary(t){return(t==null?void 0:t.typeId)===E.LargeBinary}static isUtf8(t){return(t==null?void 0:t.typeId)===E.Utf8}static isLargeUtf8(t){return(t==null?void 0:t.typeId)===E.LargeUtf8}static isBool(t){return(t==null?void 0:t.typeId)===E.Bool}static isDecimal(t){return(t==null?void 0:t.typeId)===E.Decimal}static isDate(t){return(t==null?void 0:t.typeId)===E.Date}static isTime(t){return(t==null?void 0:t.typeId)===E.Time}static isTimestamp(t){return(t==null?void 0:t.typeId)===E.Timestamp}static isInterval(t){return(t==null?void 0:t.typeId)===E.Interval}static isDuration(t){return(t==null?void 0:t.typeId)===E.Duration}static isList(t){return(t==null?void 0:t.typeId)===E.List}static isStruct(t){return(t==null?void 0:t.typeId)===E.Struct}static isUnion(t){return(t==null?void 0:t.typeId)===E.Union}static isFixedSizeBinary(t){return(t==null?void 0:t.typeId)===E.FixedSizeBinary}static isFixedSizeList(t){return(t==null?void 0:t.typeId)===E.FixedSizeList}static isMap(t){return(t==null?void 0:t.typeId)===E.Map}static isDictionary(t){return(t==null?void 0:t.typeId)===E.Dictionary}static isDenseUnion(t){return ut.isUnion(t)&&t.mode===cn.Dense}static isSparseUnion(t){return ut.isUnion(t)&&t.mode===cn.Sparse}constructor(t){this.typeId=t}}Nh=Symbol.toStringTag;ut[Nh]=(n=>(n.children=null,n.ArrayType=Array,n.OffsetArrayType=Int32Array,n[Symbol.toStringTag]="DataType"))(ut.prototype);class Cr extends ut{constructor(){super(E.Null)}toString(){return"Null"}}Ch=Symbol.toStringTag;Cr[Ch]=(n=>n[Symbol.toStringTag]="Null")(Cr.prototype);class ni extends ut{constructor(t,e){super(E.Int),this.isSigned=t,this.bitWidth=e}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}Fh=Symbol.toStringTag;ni[Fh]=(n=>(n.isSigned=null,n.bitWidth=null,n[Symbol.toStringTag]="Int"))(ni.prototype);class Yo extends ni{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Yo.prototype,"ArrayType",{value:Int32Array});class ta extends ut{constructor(t){super(E.Float),this.precision=t}get ArrayType(){switch(this.precision){case Ye.HALF:return Uint16Array;case Ye.SINGLE:return Float32Array;case Ye.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}jh=Symbol.toStringTag;ta[jh]=(n=>(n.precision=null,n[Symbol.toStringTag]="Float"))(ta.prototype);class ea extends ut{constructor(){super(E.Binary)}toString(){return"Binary"}}Lh=Symbol.toStringTag;ea[Lh]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Binary"))(ea.prototype);class na extends ut{constructor(){super(E.LargeBinary)}toString(){return"LargeBinary"}}Rh=Symbol.toStringTag;na[Rh]=(n=>(n.ArrayType=Uint8Array,n.OffsetArrayType=BigInt64Array,n[Symbol.toStringTag]="LargeBinary"))(na.prototype);class ra extends ut{constructor(){super(E.Utf8)}toString(){return"Utf8"}}Uh=Symbol.toStringTag;ra[Uh]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Utf8"))(ra.prototype);class ia extends ut{constructor(){super(E.LargeUtf8)}toString(){return"LargeUtf8"}}Vh=Symbol.toStringTag;ia[Vh]=(n=>(n.ArrayType=Uint8Array,n.OffsetArrayType=BigInt64Array,n[Symbol.toStringTag]="LargeUtf8"))(ia.prototype);class oa extends ut{constructor(){super(E.Bool)}toString(){return"Bool"}}xh=Symbol.toStringTag;oa[xh]=(n=>(n.ArrayType=Uint8Array,n[Symbol.toStringTag]="Bool"))(oa.prototype);class sa extends ut{constructor(t,e,r=128){super(E.Decimal),this.scale=t,this.precision=e,this.bitWidth=r}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}zh=Symbol.toStringTag;sa[zh]=(n=>(n.scale=null,n.precision=null,n.ArrayType=Uint32Array,n[Symbol.toStringTag]="Decimal"))(sa.prototype);class aa extends ut{constructor(t){super(E.Date),this.unit=t}toString(){return`Date${(this.unit+1)*32}<${Dn[this.unit]}>`}get ArrayType(){return this.unit===Dn.DAY?Int32Array:BigInt64Array}}Wh=Symbol.toStringTag;aa[Wh]=(n=>(n.unit=null,n[Symbol.toStringTag]="Date"))(aa.prototype);class la extends ut{constructor(t,e){super(E.Time),this.unit=t,this.bitWidth=e}toString(){return`Time${this.bitWidth}<${yt[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}Hh=Symbol.toStringTag;la[Hh]=(n=>(n.unit=null,n.bitWidth=null,n[Symbol.toStringTag]="Time"))(la.prototype);class ca extends ut{constructor(t,e){super(E.Timestamp),this.unit=t,this.timezone=e}toString(){return`Timestamp<${yt[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}qh=Symbol.toStringTag;ca[qh]=(n=>(n.unit=null,n.timezone=null,n.ArrayType=BigInt64Array,n[Symbol.toStringTag]="Timestamp"))(ca.prototype);class ua extends ut{constructor(t){super(E.Interval),this.unit=t}toString(){return`Interval<${nr[this.unit]}>`}}Yh=Symbol.toStringTag;ua[Yh]=(n=>(n.unit=null,n.ArrayType=Int32Array,n[Symbol.toStringTag]="Interval"))(ua.prototype);class fa extends ut{constructor(t){super(E.Duration),this.unit=t}toString(){return`Duration<${yt[this.unit]}>`}}Kh=Symbol.toStringTag;fa[Kh]=(n=>(n.unit=null,n.ArrayType=BigInt64Array,n[Symbol.toStringTag]="Duration"))(fa.prototype);class da extends ut{constructor(t){super(E.List),this.children=[t]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}$h=Symbol.toStringTag;da[$h]=(n=>(n.children=null,n[Symbol.toStringTag]="List"))(da.prototype);class tn extends ut{constructor(t){super(E.Struct),this.children=t}toString(){return`Struct<{${this.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Jh=Symbol.toStringTag;tn[Jh]=(n=>(n.children=null,n[Symbol.toStringTag]="Struct"))(tn.prototype);class ha extends ut{constructor(t,e,r){super(E.Union),this.mode=t,this.children=r,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce((i,o,s)=>(i[o]=s)&&i||i,Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(t=>`${t.type}`).join(" | ")}>`}}Qh=Symbol.toStringTag;ha[Qh]=(n=>(n.mode=null,n.typeIds=null,n.children=null,n.typeIdToChildIndex=null,n.ArrayType=Int8Array,n[Symbol.toStringTag]="Union"))(ha.prototype);class ma extends ut{constructor(t){super(E.FixedSizeBinary),this.byteWidth=t}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}Gh=Symbol.toStringTag;ma[Gh]=(n=>(n.byteWidth=null,n.ArrayType=Uint8Array,n[Symbol.toStringTag]="FixedSizeBinary"))(ma.prototype);class pa extends ut{constructor(t,e){super(E.FixedSizeList),this.listSize=t,this.children=[e]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}Xh=Symbol.toStringTag;pa[Xh]=(n=>(n.children=null,n.listSize=null,n[Symbol.toStringTag]="FixedSizeList"))(pa.prototype);class ga extends ut{constructor(t,e=!1){var r,i,o;if(super(E.Map),this.children=[t],this.keysSorted=e,t&&(t.name="entries",!((r=t==null?void 0:t.type)===null||r===void 0)&&r.children)){const s=(i=t==null?void 0:t.type)===null||i===void 0?void 0:i.children[0];s&&(s.name="key");const a=(o=t==null?void 0:t.type)===null||o===void 0?void 0:o.children[1];a&&(a.name="value")}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Zh=Symbol.toStringTag;ga[Zh]=(n=>(n.children=null,n.keysSorted=null,n[Symbol.toStringTag]="Map_"))(ga.prototype);const $_=(n=>()=>++n)(-1);class Ri extends ut{constructor(t,e,r,i){super(E.Dictionary),this.indices=e,this.dictionary=t,this.isOrdered=i||!1,this.id=r==null?$_():_e(r)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}tm=Symbol.toStringTag;Ri[tm]=(n=>(n.id=null,n.indices=null,n.isOrdered=null,n.dictionary=null,n[Symbol.toStringTag]="Dictionary"))(Ri.prototype);function ur(n){const t=n;switch(n.typeId){case E.Decimal:return n.bitWidth/32;case E.Interval:return 1+t.unit;case E.FixedSizeList:return t.listSize;case E.FixedSizeBinary:return t.byteWidth;default:return 1}}class zt{visitMany(t,...e){return t.map((r,i)=>this.visit(r,...e.map(o=>o[i])))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return J_(this,t,e)}getVisitFnByTypeId(t,e=!0){return Si(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitLargeUtf8(t,...e){return null}visitBinary(t,...e){return null}visitLargeBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitDuration(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function J_(n,t,e=!0){return typeof t=="number"?Si(n,t,e):typeof t=="string"&&t in E?Si(n,E[t],e):t&&t instanceof ut?Si(n,au(t),e):t!=null&&t.type&&t.type instanceof ut?Si(n,au(t.type),e):Si(n,E.NONE,e)}function Si(n,t,e=!0){let r=null;switch(t){case E.Null:r=n.visitNull;break;case E.Bool:r=n.visitBool;break;case E.Int:r=n.visitInt;break;case E.Int8:r=n.visitInt8||n.visitInt;break;case E.Int16:r=n.visitInt16||n.visitInt;break;case E.Int32:r=n.visitInt32||n.visitInt;break;case E.Int64:r=n.visitInt64||n.visitInt;break;case E.Uint8:r=n.visitUint8||n.visitInt;break;case E.Uint16:r=n.visitUint16||n.visitInt;break;case E.Uint32:r=n.visitUint32||n.visitInt;break;case E.Uint64:r=n.visitUint64||n.visitInt;break;case E.Float:r=n.visitFloat;break;case E.Float16:r=n.visitFloat16||n.visitFloat;break;case E.Float32:r=n.visitFloat32||n.visitFloat;break;case E.Float64:r=n.visitFloat64||n.visitFloat;break;case E.Utf8:r=n.visitUtf8;break;case E.LargeUtf8:r=n.visitLargeUtf8;break;case E.Binary:r=n.visitBinary;break;case E.LargeBinary:r=n.visitLargeBinary;break;case E.FixedSizeBinary:r=n.visitFixedSizeBinary;break;case E.Date:r=n.visitDate;break;case E.DateDay:r=n.visitDateDay||n.visitDate;break;case E.DateMillisecond:r=n.visitDateMillisecond||n.visitDate;break;case E.Timestamp:r=n.visitTimestamp;break;case E.TimestampSecond:r=n.visitTimestampSecond||n.visitTimestamp;break;case E.TimestampMillisecond:r=n.visitTimestampMillisecond||n.visitTimestamp;break;case E.TimestampMicrosecond:r=n.visitTimestampMicrosecond||n.visitTimestamp;break;case E.TimestampNanosecond:r=n.visitTimestampNanosecond||n.visitTimestamp;break;case E.Time:r=n.visitTime;break;case E.TimeSecond:r=n.visitTimeSecond||n.visitTime;break;case E.TimeMillisecond:r=n.visitTimeMillisecond||n.visitTime;break;case E.TimeMicrosecond:r=n.visitTimeMicrosecond||n.visitTime;break;case E.TimeNanosecond:r=n.visitTimeNanosecond||n.visitTime;break;case E.Decimal:r=n.visitDecimal;break;case E.List:r=n.visitList;break;case E.Struct:r=n.visitStruct;break;case E.Union:r=n.visitUnion;break;case E.DenseUnion:r=n.visitDenseUnion||n.visitUnion;break;case E.SparseUnion:r=n.visitSparseUnion||n.visitUnion;break;case E.Dictionary:r=n.visitDictionary;break;case E.Interval:r=n.visitInterval;break;case E.IntervalDayTime:r=n.visitIntervalDayTime||n.visitInterval;break;case E.IntervalYearMonth:r=n.visitIntervalYearMonth||n.visitInterval;break;case E.Duration:r=n.visitDuration;break;case E.DurationSecond:r=n.visitDurationSecond||n.visitDuration;break;case E.DurationMillisecond:r=n.visitDurationMillisecond||n.visitDuration;break;case E.DurationMicrosecond:r=n.visitDurationMicrosecond||n.visitDuration;break;case E.DurationNanosecond:r=n.visitDurationNanosecond||n.visitDuration;break;case E.FixedSizeList:r=n.visitFixedSizeList;break;case E.Map:r=n.visitMap;break}if(typeof r=="function")return r;if(!e)return()=>null;throw new Error(`Unrecognized type '${E[t]}'`)}function au(n){switch(n.typeId){case E.Null:return E.Null;case E.Int:{const{bitWidth:t,isSigned:e}=n;switch(t){case 8:return e?E.Int8:E.Uint8;case 16:return e?E.Int16:E.Uint16;case 32:return e?E.Int32:E.Uint32;case 64:return e?E.Int64:E.Uint64}return E.Int}case E.Float:switch(n.precision){case Ye.HALF:return E.Float16;case Ye.SINGLE:return E.Float32;case Ye.DOUBLE:return E.Float64}return E.Float;case E.Binary:return E.Binary;case E.LargeBinary:return E.LargeBinary;case E.Utf8:return E.Utf8;case E.LargeUtf8:return E.LargeUtf8;case E.Bool:return E.Bool;case E.Decimal:return E.Decimal;case E.Time:switch(n.unit){case yt.SECOND:return E.TimeSecond;case yt.MILLISECOND:return E.TimeMillisecond;case yt.MICROSECOND:return E.TimeMicrosecond;case yt.NANOSECOND:return E.TimeNanosecond}return E.Time;case E.Timestamp:switch(n.unit){case yt.SECOND:return E.TimestampSecond;case yt.MILLISECOND:return E.TimestampMillisecond;case yt.MICROSECOND:return E.TimestampMicrosecond;case yt.NANOSECOND:return E.TimestampNanosecond}return E.Timestamp;case E.Date:switch(n.unit){case Dn.DAY:return E.DateDay;case Dn.MILLISECOND:return E.DateMillisecond}return E.Date;case E.Interval:switch(n.unit){case nr.DAY_TIME:return E.IntervalDayTime;case nr.YEAR_MONTH:return E.IntervalYearMonth}return E.Interval;case E.Duration:switch(n.unit){case yt.SECOND:return E.DurationSecond;case yt.MILLISECOND:return E.DurationMillisecond;case yt.MICROSECOND:return E.DurationMicrosecond;case yt.NANOSECOND:return E.DurationNanosecond}return E.Duration;case E.Map:return E.Map;case E.List:return E.List;case E.Struct:return E.Struct;case E.Union:switch(n.mode){case cn.Dense:return E.DenseUnion;case cn.Sparse:return E.SparseUnion}return E.Union;case E.FixedSizeBinary:return E.FixedSizeBinary;case E.FixedSizeList:return E.FixedSizeList;case E.Dictionary:return E.Dictionary}throw new Error(`Unrecognized type '${E[n.typeId]}'`)}zt.prototype.visitInt8=null;zt.prototype.visitInt16=null;zt.prototype.visitInt32=null;zt.prototype.visitInt64=null;zt.prototype.visitUint8=null;zt.prototype.visitUint16=null;zt.prototype.visitUint32=null;zt.prototype.visitUint64=null;zt.prototype.visitFloat16=null;zt.prototype.visitFloat32=null;zt.prototype.visitFloat64=null;zt.prototype.visitDateDay=null;zt.prototype.visitDateMillisecond=null;zt.prototype.visitTimestampSecond=null;zt.prototype.visitTimestampMillisecond=null;zt.prototype.visitTimestampMicrosecond=null;zt.prototype.visitTimestampNanosecond=null;zt.prototype.visitTimeSecond=null;zt.prototype.visitTimeMillisecond=null;zt.prototype.visitTimeMicrosecond=null;zt.prototype.visitTimeNanosecond=null;zt.prototype.visitDenseUnion=null;zt.prototype.visitSparseUnion=null;zt.prototype.visitIntervalDayTime=null;zt.prototype.visitIntervalYearMonth=null;zt.prototype.visitDuration=null;zt.prototype.visitDurationSecond=null;zt.prototype.visitDurationMillisecond=null;zt.prototype.visitDurationMicrosecond=null;zt.prototype.visitDurationNanosecond=null;const em=new Float64Array(1),li=new Uint32Array(em.buffer);function nm(n){const t=(n&31744)>>10,e=(n&1023)/1024,r=Math.pow(-1,(n&32768)>>15);switch(t){case 31:return r*(e?Number.NaN:1/0);case 0:return r*(e?6103515625e-14*e:0)}return r*Math.pow(2,t-15)*(1+e)}function Q_(n){if(n!==n)return 32256;em[0]=n;const t=(li[1]&2147483648)>>16&65535;let e=li[1]&2146435072,r=0;return e>=1089470464?li[0]>0?e=31744:(e=(e&2080374784)>>16,r=(li[1]&1048575)>>10):e<=1056964608?(r=1048576+(li[1]&1048575),r=1048576+(r<<(e>>20)-998)>>21,e=0):(e=e-1056964608>>10,r=(li[1]&1048575)+512>>10),t|e|r&65535}class St extends zt{}function kt(n){return(t,e,r)=>{if(t.setValid(e,r!=null))return n(t,e,r)}}const G_=(n,t,e)=>{n[t]=Math.floor(e/864e5)},rm=(n,t,e,r)=>{if(e+1<t.length){const i=_e(t[e]),o=_e(t[e+1]);n.set(r.subarray(0,o-i),i)}},X_=({offset:n,values:t},e,r)=>{const i=n+e;r?t[i>>3]|=1<<i%8:t[i>>3]&=~(1<<i%8)},Sr=({values:n},t,e)=>{n[t]=e},nc=({values:n},t,e)=>{n[t]=e},im=({values:n},t,e)=>{n[t]=Q_(e)},Z_=(n,t,e)=>{switch(n.type.precision){case Ye.HALF:return im(n,t,e);case Ye.SINGLE:case Ye.DOUBLE:return nc(n,t,e)}},om=({values:n},t,e)=>{G_(n,t,e.valueOf())},sm=({values:n},t,e)=>{n[t]=BigInt(e)},tb=({stride:n,values:t},e,r)=>{t.set(r.subarray(0,n),n*e)},am=({values:n,valueOffsets:t},e,r)=>rm(n,t,e,r),lm=({values:n,valueOffsets:t},e,r)=>rm(n,t,e,Gl(r)),eb=(n,t,e)=>{n.type.unit===Dn.DAY?om(n,t,e):sm(n,t,e)},cm=({values:n},t,e)=>{n[t]=BigInt(e/1e3)},um=({values:n},t,e)=>{n[t]=BigInt(e)},fm=({values:n},t,e)=>{n[t]=BigInt(e*1e3)},dm=({values:n},t,e)=>{n[t]=BigInt(e*1e6)},nb=(n,t,e)=>{switch(n.type.unit){case yt.SECOND:return cm(n,t,e);case yt.MILLISECOND:return um(n,t,e);case yt.MICROSECOND:return fm(n,t,e);case yt.NANOSECOND:return dm(n,t,e)}},hm=({values:n},t,e)=>{n[t]=e},mm=({values:n},t,e)=>{n[t]=e},pm=({values:n},t,e)=>{n[t]=e},gm=({values:n},t,e)=>{n[t]=e},rb=(n,t,e)=>{switch(n.type.unit){case yt.SECOND:return hm(n,t,e);case yt.MILLISECOND:return mm(n,t,e);case yt.MICROSECOND:return pm(n,t,e);case yt.NANOSECOND:return gm(n,t,e)}},ib=({values:n,stride:t},e,r)=>{n.set(r.subarray(0,t),t*e)},ob=(n,t,e)=>{const r=n.children[0],i=n.valueOffsets,o=En.getVisitFn(r);if(Array.isArray(e))for(let s=-1,a=i[t],l=i[t+1];a<l;)o(r,a++,e[++s]);else for(let s=-1,a=i[t],l=i[t+1];a<l;)o(r,a++,e.get(++s))},sb=(n,t,e)=>{const r=n.children[0],{valueOffsets:i}=n,o=En.getVisitFn(r);let{[t]:s,[t+1]:a}=i;const l=e instanceof Map?e.entries():Object.entries(e);for(const c of l)if(o(r,s,c),++s>=a)break},ab=(n,t)=>(e,r,i,o)=>r&&e(r,n,t[o]),lb=(n,t)=>(e,r,i,o)=>r&&e(r,n,t.get(o)),cb=(n,t)=>(e,r,i,o)=>r&&e(r,n,t.get(i.name)),ub=(n,t)=>(e,r,i,o)=>r&&e(r,n,t[i.name]),fb=(n,t,e)=>{const r=n.type.children.map(o=>En.getVisitFn(o.type)),i=e instanceof Map?cb(t,e):e instanceof ce?lb(t,e):Array.isArray(e)?ab(t,e):ub(t,e);n.type.children.forEach((o,s)=>i(r[s],n.children[s],o,s))},db=(n,t,e)=>{n.type.mode===cn.Dense?_m(n,t,e):bm(n,t,e)},_m=(n,t,e)=>{const r=n.type.typeIdToChildIndex[n.typeIds[t]],i=n.children[r];En.visit(i,n.valueOffsets[t],e)},bm=(n,t,e)=>{const r=n.type.typeIdToChildIndex[n.typeIds[t]],i=n.children[r];En.visit(i,t,e)},hb=(n,t,e)=>{var r;(r=n.dictionary)===null||r===void 0||r.set(n.values[t],e)},mb=(n,t,e)=>{n.type.unit===nr.DAY_TIME?ym(n,t,e):vm(n,t,e)},ym=({values:n},t,e)=>{n.set(e.subarray(0,2),2*t)},vm=({values:n},t,e)=>{n[t]=e[0]*12+e[1]%12},wm=({values:n},t,e)=>{n[t]=e},Sm=({values:n},t,e)=>{n[t]=e},Im=({values:n},t,e)=>{n[t]=e},Om=({values:n},t,e)=>{n[t]=e},pb=(n,t,e)=>{switch(n.type.unit){case yt.SECOND:return wm(n,t,e);case yt.MILLISECOND:return Sm(n,t,e);case yt.MICROSECOND:return Im(n,t,e);case yt.NANOSECOND:return Om(n,t,e)}},gb=(n,t,e)=>{const{stride:r}=n,i=n.children[0],o=En.getVisitFn(i);if(Array.isArray(e))for(let s=-1,a=t*r;++s<r;)o(i,a+s,e[s]);else for(let s=-1,a=t*r;++s<r;)o(i,a+s,e.get(s))};St.prototype.visitBool=kt(X_);St.prototype.visitInt=kt(Sr);St.prototype.visitInt8=kt(Sr);St.prototype.visitInt16=kt(Sr);St.prototype.visitInt32=kt(Sr);St.prototype.visitInt64=kt(Sr);St.prototype.visitUint8=kt(Sr);St.prototype.visitUint16=kt(Sr);St.prototype.visitUint32=kt(Sr);St.prototype.visitUint64=kt(Sr);St.prototype.visitFloat=kt(Z_);St.prototype.visitFloat16=kt(im);St.prototype.visitFloat32=kt(nc);St.prototype.visitFloat64=kt(nc);St.prototype.visitUtf8=kt(lm);St.prototype.visitLargeUtf8=kt(lm);St.prototype.visitBinary=kt(am);St.prototype.visitLargeBinary=kt(am);St.prototype.visitFixedSizeBinary=kt(tb);St.prototype.visitDate=kt(eb);St.prototype.visitDateDay=kt(om);St.prototype.visitDateMillisecond=kt(sm);St.prototype.visitTimestamp=kt(nb);St.prototype.visitTimestampSecond=kt(cm);St.prototype.visitTimestampMillisecond=kt(um);St.prototype.visitTimestampMicrosecond=kt(fm);St.prototype.visitTimestampNanosecond=kt(dm);St.prototype.visitTime=kt(rb);St.prototype.visitTimeSecond=kt(hm);St.prototype.visitTimeMillisecond=kt(mm);St.prototype.visitTimeMicrosecond=kt(pm);St.prototype.visitTimeNanosecond=kt(gm);St.prototype.visitDecimal=kt(ib);St.prototype.visitList=kt(ob);St.prototype.visitStruct=kt(fb);St.prototype.visitUnion=kt(db);St.prototype.visitDenseUnion=kt(_m);St.prototype.visitSparseUnion=kt(bm);St.prototype.visitDictionary=kt(hb);St.prototype.visitInterval=kt(mb);St.prototype.visitIntervalDayTime=kt(ym);St.prototype.visitIntervalYearMonth=kt(vm);St.prototype.visitDuration=kt(pb);St.prototype.visitDurationSecond=kt(wm);St.prototype.visitDurationMillisecond=kt(Sm);St.prototype.visitDurationMicrosecond=kt(Im);St.prototype.visitDurationNanosecond=kt(Om);St.prototype.visitFixedSizeList=kt(gb);St.prototype.visitMap=kt(sb);const En=new St,Cn=Symbol.for("parent"),Pi=Symbol.for("rowIndex");class rc{constructor(t,e){return this[Cn]=t,this[Pi]=e,new Proxy(this,new bb)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Pi],e=this[Cn],r=e.type.children,i={};for(let o=-1,s=r.length;++o<s;)i[r[o].name]=un.visit(e.children[o],t);return i}toString(){return`{${[...this].map(([t,e])=>`${Wo(t)}: ${Wo(e)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new _b(this[Cn],this[Pi])}}class _b{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,un.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(rc.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Cn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Pi]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class bb{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Cn].type.children.map(e=>e.name)}has(t,e){return t[Cn].type.children.findIndex(r=>r.name===e)!==-1}getOwnPropertyDescriptor(t,e){if(t[Cn].type.children.findIndex(r=>r.name===e)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const r=t[Cn].type.children.findIndex(i=>i.name===e);if(r!==-1){const i=un.visit(t[Cn].children[r],t[Pi]);return Reflect.set(t,e,i),i}}set(t,e,r){const i=t[Cn].type.children.findIndex(o=>o.name===e);return i!==-1?(En.visit(t[Cn].children[i],t[Pi],r),Reflect.set(t,e,r)):Reflect.has(t,e)||typeof e=="symbol"?Reflect.set(t,e,r):!1}}class pt extends zt{}function It(n){return(t,e)=>t.getValid(e)?n(t,e):null}const yb=(n,t)=>864e5*n[t],vb=(n,t)=>null,km=(n,t,e)=>{if(e+1>=t.length)return null;const r=_e(t[e]),i=_e(t[e+1]);return n.subarray(r,i)},wb=({offset:n,values:t},e)=>{const r=n+e;return(t[r>>3]&1<<r%8)!==0},Dm=({values:n},t)=>yb(n,t),Em=({values:n},t)=>_e(n[t]),Ur=({stride:n,values:t},e)=>t[n*e],Sb=({stride:n,values:t},e)=>nm(t[n*e]),Am=({values:n},t)=>n[t],Ib=({stride:n,values:t},e)=>t.subarray(n*e,n*(e+1)),Tm=({values:n,valueOffsets:t},e)=>km(n,t,e),Bm=({values:n,valueOffsets:t},e)=>{const r=km(n,t,e);return r!==null?wl(r):null},Ob=({values:n},t)=>n[t],kb=({type:n,values:t},e)=>n.precision!==Ye.HALF?t[e]:nm(t[e]),Db=(n,t)=>n.type.unit===Dn.DAY?Dm(n,t):Em(n,t),Mm=({values:n},t)=>1e3*_e(n[t]),Pm=({values:n},t)=>_e(n[t]),Nm=({values:n},t)=>Mh(n[t],BigInt(1e3)),Cm=({values:n},t)=>Mh(n[t],BigInt(1e6)),Eb=(n,t)=>{switch(n.type.unit){case yt.SECOND:return Mm(n,t);case yt.MILLISECOND:return Pm(n,t);case yt.MICROSECOND:return Nm(n,t);case yt.NANOSECOND:return Cm(n,t)}},Fm=({values:n},t)=>n[t],jm=({values:n},t)=>n[t],Lm=({values:n},t)=>n[t],Rm=({values:n},t)=>n[t],Ab=(n,t)=>{switch(n.type.unit){case yt.SECOND:return Fm(n,t);case yt.MILLISECOND:return jm(n,t);case yt.MICROSECOND:return Lm(n,t);case yt.NANOSECOND:return Rm(n,t)}},Tb=({values:n,stride:t},e)=>ec.decimal(n.subarray(t*e,t*(e+1))),Bb=(n,t)=>{const{valueOffsets:e,stride:r,children:i}=n,{[t*r]:o,[t*r+1]:s}=e,l=i[0].slice(o,s-o);return new ce([l])},Mb=(n,t)=>{const{valueOffsets:e,children:r}=n,{[t]:i,[t+1]:o}=e,s=r[0];return new ic(s.slice(i,o-i))},Pb=(n,t)=>new rc(n,t),Nb=(n,t)=>n.type.mode===cn.Dense?Um(n,t):Vm(n,t),Um=(n,t)=>{const e=n.type.typeIdToChildIndex[n.typeIds[t]],r=n.children[e];return un.visit(r,n.valueOffsets[t])},Vm=(n,t)=>{const e=n.type.typeIdToChildIndex[n.typeIds[t]],r=n.children[e];return un.visit(r,t)},Cb=(n,t)=>{var e;return(e=n.dictionary)===null||e===void 0?void 0:e.get(n.values[t])},Fb=(n,t)=>n.type.unit===nr.DAY_TIME?xm(n,t):zm(n,t),xm=({values:n},t)=>n.subarray(2*t,2*(t+1)),zm=({values:n},t)=>{const e=n[t],r=new Int32Array(2);return r[0]=Math.trunc(e/12),r[1]=Math.trunc(e%12),r},Wm=({values:n},t)=>n[t],Hm=({values:n},t)=>n[t],qm=({values:n},t)=>n[t],Ym=({values:n},t)=>n[t],jb=(n,t)=>{switch(n.type.unit){case yt.SECOND:return Wm(n,t);case yt.MILLISECOND:return Hm(n,t);case yt.MICROSECOND:return qm(n,t);case yt.NANOSECOND:return Ym(n,t)}},Lb=(n,t)=>{const{stride:e,children:r}=n,o=r[0].slice(t*e,e);return new ce([o])};pt.prototype.visitNull=It(vb);pt.prototype.visitBool=It(wb);pt.prototype.visitInt=It(Ob);pt.prototype.visitInt8=It(Ur);pt.prototype.visitInt16=It(Ur);pt.prototype.visitInt32=It(Ur);pt.prototype.visitInt64=It(Am);pt.prototype.visitUint8=It(Ur);pt.prototype.visitUint16=It(Ur);pt.prototype.visitUint32=It(Ur);pt.prototype.visitUint64=It(Am);pt.prototype.visitFloat=It(kb);pt.prototype.visitFloat16=It(Sb);pt.prototype.visitFloat32=It(Ur);pt.prototype.visitFloat64=It(Ur);pt.prototype.visitUtf8=It(Bm);pt.prototype.visitLargeUtf8=It(Bm);pt.prototype.visitBinary=It(Tm);pt.prototype.visitLargeBinary=It(Tm);pt.prototype.visitFixedSizeBinary=It(Ib);pt.prototype.visitDate=It(Db);pt.prototype.visitDateDay=It(Dm);pt.prototype.visitDateMillisecond=It(Em);pt.prototype.visitTimestamp=It(Eb);pt.prototype.visitTimestampSecond=It(Mm);pt.prototype.visitTimestampMillisecond=It(Pm);pt.prototype.visitTimestampMicrosecond=It(Nm);pt.prototype.visitTimestampNanosecond=It(Cm);pt.prototype.visitTime=It(Ab);pt.prototype.visitTimeSecond=It(Fm);pt.prototype.visitTimeMillisecond=It(jm);pt.prototype.visitTimeMicrosecond=It(Lm);pt.prototype.visitTimeNanosecond=It(Rm);pt.prototype.visitDecimal=It(Tb);pt.prototype.visitList=It(Bb);pt.prototype.visitStruct=It(Pb);pt.prototype.visitUnion=It(Nb);pt.prototype.visitDenseUnion=It(Um);pt.prototype.visitSparseUnion=It(Vm);pt.prototype.visitDictionary=It(Cb);pt.prototype.visitInterval=It(Fb);pt.prototype.visitIntervalDayTime=It(xm);pt.prototype.visitIntervalYearMonth=It(zm);pt.prototype.visitDuration=It(jb);pt.prototype.visitDurationSecond=It(Wm);pt.prototype.visitDurationMillisecond=It(Hm);pt.prototype.visitDurationMicrosecond=It(qm);pt.prototype.visitDurationNanosecond=It(Ym);pt.prototype.visitFixedSizeList=It(Lb);pt.prototype.visitMap=It(Mb);const un=new pt,Ii=Symbol.for("keys"),Ni=Symbol.for("vals"),Oi=Symbol.for("kKeysAsStrings"),El=Symbol.for("_kKeysAsStrings");class ic{constructor(t){return this[Ii]=new ce([t.children[0]]).memoize(),this[Ni]=t.children[1],new Proxy(this,new Ub)}get[Oi](){return this[El]||(this[El]=Array.from(this[Ii].toArray(),String))}[Symbol.iterator](){return new Rb(this[Ii],this[Ni])}get size(){return this[Ii].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ii],e=this[Ni],r={};for(let i=-1,o=t.length;++i<o;)r[t.get(i)]=un.visit(e,i);return r}toString(){return`{${[...this].map(([t,e])=>`${Wo(t)}: ${Wo(e)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class Rb{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),un.visit(this.vals,t)]})}}class Ub{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Oi]}has(t,e){return t[Oi].includes(e)}getOwnPropertyDescriptor(t,e){if(t[Oi].indexOf(e)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const r=t[Oi].indexOf(e);if(r!==-1){const i=un.visit(Reflect.get(t,Ni),r);return Reflect.set(t,e,i),i}}set(t,e,r){const i=t[Oi].indexOf(e);return i!==-1?(En.visit(Reflect.get(t,Ni),i,r),Reflect.set(t,e,r)):Reflect.has(t,e)?Reflect.set(t,e,r):!1}}Object.defineProperties(ic.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Ii]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ni]:{writable:!0,enumerable:!1,configurable:!1,value:null},[El]:{writable:!0,enumerable:!1,configurable:!1,value:null}});let lu;function Km(n,t,e,r){const{length:i=0}=n;let o=typeof t!="number"?0:t,s=typeof e!="number"?i:e;return o<0&&(o=(o%i+i)%i),s<0&&(s=(s%i+i)%i),s<o&&(lu=o,o=s,s=lu),s>i&&(s=i),r?r(n,o,s):[o,s]}const oc=(n,t)=>n<0?t+n:n,cu=n=>n!==n;function qi(n){if(typeof n!=="object"||n===null)return cu(n)?cu:e=>e===n;if(n instanceof Date){const e=n.valueOf();return r=>r instanceof Date?r.valueOf()===e:!1}return ArrayBuffer.isView(n)?e=>e?L_(n,e):!1:n instanceof Map?xb(n):Array.isArray(n)?Vb(n):n instanceof ce?zb(n):Wb(n,!0)}function Vb(n){const t=[];for(let e=-1,r=n.length;++e<r;)t[e]=qi(n[e]);return Na(t)}function xb(n){let t=-1;const e=[];for(const r of n.values())e[++t]=qi(r);return Na(e)}function zb(n){const t=[];for(let e=-1,r=n.length;++e<r;)t[e]=qi(n.get(e));return Na(t)}function Wb(n,t=!1){const e=Object.keys(n);if(!t&&e.length===0)return()=>!1;const r=[];for(let i=-1,o=e.length;++i<o;)r[i]=qi(n[e[i]]);return Na(r,e)}function Na(n,t){return e=>{if(!e||typeof e!="object")return!1;switch(e.constructor){case Array:return Hb(n,e);case Map:return uu(n,e,e.keys());case ic:case rc:case Object:case void 0:return uu(n,e,t||Object.keys(e))}return e instanceof ce?qb(n,e):!1}}function Hb(n,t){const e=n.length;if(t.length!==e)return!1;for(let r=-1;++r<e;)if(!n[r](t[r]))return!1;return!0}function qb(n,t){const e=n.length;if(t.length!==e)return!1;for(let r=-1;++r<e;)if(!n[r](t.get(r)))return!1;return!0}function uu(n,t,e){const r=e[Symbol.iterator](),i=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),o=t instanceof Map?t.values():Object.values(t)[Symbol.iterator]();let s=0;const a=n.length;let l=o.next(),c=r.next(),u=i.next();for(;s<a&&!c.done&&!u.done&&!l.done&&!(c.value!==u.value||!n[s](l.value));++s,c=r.next(),u=i.next(),l=o.next());return s===a&&c.done&&u.done&&l.done?!0:(r.return&&r.return(),i.return&&i.return(),o.return&&o.return(),!1)}function $m(n,t,e,r){return(e&1<<r)!==0}function Yb(n,t,e,r){return(e&1<<r)>>r}function fu(n,t,e){const r=e.byteLength+7&-8;if(n>0||e.byteLength<r){const i=new Uint8Array(r);return i.set(n%8===0?e.subarray(n>>3):Al(new sc(e,n,t,null,$m)).subarray(0,r)),i}return e}function Al(n){const t=[];let e=0,r=0,i=0;for(const s of n)s&&(i|=1<<r),++r===8&&(t[e++]=i,i=r=0);(e===0||r>0)&&(t[e++]=i);const o=new Uint8Array(t.length+7&-8);return o.set(t),o}class sc{constructor(t,e,r,i,o){this.bytes=t,this.length=r,this.context=i,this.get=o,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(this.bit===8&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Tl(n,t,e){if(e-t<=0)return 0;if(e-t<8){let o=0;for(const s of new sc(n,t,e-t,n,Yb))o+=s;return o}const r=e>>3<<3,i=t+(t%8===0?0:8-t%8);return Tl(n,t,i)+Tl(n,r,e)+Kb(n,i>>3,r-i>>3)}function Kb(n,t,e){let r=0,i=Math.trunc(t);const o=new DataView(n.buffer,n.byteOffset,n.byteLength),s=e===void 0?n.byteLength:i+e;for(;s-i>=4;)r+=Za(o.getUint32(i)),i+=4;for(;s-i>=2;)r+=Za(o.getUint16(i)),i+=2;for(;s-i>=1;)r+=Za(o.getUint8(i)),i+=1;return r}function Za(n){let t=Math.trunc(n);return t=t-(t>>>1&1431655765),t=(t&858993459)+(t>>>2&858993459),(t+(t>>>4)&252645135)*16843009>>>24}const $b=-1;class re{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(this._nullCount!==0){const{type:t}=this;return ut.isSparseUnion(t)?this.children.some(e=>e.nullable):ut.isDenseUnion(t)?this.children.some(e=>e.nullable):this.nullBitmap&&this.nullBitmap.byteLength>0}return!0}get byteLength(){let t=0;const{valueOffsets:e,values:r,nullBitmap:i,typeIds:o}=this;return e&&(t+=e.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),o&&(t+=o.byteLength),this.children.reduce((s,a)=>s+a.byteLength,t)}get nullCount(){if(ut.isUnion(this.type))return this.children.reduce((r,i)=>r+i.nullCount,0);let t=this._nullCount,e;return t<=$b&&(e=this.nullBitmap)&&(this._nullCount=t=e.length===0?0:this.length-Tl(e,this.offset,this.offset+this.length)),t}constructor(t,e,r,i,o,s=[],a){this.type=t,this.children=s,this.dictionary=a,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(r||0,0)),this._nullCount=Math.floor(Math.max(i||0,-1));let l;o instanceof re?(this.stride=o.stride,this.values=o.values,this.typeIds=o.typeIds,this.nullBitmap=o.nullBitmap,this.valueOffsets=o.valueOffsets):(this.stride=ur(t),o&&((l=o[0])&&(this.valueOffsets=l),(l=o[1])&&(this.values=l),(l=o[2])&&(this.nullBitmap=l),(l=o[3])&&(this.typeIds=l)))}getValid(t){const{type:e}=this;if(ut.isUnion(e)){const r=e,i=this.children[r.typeIdToChildIndex[this.typeIds[t]]],o=r.mode===cn.Dense?this.valueOffsets[t]:t;return i.getValid(o)}if(this.nullable&&this.nullCount>0){const r=this.offset+t;return(this.nullBitmap[r>>3]&1<<r%8)!==0}return!0}setValid(t,e){let r;const{type:i}=this;if(ut.isUnion(i)){const o=i,s=this.children[o.typeIdToChildIndex[this.typeIds[t]]],a=o.mode===cn.Dense?this.valueOffsets[t]:t;r=s.getValid(a),s.setValid(a,e)}else{let{nullBitmap:o}=this;const{offset:s,length:a}=this,l=s+t,c=1<<l%8,u=l>>3;(!o||o.byteLength<=u)&&(o=new Uint8Array((s+a+63&-64)>>3).fill(255),this.nullCount>0?(o.set(fu(s,a,this.nullBitmap),0),Object.assign(this,{nullBitmap:o})):Object.assign(this,{nullBitmap:o,_nullCount:0}));const f=o[u];r=(f&c)!==0,o[u]=e?f|c:f&~c}return r!==!!e&&(this._nullCount=this.nullCount+(e?-1:1)),e}clone(t=this.type,e=this.offset,r=this.length,i=this._nullCount,o=this,s=this.children){return new re(t,e,r,i,o,s,this.dictionary)}slice(t,e){const{stride:r,typeId:i,children:o}=this,s=+(this._nullCount===0)-1,a=i===16?r:1,l=this._sliceBuffers(t,e,r,i);return this.clone(this.type,this.offset+t,e,s,l,o.length===0||this.valueOffsets?o:this._sliceChildren(o,a*t,a*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===E.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:r}=this,i=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);i[e>>3]=(1<<e-(e&-8))-1,r>0&&i.set(fu(this.offset,e,this.nullBitmap),0);const o=this.buffers;return o[zr.VALIDITY]=i,this.clone(this.type,0,t,r+(t-e),o)}_sliceBuffers(t,e,r,i){let o;const{buffers:s}=this;return(o=s[zr.TYPE])&&(s[zr.TYPE]=o.subarray(t,t+e)),(o=s[zr.OFFSET])&&(s[zr.OFFSET]=o.subarray(t,t+e+1))||(o=s[zr.DATA])&&(s[zr.DATA]=i===6?o:o.subarray(r*t,r*(t+e))),s}_sliceChildren(t,e,r){return t.map(i=>i.slice(e,r))}}re.prototype.children=Object.freeze([]);class Oo extends zt{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{["type"]:e,["offset"]:r=0,["length"]:i=0}=t;return new re(e,r,i,i)}visitBool(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length>>3,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitInt(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitFloat(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitUtf8(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.data),o=Jt(t.nullBitmap),s=io(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[s,i,o])}visitLargeUtf8(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.data),o=Jt(t.nullBitmap),s=Gc(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[s,i,o])}visitBinary(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.data),o=Jt(t.nullBitmap),s=io(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[s,i,o])}visitLargeBinary(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.data),o=Jt(t.nullBitmap),s=Gc(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[s,i,o])}visitFixedSizeBinary(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitDate(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitTimestamp(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitTime(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitDecimal(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitList(t){const{["type"]:e,["offset"]:r=0,["child"]:i}=t,o=Jt(t.nullBitmap),s=io(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[s,void 0,o],[i])}visitStruct(t){const{["type"]:e,["offset"]:r=0,["children"]:i=[]}=t,o=Jt(t.nullBitmap),{length:s=i.reduce((l,{length:c})=>Math.max(l,c),0),nullCount:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,void 0,o],i)}visitUnion(t){const{["type"]:e,["offset"]:r=0,["children"]:i=[]}=t,o=se(e.ArrayType,t.typeIds),{["length"]:s=o.length,["nullCount"]:a=-1}=t;if(ut.isSparseUnion(e))return new re(e,r,s,a,[void 0,void 0,void 0,o],i);const l=io(t.valueOffsets);return new re(e,r,s,a,[l,void 0,void 0,o],i)}visitDictionary(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.indices.ArrayType,t.data),{["dictionary"]:s=new ce([new Oo().visit({type:e.dictionary})])}=t,{["length"]:a=o.length,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[void 0,o,i],[],s)}visitInterval(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitDuration(t){const{["type"]:e,["offset"]:r=0}=t,i=Jt(t.nullBitmap),o=se(e.ArrayType,t.data),{["length"]:s=o.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,o,i])}visitFixedSizeList(t){const{["type"]:e,["offset"]:r=0,["child"]:i=new Oo().visit({type:e.valueType})}=t,o=Jt(t.nullBitmap),{["length"]:s=i.length/ur(e),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new re(e,r,s,a,[void 0,void 0,o],[i])}visitMap(t){const{["type"]:e,["offset"]:r=0,["child"]:i=new Oo().visit({type:e.childType})}=t,o=Jt(t.nullBitmap),s=io(t.valueOffsets),{["length"]:a=s.length-1,["nullCount"]:l=t.nullBitmap?-1:0}=t;return new re(e,r,a,l,[s,void 0,o],[i])}}const Jb=new Oo;function Vt(n){return Jb.visit(n)}class du{constructor(t=0,e){this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function Qb(n){return n.some(t=>t.nullable)}function Jm(n){return n.reduce((t,e)=>t+e.nullCount,0)}function Qm(n){return n.reduce((t,e,r)=>(t[r+1]=t[r]+e.length,t),new Uint32Array(n.length+1))}function Gm(n,t,e,r){const i=[];for(let o=-1,s=n.length;++o<s;){const a=n[o],l=t[o],{length:c}=a;if(l>=r)break;if(e>=l+c)continue;if(l>=e&&l+c<=r){i.push(a);continue}const u=Math.max(0,e-l),f=Math.min(r-l,c);i.push(a.slice(u,f-u))}return i.length===0&&i.push(n[0].slice(0,0)),i}function ac(n,t,e,r){let i=0,o=0,s=t.length-1;do{if(i>=s-1)return e<t[s]?r(n,i,e-t[i]):null;o=i+Math.trunc((s-i)*.5),e<t[o]?s=o:i=o}while(i<s)}function lc(n,t){return n.getValid(t)}function _a(n){function t(e,r,i){return n(e[r],i)}return function(e){const r=this.data;return ac(r,this._offsets,e,t)}}function Xm(n){let t;function e(r,i,o){return n(r[i],o,t)}return function(r,i){const o=this.data;t=i;const s=ac(o,this._offsets,r,e);return t=void 0,s}}function Zm(n){let t;function e(r,i,o){let s=o,a=0,l=0;for(let c=i-1,u=r.length;++c<u;){const f=r[c];if(~(a=n(f,t,s)))return l+a;s=0,l+=f.length}return-1}return function(r,i){t=r;const o=this.data,s=typeof i!="number"?e(o,0,0):ac(o,this._offsets,i,e);return t=void 0,s}}class gt extends zt{}function Gb(n,t){return t===null&&n.length>0?0:-1}function Xb(n,t){const{nullBitmap:e}=n;if(!e||n.nullCount<=0)return-1;let r=0;for(const i of new sc(e,n.offset+(t||0),n.length,e,$m)){if(!i)return r;++r}return-1}function Tt(n,t,e){if(t===void 0)return-1;if(t===null)switch(n.typeId){case E.Union:break;case E.Dictionary:break;default:return Xb(n,e)}const r=un.getVisitFn(n),i=qi(t);for(let o=(e||0)-1,s=n.length;++o<s;)if(i(r(n,o)))return o;return-1}function tp(n,t,e){const r=un.getVisitFn(n),i=qi(t);for(let o=(e||0)-1,s=n.length;++o<s;)if(i(r(n,o)))return o;return-1}gt.prototype.visitNull=Gb;gt.prototype.visitBool=Tt;gt.prototype.visitInt=Tt;gt.prototype.visitInt8=Tt;gt.prototype.visitInt16=Tt;gt.prototype.visitInt32=Tt;gt.prototype.visitInt64=Tt;gt.prototype.visitUint8=Tt;gt.prototype.visitUint16=Tt;gt.prototype.visitUint32=Tt;gt.prototype.visitUint64=Tt;gt.prototype.visitFloat=Tt;gt.prototype.visitFloat16=Tt;gt.prototype.visitFloat32=Tt;gt.prototype.visitFloat64=Tt;gt.prototype.visitUtf8=Tt;gt.prototype.visitLargeUtf8=Tt;gt.prototype.visitBinary=Tt;gt.prototype.visitLargeBinary=Tt;gt.prototype.visitFixedSizeBinary=Tt;gt.prototype.visitDate=Tt;gt.prototype.visitDateDay=Tt;gt.prototype.visitDateMillisecond=Tt;gt.prototype.visitTimestamp=Tt;gt.prototype.visitTimestampSecond=Tt;gt.prototype.visitTimestampMillisecond=Tt;gt.prototype.visitTimestampMicrosecond=Tt;gt.prototype.visitTimestampNanosecond=Tt;gt.prototype.visitTime=Tt;gt.prototype.visitTimeSecond=Tt;gt.prototype.visitTimeMillisecond=Tt;gt.prototype.visitTimeMicrosecond=Tt;gt.prototype.visitTimeNanosecond=Tt;gt.prototype.visitDecimal=Tt;gt.prototype.visitList=Tt;gt.prototype.visitStruct=Tt;gt.prototype.visitUnion=Tt;gt.prototype.visitDenseUnion=tp;gt.prototype.visitSparseUnion=tp;gt.prototype.visitDictionary=Tt;gt.prototype.visitInterval=Tt;gt.prototype.visitIntervalDayTime=Tt;gt.prototype.visitIntervalYearMonth=Tt;gt.prototype.visitDuration=Tt;gt.prototype.visitDurationSecond=Tt;gt.prototype.visitDurationMillisecond=Tt;gt.prototype.visitDurationMicrosecond=Tt;gt.prototype.visitDurationNanosecond=Tt;gt.prototype.visitFixedSizeList=Tt;gt.prototype.visitMap=Tt;const ba=new gt;class _t extends zt{}function Ot(n){const{type:t}=n;if(n.nullCount===0&&n.stride===1&&(ut.isInt(t)&&t.bitWidth!==64||ut.isTime(t)&&t.bitWidth!==64||ut.isFloat(t)&&t.precision!==Ye.HALF))return new du(n.data.length,r=>{const i=n.data[r];return i.values.subarray(0,i.length)[Symbol.iterator]()});let e=0;return new du(n.data.length,r=>{const o=n.data[r].length,s=n.slice(e,e+o);return e+=o,new Zb(s)})}class Zb{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}_t.prototype.visitNull=Ot;_t.prototype.visitBool=Ot;_t.prototype.visitInt=Ot;_t.prototype.visitInt8=Ot;_t.prototype.visitInt16=Ot;_t.prototype.visitInt32=Ot;_t.prototype.visitInt64=Ot;_t.prototype.visitUint8=Ot;_t.prototype.visitUint16=Ot;_t.prototype.visitUint32=Ot;_t.prototype.visitUint64=Ot;_t.prototype.visitFloat=Ot;_t.prototype.visitFloat16=Ot;_t.prototype.visitFloat32=Ot;_t.prototype.visitFloat64=Ot;_t.prototype.visitUtf8=Ot;_t.prototype.visitLargeUtf8=Ot;_t.prototype.visitBinary=Ot;_t.prototype.visitLargeBinary=Ot;_t.prototype.visitFixedSizeBinary=Ot;_t.prototype.visitDate=Ot;_t.prototype.visitDateDay=Ot;_t.prototype.visitDateMillisecond=Ot;_t.prototype.visitTimestamp=Ot;_t.prototype.visitTimestampSecond=Ot;_t.prototype.visitTimestampMillisecond=Ot;_t.prototype.visitTimestampMicrosecond=Ot;_t.prototype.visitTimestampNanosecond=Ot;_t.prototype.visitTime=Ot;_t.prototype.visitTimeSecond=Ot;_t.prototype.visitTimeMillisecond=Ot;_t.prototype.visitTimeMicrosecond=Ot;_t.prototype.visitTimeNanosecond=Ot;_t.prototype.visitDecimal=Ot;_t.prototype.visitList=Ot;_t.prototype.visitStruct=Ot;_t.prototype.visitUnion=Ot;_t.prototype.visitDenseUnion=Ot;_t.prototype.visitSparseUnion=Ot;_t.prototype.visitDictionary=Ot;_t.prototype.visitInterval=Ot;_t.prototype.visitIntervalDayTime=Ot;_t.prototype.visitIntervalYearMonth=Ot;_t.prototype.visitDuration=Ot;_t.prototype.visitDurationSecond=Ot;_t.prototype.visitDurationMillisecond=Ot;_t.prototype.visitDurationMicrosecond=Ot;_t.prototype.visitDurationNanosecond=Ot;_t.prototype.visitFixedSizeList=Ot;_t.prototype.visitMap=Ot;const cc=new _t;var ep;const np={},rp={};class ce{constructor(t){var e,r,i;const o=t[0]instanceof ce?t.flatMap(a=>a.data):t;if(o.length===0||o.some(a=>!(a instanceof re)))throw new TypeError("Vector constructor expects an Array of Data instances.");const s=(e=o[0])===null||e===void 0?void 0:e.type;switch(o.length){case 0:this._offsets=[0];break;case 1:{const{get:a,set:l,indexOf:c}=np[s.typeId],u=o[0];this.isValid=f=>lc(u,f),this.get=f=>a(u,f),this.set=(f,h)=>l(u,f,h),this.indexOf=f=>c(u,f),this._offsets=[0,u.length];break}default:Object.setPrototypeOf(this,rp[s.typeId]),this._offsets=Qm(o);break}this.data=o,this.type=s,this.stride=ur(s),this.numChildren=(i=(r=s.children)===null||r===void 0?void 0:r.length)!==null&&i!==void 0?i:0,this.length=this._offsets.at(-1)}get byteLength(){return this.data.reduce((t,e)=>t+e.byteLength,0)}get nullable(){return Qb(this.data)}get nullCount(){return Jm(this.data)}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${E[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}at(t){return this.get(oc(t,this.length))}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>-1}[Symbol.iterator](){return cc.visit(this)}concat(...t){return new ce(this.data.concat(t.flatMap(e=>e.data).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new ce(Km(this,t,e,({data:r,_offsets:i},o,s)=>Gm(r,i,o,s)))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:r,stride:i,ArrayType:o}=this;switch(t.typeId){case E.Int:case E.Float:case E.Decimal:case E.Time:case E.Timestamp:switch(e.length){case 0:return new o;case 1:return e[0].values.subarray(0,r*i);default:return e.reduce((s,{values:a,length:l})=>(s.array.set(a.subarray(0,l*i),s.offset),s.offset+=l*i,s),{array:new o(r*i),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt((e=this.type.children)===null||e===void 0?void 0:e.findIndex(r=>r.name===t))}getChildAt(t){return t>-1&&t<this.numChildren?new ce(this.data.map(({children:e})=>e[t])):null}get isMemoized(){return ut.isDictionary(this.type)?this.data[0].dictionary.isMemoized:!1}memoize(){if(ut.isDictionary(this.type)){const t=new ya(this.data[0].dictionary),e=this.data.map(r=>{const i=r.clone();return i.dictionary=t,i});return new ce(e)}return new ya(this)}unmemoize(){if(ut.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map(r=>{const i=r.clone();return i.dictionary=t,i});return new ce(e)}return this}}ep=Symbol.toStringTag;ce[ep]=(n=>{n.type=ut.prototype,n.data=[],n.length=0,n.stride=1,n.numChildren=0,n._offsets=new Uint32Array([0]),n[Symbol.isConcatSpreadable]=!0;const t=Object.keys(E).map(e=>E[e]).filter(e=>typeof e=="number"&&e!==E.NONE);for(const e of t){const r=un.getVisitFnByTypeId(e),i=En.getVisitFnByTypeId(e),o=ba.getVisitFnByTypeId(e);np[e]={get:r,set:i,indexOf:o},rp[e]=Object.create(n,{isValid:{value:_a(lc)},get:{value:_a(un.getVisitFnByTypeId(e))},set:{value:Xm(En.getVisitFnByTypeId(e))},indexOf:{value:Zm(ba.getVisitFnByTypeId(e))}})}return"Vector"})(ce.prototype);class ya extends ce{constructor(t){super(t.data);const e=this.get,r=this.set,i=this.slice,o=new Array(this.length);Object.defineProperty(this,"get",{value(s){const a=o[s];if(a!==void 0)return a;const l=e.call(this,s);return o[s]=l,l}}),Object.defineProperty(this,"set",{value(s,a){r.call(this,s,a),o[s]=a}}),Object.defineProperty(this,"slice",{value:(s,a)=>new ya(i.call(this,s,a))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new ce(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Bl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,r,i){return t.prep(8,24),t.writeInt64(BigInt(i??0)),t.pad(4),t.writeInt32(r),t.writeInt64(BigInt(e??0)),t.offset()}}class hn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new hn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+ne),(e||new hn).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ce.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new qn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const r=this.bb.__offset(this.bb_pos,8);return r?(e||new Bl).__init(this.bb.__vector(this.bb_pos+r)+t*24,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const r=this.bb.__offset(this.bb_pos,10);return r?(e||new Bl).__init(this.bb.__vector(this.bb_pos+r)+t*24,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,12);return r?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Ce.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}class te{constructor(t=[],e,r,i=Ce.V5){this.fields=t||[],this.metadata=e||new Map,r||(r=Ml(this.fields)),this.dictionaries=r,this.metadataVersion=i}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map(t=>t.name)}toString(){return`Schema<{ ${this.fields.map((t,e)=>`${e}: ${t}`).join(", ")} }>`}select(t){const e=new Set(t),r=this.fields.filter(i=>e.has(i.name));return new te(r,this.metadata)}selectAt(t){const e=t.map(r=>this.fields[r]).filter(Boolean);return new te(e,this.metadata)}assign(...t){const e=t[0]instanceof te?t[0]:Array.isArray(t[0])?new te(t[0]):new te(t),r=[...this.fields],i=ws(ws(new Map,this.metadata),e.metadata),o=e.fields.filter(a=>{const l=r.findIndex(c=>c.name===a.name);return~l?(r[l]=a.clone({metadata:ws(ws(new Map,r[l].metadata),a.metadata)}))&&!1:!0}),s=Ml(o,new Map);return new te([...r,...o],i,new Map([...this.dictionaries,...s]))}}te.prototype.fields=null;te.prototype.metadata=null;te.prototype.dictionaries=null;class be{static new(...t){let[e,r,i,o]=t;return t[0]&&typeof t[0]=="object"&&({name:e}=t[0],r===void 0&&(r=t[0].type),i===void 0&&(i=t[0].nullable),o===void 0&&(o=t[0].metadata)),new be(`${e}`,r,i,o)}constructor(t,e,r=!1,i){this.name=t,this.type=e,this.nullable=r,this.metadata=i||new Map}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[e,r,i,o]=t;return!t[0]||typeof t[0]!="object"?[e=this.name,r=this.type,i=this.nullable,o=this.metadata]=t:{name:e=this.name,type:r=this.type,nullable:i=this.nullable,metadata:o=this.metadata}=t[0],be.new(e,r,i,o)}}be.prototype.type=null;be.prototype.name=null;be.prototype.nullable=null;be.prototype.metadata=null;function ws(n,t){return new Map([...n||new Map,...t||new Map])}function Ml(n,t=new Map){for(let e=-1,r=n.length;++e<r;){const o=n[e].type;if(ut.isDictionary(o)){if(!t.has(o.id))t.set(o.id,o.dictionary);else if(t.get(o.id)!==o.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}o.children&&o.children.length>0&&Ml(o.children,t)}return t}var ty=Eh,ey=ji;class uc{static decode(t){t=new ey(Jt(t));const e=hn.getRootAsFooter(t),r=te.decode(e.schema(),new Map,e.version());return new ny(r,e)}static encode(t){const e=new ty,r=te.encode(e,t.schema);hn.startRecordBatchesVector(e,t.numRecordBatches);for(const s of[...t.recordBatches()].slice().reverse())Ui.encode(e,s);const i=e.endVector();hn.startDictionariesVector(e,t.numDictionaries);for(const s of[...t.dictionaryBatches()].slice().reverse())Ui.encode(e,s);const o=e.endVector();return hn.startFooter(e),hn.addSchema(e,r),hn.addVersion(e,Ce.V5),hn.addRecordBatches(e,i),hn.addDictionaries(e,o),hn.finishFooterBuffer(e,hn.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}constructor(t,e=Ce.V5,r,i){this.schema=t,this.version=e,r&&(this._recordBatches=r),i&&(this._dictionaryBatches=i)}*recordBatches(){for(let t,e=-1,r=this.numRecordBatches;++e<r;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,r=this.numDictionaries;++e<r;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class ny extends uc{get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}constructor(t,e){super(t,e.version()),this._footer=e}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return Ui.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return Ui.decode(e)}return null}}class Ui{static decode(t){return new Ui(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:r}=e,i=BigInt(e.offset),o=BigInt(e.bodyLength);return Bl.createBlock(t,i,r,o)}constructor(t,e,r){this.metaDataLength=t,this.offset=_e(r),this.bodyLength=_e(e)}}const Ie=Object.freeze({done:!0,value:void 0});class hu{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class ip{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class ry extends ip{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise(t=>this._closedPromiseResolve=t)}get closed(){return this._closedPromise}cancel(t){return Mt(this,void 0,void 0,function*(){yield this.return(t)})}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Ie);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return yn.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return yn.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return Mt(this,void 0,void 0,function*(){return yield this.abort(t),Ie})}return(t){return Mt(this,void 0,void 0,function*(){return yield this.close(),Ie})}read(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise((e,r)=>{this.resolvers.push({resolve:e,reject:r})}):Promise.resolve(Ie)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class iy extends ry{write(t){if((t=Jt(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?wl(this.toUint8Array(!0)):this.toUint8Array(!1).then(wl)}toUint8Array(t=!1){return t?er(this._values)[0]:Mt(this,void 0,void 0,function*(){var e,r,i,o;const s=[];let a=0;try{for(var l=!0,c=Ti(this),u;u=yield c.next(),e=u.done,!e;l=!0){o=u.value,l=!1;const f=o;s.push(f),a+=f.byteLength}}catch(f){r={error:f}}finally{try{!l&&!e&&(i=c.return)&&(yield i.call(c))}finally{if(r)throw r.error}}return er(s,a)[0]})}}class va{constructor(t){t&&(this.source=new oy(yn.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Vi{constructor(t){t instanceof Vi?this.source=t.source:t instanceof iy?this.source=new Hr(yn.fromAsyncIterable(t)):Oh(t)?this.source=new Hr(yn.fromNodeStream(t)):Zl(t)?this.source=new Hr(yn.fromDOMStream(t)):Sh(t)?this.source=new Hr(yn.fromDOMStream(t.body)):Pa(t)?this.source=new Hr(yn.fromIterable(t)):zo(t)?this.source=new Hr(yn.fromAsyncIterable(t)):Xl(t)&&(this.source=new Hr(yn.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class oy{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Ie)}return(t){return Object.create(this.source.return&&this.source.return(t)||Ie)}}class Hr{constructor(t){this.source=t,this._closedPromise=new Promise(e=>this._closedPromiseResolve=e)}cancel(t){return Mt(this,void 0,void 0,function*(){yield this.return(t)})}get closed(){return this._closedPromise}read(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return Mt(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(t){return Mt(this,arguments,void 0,function*(e,r="read"){return yield this.source.next({cmd:r,size:e})})}throw(t){return Mt(this,void 0,void 0,function*(){const e=this.source.throw&&(yield this.source.throw(t))||Ie;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)})}return(t){return Mt(this,void 0,void 0,function*(){const e=this.source.return&&(yield this.source.return(t))||Ie;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)})}}class mu extends va{constructor(t,e){super(),this.position=0,this.buffer=Jt(t),this.size=e===void 0?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:r}=this.readAt(t,4);return new DataView(e,r).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:r,position:i}=this;return e&&i<r?(typeof t!="number"&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(r,i+Math.min(r-i,t)),e.subarray(i,this.position)):null}readAt(t,e){const r=this.buffer,i=Math.min(this.size,t+e);return r?r.subarray(t,i):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class wa extends Vi{constructor(t,e){super(),this.position=0,this._handle=t,typeof e=="number"?this.size=e:this._pending=Mt(this,void 0,void 0,function*(){this.size=(yield t.stat()).size,delete this._pending})}readInt32(t){return Mt(this,void 0,void 0,function*(){const{buffer:e,byteOffset:r}=yield this.readAt(t,4);return new DataView(e,r).getInt32(0,!0)})}seek(t){return Mt(this,void 0,void 0,function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size})}read(t){return Mt(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:e,size:r,position:i}=this;if(e&&i<r){typeof t!="number"&&(t=Number.POSITIVE_INFINITY);let o=i,s=0,a=0;const l=Math.min(r,o+Math.min(r-o,t)),c=new Uint8Array(Math.max(0,(this.position=l)-o));for(;(o+=a)<l&&(s+=a)<c.byteLength;)({bytesRead:a}=yield e.read(c,s,c.byteLength-s,o));return c}return null})}readAt(t,e){return Mt(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:r,size:i}=this;if(r&&t+e<i){const o=Math.min(i,t+e),s=new Uint8Array(o-t);return(yield r.read(s,0,e,t)).buffer}return new Uint8Array(e)})}close(){return Mt(this,void 0,void 0,function*(){const t=this._handle;this._handle=null,t&&(yield t.close())})}throw(t){return Mt(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}return(t){return Mt(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}}const sy=65536;function Di(n){return n<0&&(n=4294967295+n+1),`0x${n.toString(16)}`}const xi=8,fc=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class op{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,this.buffer[1]&65535,this.buffer[0]>>>16,this.buffer[0]&65535]),r=new Uint32Array([t.buffer[1]>>>16,t.buffer[1]&65535,t.buffer[0]>>>16,t.buffer[0]&65535]);let i=e[3]*r[3];this.buffer[0]=i&65535;let o=i>>>16;return i=e[2]*r[3],o+=i,i=e[3]*r[2]>>>0,o+=i,this.buffer[0]+=o<<16,this.buffer[1]=o>>>0<i?sy:0,this.buffer[1]+=o>>>16,this.buffer[1]+=e[1]*r[3]+e[2]*r[2]+e[3]*r[1],this.buffer[1]+=e[0]*r[3]+e[1]*r[2]+e[2]*r[1]+e[3]*r[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Di(this.buffer[1])} ${Di(this.buffer[0])}`}}class ie extends op{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return ie.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return ie.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const r=t.length,i=new ie(e);for(let o=0;o<r;){const s=xi<r-o?xi:r-o,a=new ie(new Uint32Array([Number.parseInt(t.slice(o,o+s),10),0])),l=new ie(new Uint32Array([fc[s],0]));i.times(l),i.plus(a),o+=s}return i}static convertArray(t){const e=new Uint32Array(t.length*2);for(let r=-1,i=t.length;++r<i;)ie.from(t[r],new Uint32Array(e.buffer,e.byteOffset+2*r*4,2));return e}static multiply(t,e){return new ie(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ie(new Uint32Array(t.buffer)).plus(e)}}class on extends op{negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[0]==0&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=this.buffer[1]<<0,r=t.buffer[1]<<0;return e<r||e===r&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return on.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return on.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const r=t.startsWith("-"),i=t.length,o=new on(e);for(let s=r?1:0;s<i;){const a=xi<i-s?xi:i-s,l=new on(new Uint32Array([Number.parseInt(t.slice(s,s+a),10),0])),c=new on(new Uint32Array([fc[a],0]));o.times(c),o.plus(l),s+=a}return r?o.negate():o}static convertArray(t){const e=new Uint32Array(t.length*2);for(let r=-1,i=t.length;++r<i;)on.from(t[r],new Uint32Array(e.buffer,e.byteOffset+2*r*4,2));return e}static multiply(t,e){return new on(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new on(new Uint32Array(t.buffer)).plus(e)}}class Yn{constructor(t){this.buffer=t}high(){return new on(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new on(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],this.buffer[0]==0&&++this.buffer[1],this.buffer[1]==0&&++this.buffer[2],this.buffer[2]==0&&++this.buffer[3],this}times(t){const e=new ie(new Uint32Array([this.buffer[3],0])),r=new ie(new Uint32Array([this.buffer[2],0])),i=new ie(new Uint32Array([this.buffer[1],0])),o=new ie(new Uint32Array([this.buffer[0],0])),s=new ie(new Uint32Array([t.buffer[3],0])),a=new ie(new Uint32Array([t.buffer[2],0])),l=new ie(new Uint32Array([t.buffer[1],0])),c=new ie(new Uint32Array([t.buffer[0],0]));let u=ie.multiply(o,c);this.buffer[0]=u.low();const f=new ie(new Uint32Array([u.high(),0]));return u=ie.multiply(i,c),f.plus(u),u=ie.multiply(o,l),f.plus(u),this.buffer[1]=f.low(),this.buffer[3]=f.lessThan(u)?1:0,this.buffer[2]=f.high(),new ie(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(ie.multiply(r,c)).plus(ie.multiply(i,l)).plus(ie.multiply(o,a)),this.buffer[3]+=ie.multiply(e,c).plus(ie.multiply(r,l)).plus(ie.multiply(i,a)).plus(ie.multiply(o,s)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Di(this.buffer[3])} ${Di(this.buffer[2])} ${Di(this.buffer[1])} ${Di(this.buffer[0])}`}static multiply(t,e){return new Yn(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new Yn(new Uint32Array(t.buffer)).plus(e)}static from(t,e=new Uint32Array(4)){return Yn.fromString(typeof t=="string"?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return Yn.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const r=t.startsWith("-"),i=t.length,o=new Yn(e);for(let s=r?1:0;s<i;){const a=xi<i-s?xi:i-s,l=new Yn(new Uint32Array([Number.parseInt(t.slice(s,s+a),10),0,0,0])),c=new Yn(new Uint32Array([fc[a],0,0,0]));o.times(c),o.plus(l),s+=a}return r?o.negate():o}static convertArray(t){const e=new Uint32Array(t.length*4);for(let r=-1,i=t.length;++r<i;)Yn.from(t[r],new Uint32Array(e.buffer,e.byteOffset+4*4*r,4));return e}}class sp extends zt{constructor(t,e,r,i,o=Ce.V5){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=r,this.dictionaries=i,this.metadataVersion=o}visit(t){return super.visit(t instanceof be?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return Vt({type:t,length:e})}visitBool(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitInt(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitFloat(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitUtf8(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeUtf8(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeBinary(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitDate(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitTimestamp(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitTime(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitDecimal(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitList(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),children:this.visitMany(t.children)})}visitUnion(t,{length:e,nullCount:r}=this.nextFieldNode()){return this.metadataVersion<Ce.V5&&this.readNullBitmap(t,r),t.mode===cn.Sparse?this.visitSparseUnion(t,{length:e,nullCount:r}):this.visitDenseUnion(t,{length:e,nullCount:r})}visitDenseUnion(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitDuration(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),data:this.readData(t)})}visitFixedSizeList(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),child:this.visit(t.children[0])})}visitMap(t,{length:e,nullCount:r}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:r,nullBitmap:this.readNullBitmap(t,r),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,r=this.nextBufferRange()){return e>0&&this.readData(t,r)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:r}=this.nextBufferRange()){return this.bytes.subarray(r,r+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class ay extends sp{constructor(t,e,r,i,o){super(new Uint8Array(0),e,r,i,o),this.sources=t}readNullBitmap(t,e,{offset:r}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Al(this.sources[r])}readOffsets(t,{offset:e}=this.nextBufferRange()){return se(Uint8Array,se(t.OffsetArrayType,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return se(Uint8Array,se(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:r}=this;return ut.isTimestamp(t)||(ut.isInt(t)||ut.isTime(t))&&t.bitWidth===64||ut.isDuration(t)||ut.isDate(t)&&t.unit===Dn.MILLISECOND?se(Uint8Array,on.convertArray(r[e])):ut.isDecimal(t)?se(Uint8Array,Yn.convertArray(r[e])):ut.isBinary(t)||ut.isLargeBinary(t)||ut.isFixedSizeBinary(t)?ly(r[e]):ut.isBool(t)?Al(r[e]):ut.isUtf8(t)||ut.isLargeUtf8(t)?Gl(r[e].join("")):se(Uint8Array,se(t.ArrayType,r[e].map(i=>+i)))}}function ly(n){const t=n.join(""),e=new Uint8Array(t.length/2);for(let r=0;r<t.length;r+=2)e[r>>1]=Number.parseInt(t.slice(r,r+2),16);return e}class bt extends zt{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every((r,i)=>this.compareFields(r,e[i]))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function en(n,t){return t instanceof n.constructor}function ii(n,t){return n===t||en(n,t)}function Ir(n,t){return n===t||en(n,t)&&n.bitWidth===t.bitWidth&&n.isSigned===t.isSigned}function Ca(n,t){return n===t||en(n,t)&&n.precision===t.precision}function cy(n,t){return n===t||en(n,t)&&n.byteWidth===t.byteWidth}function dc(n,t){return n===t||en(n,t)&&n.unit===t.unit}function ns(n,t){return n===t||en(n,t)&&n.unit===t.unit&&n.timezone===t.timezone}function rs(n,t){return n===t||en(n,t)&&n.unit===t.unit&&n.bitWidth===t.bitWidth}function uy(n,t){return n===t||en(n,t)&&n.children.length===t.children.length&&Fr.compareManyFields(n.children,t.children)}function fy(n,t){return n===t||en(n,t)&&n.children.length===t.children.length&&Fr.compareManyFields(n.children,t.children)}function hc(n,t){return n===t||en(n,t)&&n.mode===t.mode&&n.typeIds.every((e,r)=>e===t.typeIds[r])&&Fr.compareManyFields(n.children,t.children)}function dy(n,t){return n===t||en(n,t)&&n.id===t.id&&n.isOrdered===t.isOrdered&&Fr.visit(n.indices,t.indices)&&Fr.visit(n.dictionary,t.dictionary)}function mc(n,t){return n===t||en(n,t)&&n.unit===t.unit}function is(n,t){return n===t||en(n,t)&&n.unit===t.unit}function hy(n,t){return n===t||en(n,t)&&n.listSize===t.listSize&&n.children.length===t.children.length&&Fr.compareManyFields(n.children,t.children)}function my(n,t){return n===t||en(n,t)&&n.keysSorted===t.keysSorted&&n.children.length===t.children.length&&Fr.compareManyFields(n.children,t.children)}bt.prototype.visitNull=ii;bt.prototype.visitBool=ii;bt.prototype.visitInt=Ir;bt.prototype.visitInt8=Ir;bt.prototype.visitInt16=Ir;bt.prototype.visitInt32=Ir;bt.prototype.visitInt64=Ir;bt.prototype.visitUint8=Ir;bt.prototype.visitUint16=Ir;bt.prototype.visitUint32=Ir;bt.prototype.visitUint64=Ir;bt.prototype.visitFloat=Ca;bt.prototype.visitFloat16=Ca;bt.prototype.visitFloat32=Ca;bt.prototype.visitFloat64=Ca;bt.prototype.visitUtf8=ii;bt.prototype.visitLargeUtf8=ii;bt.prototype.visitBinary=ii;bt.prototype.visitLargeBinary=ii;bt.prototype.visitFixedSizeBinary=cy;bt.prototype.visitDate=dc;bt.prototype.visitDateDay=dc;bt.prototype.visitDateMillisecond=dc;bt.prototype.visitTimestamp=ns;bt.prototype.visitTimestampSecond=ns;bt.prototype.visitTimestampMillisecond=ns;bt.prototype.visitTimestampMicrosecond=ns;bt.prototype.visitTimestampNanosecond=ns;bt.prototype.visitTime=rs;bt.prototype.visitTimeSecond=rs;bt.prototype.visitTimeMillisecond=rs;bt.prototype.visitTimeMicrosecond=rs;bt.prototype.visitTimeNanosecond=rs;bt.prototype.visitDecimal=ii;bt.prototype.visitList=uy;bt.prototype.visitStruct=fy;bt.prototype.visitUnion=hc;bt.prototype.visitDenseUnion=hc;bt.prototype.visitSparseUnion=hc;bt.prototype.visitDictionary=dy;bt.prototype.visitInterval=mc;bt.prototype.visitIntervalDayTime=mc;bt.prototype.visitIntervalYearMonth=mc;bt.prototype.visitDuration=is;bt.prototype.visitDurationSecond=is;bt.prototype.visitDurationMillisecond=is;bt.prototype.visitDurationMicrosecond=is;bt.prototype.visitDurationNanosecond=is;bt.prototype.visitFixedSizeList=hy;bt.prototype.visitMap=my;const Fr=new bt;function py(n,t){return Fr.compareSchemas(n,t)}function tl(n,t){return gy(n,t.map(e=>e.data.concat()))}function gy(n,t){const e=[...n.fields],r=[],i={numBatches:t.reduce((f,h)=>Math.max(f,h.length),0)};let o=0,s=0,a=-1;const l=t.length;let c,u=[];for(;i.numBatches-- >0;){for(s=Number.POSITIVE_INFINITY,a=-1;++a<l;)u[a]=c=t[a].shift(),s=Math.min(s,c?c.length:s);Number.isFinite(s)&&(u=_y(e,s,u,t,i),s>0&&(r[o++]=Vt({type:new tn(e),length:s,nullCount:0,children:u.slice()})))}return[n=n.assign(e),r.map(f=>new Fn(n,f))]}function _y(n,t,e,r,i){var o;const s=(t+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const c=e[a],u=c==null?void 0:c.length;if(u>=t)u===t?e[a]=c:(e[a]=c.slice(0,t),i.numBatches=Math.max(i.numBatches,r[a].unshift(c.slice(t,u-t))));else{const f=n[a];n[a]=f.clone({nullable:!0}),e[a]=(o=c==null?void 0:c._changeLengthAndBackfillNullBitmap(t))!==null&&o!==void 0?o:Vt({type:f.type,length:t,nullCount:t,nullBitmap:new Uint8Array(s)})}}return e}var ap;class Sn{constructor(...t){var e,r;if(t.length===0)return this.batches=[],this.schema=new te([]),this._offsets=[0],this;let i,o;t[0]instanceof te&&(i=t.shift()),t.at(-1)instanceof Uint32Array&&(o=t.pop());const s=l=>{if(l){if(l instanceof Fn)return[l];if(l instanceof Sn)return l.batches;if(l instanceof re){if(l.type instanceof tn)return[new Fn(new te(l.type.children),l)]}else{if(Array.isArray(l))return l.flatMap(c=>s(c));if(typeof l[Symbol.iterator]=="function")return[...l].flatMap(c=>s(c));if(typeof l=="object"){const c=Object.keys(l),u=c.map(d=>new ce([l[d]])),f=i??new te(c.map((d,g)=>new be(String(d),u[g].type,u[g].nullable))),[,h]=tl(f,u);return h.length===0?[new Fn(l)]:h}}}return[]},a=t.flatMap(l=>s(l));if(i=(r=i??((e=a[0])===null||e===void 0?void 0:e.schema))!==null&&r!==void 0?r:new te([]),!(i instanceof te))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const l of a){if(!(l instanceof Fn))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!py(i,l.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=i,this.batches=a,this._offsets=o??Qm(this.data)}get data(){return this.batches.map(({data:t})=>t)}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce((t,e)=>t+e.length,0)}get nullCount(){return this._nullCount===-1&&(this._nullCount=Jm(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}at(t){return this.get(oc(t,this.numRows))}set(t,e){}indexOf(t,e){return-1}[Symbol.iterator](){return this.batches.length>0?cc.visit(new ce(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[
  ${this.toArray().join(`,
  `)}
]`}concat(...t){const e=this.schema,r=this.data.concat(t.flatMap(({data:i})=>i));return new Sn(e,r.map(i=>new Fn(e,i)))}slice(t,e){const r=this.schema;[t,e]=Km({length:this.numRows},t,e);const i=Gm(this.data,this._offsets,t,e);return new Sn(r,i.map(o=>new Fn(r,o)))}getChild(t){return this.getChildAt(this.schema.fields.findIndex(e=>e.name===t))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map(r=>r.children[t]);if(e.length===0){const{type:r}=this.schema.fields[t],i=Vt({type:r,length:0,nullCount:0});e.push(i._changeLengthAndBackfillNullBitmap(this.numRows))}return new ce(e)}return null}setChild(t,e){var r;return this.setChildAt((r=this.schema.fields)===null||r===void 0?void 0:r.findIndex(i=>i.name===t),e)}setChildAt(t,e){let r=this.schema,i=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new ce([Vt({type:new Cr,length:this.numRows})]));const o=r.fields.slice(),s=o[t].clone({type:e.type}),a=this.schema.fields.map((l,c)=>this.getChildAt(c));[o[t],a[t]]=[s,e],[r,i]=tl(r,a)}return new Sn(r,i)}select(t){const e=this.schema.fields.reduce((r,i,o)=>r.set(i.name,o),new Map);return this.selectAt(t.map(r=>e.get(r)).filter(r=>r>-1))}selectAt(t){const e=this.schema.selectAt(t),r=this.batches.map(i=>i.selectAt(t));return new Sn(e,r)}assign(t){const e=this.schema.fields,[r,i]=t.schema.fields.reduce((a,l,c)=>{const[u,f]=a,h=e.findIndex(d=>d.name===l.name);return~h?f[h]=c:u.push(c),a},[[],[]]),o=this.schema.assign(t.schema),s=[...e.map((a,l)=>[l,i[l]]).map(([a,l])=>l===void 0?this.getChildAt(a):t.getChildAt(l)),...r.map(a=>t.getChildAt(a))].filter(Boolean);return new Sn(...tl(o,s))}}ap=Symbol.toStringTag;Sn[ap]=(n=>(n.schema=null,n.batches=[],n._offsets=new Uint32Array([0]),n._nullCount=-1,n[Symbol.isConcatSpreadable]=!0,n.isValid=_a(lc),n.get=_a(un.getVisitFn(E.Struct)),n.set=Xm(En.getVisitFn(E.Struct)),n.indexOf=Zm(ba.getVisitFn(E.Struct)),"Table"))(Sn.prototype);var lp;let Fn=class vo{constructor(...t){switch(t.length){case 2:{if([this.schema]=t,!(this.schema instanceof te))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=Vt({nullCount:0,type:new tn(this.schema.fields),children:this.schema.fields.map(e=>Vt({type:e.type,nullCount:0}))})]=t,!(this.data instanceof re))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=pu(this.schema,this.data.children);break}case 1:{const[e]=t,{fields:r,children:i,length:o}=Object.keys(e).reduce((l,c,u)=>(l.children[u]=e[c],l.length=Math.max(l.length,e[c].length),l.fields[u]=be.new({name:c,type:e[c].type,nullable:!0}),l),{length:0,fields:new Array,children:new Array}),s=new te(r),a=Vt({type:new tn(r),length:o,children:i,nullCount:0});[this.schema,this.data]=pu(s,a.children,o);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=cp(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return un.visit(this.data,t)}at(t){return this.get(oc(t,this.numRows))}set(t,e){return En.visit(this.data,t,e)}indexOf(t,e){return ba.visit(this.data,t,e)}[Symbol.iterator](){return cc.visit(new ce([this.data]))}toArray(){return[...this]}concat(...t){return new Sn(this.schema,[this,...t])}slice(t,e){const[r]=new ce([this.data]).slice(t,e).data;return new vo(this.schema,r)}getChild(t){var e;return this.getChildAt((e=this.schema.fields)===null||e===void 0?void 0:e.findIndex(r=>r.name===t))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new ce([this.data.children[t]]):null}setChild(t,e){var r;return this.setChildAt((r=this.schema.fields)===null||r===void 0?void 0:r.findIndex(i=>i.name===t),e)}setChildAt(t,e){let r=this.schema,i=this.data;if(t>-1&&t<this.numCols){e||(e=new ce([Vt({type:new Cr,length:this.numRows})]));const o=r.fields.slice(),s=i.children.slice(),a=o[t].clone({type:e.type});[o[t],s[t]]=[a,e.data[0]],r=new te(o,new Map(this.schema.metadata)),i=Vt({type:new tn(o),children:s})}return new vo(r,i)}select(t){const e=this.schema.select(t),r=new tn(e.fields),i=[];for(const o of t){const s=this.schema.fields.findIndex(a=>a.name===o);~s&&(i[s]=this.data.children[s])}return new vo(e,Vt({type:r,length:this.numRows,children:i}))}selectAt(t){const e=this.schema.selectAt(t),r=t.map(o=>this.data.children[o]).filter(Boolean),i=Vt({type:new tn(e.fields),length:this.numRows,children:r});return new vo(e,i)}};lp=Symbol.toStringTag;Fn[lp]=(n=>(n._nullCount=-1,n[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(Fn.prototype);function pu(n,t,e=t.reduce((r,i)=>Math.max(r,i.length),0)){var r;const i=[...n.fields],o=[...t],s=(e+63&-64)>>3;for(const[a,l]of n.fields.entries()){const c=t[a];(!c||c.length!==e)&&(i[a]=l.clone({nullable:!0}),o[a]=(r=c==null?void 0:c._changeLengthAndBackfillNullBitmap(e))!==null&&r!==void 0?r:Vt({type:l.type,length:e,nullCount:e,nullBitmap:new Uint8Array(s)}))}return[n.assign(i),Vt({type:new tn(i),length:e,children:o})]}function cp(n,t,e=new Map){var r,i;if(((r=n==null?void 0:n.length)!==null&&r!==void 0?r:0)>0&&(n==null?void 0:n.length)===(t==null?void 0:t.length))for(let o=-1,s=n.length;++o<s;){const{type:a}=n[o],l=t[o];for(const c of[l,...((i=l==null?void 0:l.dictionary)===null||i===void 0?void 0:i.data)||[]])cp(a.children,c==null?void 0:c.children,e);if(ut.isDictionary(a)){const{id:c}=a;if(!e.has(c))l!=null&&l.dictionary&&e.set(c,l.dictionary);else if(e.get(c)!==l.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}}return e}class up extends Fn{constructor(t){const e=t.fields.map(i=>Vt({type:i.type})),r=Vt({type:new tn(t.fields),nullCount:0,children:e});super(t,r)}}let Er=class Hn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(t,e){return(e||new Hn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMessage(t,e){return t.setPosition(t.position()+ne),(e||new Hn).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ce.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):Zt.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}customMetadata(t,e){const r=this.bb.__offset(this.bb_pos,12);return r?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Ce.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,Zt.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,BigInt("0"))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let r=e.length-1;r>=0;r--)t.addOffset(e[r]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(t,e,r,i,o,s){return Hn.startMessage(t),Hn.addVersion(t,e),Hn.addHeaderType(t,r),Hn.addHeader(t,i),Hn.addBodyLength(t,o),Hn.addCustomMetadata(t,s),Hn.endMessage(t)}};class by extends zt{visit(t,e){return t==null||e==null?void 0:super.visit(t,e)}visitNull(t,e){return ou.startNull(e),ou.endNull(e)}visitInt(t,e){return mn.startInt(e),mn.addBitWidth(e,t.bitWidth),mn.addIsSigned(e,t.isSigned),mn.endInt(e)}visitFloat(t,e){return Jn.startFloatingPoint(e),Jn.addPrecision(e,t.precision),Jn.endFloatingPoint(e)}visitBinary(t,e){return tu.startBinary(e),tu.endBinary(e)}visitLargeBinary(t,e){return nu.startLargeBinary(e),nu.endLargeBinary(e)}visitBool(t,e){return eu.startBool(e),eu.endBool(e)}visitUtf8(t,e){return su.startUtf8(e),su.endUtf8(e)}visitLargeUtf8(t,e){return ru.startLargeUtf8(e),ru.endLargeUtf8(e)}visitDecimal(t,e){return _i.startDecimal(e),_i.addScale(e,t.scale),_i.addPrecision(e,t.precision),_i.addBitWidth(e,t.bitWidth),_i.endDecimal(e)}visitDate(t,e){return Ls.startDate(e),Ls.addUnit(e,t.unit),Ls.endDate(e)}visitTime(t,e){return vn.startTime(e),vn.addUnit(e,t.unit),vn.addBitWidth(e,t.bitWidth),vn.endTime(e)}visitTimestamp(t,e){const r=t.timezone&&e.createString(t.timezone)||void 0;return wn.startTimestamp(e),wn.addUnit(e,t.unit),r!==void 0&&wn.addTimezone(e,r),wn.endTimestamp(e)}visitInterval(t,e){return Qn.startInterval(e),Qn.addUnit(e,t.unit),Qn.endInterval(e)}visitDuration(t,e){return Rs.startDuration(e),Rs.addUnit(e,t.unit),Rs.endDuration(e)}visitList(t,e){return iu.startList(e),iu.endList(e)}visitStruct(t,e){return ti.startStruct_(e),ti.endStruct_(e)}visitUnion(t,e){sn.startTypeIdsVector(e,t.typeIds.length);const r=sn.createTypeIdsVector(e,t.typeIds);return sn.startUnion(e),sn.addMode(e,t.mode),sn.addTypeIds(e,r),sn.endUnion(e)}visitDictionary(t,e){const r=this.visit(t.indices,e);return dr.startDictionaryEncoding(e),dr.addId(e,BigInt(t.id)),dr.addIsOrdered(e,t.isOrdered),r!==void 0&&dr.addIndexType(e,r),dr.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return Us.startFixedSizeBinary(e),Us.addByteWidth(e,t.byteWidth),Us.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return Vs.startFixedSizeList(e),Vs.addListSize(e,t.listSize),Vs.endFixedSizeList(e)}visitMap(t,e){return xs.startMap(e),xs.addKeysSorted(e,t.keysSorted),xs.endMap(e)}}const el=new by;function yy(n,t=new Map){return new te(wy(n,t),Ws(n.metadata),t)}function fp(n){return new Rn(n.count,dp(n.columns),hp(n.columns))}function vy(n){return new vr(fp(n.data),n.id,n.isDelta)}function wy(n,t){return(n.fields||[]).filter(Boolean).map(e=>be.fromJSON(e,t))}function gu(n,t){return(n.children||[]).filter(Boolean).map(e=>be.fromJSON(e,t))}function dp(n){return(n||[]).reduce((t,e)=>[...t,new Yi(e.count,Sy(e.VALIDITY)),...dp(e.children)],[])}function hp(n,t=[]){for(let e=-1,r=(n||[]).length;++e<r;){const i=n[e];i.VALIDITY&&t.push(new hr(t.length,i.VALIDITY.length)),i.TYPE_ID&&t.push(new hr(t.length,i.TYPE_ID.length)),i.OFFSET&&t.push(new hr(t.length,i.OFFSET.length)),i.DATA&&t.push(new hr(t.length,i.DATA.length)),t=hp(i.children,t)}return t}function Sy(n){return(n||[]).reduce((t,e)=>t+ +(e===0),0)}function Iy(n,t){let e,r,i,o,s,a;return!t||!(o=n.dictionary)?(s=bu(n,gu(n,t)),i=new be(n.name,s,n.nullable,Ws(n.metadata))):t.has(e=o.id)?(r=(r=o.indexType)?_u(r):new Yo,a=new Ri(t.get(e),r,e,o.isOrdered),i=new be(n.name,a,n.nullable,Ws(n.metadata))):(r=(r=o.indexType)?_u(r):new Yo,t.set(e,s=bu(n,gu(n,t))),a=new Ri(s,r,e,o.isOrdered),i=new be(n.name,a,n.nullable,Ws(n.metadata))),i||null}function Ws(n=[]){return new Map(n.map(({key:t,value:e})=>[t,e]))}function _u(n){return new ni(n.isSigned,n.bitWidth)}function bu(n,t){const e=n.type.name;switch(e){case"NONE":return new Cr;case"null":return new Cr;case"binary":return new ea;case"largebinary":return new na;case"utf8":return new ra;case"largeutf8":return new ia;case"bool":return new oa;case"list":return new da((t||[])[0]);case"struct":return new tn(t||[]);case"struct_":return new tn(t||[])}switch(e){case"int":{const r=n.type;return new ni(r.isSigned,r.bitWidth)}case"floatingpoint":{const r=n.type;return new ta(Ye[r.precision])}case"decimal":{const r=n.type;return new sa(r.scale,r.precision,r.bitWidth)}case"date":{const r=n.type;return new aa(Dn[r.unit])}case"time":{const r=n.type;return new la(yt[r.unit],r.bitWidth)}case"timestamp":{const r=n.type;return new ca(yt[r.unit],r.timezone)}case"interval":{const r=n.type;return new ua(nr[r.unit])}case"duration":{const r=n.type;return new fa(yt[r.unit])}case"union":{const r=n.type,[i,...o]=(r.mode+"").toLowerCase(),s=i.toUpperCase()+o.join("");return new ha(cn[s],r.typeIds||[],t||[])}case"fixedsizebinary":{const r=n.type;return new ma(r.byteWidth)}case"fixedsizelist":{const r=n.type;return new pa(r.listSize,(t||[])[0])}case"map":{const r=n.type;return new ga((t||[])[0],r.keysSorted)}}throw new Error(`Unrecognized type: "${e}"`)}var Oy=Eh,ky=ji;class jn{static fromJSON(t,e){const r=new jn(0,Ce.V5,e);return r._createHeader=Dy(t,e),r}static decode(t){t=new ky(Jt(t));const e=Er.getRootAsMessage(t),r=e.bodyLength(),i=e.version(),o=e.headerType(),s=new jn(r,i,o);return s._createHeader=Ey(e,o),s}static encode(t){const e=new Oy;let r=-1;return t.isSchema()?r=te.encode(e,t.header()):t.isRecordBatch()?r=Rn.encode(e,t.header()):t.isDictionaryBatch()&&(r=vr.encode(e,t.header())),Er.startMessage(e),Er.addVersion(e,Ce.V5),Er.addHeader(e,r),Er.addHeaderType(e,t.headerType),Er.addBodyLength(e,BigInt(t.bodyLength)),Er.finishMessageBuffer(e,Er.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof te)return new jn(0,Ce.V5,Zt.Schema,t);if(t instanceof Rn)return new jn(e,Ce.V5,Zt.RecordBatch,t);if(t instanceof vr)return new jn(e,Ce.V5,Zt.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===Zt.Schema}isRecordBatch(){return this.headerType===Zt.RecordBatch}isDictionaryBatch(){return this.headerType===Zt.DictionaryBatch}constructor(t,e,r,i){this._version=e,this._headerType=r,this.body=new Uint8Array(0),i&&(this._createHeader=()=>i),this._bodyLength=_e(t)}}class Rn{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,r){this._nodes=e,this._buffers=r,this._length=_e(t)}}class vr{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,r=!1){this._data=t,this._isDelta=r,this._id=_e(e)}}class hr{constructor(t,e){this.offset=_e(t),this.length=_e(e)}}class Yi{constructor(t,e){this.length=_e(t),this.nullCount=_e(e)}}function Dy(n,t){return()=>{switch(t){case Zt.Schema:return te.fromJSON(n);case Zt.RecordBatch:return Rn.fromJSON(n);case Zt.DictionaryBatch:return vr.fromJSON(n)}throw new Error(`Unrecognized Message type: { name: ${Zt[t]}, type: ${t} }`)}}function Ey(n,t){return()=>{switch(t){case Zt.Schema:return te.decode(n.header(new qn),new Map,n.version());case Zt.RecordBatch:return Rn.decode(n.header(new cr),n.version());case Zt.DictionaryBatch:return vr.decode(n.header(new pi),n.version())}throw new Error(`Unrecognized Message type: { name: ${Zt[t]}, type: ${t} }`)}}be.encode=Ry;be.decode=jy;be.fromJSON=Iy;te.encode=Ly;te.decode=Ay;te.fromJSON=yy;Rn.encode=Uy;Rn.decode=Ty;Rn.fromJSON=fp;vr.encode=Vy;vr.decode=By;vr.fromJSON=vy;Yi.encode=xy;Yi.decode=Py;hr.encode=zy;hr.decode=My;function Ay(n,t=new Map,e=Ce.V5){const r=Fy(n,t);return new te(r,Hs(n),t,e)}function Ty(n,t=Ce.V5){if(n.compression()!==null)throw new Error("Record batch compression not implemented");return new Rn(n.length(),Ny(n),Cy(n,t))}function By(n,t=Ce.V5){return new vr(Rn.decode(n.data(),t),n.id(),n.isDelta())}function My(n){return new hr(n.offset(),n.length())}function Py(n){return new Yi(n.length(),n.nullCount())}function Ny(n){const t=[];for(let e,r=-1,i=-1,o=n.nodesLength();++r<o;)(e=n.nodes(r))&&(t[++i]=Yi.decode(e));return t}function Cy(n,t){const e=[];for(let r,i=-1,o=-1,s=n.buffersLength();++i<s;)(r=n.buffers(i))&&(t<Ce.V4&&(r.bb_pos+=8*(i+1)),e[++o]=hr.decode(r));return e}function Fy(n,t){const e=[];for(let r,i=-1,o=-1,s=n.fieldsLength();++i<s;)(r=n.fields(i))&&(e[++o]=be.decode(r,t));return e}function yu(n,t){const e=[];for(let r,i=-1,o=-1,s=n.childrenLength();++i<s;)(r=n.children(i))&&(e[++o]=be.decode(r,t));return e}function jy(n,t){let e,r,i,o,s,a;return!t||!(a=n.dictionary())?(i=wu(n,yu(n,t)),r=new be(n.name(),i,n.nullable(),Hs(n))):t.has(e=_e(a.id()))?(o=(o=a.indexType())?vu(o):new Yo,s=new Ri(t.get(e),o,e,a.isOrdered()),r=new be(n.name(),s,n.nullable(),Hs(n))):(o=(o=a.indexType())?vu(o):new Yo,t.set(e,i=wu(n,yu(n,t))),s=new Ri(i,o,e,a.isOrdered()),r=new be(n.name(),s,n.nullable(),Hs(n))),r||null}function Hs(n){const t=new Map;if(n)for(let e,r,i=-1,o=Math.trunc(n.customMetadataLength());++i<o;)(e=n.customMetadata(i))&&(r=e.key())!=null&&t.set(r,e.value());return t}function vu(n){return new ni(n.isSigned(),n.bitWidth())}function wu(n,t){const e=n.typeType();switch(e){case de.NONE:return new Cr;case de.Null:return new Cr;case de.Binary:return new ea;case de.LargeBinary:return new na;case de.Utf8:return new ra;case de.LargeUtf8:return new ia;case de.Bool:return new oa;case de.List:return new da((t||[])[0]);case de.Struct_:return new tn(t||[])}switch(e){case de.Int:{const r=n.type(new mn);return new ni(r.isSigned(),r.bitWidth())}case de.FloatingPoint:{const r=n.type(new Jn);return new ta(r.precision())}case de.Decimal:{const r=n.type(new _i);return new sa(r.scale(),r.precision(),r.bitWidth())}case de.Date:{const r=n.type(new Ls);return new aa(r.unit())}case de.Time:{const r=n.type(new vn);return new la(r.unit(),r.bitWidth())}case de.Timestamp:{const r=n.type(new wn);return new ca(r.unit(),r.timezone())}case de.Interval:{const r=n.type(new Qn);return new ua(r.unit())}case de.Duration:{const r=n.type(new Rs);return new fa(r.unit())}case de.Union:{const r=n.type(new sn);return new ha(r.mode(),r.typeIdsArray()||[],t||[])}case de.FixedSizeBinary:{const r=n.type(new Us);return new ma(r.byteWidth())}case de.FixedSizeList:{const r=n.type(new Vs);return new pa(r.listSize(),(t||[])[0])}case de.Map:{const r=n.type(new xs);return new ga((t||[])[0],r.keysSorted())}}throw new Error(`Unrecognized type: "${de[e]}" (${e})`)}function Ly(n,t){const e=t.fields.map(o=>be.encode(n,o));qn.startFieldsVector(n,e.length);const r=qn.createFieldsVector(n,e),i=t.metadata&&t.metadata.size>0?qn.createCustomMetadataVector(n,[...t.metadata].map(([o,s])=>{const a=n.createString(`${o}`),l=n.createString(`${s}`);return je.startKeyValue(n),je.addKey(n,a),je.addValue(n,l),je.endKeyValue(n)})):-1;return qn.startSchema(n),qn.addFields(n,r),qn.addEndianness(n,Wy?Li.Little:Li.Big),i!==-1&&qn.addCustomMetadata(n,i),qn.endSchema(n)}function Ry(n,t){let e=-1,r=-1,i=-1;const o=t.type;let s=t.typeId;ut.isDictionary(o)?(s=o.dictionary.typeId,i=el.visit(o,n),r=el.visit(o.dictionary,n)):r=el.visit(o,n);const a=(o.children||[]).map(u=>be.encode(n,u)),l=bn.createChildrenVector(n,a),c=t.metadata&&t.metadata.size>0?bn.createCustomMetadataVector(n,[...t.metadata].map(([u,f])=>{const h=n.createString(`${u}`),d=n.createString(`${f}`);return je.startKeyValue(n),je.addKey(n,h),je.addValue(n,d),je.endKeyValue(n)})):-1;return t.name&&(e=n.createString(t.name)),bn.startField(n),bn.addType(n,r),bn.addTypeType(n,s),bn.addChildren(n,l),bn.addNullable(n,!!t.nullable),e!==-1&&bn.addName(n,e),i!==-1&&bn.addDictionary(n,i),c!==-1&&bn.addCustomMetadata(n,c),bn.endField(n)}function Uy(n,t){const e=t.nodes||[],r=t.buffers||[];cr.startNodesVector(n,e.length);for(const s of e.slice().reverse())Yi.encode(n,s);const i=n.endVector();cr.startBuffersVector(n,r.length);for(const s of r.slice().reverse())hr.encode(n,s);const o=n.endVector();return cr.startRecordBatch(n),cr.addLength(n,BigInt(t.length)),cr.addNodes(n,i),cr.addBuffers(n,o),cr.endRecordBatch(n)}function Vy(n,t){const e=Rn.encode(n,t.data);return pi.startDictionaryBatch(n),pi.addId(n,BigInt(t.id)),pi.addIsDelta(n,t.isDelta),pi.addData(n,e),pi.endDictionaryBatch(n)}function xy(n,t){return Bh.createFieldNode(n,BigInt(t.length),BigInt(t.nullCount))}function zy(n,t){return Th.createBuffer(n,BigInt(t.offset),BigInt(t.length))}const Wy=(()=>{const n=new ArrayBuffer(2);return new DataView(n).setInt16(0,256,!0),new Int16Array(n)[0]===256})(),pc=n=>`Expected ${Zt[n]} Message in stream, but was null or length 0.`,gc=n=>`Header pointer of flatbuffer-encoded ${Zt[n]} Message is null or length 0.`,mp=(n,t)=>`Expected to read ${n} metadata bytes, but only read ${t}.`,pp=(n,t)=>`Expected to read ${n} bytes for message body, but only read ${t}.`;class gp{constructor(t){this.source=t instanceof va?t:new va(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||t.value===-1&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Ie:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(pc(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=Jt(this.source.read(t));if(e.byteLength<t)throw new Error(pp(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=Zt.Schema,r=this.readMessage(e),i=r==null?void 0:r.header();if(t&&!i)throw new Error(gc(e));return i}readMetadataLength(){const t=this.source.read(Fa),e=t&&new ji(t),r=(e==null?void 0:e.readInt32(0))||0;return{done:r===0,value:r}}readMetadata(t){const e=this.source.read(t);if(!e)return Ie;if(e.byteLength<t)throw new Error(mp(t,e.byteLength));return{done:!1,value:jn.decode(e)}}}class Hy{constructor(t,e){this.source=t instanceof Vi?t:wh(t)?new wa(t,e):new Vi(t)}[Symbol.asyncIterator](){return this}next(){return Mt(this,void 0,void 0,function*(){let t;return(t=yield this.readMetadataLength()).done||t.value===-1&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Ie:t})}throw(t){return Mt(this,void 0,void 0,function*(){return yield this.source.throw(t)})}return(t){return Mt(this,void 0,void 0,function*(){return yield this.source.return(t)})}readMessage(t){return Mt(this,void 0,void 0,function*(){let e;if((e=yield this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(pc(t));return e.value})}readMessageBody(t){return Mt(this,void 0,void 0,function*(){if(t<=0)return new Uint8Array(0);const e=Jt(yield this.source.read(t));if(e.byteLength<t)throw new Error(pp(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()})}readSchema(){return Mt(this,arguments,void 0,function*(t=!1){const e=Zt.Schema,r=yield this.readMessage(e),i=r==null?void 0:r.header();if(t&&!i)throw new Error(gc(e));return i})}readMetadataLength(){return Mt(this,void 0,void 0,function*(){const t=yield this.source.read(Fa),e=t&&new ji(t),r=(e==null?void 0:e.readInt32(0))||0;return{done:r===0,value:r}})}readMetadata(t){return Mt(this,void 0,void 0,function*(){const e=yield this.source.read(t);if(!e)return Ie;if(e.byteLength<t)throw new Error(mp(t,e.byteLength));return{done:!1,value:jn.decode(e)}})}}class qy extends gp{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof hu?t:new hu(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:jn.fromJSON(t.schema,Zt.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];return this._body=e.data.columns,{done:!1,value:jn.fromJSON(e,Zt.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];return this._body=e.columns,{done:!1,value:jn.fromJSON(e,Zt.RecordBatch)}}return this._body=[],Ie}readMessageBody(t){return e(this._body);function e(r){return(r||[]).reduce((i,o)=>[...i,...o.VALIDITY&&[o.VALIDITY]||[],...o.TYPE_ID&&[o.TYPE_ID]||[],...o.OFFSET&&[o.OFFSET]||[],...o.DATA&&[o.DATA]||[],...e(o.children)],[])}}readMessage(t){let e;if((e=this.next()).done)return null;if(t!=null&&e.value.headerType!==t)throw new Error(pc(t));return e.value}readSchema(){const t=Zt.Schema,e=this.readMessage(t),r=e==null?void 0:e.header();if(!e||!r)throw new Error(gc(t));return r}}const Fa=4,Pl="ARROW1",Sa=new Uint8Array(Pl.length);for(let n=0;n<Pl.length;n+=1)Sa[n]=Pl.codePointAt(n);function _c(n,t=0){for(let e=-1,r=Sa.length;++e<r;)if(Sa[e]!==n[t+e])return!1;return!0}const os=Sa.length,_p=os+Fa,Yy=os*2+Fa;class pr extends ip{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return zo(e)?e.then(()=>this):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return yn.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return yn.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof pr?t:Sl(t)?Qy(t):wh(t)?Zy(t):zo(t)?Mt(this,void 0,void 0,function*(){return yield pr.from(yield t)}):Sh(t)||Zl(t)||Oh(t)||Xl(t)?Xy(new Vi(t)):Gy(new va(t))}static readAll(t){return t instanceof pr?t.isSync()?Su(t):Iu(t):Sl(t)||ArrayBuffer.isView(t)||Pa(t)||vh(t)?Su(t):Iu(t)}}class Ia extends pr{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return Gn(this,arguments,function*(){yield Wt(yield*Cs(Ti(this[Symbol.iterator]())))})}}class Oa extends pr{constructor(t){super(t),this._impl=t}readAll(){return Mt(this,void 0,void 0,function*(){var t,e,r,i;const o=new Array;try{for(var s=!0,a=Ti(this),l;l=yield a.next(),t=l.done,!t;s=!0){i=l.value,s=!1;const c=i;o.push(c)}}catch(c){e={error:c}}finally{try{!s&&!t&&(r=a.return)&&(yield r.call(a))}finally{if(e)throw e.error}}return o})}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class bp extends Ia{constructor(t){super(t),this._impl=t}}class Ky extends Oa{constructor(t){super(t),this._impl=t}}class yp{get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const r=this._loadVectors(t,e,this.schema.fields),i=Vt({type:new tn(this.schema.fields),length:t.length,children:r});return new Fn(this.schema,i)}_loadDictionaryBatch(t,e){const{id:r,isDelta:i}=t,{dictionaries:o,schema:s}=this,a=o.get(r),l=s.dictionaries.get(r),c=this._loadVectors(t.data,e,[l]);return(a&&i?a.concat(new ce(c)):new ce(c)).memoize()}_loadVectors(t,e,r){return new sp(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(r)}}class ka extends yp{constructor(t,e){super(e),this._reader=Sl(t)?new qy(this._handle=t):new gp(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=wp(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Ie}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Ie}next(){if(this.closed)return Ie;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const r=t.header(),i=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(r,i)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const r=t.header(),i=e.readMessageBody(t.bodyLength),o=this._loadDictionaryBatch(r,i);this.dictionaries.set(r.id,o)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new up(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class Da extends yp{constructor(t,e){super(e),this._reader=new Hy(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return Mt(this,void 0,void 0,function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)})}open(t){return Mt(this,void 0,void 0,function*(){return this.closed||(this.autoDestroy=wp(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this})}throw(t){return Mt(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Ie})}return(t){return Mt(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Ie})}next(){return Mt(this,void 0,void 0,function*(){if(this.closed)return Ie;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const r=t.header(),i=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(r,i)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const r=t.header(),i=yield e.readMessageBody(t.bodyLength),o=this._loadDictionaryBatch(r,i);this.dictionaries.set(r.id,o)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new up(this.schema)}):yield this.return()})}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,function*(){return yield this._reader.readMessage(t)})}}class vp extends ka{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,e){super(t instanceof mu?t:new mu(t),e)}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const e of this._footer.dictionaryBatches())e&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const r=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(t);if(r&&this._handle.seek(r.offset)){const i=this._reader.readMessage(Zt.RecordBatch);if(i!=null&&i.isRecordBatch()){const o=i.header(),s=this._reader.readMessageBody(i.bodyLength);return this._loadRecordBatch(o,s)}}return null}_readDictionaryBatch(t){var e;const r=(e=this._footer)===null||e===void 0?void 0:e.getDictionaryBatch(t);if(r&&this._handle.seek(r.offset)){const i=this._reader.readMessage(Zt.DictionaryBatch);if(i!=null&&i.isDictionaryBatch()){const o=i.header(),s=this._reader.readMessageBody(i.bodyLength),a=this._loadDictionaryBatch(o,s);this.dictionaries.set(o.id,a)}}}_readFooter(){const{_handle:t}=this,e=t.size-_p,r=t.readInt32(e),i=t.readAt(e-r,r);return uc.decode(i)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const r=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(this._recordBatchIndex);if(r&&this._handle.seek(r.offset))return this._reader.readMessage(t)}return null}}class $y extends Da{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,...e){const r=typeof e[0]!="number"?e.shift():void 0,i=e[0]instanceof Map?e.shift():void 0;super(t instanceof wa?t:new wa(t,r),i)}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return Mt(this,void 0,void 0,function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const r of this._footer.dictionaryBatches())r&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)})}readRecordBatch(t){return Mt(this,void 0,void 0,function*(){var e;if(this.closed)return null;this._footer||(yield this.open());const r=(e=this._footer)===null||e===void 0?void 0:e.getRecordBatch(t);if(r&&(yield this._handle.seek(r.offset))){const i=yield this._reader.readMessage(Zt.RecordBatch);if(i!=null&&i.isRecordBatch()){const o=i.header(),s=yield this._reader.readMessageBody(i.bodyLength);return this._loadRecordBatch(o,s)}}return null})}_readDictionaryBatch(t){return Mt(this,void 0,void 0,function*(){var e;const r=(e=this._footer)===null||e===void 0?void 0:e.getDictionaryBatch(t);if(r&&(yield this._handle.seek(r.offset))){const i=yield this._reader.readMessage(Zt.DictionaryBatch);if(i!=null&&i.isDictionaryBatch()){const o=i.header(),s=yield this._reader.readMessageBody(i.bodyLength),a=this._loadDictionaryBatch(o,s);this.dictionaries.set(o.id,a)}}})}_readFooter(){return Mt(this,void 0,void 0,function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-_p,r=yield t.readInt32(e),i=yield t.readAt(e-r,r);return uc.decode(i)})}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null})}}class Jy extends ka{constructor(t,e){super(t,e)}_loadVectors(t,e,r){return new ay(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(r)}}function wp(n,t){return t&&typeof t.autoDestroy=="boolean"?t.autoDestroy:n.autoDestroy}function*Su(n){const t=pr.from(n);try{if(!t.open({autoDestroy:!1}).closed)do yield t;while(!t.reset().open().closed)}finally{t.cancel()}}function Iu(n){return Gn(this,arguments,function*(){const e=yield Wt(pr.from(n));try{if(!(yield Wt(e.open({autoDestroy:!1}))).closed)do yield yield Wt(e);while(!(yield Wt(e.reset().open())).closed)}finally{yield Wt(e.cancel())}})}function Qy(n){return new Ia(new Jy(n))}function Gy(n){const t=n.peek(os+7&-8);return t&&t.byteLength>=4?_c(t)?new bp(new vp(n.read())):new Ia(new ka(n)):new Ia(new ka(function*(){}()))}function Xy(n){return Mt(this,void 0,void 0,function*(){const t=yield n.peek(os+7&-8);return t&&t.byteLength>=4?_c(t)?new bp(new vp(yield n.read())):new Oa(new Da(n)):new Oa(new Da(function(){return Gn(this,arguments,function*(){})}()))})}function Zy(n){return Mt(this,void 0,void 0,function*(){const{size:t}=yield n.stat(),e=new wa(n,t);return t>=Yy&&_c(yield e.readAt(0,os+7&-8))?new Ky(new $y(e)):new Oa(new Da(e))})}function Sp(n){const t=pr.from(n);return zo(t)?t.then(e=>Sp(e)):t.isAsync()?t.readAll().then(e=>new Sn(e)):new Sn(t.readAll())}var Ip={exports:{}};(function(n){(function(t){function e(y,A){var w=(y&65535)+(A&65535),F=(y>>16)+(A>>16)+(w>>16);return F<<16|w&65535}function r(y,A){return y<<A|y>>>32-A}function i(y,A,w,F,T,P){return e(r(e(e(A,y),e(F,P)),T),w)}function o(y,A,w,F,T,P,L){return i(A&w|~A&F,y,A,T,P,L)}function s(y,A,w,F,T,P,L){return i(A&F|w&~F,y,A,T,P,L)}function a(y,A,w,F,T,P,L){return i(A^w^F,y,A,T,P,L)}function l(y,A,w,F,T,P,L){return i(w^(A|~F),y,A,T,P,L)}function c(y,A){y[A>>5]|=128<<A%32,y[(A+64>>>9<<4)+14]=A;var w,F,T,P,L,B=1732584193,M=-271733879,j=-1732584194,V=271733878;for(w=0;w<y.length;w+=16)F=B,T=M,P=j,L=V,B=o(B,M,j,V,y[w],7,-680876936),V=o(V,B,M,j,y[w+1],12,-389564586),j=o(j,V,B,M,y[w+2],17,606105819),M=o(M,j,V,B,y[w+3],22,-1044525330),B=o(B,M,j,V,y[w+4],7,-176418897),V=o(V,B,M,j,y[w+5],12,1200080426),j=o(j,V,B,M,y[w+6],17,-1473231341),M=o(M,j,V,B,y[w+7],22,-45705983),B=o(B,M,j,V,y[w+8],7,1770035416),V=o(V,B,M,j,y[w+9],12,-1958414417),j=o(j,V,B,M,y[w+10],17,-42063),M=o(M,j,V,B,y[w+11],22,-1990404162),B=o(B,M,j,V,y[w+12],7,1804603682),V=o(V,B,M,j,y[w+13],12,-40341101),j=o(j,V,B,M,y[w+14],17,-1502002290),M=o(M,j,V,B,y[w+15],22,1236535329),B=s(B,M,j,V,y[w+1],5,-165796510),V=s(V,B,M,j,y[w+6],9,-1069501632),j=s(j,V,B,M,y[w+11],14,643717713),M=s(M,j,V,B,y[w],20,-373897302),B=s(B,M,j,V,y[w+5],5,-701558691),V=s(V,B,M,j,y[w+10],9,38016083),j=s(j,V,B,M,y[w+15],14,-660478335),M=s(M,j,V,B,y[w+4],20,-405537848),B=s(B,M,j,V,y[w+9],5,568446438),V=s(V,B,M,j,y[w+14],9,-1019803690),j=s(j,V,B,M,y[w+3],14,-187363961),M=s(M,j,V,B,y[w+8],20,1163531501),B=s(B,M,j,V,y[w+13],5,-1444681467),V=s(V,B,M,j,y[w+2],9,-51403784),j=s(j,V,B,M,y[w+7],14,1735328473),M=s(M,j,V,B,y[w+12],20,-1926607734),B=a(B,M,j,V,y[w+5],4,-378558),V=a(V,B,M,j,y[w+8],11,-2022574463),j=a(j,V,B,M,y[w+11],16,1839030562),M=a(M,j,V,B,y[w+14],23,-35309556),B=a(B,M,j,V,y[w+1],4,-1530992060),V=a(V,B,M,j,y[w+4],11,1272893353),j=a(j,V,B,M,y[w+7],16,-155497632),M=a(M,j,V,B,y[w+10],23,-1094730640),B=a(B,M,j,V,y[w+13],4,681279174),V=a(V,B,M,j,y[w],11,-358537222),j=a(j,V,B,M,y[w+3],16,-722521979),M=a(M,j,V,B,y[w+6],23,76029189),B=a(B,M,j,V,y[w+9],4,-640364487),V=a(V,B,M,j,y[w+12],11,-421815835),j=a(j,V,B,M,y[w+15],16,530742520),M=a(M,j,V,B,y[w+2],23,-995338651),B=l(B,M,j,V,y[w],6,-198630844),V=l(V,B,M,j,y[w+7],10,1126891415),j=l(j,V,B,M,y[w+14],15,-1416354905),M=l(M,j,V,B,y[w+5],21,-57434055),B=l(B,M,j,V,y[w+12],6,1700485571),V=l(V,B,M,j,y[w+3],10,-1894986606),j=l(j,V,B,M,y[w+10],15,-1051523),M=l(M,j,V,B,y[w+1],21,-2054922799),B=l(B,M,j,V,y[w+8],6,1873313359),V=l(V,B,M,j,y[w+15],10,-30611744),j=l(j,V,B,M,y[w+6],15,-1560198380),M=l(M,j,V,B,y[w+13],21,1309151649),B=l(B,M,j,V,y[w+4],6,-145523070),V=l(V,B,M,j,y[w+11],10,-1120210379),j=l(j,V,B,M,y[w+2],15,718787259),M=l(M,j,V,B,y[w+9],21,-343485551),B=e(B,F),M=e(M,T),j=e(j,P),V=e(V,L);return[B,M,j,V]}function u(y){var A,w="",F=y.length*32;for(A=0;A<F;A+=8)w+=String.fromCharCode(y[A>>5]>>>A%32&255);return w}function f(y){var A,w=[];for(w[(y.length>>2)-1]=void 0,A=0;A<w.length;A+=1)w[A]=0;var F=y.length*8;for(A=0;A<F;A+=8)w[A>>5]|=(y.charCodeAt(A/8)&255)<<A%32;return w}function h(y){return u(c(f(y),y.length*8))}function d(y,A){var w,F=f(y),T=[],P=[],L;for(T[15]=P[15]=void 0,F.length>16&&(F=c(F,y.length*8)),w=0;w<16;w+=1)T[w]=F[w]^909522486,P[w]=F[w]^1549556828;return L=c(T.concat(f(A)),512+A.length*8),u(c(P.concat(L),640))}function g(y){var A="0123456789abcdef",w="",F,T;for(T=0;T<y.length;T+=1)F=y.charCodeAt(T),w+=A.charAt(F>>>4&15)+A.charAt(F&15);return w}function m(y){return unescape(encodeURIComponent(y))}function p(y){return h(m(y))}function v(y){return g(p(y))}function b(y,A){return d(m(y),m(A))}function _(y,A){return g(b(y,A))}function I(y,A,w){return A?w?b(A,y):_(A,y):w?p(y):v(y)}n.exports?n.exports=I:t.md5=I})(Tg)})(Ip);var tv=Ip.exports;const Ou=Bg(tv),ev=!0,Op=!0,nv="always",rv=async()=>{let n={};{const t=await fetch(xt("/data/manifest.json"));t.ok&&({renderedFiles:n}=await t.json())}await Io(Ng),Object.keys(n??{}).length===0?console.warn('No sources found, execute "npm run sources" to generate'.trim()):(await Io(uh,n,{addBasePath:xt}),await Io(Cg,Object.keys(n)))},ku=Io(rv);async function iv(n,t,e){const r=await e(xt(`/api/${n}/${t}/all-queries.json`));if(!r.ok)return{};const i=await r.json(),o=await Promise.all(Object.entries(i).map(async([s,a])=>{const l=await e(xt(`/api/prerendered_queries/${a}.arrow`));if(!l.ok)return null;const c=await Sp(l);return[s,Mg(c)]}));return Object.fromEntries(o.filter(Boolean))}const ov=["/settings","/explore"],Du=new Map,sv=async({fetch:n,route:t,params:e,url:r})=>{var g,m,p;const[{customFormattingSettings:i},o,s]=await Promise.all([n(xt("/api/customFormattingSettings.json/GET.json")).then(v=>v.json()),n(xt("/api/pagesManifest.json")).then(v=>v.json()),n(xt(`/api/${t.id}/evidencemeta.json`)).then(v=>v.json()).catch(()=>({queries:[]}))]),a=Ou(t.id),l=Ou(Object.entries(e).sort().map(([v,b])=>`${v}${b}`).join("")),c=t.id&&ov.every(v=>!t.id.startsWith(v));let u={};const{inputs:f=a_({label:"",value:"(SELECT NULL WHERE 0 /* An Input has not been set */)"})}=Du.get(r.pathname)??{};Du.has(r.pathname),c&&Op&&(u=await iv(a,l,n));function h(v,{query_name:b,callback:_=I=>I}={}){return(async()=>{await ku;const I=await Pg(v);return _(I)})()}let d=o;for(const v of(t.id??"").split("/").slice(1)){if(d=d.children[v],!d)break;if((g=d.frontMatter)!=null&&g.title)d.title=d.frontMatter.title;else if((m=d.frontMatter)!=null&&m.breadcrumb){let{breadcrumb:b}=d.frontMatter;for(const[_,I]of Object.entries(e))b=b.replaceAll(`\${params.${_}}`,I);d.title=(p=(await h(b))[0])==null?void 0:p.breadcrumb}}return{__db:{query:h,async load(){return ku},async updateParquetURLs(v){const{renderedFiles:b}=JSON.parse(v);await Io(uh,b,{addBasePath:xt})}},inputs:f,data:u,customFormattingSettings:i,isUserPage:c,evidencemeta:s,pagesManifest:o}},gO=Object.freeze(Object.defineProperty({__proto__:null,load:sv,prerender:Op,ssr:ev,trailingSlash:nv},Symbol.toStringTag,{value:"Module"})),av={ltr:[..._l,rn.ARROW_RIGHT]},lv={ltr:[rn.ARROW_LEFT]},Eu=["menu","trigger"],cv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function uv(n){const{name:t,selector:e}=jg(n.selector),{preventScroll:r,arrowSize:i,positioning:o,closeOnEscape:s,closeOnOutsideClick:a,portal:l,forceVisible:c,typeahead:u,loop:f,closeFocus:h,disableFocusFirstItem:d,closeOnItemClick:g,onOutsideClick:m}=n.rootOptions,p=n.rootOpen,v=n.rootActiveTrigger,b=n.nextFocusable,_=n.prevFocusable,I=Xe.writable(!1),y=Xe(qe(0)),A=Xe(qe(null)),w=Xe(qe("right")),F=Xe(qe(null)),T=Xe(fo([w,A],([x,Z])=>W=>x===(Z==null?void 0:Z.side)&&fv(W,Z==null?void 0:Z.area))),{typed:P,handleTypeaheadSearch:L}=b_(),B=Fs({...Vc(Eu),...n.ids}),M=xc({open:p,forceVisible:c,activeTrigger:v}),j=dn(t(),{stores:[M,l,B.menu,B.trigger],returned:([x,Z,W,tt])=>({role:"menu",hidden:x?void 0:!0,style:no({display:x?void 0:"none"}),id:W,"aria-labelledby":tt,"data-state":x?"open":"closed","data-portal":Fg(Z),tabindex:-1}),action:x=>{let Z=ro;const W=Wn([M,v,o,a,l,s],([it,$t,ve,Ht,Et,At])=>{Z(),!(!it||!$t)&&_s().then(()=>{Z(),so(x,e),Z=Hc(x,{anchorElement:$t,open:p,options:{floating:ve,modal:{closeOnInteractOutside:Ht,shouldCloseOnInteractOutside:Q=>{var ot;return(ot=m.get())==null||ot(Q),!(Q.defaultPrevented||Ct($t)&&$t.contains(Q.target))},onClose:()=>{p.set(!1),$t.focus()},open:it},portal:qc(x,Et),escapeKeydown:At?void 0:null}}).destroy})}),tt=or(Gt(x,"keydown",it=>{const $t=it.target,ve=it.currentTarget;if(!Ct($t)||!Ct(ve)||!($t.closest('[role="menu"]')===ve))return;if(zc.includes(it.key)&&Tu(it,f.get()??!1),it.key===rn.TAB){it.preventDefault(),p.set(!1),Au(it,b,_);return}const Et=it.key.length===1;!(it.ctrlKey||it.altKey||it.metaKey)&&Et&&u.get()===!0&&L(it.key,Jr(ve))}));return{destroy(){W(),tt(),Z()}}}}),V=dn(t("trigger"),{stores:[p,B.menu,B.trigger],returned:([x,Z,W])=>({"aria-controls":Z,"aria-expanded":x,"data-state":x?"open":"closed",id:W,tabindex:0}),action:x=>(Ss(x),v.update(W=>W||x),{destroy:or(Gt(x,"click",W=>{const tt=p.get(),it=W.currentTarget;Ct(it)&&(Yt(it),tt||W.preventDefault())}),Gt(x,"keydown",W=>{const tt=W.currentTarget;if(!Ct(tt)||!(_l.includes(W.key)||W.key===rn.ARROW_DOWN))return;W.preventDefault(),Yt(tt);const it=tt.getAttribute("aria-controls");if(!it)return;const $t=document.getElementById(it);if(!$t)return;const ve=Jr($t);ve.length&&Ge(ve[0])}))})}),U=dn(t("arrow"),{stores:i,returned:x=>({"data-arrow":!0,style:no({position:"absolute",width:`var(--arrow-size, ${x}px)`,height:`var(--arrow-size, ${x}px)`})})}),H=dn(t("overlay"),{stores:[M],returned:([x])=>({hidden:x?void 0:!0,tabindex:-1,style:no({display:x?void 0:"none"}),"aria-hidden":"true","data-state":mv(x)}),action:x=>{let Z=ro;if(s.get()){const tt=Lg(x,{handler:()=>{p.set(!1);const it=v.get();it&&it.focus()}});tt&&tt.destroy&&(Z=tt.destroy)}const W=Wn([l],([tt])=>{if(tt===null)return ro;const it=qc(x,tt);return it===null?ro:Rg(x,it).destroy});return{destroy(){Z(),W()}}}}),C=dn(t("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:x=>(so(x,e),Ss(x),{destroy:or(Gt(x,"pointerdown",W=>{const tt=W.currentTarget;if(Ct(tt)&&sr(tt)){W.preventDefault();return}}),Gt(x,"click",W=>{const tt=W.currentTarget;if(Ct(tt)){if(sr(tt)){W.preventDefault();return}if(W.defaultPrevented){Ge(tt);return}g.get()&&ki(1).then(()=>{p.set(!1)})}}),Gt(x,"keydown",W=>{et(W)}),Gt(x,"pointermove",W=>{Vn(W)}),Gt(x,"pointerleave",W=>{Vr(W)}),Gt(x,"focusin",W=>{Te(W)}),Gt(x,"focusout",W=>{We(W)}))})}),G=dn(t("group"),{returned:()=>x=>({role:"group","aria-labelledby":x})}),J=dn(t("group-label"),{returned:()=>x=>({id:x})}),X={defaultChecked:!1,disabled:!1},Dt=x=>{const Z={...X,...x},W=Z.checked??qe(Z.defaultChecked??null),tt=js(W,Z.onCheckedChange),it=qe(Z.disabled),$t=dn(t("checkbox-item"),{stores:[tt,it],returned:([Et,At])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":dt(Et)?"mixed":Et?"true":"false","data-disabled":$a(At),"data-state":Kt(Et)}),action:Et=>(so(Et,e),Ss(Et),{destroy:or(Gt(Et,"pointerdown",Q=>{const ot=Q.currentTarget;if(Ct(ot)&&sr(ot)){Q.preventDefault();return}}),Gt(Et,"click",Q=>{const ot=Q.currentTarget;if(Ct(ot)){if(sr(ot)){Q.preventDefault();return}if(Q.defaultPrevented){Ge(ot);return}tt.update(fe=>dt(fe)?!0:!fe),g.get()&&_s().then(()=>{p.set(!1)})}}),Gt(Et,"keydown",Q=>{et(Q)}),Gt(Et,"pointermove",Q=>{const ot=Q.currentTarget;if(Ct(ot)){if(sr(ot)){Re(Q);return}Vn(Q,ot)}}),Gt(Et,"pointerleave",Q=>{Vr(Q)}),Gt(Et,"focusin",Q=>{Te(Q)}),Gt(Et,"focusout",Q=>{We(Q)}))})}),ve=fo(tt,Et=>Et===!0),Ht=fo(tt,Et=>Et==="indeterminate");return{elements:{checkboxItem:$t},states:{checked:tt},helpers:{isChecked:ve,isIndeterminate:Ht},options:{disabled:it}}},Bt=(x={})=>{const Z=x.value??qe(x.defaultValue??null),W=js(Z,x.onValueChange),tt=dn(t("radio-group"),{returned:()=>({role:"group"})}),it={disabled:!1},$t=dn(t("radio-item"),{stores:[W],returned:([Ht])=>Et=>{const{value:At,disabled:Q}={...it,...Et},ot=Ht===At;return{disabled:Q,role:"menuitemradio","data-state":ot?"checked":"unchecked","aria-checked":ot,"data-disabled":$a(Q),"data-value":At,"data-orientation":"vertical",tabindex:-1}},action:Ht=>(so(Ht,e),{destroy:or(Gt(Ht,"pointerdown",At=>{const Q=At.currentTarget;if(!Ct(Q))return;const ot=Ht.dataset.value;if(Ht.dataset.disabled||ot===void 0){At.preventDefault();return}}),Gt(Ht,"click",At=>{const Q=At.currentTarget;if(!Ct(Q))return;const ot=Ht.dataset.value;if(Ht.dataset.disabled||ot===void 0){At.preventDefault();return}if(At.defaultPrevented){if(!Ct(Q))return;Ge(Q);return}W.set(ot),g.get()&&_s().then(()=>{p.set(!1)})}),Gt(Ht,"keydown",At=>{et(At)}),Gt(Ht,"pointermove",At=>{const Q=At.currentTarget;if(!Ct(Q))return;const ot=Ht.dataset.value;if(Ht.dataset.disabled||ot===void 0){Re(At);return}Vn(At,Q)}),Gt(Ht,"pointerleave",At=>{Vr(At)}),Gt(Ht,"focusin",At=>{Te(At)}),Gt(Ht,"focusout",At=>{We(At)}))})}),ve=fo(W,Ht=>Et=>Ht===Et);return{elements:{radioGroup:tt,radioItem:$t},states:{value:W},helpers:{isChecked:ve}}},{elements:{root:Pt}}=l_({orientation:"horizontal"}),K={...cv,disabled:!1,positioning:{placement:"right-start",gutter:8}},ye=x=>{const Z={...K,...x},W=Z.open??qe(!1),tt=js(W,Z==null?void 0:Z.onOpenChange),it=Fs(fh(Z,"ids")),{positioning:$t,arrowSize:ve,disabled:Ht}=it,Et=Xe(qe(null)),At=Xe(qe(null)),Q=Xe(qe(0)),ot=Fs({...Vc(Eu),...Z.ids});Wc(()=>{const Nt=document.getElementById(ot.trigger.get());Nt&&Et.set(Nt)});const fe=xc({open:tt,forceVisible:c,activeTrigger:Et}),Je=dn(t("submenu"),{stores:[fe,ot.menu,ot.trigger],returned:([Nt,Be,Ve])=>({role:"menu",hidden:Nt?void 0:!0,style:no({display:Nt?void 0:"none"}),id:Be,"aria-labelledby":Ve,"data-state":Nt?"open":"closed","data-id":Be,tabindex:-1}),action:Nt=>{let Be=ro;const Ve=Wn([fe,$t],([ht,Xt])=>{if(Be(),!ht)return;const pe=Et.get();pe&&_s().then(()=>{Be();const we=Ue(pe);Be=Hc(Nt,{anchorElement:pe,open:tt,options:{floating:Xt,portal:Ct(we)?we:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy})}),Ft=or(Gt(Nt,"keydown",ht=>{if(ht.key===rn.ESCAPE)return;const Xt=ht.target,pe=ht.currentTarget;if(!Ct(Xt)||!Ct(pe)||!(Xt.closest('[role="menu"]')===pe))return;if(zc.includes(ht.key)){ht.stopImmediatePropagation(),Tu(ht,f.get()??!1);return}const zn=lv.ltr.includes(ht.key),Fe=ht.ctrlKey||ht.altKey||ht.metaKey,Me=ht.key.length===1;if(zn){const Pe=Et.get();ht.preventDefault(),tt.update(()=>(Pe&&Ge(Pe),!1));return}if(ht.key===rn.TAB){ht.preventDefault(),p.set(!1),Au(ht,b,_);return}!Fe&&Me&&u.get()===!0&&L(ht.key,Jr(pe))}),Gt(Nt,"pointermove",ht=>{fn(ht)}),Gt(Nt,"focusout",ht=>{const Xt=Et.get();if(I.get()){const pe=ht.target,we=document.getElementById(ot.menu.get());if(!Ct(we)||!Ct(pe))return;!we.contains(pe)&&pe!==Xt&&tt.set(!1)}else{const pe=ht.currentTarget,we=ht.relatedTarget;if(!Ct(we)||!Ct(pe))return;!pe.contains(we)&&we!==Xt&&tt.set(!1)}}));return{destroy(){Ve(),Be(),Ft()}}}}),gn=dn(t("subtrigger"),{stores:[tt,Ht,ot.menu,ot.trigger],returned:([Nt,Be,Ve,Ft])=>({role:"menuitem",id:Ft,tabindex:-1,"aria-controls":Ve,"aria-expanded":Nt,"data-state":Nt?"open":"closed","data-disabled":$a(Be),"aria-haspopop":"menu"}),action:Nt=>{so(Nt,e),Ss(Nt),Et.update(Ft=>Ft||Nt);const Be=()=>{nl(At),window.clearTimeout(Q.get()),A.set(null)},Ve=or(Gt(Nt,"click",Ft=>{if(Ft.defaultPrevented)return;const ht=Ft.currentTarget;!Ct(ht)||sr(ht)||(Ge(ht),tt.get()||tt.update(Xt=>Xt||(Et.set(ht),!Xt)))}),Gt(Nt,"keydown",Ft=>{const ht=P.get(),Xt=Ft.currentTarget;if(!(!Ct(Xt)||sr(Xt)||ht.length>0&&Ft.key===rn.SPACE)&&av.ltr.includes(Ft.key)){if(!tt.get()){Xt.click(),Ft.preventDefault();return}const we=Xt.getAttribute("aria-controls");if(!we)return;const zn=document.getElementById(we);if(!Ct(zn))return;const Fe=Jr(zn)[0];Ge(Fe)}}),Gt(Nt,"pointermove",Ft=>{if(!oo(Ft)||(pn(Ft),Ft.defaultPrevented))return;const ht=Ft.currentTarget;if(!Ct(ht))return;hv(ot.menu.get())||Ge(ht);const Xt=At.get();!tt.get()&&!Xt&&!sr(ht)&&At.set(window.setTimeout(()=>{tt.update(()=>(Et.set(ht),!0)),nl(At)},100))}),Gt(Nt,"pointerleave",Ft=>{if(!oo(Ft))return;nl(At);const ht=document.getElementById(ot.menu.get()),Xt=ht==null?void 0:ht.getBoundingClientRect();if(Xt){const pe=ht==null?void 0:ht.dataset.side,we=pe==="right",zn=we?-5:5,Fe=Xt[we?"left":"right"],Me=Xt[we?"right":"left"];A.set({area:[{x:Ft.clientX+zn,y:Ft.clientY},{x:Fe,y:Xt.top},{x:Me,y:Xt.top},{x:Me,y:Xt.bottom},{x:Fe,y:Xt.bottom}],side:pe}),window.clearTimeout(Q.get()),Q.set(window.setTimeout(()=>{A.set(null)},300))}else{if(Mn(Ft),Ft.defaultPrevented)return;A.set(null)}}),Gt(Nt,"focusout",Ft=>{const ht=Ft.currentTarget;if(!Ct(ht))return;ai(ht);const Xt=Ft.relatedTarget;if(!Ct(Xt))return;const pe=ht.getAttribute("aria-controls");if(!pe)return;const we=document.getElementById(pe);we&&!we.contains(Xt)&&tt.set(!1)}),Gt(Nt,"focusin",Ft=>{Te(Ft)}));return{destroy(){Be(),Ve()}}}}),xn=dn(t("subarrow"),{stores:ve,returned:Nt=>({"data-arrow":!0,style:no({position:"absolute",width:`var(--arrow-size, ${Nt}px)`,height:`var(--arrow-size, ${Nt}px)`})})});return Wn([p],([Nt])=>{Nt||(Et.set(null),tt.set(!1))}),Wn([A],([Nt])=>{!mi||Nt||window.clearTimeout(Q.get())}),Wn([tt],([Nt])=>{if(mi&&(Nt&&I.get()&&ki(1).then(()=>{const Be=document.getElementById(ot.menu.get());if(!Be)return;const Ve=Jr(Be);Ve.length&&Ge(Ve[0])}),!Nt)){const Be=F.get(),Ve=document.getElementById(ot.trigger.get());if(Be&&ki(1).then(()=>{const Ft=document.getElementById(ot.menu.get());Ft&&Ft.contains(Be)&&ai(Be)}),!Ve||document.activeElement===Ve)return;ai(Ve)}}),{ids:ot,elements:{subTrigger:gn,subMenu:Je,subArrow:xn},states:{subOpen:tt},options:it}};Wc(()=>{const x=document.getElementById(B.trigger.get());Ct(x)&&p.get()&&v.set(x);const Z=[],W=()=>I.set(!1),tt=()=>{I.set(!0),Z.push(or(Wr(document,"pointerdown",W,{capture:!0,once:!0}),Wr(document,"pointermove",W,{capture:!0,once:!0})))},it=$t=>{if($t.key===rn.ESCAPE&&s.get()){p.set(!1);return}};return Z.push(Wr(document,"keydown",tt,{capture:!0})),Z.push(Wr(document,"keydown",it)),()=>{Z.forEach($t=>$t())}}),Wn([p,F],([x,Z])=>{!x&&Z&&ai(Z)}),Wn([p],([x])=>{if(mi&&!x){const Z=v.get();if(!Z)return;const W=h.get();!x&&Z&&c_({prop:W,defaultEl:Z})}}),Wn([p,r],([x,Z])=>{if(!mi)return;const W=[];return x&&Z&&W.push(D_()),ki(1).then(()=>{const tt=document.getElementById(B.menu.get());if(tt&&x&&I.get()){if(d.get()){Ge(tt);return}const it=Jr(tt);if(!it.length)return;Ge(it[0])}}),()=>{W.forEach(tt=>tt())}}),Wn(p,x=>{if(!mi)return;const Z=()=>I.set(!1),W=tt=>{if(I.set(!0),tt.key===rn.ESCAPE&&x&&s.get()){p.set(!1);return}};return or(Wr(document,"pointerdown",Z,{capture:!0,once:!0}),Wr(document,"pointermove",Z,{capture:!0,once:!0}),Wr(document,"keydown",W,{capture:!0}))});function Yt(x){p.update(Z=>{const W=!Z;return W&&(b.set(y_(x)),_.set(v_(x)),v.set(x)),W})}function Te(x){const Z=x.currentTarget;if(!Ct(Z))return;const W=F.get();W&&ai(W),w_(Z),F.set(Z)}function We(x){const Z=x.currentTarget;Ct(Z)&&ai(Z)}function pn(x){Ne(x)&&x.preventDefault()}function Re(x){if(Ne(x))return;const Z=x.target;if(!Ct(Z))return;const W=Ue(Z);W&&Ge(W)}function Mn(x){Ne(x)&&x.preventDefault()}function fn(x){if(!oo(x))return;const Z=x.target,W=x.currentTarget;if(!Ct(W)||!Ct(Z))return;const tt=y.get(),it=tt!==x.clientX;if(W.contains(Z)&&it){const $t=x.clientX>tt?"right":"left";w.set($t),y.set(x.clientX)}}function Vn(x,Z=null){if(!oo(x)||(pn(x),x.defaultPrevented))return;if(Z){Ge(Z);return}const W=x.currentTarget;Ct(W)&&Ge(W)}function Vr(x){oo(x)&&Re(x)}function et(x){if(P.get().length>0&&x.key===rn.SPACE){x.preventDefault();return}if(_l.includes(x.key)){x.preventDefault();const tt=x.currentTarget;if(!Ct(tt))return;tt.click()}}function dt(x){return x==="indeterminate"}function Kt(x){return dt(x)?"indeterminate":x?"checked":"unchecked"}function Ne(x){return T.get()(x)}function Ue(x){const Z=x.closest('[role="menu"]');return Ct(Z)?Z:null}return{elements:{trigger:V,menu:j,overlay:H,item:C,group:G,groupLabel:J,arrow:U,separator:Pt},builders:{createCheckboxItem:Dt,createSubmenu:ye,createMenuRadioGroup:Bt},states:{open:p},helpers:{handleTypeaheadSearch:L},ids:B,options:n.rootOptions}}function Au(n,t,e){if(n.shiftKey){const r=e.get();r&&(n.preventDefault(),ki(1).then(()=>r.focus()),e.set(null))}else{const r=t.get();r&&(n.preventDefault(),ki(1).then(()=>r.focus()),t.set(null))}}function Jr(n){return Array.from(n.querySelectorAll(`[data-melt-menu-id="${n.id}"]`)).filter(t=>Ct(t))}function Ss(n){!n||!sr(n)||(n.setAttribute("data-disabled",""),n.setAttribute("aria-disabled","true"))}function nl(n){if(!mi)return;const t=n.get();t&&(window.clearTimeout(t),n.set(null))}function oo(n){return n.pointerType==="mouse"}function so(n,t){if(!n)return;const e=n.closest(`${t()}, ${t("submenu")}`);Ct(e)&&n.setAttribute("data-melt-menu-id",e.id)}function Tu(n,t){n.preventDefault();const e=document.activeElement,r=n.currentTarget;if(!Ct(e)||!Ct(r))return;const i=Jr(r);if(!i.length)return;const o=i.filter(l=>!(l.hasAttribute("data-disabled")||l.getAttribute("disabled")==="true")),s=o.indexOf(e);let a;switch(n.key){case rn.ARROW_DOWN:t?a=s<o.length-1?s+1:0:a=s<o.length-1?s+1:s;break;case rn.ARROW_UP:t?a=s>0?s-1:o.length-1:a=s<0?o.length-1:s>0?s-1:0;break;case rn.HOME:a=0;break;case rn.END:a=o.length-1;break;default:return}Ge(o[a])}function fv(n,t){if(!t)return!1;const e={x:n.clientX,y:n.clientY};return dv(e,t)}function dv(n,t){const{x:e,y:r}=n;let i=!1;for(let o=0,s=t.length-1;o<t.length;s=o++){const a=t[o].x,l=t[o].y,c=t[s].x,u=t[s].y;l>r!=u>r&&e<(c-a)*(r-l)/(u-l)+a&&(i=!i)}return i}function hv(n){const t=document.activeElement;if(!Ct(t))return!1;const e=t.closest(`[data-id="${n}"]`);return Ct(e)}function mv(n){return n?"open":"closed"}const pv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function gv(n){const t={...pv,...n},e=Fs(fh(t,"ids")),r=t.open??qe(t.defaultOpen),i=js(r,t==null?void 0:t.onOpenChange),o=Xe(qe(null)),s=Xe(qe(null)),a=Xe(qe(null)),{elements:l,builders:c,ids:u,states:f,options:h}=uv({rootOptions:e,rootOpen:i,rootActiveTrigger:Xe(o),nextFocusable:Xe(s),prevFocusable:Xe(a),selector:"dropdown-menu",ids:t.ids});return{ids:u,elements:l,states:f,builders:c,options:h}}let _v="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",bv=(n=21)=>{let t="",e=n|0;for(;e--;)t+=_v[Math.random()*64|0];return t};function yv(){return bv(10)}function bc(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function ss(){const{NAME:n}=bc();return f_(n)}function vv(n){const{NAME:t,PARTS:e}=bc(),r=Ug("menu",e),i={...gv({...Vg(n),forceVisible:!0}),getAttrs:r};return gh(t,i),{...i,updateOption:xg(i.options)}}function wv(){const{GROUP_NAME:n}=bc(),{elements:{group:t},getAttrs:e}=ss(),r=yv();return gh(n,r),{group:t,id:r,getAttrs:e}}function Sv(n){const e={...{side:"bottom",align:"center"},...n},{options:{positioning:r}}=ss();zg(r)(e)}const Iv=n=>({builder:n&8}),Bu=n=>({builder:n[3]}),Ov=n=>({builder:n&8}),Mu=n=>({builder:n[3]});function kv(n){let t=n[1]?"a":"div",e,r,i=(n[1]?"a":"div")&&rl(n);return{c(){i&&i.c(),e=ft()},l(o){i&&i.l(o),e=ft()},m(o,s){i&&i.m(o,s),R(o,e,s),r=!0},p(o,s){o[1],t?ee(t,o[1]?"a":"div")?(i.d(1),i=rl(o),t=o[1]?"a":"div",i.c(),i.m(e.parentNode,e)):i.p(o,s):(i=rl(o),t=o[1]?"a":"div",i.c(),i.m(e.parentNode,e))},i(o){r||(O(i,o),r=!0)},o(o){D(i,o),r=!1},d(o){o&&S(e),i&&i.d(o)}}}function Dv(n){let t;const e=n[11].default,r=Oe(e,n,n[10],Mu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&1032)&&ke(r,e,i,i[10],t?Ee(e,i[10],o,Ov):De(i[10]),Mu)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function rl(n){let t,e,r,i;const o=n[11].default,s=Oe(o,n,n[10],Bu);let a=[{href:n[1]},n[3],n[6]],l={};for(let c=0;c<a.length;c+=1)l=me(l,a[c]);return{c(){t=Y(n[1]?"a":"div"),s&&s.c(),this.h()},l(c){t=q(c,((n[1]?"a":"div")||"null").toUpperCase(),{href:!0});var u=$(t);s&&s.l(u),u.forEach(S),this.h()},h(){Jc(n[1]?"a":"div")(t,l)},m(c,u){R(c,t,u),s&&s.m(t,null),n[13](t),e=!0,r||(i=[Rr(n[3].action(t)),he(t,"m-click",n[5]),he(t,"m-focusin",n[5]),he(t,"m-focusout",n[5]),he(t,"m-keydown",n[5]),he(t,"m-pointerdown",n[5]),he(t,"m-pointerleave",n[5]),he(t,"m-pointermove",n[5]),he(t,"pointerenter",n[12])],r=!0)},p(c,u){s&&s.p&&(!e||u&1032)&&ke(s,o,c,c[10],e?Ee(o,c[10],u,Iv):De(c[10]),Bu),Jc(c[1]?"a":"div")(t,l=An(a,[(!e||u&2)&&{href:c[1]},u&8&&c[3],u&64&&c[6]]))},i(c){e||(O(s,c),e=!0)},o(c){D(s,c),e=!1},d(c){c&&S(t),s&&s.d(c),n[13](null),r=!1,wr(i)}}}function Ev(n){let t,e,r,i;const o=[Dv,kv],s=[];function a(l,c){return l[2]?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ft()},l(l){e.l(l),r=ft()},m(l,c){s[t].m(l,c),R(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),D(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),O(e,1),e.m(r.parentNode,r))},i(l){i||(O(e),i=!0)},o(l){D(e),i=!1},d(l){l&&S(r),s[t].d(l)}}}function Av(n,t,e){let r,i;const o=["href","asChild","disabled","el"];let s=ln(t,o),a,{$$slots:l={},$$scope:c}=t,{href:u=void 0}=t,{asChild:f=!1}=t,{disabled:h=!1}=t,{el:d=void 0}=t;const{elements:{item:g},getAttrs:m}=ss();Ae(n,g,_=>e(9,a=_));const p=Jl();function v(_){lr.call(this,n,_)}function b(_){Kn[_?"unshift":"push"](()=>{d=_,e(0,d)})}return n.$$set=_=>{t=me(me({},t),ri(_)),e(6,s=ln(t,o)),"href"in _&&e(1,u=_.href),"asChild"in _&&e(2,f=_.asChild),"disabled"in _&&e(7,h=_.disabled),"el"in _&&e(0,d=_.el),"$$scope"in _&&e(10,c=_.$$scope)},n.$$.update=()=>{n.$$.dirty&512&&e(3,r=a),n.$$.dirty&128&&e(8,i={...m("item"),...Wg(h)}),n.$$.dirty&264&&Object.assign(r,i)},[d,u,f,r,g,p,s,h,i,a,c,l,v,b]}class Tv extends ae{constructor(t){super(),le(this,t,Av,Ev,ee,{href:1,asChild:2,disabled:7,el:0})}}const Bv=n=>({builder:n&4}),Pu=n=>({builder:n[2]}),Mv=n=>({builder:n&4}),Nu=n=>({builder:n[2]});function Pv(n){let t,e,r,i;const o=n[7].default,s=Oe(o,n,n[6],Pu);let a=[n[2],n[4]],l={};for(let c=0;c<a.length;c+=1)l=me(l,a[c]);return{c(){t=Y("div"),s&&s.c(),this.h()},l(c){t=q(c,"DIV",{});var u=$(t);s&&s.l(u),u.forEach(S),this.h()},h(){$e(t,l)},m(c,u){R(c,t,u),s&&s.m(t,null),n[8](t),e=!0,r||(i=Rr(n[2].action(t)),r=!0)},p(c,u){s&&s.p&&(!e||u&68)&&ke(s,o,c,c[6],e?Ee(o,c[6],u,Bv):De(c[6]),Pu),$e(t,l=An(a,[u&4&&c[2],u&16&&c[4]]))},i(c){e||(O(s,c),e=!0)},o(c){D(s,c),e=!1},d(c){c&&S(t),s&&s.d(c),n[8](null),r=!1,i()}}}function Nv(n){let t;const e=n[7].default,r=Oe(e,n,n[6],Nu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&68)&&ke(r,e,i,i[6],t?Ee(e,i[6],o,Mv):De(i[6]),Nu)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function Cv(n){let t,e,r,i;const o=[Nv,Pv],s=[];function a(l,c){return l[1]?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ft()},l(l){e.l(l),r=ft()},m(l,c){s[t].m(l,c),R(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),D(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),O(e,1),e.m(r.parentNode,r))},i(l){i||(O(e),i=!0)},o(l){D(e),i=!1},d(l){l&&S(r),s[t].d(l)}}}function Fv(n,t,e){let r;const i=["asChild","el"];let o=ln(t,i),s,{$$slots:a={},$$scope:l}=t,{asChild:c=!1}=t,{el:u=void 0}=t;const{group:f,id:h,getAttrs:d}=wv();Ae(n,f,p=>e(5,s=p));const g=d("group");function m(p){Kn[p?"unshift":"push"](()=>{u=p,e(0,u)})}return n.$$set=p=>{t=me(me({},t),ri(p)),e(4,o=ln(t,i)),"asChild"in p&&e(1,c=p.asChild),"el"in p&&e(0,u=p.el),"$$scope"in p&&e(6,l=p.$$scope)},n.$$.update=()=>{n.$$.dirty&32&&e(2,r=s(h)),n.$$.dirty&4&&Object.assign(r,g)},[u,c,r,f,o,s,l,a,m]}class jv extends ae{constructor(t){super(),le(this,t,Fv,Cv,ee,{asChild:1,el:0})}}const Lv=n=>({ids:n&1}),Cu=n=>({ids:n[0]});function Rv(n){let t;const e=n[16].default,r=Oe(e,n,n[15],Cu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,[o]){r&&r.p&&(!t||o&32769)&&ke(r,e,i,i[15],t?Ee(e,i[15],o,Lv):De(i[15]),Cu)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function Uv(n,t,e){let r,{$$slots:i={},$$scope:o}=t,{closeOnOutsideClick:s=void 0}=t,{closeOnEscape:a=void 0}=t,{portal:l=void 0}=t,{open:c=void 0}=t,{onOpenChange:u=void 0}=t,{preventScroll:f=void 0}=t,{loop:h=void 0}=t,{dir:d=void 0}=t,{typeahead:g=void 0}=t,{closeFocus:m=void 0}=t,{disableFocusFirstItem:p=void 0}=t,{closeOnItemClick:v=void 0}=t,{onOutsideClick:b=void 0}=t;const{states:{open:_},updateOption:I,ids:y}=vv({closeOnOutsideClick:s,closeOnEscape:a,portal:l,forceVisible:!0,defaultOpen:c,preventScroll:f,loop:h,dir:d,typeahead:g,closeFocus:m,disableFocusFirstItem:p,closeOnItemClick:v,onOutsideClick:b,onOpenChange:({next:w})=>(c!==w&&(u==null||u(w),e(2,c=w)),w)}),A=fo([y.menu,y.trigger],([w,F])=>({menu:w,trigger:F}));return Ae(n,A,w=>e(0,r=w)),n.$$set=w=>{"closeOnOutsideClick"in w&&e(3,s=w.closeOnOutsideClick),"closeOnEscape"in w&&e(4,a=w.closeOnEscape),"portal"in w&&e(5,l=w.portal),"open"in w&&e(2,c=w.open),"onOpenChange"in w&&e(6,u=w.onOpenChange),"preventScroll"in w&&e(7,f=w.preventScroll),"loop"in w&&e(8,h=w.loop),"dir"in w&&e(9,d=w.dir),"typeahead"in w&&e(10,g=w.typeahead),"closeFocus"in w&&e(11,m=w.closeFocus),"disableFocusFirstItem"in w&&e(12,p=w.disableFocusFirstItem),"closeOnItemClick"in w&&e(13,v=w.closeOnItemClick),"onOutsideClick"in w&&e(14,b=w.onOutsideClick),"$$scope"in w&&e(15,o=w.$$scope)},n.$$.update=()=>{n.$$.dirty&4&&c!==void 0&&_.set(c),n.$$.dirty&8&&I("closeOnOutsideClick",s),n.$$.dirty&16&&I("closeOnEscape",a),n.$$.dirty&32&&I("portal",l),n.$$.dirty&128&&I("preventScroll",f),n.$$.dirty&256&&I("loop",h),n.$$.dirty&512&&I("dir",d),n.$$.dirty&2048&&I("closeFocus",m),n.$$.dirty&4096&&I("disableFocusFirstItem",p),n.$$.dirty&1024&&I("typeahead",g),n.$$.dirty&8192&&I("closeOnItemClick",v),n.$$.dirty&16384&&I("onOutsideClick",b)},[r,A,c,s,a,l,u,f,h,d,g,m,p,v,b,o,i]}class Vv extends ae{constructor(t){super(),le(this,t,Uv,Rv,ee,{closeOnOutsideClick:3,closeOnEscape:4,portal:5,open:2,onOpenChange:6,preventScroll:7,loop:8,dir:9,typeahead:10,closeFocus:11,disableFocusFirstItem:12,closeOnItemClick:13,onOutsideClick:14})}}const xv=n=>({builder:n[0]&256}),Fu=n=>({builder:n[8]}),zv=n=>({builder:n[0]&256}),ju=n=>({builder:n[8]}),Wv=n=>({builder:n[0]&256}),Lu=n=>({builder:n[8]}),Hv=n=>({builder:n[0]&256}),Ru=n=>({builder:n[8]}),qv=n=>({builder:n[0]&256}),Uu=n=>({builder:n[8]}),Yv=n=>({builder:n[0]&256}),Vu=n=>({builder:n[8]});function Kv(n){let t,e,r,i;const o=n[28].default,s=Oe(o,n,n[27],Fu);let a=[n[8],n[13]],l={};for(let c=0;c<a.length;c+=1)l=me(l,a[c]);return{c(){t=Y("div"),s&&s.c(),this.h()},l(c){t=q(c,"DIV",{});var u=$(t);s&&s.l(u),u.forEach(S),this.h()},h(){$e(t,l)},m(c,u){R(c,t,u),s&&s.m(t,null),n[33](t),e=!0,r||(i=[Rr(n[8].action(t)),he(t,"m-keydown",n[12])],r=!0)},p(c,u){s&&s.p&&(!e||u[0]&134217984)&&ke(s,o,c,c[27],e?Ee(o,c[27],u,xv):De(c[27]),Fu),$e(t,l=An(a,[u[0]&256&&c[8],u[0]&8192&&c[13]]))},i(c){e||(O(s,c),e=!0)},o(c){D(s,c),e=!1},d(c){c&&S(t),s&&s.d(c),n[33](null),r=!1,wr(i)}}}function $v(n){let t,e,r,i,o;const s=n[28].default,a=Oe(s,n,n[27],ju);let l=[n[8],n[13]],c={};for(let u=0;u<l.length;u+=1)c=me(c,l[u]);return{c(){t=Y("div"),a&&a.c(),this.h()},l(u){t=q(u,"DIV",{});var f=$(t);a&&a.l(f),f.forEach(S),this.h()},h(){$e(t,c)},m(u,f){R(u,t,f),a&&a.m(t,null),n[32](t),r=!0,i||(o=[Rr(n[8].action(t)),he(t,"m-keydown",n[12])],i=!0)},p(u,f){n=u,a&&a.p&&(!r||f[0]&134217984)&&ke(a,s,n,n[27],r?Ee(s,n[27],f,zv):De(n[27]),ju),$e(t,c=An(l,[f[0]&256&&n[8],f[0]&8192&&n[13]]))},i(u){r||(O(a,u),e&&e.end(1),r=!0)},o(u){D(a,u),u&&(e=es(t,n[5],n[6])),r=!1},d(u){u&&S(t),a&&a.d(u),n[32](null),u&&e&&e.end(),i=!1,wr(o)}}}function Jv(n){let t,e,r,i,o;const s=n[28].default,a=Oe(s,n,n[27],Lu);let l=[n[8],n[13]],c={};for(let u=0;u<l.length;u+=1)c=me(c,l[u]);return{c(){t=Y("div"),a&&a.c(),this.h()},l(u){t=q(u,"DIV",{});var f=$(t);a&&a.l(f),f.forEach(S),this.h()},h(){$e(t,c)},m(u,f){R(u,t,f),a&&a.m(t,null),n[31](t),r=!0,i||(o=[Rr(n[8].action(t)),he(t,"m-keydown",n[12])],i=!0)},p(u,f){n=u,a&&a.p&&(!r||f[0]&134217984)&&ke(a,s,n,n[27],r?Ee(s,n[27],f,Wv):De(n[27]),Lu),$e(t,c=An(l,[f[0]&256&&n[8],f[0]&8192&&n[13]]))},i(u){r||(O(a,u),u&&(e||On(()=>{e=Hi(t,n[3],n[4]),e.start()})),r=!0)},o(u){D(a,u),r=!1},d(u){u&&S(t),a&&a.d(u),n[31](null),i=!1,wr(o)}}}function Qv(n){let t,e,r,i,o,s;const a=n[28].default,l=Oe(a,n,n[27],Ru);let c=[n[8],n[13]],u={};for(let f=0;f<c.length;f+=1)u=me(u,c[f]);return{c(){t=Y("div"),l&&l.c(),this.h()},l(f){t=q(f,"DIV",{});var h=$(t);l&&l.l(h),h.forEach(S),this.h()},h(){$e(t,u)},m(f,h){R(f,t,h),l&&l.m(t,null),n[30](t),i=!0,o||(s=[Rr(n[8].action(t)),he(t,"m-keydown",n[12])],o=!0)},p(f,h){n=f,l&&l.p&&(!i||h[0]&134217984)&&ke(l,a,n,n[27],i?Ee(a,n[27],h,Hv):De(n[27]),Ru),$e(t,u=An(c,[h[0]&256&&n[8],h[0]&8192&&n[13]]))},i(f){i||(O(l,f),f&&On(()=>{i&&(r&&r.end(1),e=Hi(t,n[3],n[4]),e.start())}),i=!0)},o(f){D(l,f),e&&e.invalidate(),f&&(r=es(t,n[5],n[6])),i=!1},d(f){f&&S(t),l&&l.d(f),n[30](null),f&&r&&r.end(),o=!1,wr(s)}}}function Gv(n){let t,e,r,i,o;const s=n[28].default,a=Oe(s,n,n[27],Uu);let l=[n[8],n[13]],c={};for(let u=0;u<l.length;u+=1)c=me(c,l[u]);return{c(){t=Y("div"),a&&a.c(),this.h()},l(u){t=q(u,"DIV",{});var f=$(t);a&&a.l(f),f.forEach(S),this.h()},h(){$e(t,c)},m(u,f){R(u,t,f),a&&a.m(t,null),n[29](t),r=!0,i||(o=[Rr(n[8].action(t)),he(t,"m-keydown",n[12])],i=!0)},p(u,f){n=u,a&&a.p&&(!r||f[0]&134217984)&&ke(a,s,n,n[27],r?Ee(s,n[27],f,qv):De(n[27]),Uu),$e(t,c=An(l,[f[0]&256&&n[8],f[0]&8192&&n[13]]))},i(u){r||(O(a,u),u&&On(()=>{r&&(e||(e=In(t,n[1],n[2],!0)),e.run(1))}),r=!0)},o(u){D(a,u),u&&(e||(e=In(t,n[1],n[2],!1)),e.run(0)),r=!1},d(u){u&&S(t),a&&a.d(u),n[29](null),u&&e&&e.end(),i=!1,wr(o)}}}function Xv(n){let t;const e=n[28].default,r=Oe(e,n,n[27],Vu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o[0]&134217984)&&ke(r,e,i,i[27],t?Ee(e,i[27],o,Yv):De(i[27]),Vu)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function Zv(n){let t,e,r,i;const o=[Xv,Gv,Qv,Jv,$v,Kv],s=[];function a(l,c){return l[7]&&l[9]?0:l[1]&&l[9]?1:l[3]&&l[5]&&l[9]?2:l[3]&&l[9]?3:l[5]&&l[9]?4:l[9]?5:-1}return~(t=a(n))&&(e=s[t]=o[t](n)),{c(){e&&e.c(),r=ft()},l(l){e&&e.l(l),r=ft()},m(l,c){~t&&s[t].m(l,c),R(l,r,c),i=!0},p(l,c){let u=t;t=a(l),t===u?~t&&s[t].p(l,c):(e&&(jt(),D(s[u],1,1,()=>{s[u]=null}),Lt()),~t?(e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),O(e,1),e.m(r.parentNode,r)):e=null)},i(l){i||(O(e),i=!0)},o(l){D(e),i=!1},d(l){l&&S(r),~t&&s[t].d(l)}}}function t0(n,t,e){let r;const i=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let o=ln(t,i),s,a,{$$slots:l={},$$scope:c}=t,{transition:u=void 0}=t,{transitionConfig:f=void 0}=t,{inTransition:h=void 0}=t,{inTransitionConfig:d=void 0}=t,{outTransition:g=void 0}=t,{outTransitionConfig:m=void 0}=t,{asChild:p=!1}=t,{id:v=void 0}=t,{side:b="bottom"}=t,{align:_="center"}=t,{sideOffset:I=0}=t,{alignOffset:y=0}=t,{collisionPadding:A=8}=t,{avoidCollisions:w=!0}=t,{collisionBoundary:F=void 0}=t,{sameWidth:T=!1}=t,{fitViewport:P=!1}=t,{strategy:L="absolute"}=t,{overlap:B=!1}=t,{el:M=void 0}=t;const{elements:{menu:j},states:{open:V},ids:U,getAttrs:H}=ss();Ae(n,j,K=>e(26,a=K)),Ae(n,V,K=>e(9,s=K));const C=Jl(),G=H("content");function J(K){Kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function X(K){Kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Dt(K){Kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Bt(K){Kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}function Pt(K){Kn[K?"unshift":"push"](()=>{M=K,e(0,M)})}return n.$$set=K=>{t=me(me({},t),ri(K)),e(13,o=ln(t,i)),"transition"in K&&e(1,u=K.transition),"transitionConfig"in K&&e(2,f=K.transitionConfig),"inTransition"in K&&e(3,h=K.inTransition),"inTransitionConfig"in K&&e(4,d=K.inTransitionConfig),"outTransition"in K&&e(5,g=K.outTransition),"outTransitionConfig"in K&&e(6,m=K.outTransitionConfig),"asChild"in K&&e(7,p=K.asChild),"id"in K&&e(14,v=K.id),"side"in K&&e(15,b=K.side),"align"in K&&e(16,_=K.align),"sideOffset"in K&&e(17,I=K.sideOffset),"alignOffset"in K&&e(18,y=K.alignOffset),"collisionPadding"in K&&e(19,A=K.collisionPadding),"avoidCollisions"in K&&e(20,w=K.avoidCollisions),"collisionBoundary"in K&&e(21,F=K.collisionBoundary),"sameWidth"in K&&e(22,T=K.sameWidth),"fitViewport"in K&&e(23,P=K.fitViewport),"strategy"in K&&e(24,L=K.strategy),"overlap"in K&&e(25,B=K.overlap),"el"in K&&e(0,M=K.el),"$$scope"in K&&e(27,c=K.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&16384&&v&&U.menu.set(v),n.$$.dirty[0]&67108864&&e(8,r=a),n.$$.dirty[0]&256&&Object.assign(r,G),n.$$.dirty[0]&67076608&&s&&Sv({side:b,align:_,sideOffset:I,alignOffset:y,collisionPadding:A,avoidCollisions:w,collisionBoundary:F,sameWidth:T,fitViewport:P,strategy:L,overlap:B})},[M,u,f,h,d,g,m,p,r,s,j,V,C,o,v,b,_,I,y,A,w,F,T,P,L,B,a,c,l,J,X,Dt,Bt,Pt]}class e0 extends ae{constructor(t){super(),le(this,t,t0,Zv,ee,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:14,side:15,align:16,sideOffset:17,alignOffset:18,collisionPadding:19,avoidCollisions:20,collisionBoundary:21,sameWidth:22,fitViewport:23,strategy:24,overlap:25,el:0},null,[-1,-1])}}const n0=n=>({builder:n&4}),xu=n=>({builder:n[2]}),r0=n=>({builder:n&4}),zu=n=>({builder:n[2]});function i0(n){let t,e,r,i;const o=n[9].default,s=Oe(o,n,n[8],xu);let a=[n[2],{type:"button"},n[5]],l={};for(let c=0;c<a.length;c+=1)l=me(l,a[c]);return{c(){t=Y("button"),s&&s.c(),this.h()},l(c){t=q(c,"BUTTON",{type:!0});var u=$(t);s&&s.l(u),u.forEach(S),this.h()},h(){$e(t,l)},m(c,u){R(c,t,u),s&&s.m(t,null),t.autofocus&&t.focus(),n[10](t),e=!0,r||(i=[Rr(n[2].action(t)),he(t,"m-keydown",n[4]),he(t,"m-pointerdown",n[4])],r=!0)},p(c,u){s&&s.p&&(!e||u&260)&&ke(s,o,c,c[8],e?Ee(o,c[8],u,n0):De(c[8]),xu),$e(t,l=An(a,[u&4&&c[2],{type:"button"},u&32&&c[5]]))},i(c){e||(O(s,c),e=!0)},o(c){D(s,c),e=!1},d(c){c&&S(t),s&&s.d(c),n[10](null),r=!1,wr(i)}}}function o0(n){let t;const e=n[9].default,r=Oe(e,n,n[8],zu);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&260)&&ke(r,e,i,i[8],t?Ee(e,i[8],o,r0):De(i[8]),zu)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function s0(n){let t,e,r,i;const o=[o0,i0],s=[];function a(l,c){return l[1]?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ft()},l(l){e.l(l),r=ft()},m(l,c){s[t].m(l,c),R(l,r,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?s[t].p(l,c):(jt(),D(s[u],1,1,()=>{s[u]=null}),Lt(),e=s[t],e?e.p(l,c):(e=s[t]=o[t](l),e.c()),O(e,1),e.m(r.parentNode,r))},i(l){i||(O(e),i=!0)},o(l){D(e),i=!1},d(l){l&&S(r),s[t].d(l)}}}function a0(n,t,e){let r;const i=["asChild","id","el"];let o=ln(t,i),s,{$$slots:a={},$$scope:l}=t,{asChild:c=!1}=t,{id:u=void 0}=t,{el:f=void 0}=t;const{elements:{trigger:h},ids:d,getAttrs:g}=ss();Ae(n,h,b=>e(7,s=b));const m=Jl(),p=g("trigger");function v(b){Kn[b?"unshift":"push"](()=>{f=b,e(0,f)})}return n.$$set=b=>{t=me(me({},t),ri(b)),e(5,o=ln(t,i)),"asChild"in b&&e(1,c=b.asChild),"id"in b&&e(6,u=b.id),"el"in b&&e(0,f=b.el),"$$scope"in b&&e(8,l=b.$$scope)},n.$$.update=()=>{n.$$.dirty&64&&u&&d.trigger.set(u),n.$$.dirty&128&&e(2,r=s),n.$$.dirty&4&&Object.assign(r,p)},[f,c,r,h,m,o,u,s,l,a,v]}class l0 extends ae{constructor(t){super(),le(this,t,a0,s0,ee,{asChild:1,id:6,el:0})}}const il=n=>n instanceof Date,yc=n=>Object.keys(n).length===0,gr=n=>n!=null&&typeof n=="object",vc=(n,...t)=>Object.prototype.hasOwnProperty.call(n,...t),ol=n=>gr(n)&&yc(n),wc=()=>Object.create(null),kp=(n,t)=>n===t||!gr(n)||!gr(t)?{}:Object.keys(t).reduce((e,r)=>{if(vc(n,r)){const i=kp(n[r],t[r]);return gr(i)&&yc(i)||(e[r]=i),e}return e[r]=t[r],e},wc()),Dp=(n,t)=>n===t||!gr(n)||!gr(t)?{}:Object.keys(n).reduce((e,r)=>{if(vc(t,r)){const i=Dp(n[r],t[r]);return gr(i)&&yc(i)||(e[r]=i),e}return e[r]=void 0,e},wc()),Ep=(n,t)=>n===t?{}:!gr(n)||!gr(t)?t:il(n)||il(t)?n.valueOf()==t.valueOf()?{}:t:Object.keys(t).reduce((e,r)=>{if(vc(n,r)){const i=Ep(n[r],t[r]);return ol(i)&&!il(i)&&(ol(n[r])||!ol(t[r]))||(e[r]=i),e}return e},wc()),c0=(n,t)=>({added:kp(n,t),deleted:Dp(n,t),updated:Ep(n,t)});var Zo,ts,Ci,Ba;class u0{constructor(){eo(this,Zo,[]);eo(this,ts,{});eo(this,Ci,new Set);Ya(this,"subscribe",t=>(Dr(this,Ci).add(t),t(this.generations),()=>Dr(this,Ci).delete(t)));eo(this,Ba,0);Ya(this,"publish",()=>{if(Uc(this,Ba)._++>1e5)throw new Error("History published too many times.");Dr(this,Ci).forEach(t=>t(this.generations))})}get generations(){return[...Dr(this,Zo)]}push(t){const e=s=>{let a=Object.entries(s);a.sort((c,u)=>c[0].localeCompare(u[0]));const l=Object.fromEntries(a);return JSON.parse(JSON.stringify(l))},r=e(Dr(this,ts)),i=e(t),o=c0(r,i);Dr(this,Zo).push({...o,before:r,after:i,asof:new Date}),Ka(this,ts,i),this.publish()}}Zo=new WeakMap,ts=new WeakMap,Ci=new WeakMap,Ba=new WeakMap;function Wu(n){let t,e;return{c(){t=Y("span"),e=wt(n[1]),this.h()},l(r){t=q(r,"SPAN",{class:!0});var i=$(t);e=vt(i,n[1]),i.forEach(S),this.h()},h(){N(t,"class","cursor-pointer font-bold pr-8 flex items-center")},m(r,i){R(r,t,i),z(t,e)},p(r,i){i&2&&Le(e,r[1])},d(r){r&&S(t)}}}function f0(n){let t,e,r,i,o,s,a,l,c,u,f=n[1]&&Wu(n);return{c(){t=Y("div"),f&&f.c(),e=rt(),r=Y("span"),i=wt(n[2]),this.h()},l(h){t=q(h,"DIV",{role:!0,class:!0});var d=$(t);f&&f.l(d),e=nt(d),r=q(d,"SPAN",{class:!0});var g=$(r);i=vt(g,n[2]),g.forEach(S),d.forEach(S),this.h()},h(){N(r,"class","cursor-pointer"),N(t,"role","none"),N(t,"class",o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+Hu[n[0]])},m(h,d){R(h,t,d),f&&f.m(t,null),z(t,e),z(t,r),z(r,i),l=!0,c||(u=[he(t,"click",n[3]),he(t,"keypress",n[3])],c=!0)},p(h,[d]){h[1]?f?f.p(h,d):(f=Wu(h),f.c(),f.m(t,e)):f&&(f.d(1),f=null),(!l||d&4)&&Le(i,h[2]),(!l||d&1&&o!==(o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+Hu[h[0]]))&&N(t,"class",o)},i(h){l||(h&&On(()=>{l&&(a&&a.end(1),s=Hi(t,bl,{}),s.start())}),l=!0)},o(h){s&&s.invalidate(),h&&(a=es(t,ei,{x:1e3,duration:1e3,delay:0,opacity:.8})),l=!1},d(h){h&&S(t),f&&f.d(),h&&a&&a.end(),c=!1,wr(u)}}}const Nl={error:"negative",success:"positive"},d0=n=>Object.keys(Nl).includes(n),h0=n=>d0(n)?(console.warn(`[Toast] The status "${n}" is deprecated. Please use "${Nl[n]}" instead.`),Nl[n]):n,Hu={negative:"border-negative/50 bg-negative/10 text-negative",positive:"border-positive/50 bg-positive/10 text-positive",info:"border-info/50 bg-info/10 text-info",warning:"border-warning/50 bg-warning/10 text-warning"};function m0(n,t,e){let{id:r}=t,{status:i="info"}=t,{title:o}=t,{message:s}=t,{dismissable:a=!0}=t;const l=d_(),c=()=>{a&&l("dismiss",{id:r})};return n.$$set=u=>{"id"in u&&e(4,r=u.id),"status"in u&&e(0,i=u.status),"title"in u&&e(1,o=u.title),"message"in u&&e(2,s=u.message),"dismissable"in u&&e(5,a=u.dismissable)},n.$$.update=()=>{n.$$.dirty&1&&e(0,i=h0(i))},[i,o,s,c,r,a]}class p0 extends ae{constructor(t){super(),le(this,t,m0,f0,ee,{id:4,status:0,title:1,message:2,dismissable:5})}}function qu(n,t,e){const r=n.slice();return r[2]=t[e],r}function Yu(n,t){let e,r,i;const o=[t[2]];let s={};for(let a=0;a<o.length;a+=1)s=me(s,o[a]);return r=new p0({props:s}),r.$on("dismiss",t[1]),{key:n,first:null,c(){e=ft(),ct(r.$$.fragment),this.h()},l(a){e=ft(),lt(r.$$.fragment,a),this.h()},h(){this.first=e},m(a,l){R(a,e,l),at(r,a,l),i=!0},p(a,l){t=a;const c=l&1?An(o,[Ql(t[2])]):{};r.$set(c)},i(a){i||(O(r.$$.fragment,a),i=!0)},o(a){D(r.$$.fragment,a),i=!1},d(a){a&&S(e),st(r,a)}}}function g0(n){let t,e=[],r=new Map,i,o=ue(n[0]);const s=a=>a[2].id;for(let a=0;a<o.length;a+=1){let l=qu(n,o,a),c=s(l);r.set(c,e[a]=Yu(c,l))}return{c(){t=Y("div");for(let a=0;a<e.length;a+=1)e[a].c();this.h()},l(a){t=q(a,"DIV",{class:!0});var l=$(t);for(let c=0;c<e.length;c+=1)e[c].l(l);l.forEach(S),this.h()},h(){N(t,"class","z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80")},m(a,l){R(a,t,l);for(let c=0;c<e.length;c+=1)e[c]&&e[c].m(t,null);i=!0},p(a,[l]){l&1&&(o=ue(a[0]),jt(),e=dh(e,l,s,1,a,o,r,t,Hg,Yu,null,qu),Lt())},i(a){if(!i){for(let l=0;l<o.length;l+=1)O(e[l]);i=!0}},o(a){for(let l=0;l<e.length;l+=1)D(e[l]);i=!1},d(a){a&&S(t);for(let l=0;l<e.length;l+=1)e[l].d()}}}function _0(n,t,e){let r;return Ae(n,Yc,o=>e(0,r=o)),[r,({detail:o})=>Yc.dismiss(o.id)]}class b0 extends ae{constructor(t){super(),le(this,t,_0,g0,ee,{})}}const Ku="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png",$u="/_app/immutable/assets/wordmark-black.rfl-FBgf.png";function y0(n){let t,e,r,i,o;return{c(){t=Y("img"),r=rt(),i=Y("img"),this.h()},l(s){t=q(s,"IMG",{src:!0,alt:!0,class:!0,href:!0}),r=nt(s),i=q(s,"IMG",{src:!0,alt:!0,class:!0,href:!0}),this.h()},h(){bs(t.src,e=n[0]??n[1]??$u)||N(t,"src",e),N(t,"alt","Home"),N(t,"class","h-5 aspect-auto block dark:hidden"),N(t,"href",xt("/")),bs(i.src,o=n[0]??n[2]??Ku)||N(i,"src",o),N(i,"alt","Home"),N(i,"class","h-5 aspect-auto hidden dark:block"),N(i,"href",xt("/"))},m(s,a){R(s,t,a),R(s,r,a),R(s,i,a)},p(s,a){a&3&&!bs(t.src,e=s[0]??s[1]??$u)&&N(t,"src",e),a&5&&!bs(i.src,o=s[0]??s[2]??Ku)&&N(i,"src",o)},d(s){s&&(S(t),S(r),S(i))}}}function v0(n){let t;return{c(){t=wt(n[3])},l(e){t=vt(e,n[3])},m(e,r){R(e,t,r)},p(e,r){r&8&&Le(t,e[3])},d(e){e&&S(t)}}}function w0(n){let t;function e(o,s){return o[3]?v0:y0}let r=e(n),i=r(n);return{c(){i.c(),t=ft()},l(o){i.l(o),t=ft()},m(o,s){i.m(o,s),R(o,t,s)},p(o,[s]){r===(r=e(o))&&i?i.p(o,s):(i.d(1),i=r(o),i&&(i.c(),i.m(t.parentNode,t)))},i:Qt,o:Qt,d(o){o&&S(t),i.d(o)}}}function S0(n,t,e){let{logo:r}=t,{lightLogo:i}=t,{darkLogo:o}=t,{title:s}=t;return n.$$set=a=>{"logo"in a&&e(0,r=a.logo),"lightLogo"in a&&e(1,i=a.lightLogo),"darkLogo"in a&&e(2,o=a.darkLogo),"title"in a&&e(3,s=a.title)},[r,i,o,s]}class Sc extends ae{constructor(t){super(),le(this,t,S0,w0,ee,{logo:0,lightLogo:1,darkLogo:2,title:3})}}/*! @docsearch/js 3.8.3 | MIT License | © Algolia, Inc. and contributors | https://docsearch.algolia.com */function Cl(){return Cl=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var r in e)({}).hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},Cl.apply(null,arguments)}function mt(n){return mt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mt(n)}var as,Rt,Ap,Xr,Ju,Tp,Fl,Bp,Ic,jl,Ll,Mp,Ko={},Pp=[],I0=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,ja=Array.isArray;function mr(n,t){for(var e in t)n[e]=t[e];return n}function Oc(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function Xn(n,t,e){var r,i,o,s={};for(o in t)o=="key"?r=t[o]:o=="ref"?i=t[o]:s[o]=t[o];if(arguments.length>2&&(s.children=arguments.length>3?as.call(arguments,2):e),typeof n=="function"&&n.defaultProps!=null)for(o in n.defaultProps)s[o]===void 0&&(s[o]=n.defaultProps[o]);return ko(n,s,r,i,null)}function ko(n,t,e,r,i){var o={type:n,props:t,key:e,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:i??++Ap,__i:-1,__u:0};return i==null&&Rt.vnode!=null&&Rt.vnode(o),o}function _r(n){return n.children}function Zn(n,t){this.props=n,this.context=t}function zi(n,t){if(t==null)return n.__?zi(n.__,n.__i+1):null;for(var e;t<n.__k.length;t++)if((e=n.__k[t])!=null&&e.__e!=null)return e.__e;return typeof n.type=="function"?zi(n):null}function Np(n){var t,e;if((n=n.__)!=null&&n.__c!=null){for(n.__e=n.__c.base=null,t=0;t<n.__k.length;t++)if((e=n.__k[t])!=null&&e.__e!=null){n.__e=n.__c.base=e.__e;break}return Np(n)}}function Rl(n){(!n.__d&&(n.__d=!0)&&Xr.push(n)&&!Ea.__r++||Ju!==Rt.debounceRendering)&&((Ju=Rt.debounceRendering)||Tp)(Ea)}function Ea(){var n,t,e,r,i,o,s,a;for(Xr.sort(Fl);n=Xr.shift();)n.__d&&(t=Xr.length,r=void 0,o=(i=(e=n).__v).__e,s=[],a=[],e.__P&&((r=mr({},i)).__v=i.__v+1,Rt.vnode&&Rt.vnode(r),kc(e.__P,r,i,e.__n,e.__P.namespaceURI,32&i.__u?[o]:null,s,o??zi(i),!!(32&i.__u),a),r.__v=i.__v,r.__.__k[r.__i]=r,jp(s,r,a),r.__e!=o&&Np(r)),Xr.length>t&&Xr.sort(Fl));Ea.__r=0}function Cp(n,t,e,r,i,o,s,a,l,c,u){var f,h,d,g,m,p,v=r&&r.__k||Pp,b=t.length;for(l=function(_,I,y,A,w){var F,T,P,L,B,M=y.length,j=M,V=0;for(_.__k=new Array(w),F=0;F<w;F++)(T=I[F])!=null&&typeof T!="boolean"&&typeof T!="function"?(L=F+V,(T=_.__k[F]=typeof T=="string"||typeof T=="number"||typeof T=="bigint"||T.constructor==String?ko(null,T,null,null,null):ja(T)?ko(_r,{children:T},null,null,null):T.constructor===void 0&&T.__b>0?ko(T.type,T.props,T.key,T.ref?T.ref:null,T.__v):T).__=_,T.__b=_.__b+1,P=null,(B=T.__i=O0(T,y,L,j))!==-1&&(j--,(P=y[B])&&(P.__u|=2)),P==null||P.__v===null?(B==-1&&V--,typeof T.type!="function"&&(T.__u|=4)):B!=L&&(B==L-1?V--:B==L+1?V++:(B>L?V--:V++,T.__u|=4))):_.__k[F]=null;if(j)for(F=0;F<M;F++)(P=y[F])!=null&&!(2&P.__u)&&(P.__e==A&&(A=zi(P)),Lp(P,P));return A}(e,t,v,l,b),f=0;f<b;f++)(d=e.__k[f])!=null&&(h=d.__i===-1?Ko:v[d.__i]||Ko,d.__i=f,p=kc(n,d,h,i,o,s,a,l,c,u),g=d.__e,d.ref&&h.ref!=d.ref&&(h.ref&&Dc(h.ref,null,d),u.push(d.ref,d.__c||g,d)),m==null&&g!=null&&(m=g),4&d.__u||h.__k===d.__k?l=Fp(d,l,n):typeof d.type=="function"&&p!==void 0?l=p:g&&(l=g.nextSibling),d.__u&=-7);return e.__e=m,l}function Fp(n,t,e){var r,i;if(typeof n.type=="function"){for(r=n.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=n,t=Fp(r[i],t,e));return t}n.__e!=t&&(t&&n.type&&!e.contains(t)&&(t=zi(n)),e.insertBefore(n.__e,t||null),t=n.__e);do t=t&&t.nextSibling;while(t!=null&&t.nodeType==8);return t}function br(n,t){return t=t||[],n==null||typeof n=="boolean"||(ja(n)?n.some(function(e){br(e,t)}):t.push(n)),t}function O0(n,t,e,r){var i,o,s=n.key,a=n.type,l=t[e];if(l===null||l&&s==l.key&&a===l.type&&!(2&l.__u))return e;if(r>(l==null||2&l.__u?0:1))for(i=e-1,o=e+1;i>=0||o<t.length;){if(i>=0){if((l=t[i])&&!(2&l.__u)&&s==l.key&&a===l.type)return i;i--}if(o<t.length){if((l=t[o])&&!(2&l.__u)&&s==l.key&&a===l.type)return o;o++}}return-1}function Qu(n,t,e){t[0]=="-"?n.setProperty(t,e??""):n[t]=e==null?"":typeof e!="number"||I0.test(t)?e:e+"px"}function Is(n,t,e,r,i){var o;t:if(t=="style")if(typeof e=="string")n.style.cssText=e;else{if(typeof r=="string"&&(n.style.cssText=r=""),r)for(t in r)e&&t in e||Qu(n.style,t,"");if(e)for(t in e)r&&e[t]===r[t]||Qu(n.style,t,e[t])}else if(t[0]=="o"&&t[1]=="n")o=t!=(t=t.replace(Bp,"$1")),t=t.toLowerCase()in n||t=="onFocusOut"||t=="onFocusIn"?t.toLowerCase().slice(2):t.slice(2),n.l||(n.l={}),n.l[t+o]=e,e?r?e.u=r.u:(e.u=Ic,n.addEventListener(t,o?Ll:jl,o)):n.removeEventListener(t,o?Ll:jl,o);else{if(i=="http://www.w3.org/2000/svg")t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!="width"&&t!="height"&&t!="href"&&t!="list"&&t!="form"&&t!="tabIndex"&&t!="download"&&t!="rowSpan"&&t!="colSpan"&&t!="role"&&t!="popover"&&t in n)try{n[t]=e??"";break t}catch{}typeof e=="function"||(e==null||e===!1&&t[4]!="-"?n.removeAttribute(t):n.setAttribute(t,t=="popover"&&e==1?"":e))}}function Gu(n){return function(t){if(this.l){var e=this.l[t.type+n];if(t.t==null)t.t=Ic++;else if(t.t<e.u)return;return e(Rt.event?Rt.event(t):t)}}}function kc(n,t,e,r,i,o,s,a,l,c){var u,f,h,d,g,m,p,v,b,_,I,y,A,w,F,T,P,L=t.type;if(t.constructor!==void 0)return null;128&e.__u&&(l=!!(32&e.__u),o=[a=t.__e=e.__e]),(u=Rt.__b)&&u(t);t:if(typeof L=="function")try{if(v=t.props,b="prototype"in L&&L.prototype.render,_=(u=L.contextType)&&r[u.__c],I=u?_?_.props.value:u.__:r,e.__c?p=(f=t.__c=e.__c).__=f.__E:(b?t.__c=f=new L(v,I):(t.__c=f=new Zn(v,I),f.constructor=L,f.render=D0),_&&_.sub(f),f.props=v,f.state||(f.state={}),f.context=I,f.__n=r,h=f.__d=!0,f.__h=[],f._sb=[]),b&&f.__s==null&&(f.__s=f.state),b&&L.getDerivedStateFromProps!=null&&(f.__s==f.state&&(f.__s=mr({},f.__s)),mr(f.__s,L.getDerivedStateFromProps(v,f.__s))),d=f.props,g=f.state,f.__v=t,h)b&&L.getDerivedStateFromProps==null&&f.componentWillMount!=null&&f.componentWillMount(),b&&f.componentDidMount!=null&&f.__h.push(f.componentDidMount);else{if(b&&L.getDerivedStateFromProps==null&&v!==d&&f.componentWillReceiveProps!=null&&f.componentWillReceiveProps(v,I),!f.__e&&(f.shouldComponentUpdate!=null&&f.shouldComponentUpdate(v,f.__s,I)===!1||t.__v==e.__v)){for(t.__v!=e.__v&&(f.props=v,f.state=f.__s,f.__d=!1),t.__e=e.__e,t.__k=e.__k,t.__k.some(function(B){B&&(B.__=t)}),y=0;y<f._sb.length;y++)f.__h.push(f._sb[y]);f._sb=[],f.__h.length&&s.push(f);break t}f.componentWillUpdate!=null&&f.componentWillUpdate(v,f.__s,I),b&&f.componentDidUpdate!=null&&f.__h.push(function(){f.componentDidUpdate(d,g,m)})}if(f.context=I,f.props=v,f.__P=n,f.__e=!1,A=Rt.__r,w=0,b){for(f.state=f.__s,f.__d=!1,A&&A(t),u=f.render(f.props,f.state,f.context),F=0;F<f._sb.length;F++)f.__h.push(f._sb[F]);f._sb=[]}else do f.__d=!1,A&&A(t),u=f.render(f.props,f.state,f.context),f.state=f.__s;while(f.__d&&++w<25);f.state=f.__s,f.getChildContext!=null&&(r=mr(mr({},r),f.getChildContext())),b&&!h&&f.getSnapshotBeforeUpdate!=null&&(m=f.getSnapshotBeforeUpdate(d,g)),a=Cp(n,ja(T=u!=null&&u.type===_r&&u.key==null?u.props.children:u)?T:[T],t,e,r,i,o,s,a,l,c),f.base=t.__e,t.__u&=-161,f.__h.length&&s.push(f),p&&(f.__E=f.__=null)}catch(B){if(t.__v=null,l||o!=null)if(B.then){for(t.__u|=l?160:128;a&&a.nodeType==8&&a.nextSibling;)a=a.nextSibling;o[o.indexOf(a)]=null,t.__e=a}else for(P=o.length;P--;)Oc(o[P]);else t.__e=e.__e,t.__k=e.__k;Rt.__e(B,t,e)}else o==null&&t.__v==e.__v?(t.__k=e.__k,t.__e=e.__e):a=t.__e=k0(e.__e,t,e,r,i,o,s,l,c);return(u=Rt.diffed)&&u(t),128&t.__u?void 0:a}function jp(n,t,e){for(var r=0;r<e.length;r++)Dc(e[r],e[++r],e[++r]);Rt.__c&&Rt.__c(t,n),n.some(function(i){try{n=i.__h,i.__h=[],n.some(function(o){o.call(i)})}catch(o){Rt.__e(o,i.__v)}})}function k0(n,t,e,r,i,o,s,a,l){var c,u,f,h,d,g,m,p=e.props,v=t.props,b=t.type;if(b=="svg"?i="http://www.w3.org/2000/svg":b=="math"?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),o!=null){for(c=0;c<o.length;c++)if((d=o[c])&&"setAttribute"in d==!!b&&(b?d.localName==b:d.nodeType==3)){n=d,o[c]=null;break}}if(n==null){if(b==null)return document.createTextNode(v);n=document.createElementNS(i,b,v.is&&v),a&&(Rt.__m&&Rt.__m(t,o),a=!1),o=null}if(b===null)p===v||a&&n.data===v||(n.data=v);else{if(o=o&&as.call(n.childNodes),p=e.props||Ko,!a&&o!=null)for(p={},c=0;c<n.attributes.length;c++)p[(d=n.attributes[c]).name]=d.value;for(c in p)if(d=p[c],c!="children"){if(c=="dangerouslySetInnerHTML")f=d;else if(!(c in v)){if(c=="value"&&"defaultValue"in v||c=="checked"&&"defaultChecked"in v)continue;Is(n,c,null,d,i)}}for(c in v)d=v[c],c=="children"?h=d:c=="dangerouslySetInnerHTML"?u=d:c=="value"?g=d:c=="checked"?m=d:a&&typeof d!="function"||p[c]===d||Is(n,c,d,p[c],i);if(u)a||f&&(u.__html===f.__html||u.__html===n.innerHTML)||(n.innerHTML=u.__html),t.__k=[];else if(f&&(n.innerHTML=""),Cp(n,ja(h)?h:[h],t,e,r,b=="foreignObject"?"http://www.w3.org/1999/xhtml":i,o,s,o?o[0]:e.__k&&zi(e,0),a,l),o!=null)for(c=o.length;c--;)Oc(o[c]);a||(c="value",b=="progress"&&g==null?n.removeAttribute("value"):g!==void 0&&(g!==n[c]||b=="progress"&&!g||b=="option"&&g!==p[c])&&Is(n,c,g,p[c],i),c="checked",m!==void 0&&m!==n[c]&&Is(n,c,m,p[c],i))}return n}function Dc(n,t,e){try{if(typeof n=="function"){var r=typeof n.__u=="function";r&&n.__u(),r&&t==null||(n.__u=n(t))}else n.current=t}catch(i){Rt.__e(i,e)}}function Lp(n,t,e){var r,i;if(Rt.unmount&&Rt.unmount(n),(r=n.ref)&&(r.current&&r.current!==n.__e||Dc(r,null,t)),(r=n.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(o){Rt.__e(o,t)}r.base=r.__P=null}if(r=n.__k)for(i=0;i<r.length;i++)r[i]&&Lp(r[i],t,e||typeof n.type!="function");e||Oc(n.__e),n.__c=n.__=n.__e=void 0}function D0(n,t,e){return this.constructor(n,e)}function $o(n,t,e){var r,i,o,s;t==document&&(t=document.documentElement),Rt.__&&Rt.__(n,t),i=(r=typeof e=="function")?null:e&&e.__k||t.__k,o=[],s=[],kc(t,n=(!r&&e||t).__k=Xn(_r,null,[n]),i||Ko,Ko,t.namespaceURI,!r&&e?[e]:i?null:t.firstChild?as.call(t.childNodes):null,o,!r&&e?e:i?i.__e:t.firstChild,r,s),jp(o,n,s)}function Rp(n,t){$o(n,t,Rp)}function E0(n,t,e){var r,i,o,s,a=mr({},n.props);for(o in n.type&&n.type.defaultProps&&(s=n.type.defaultProps),t)o=="key"?r=t[o]:o=="ref"?i=t[o]:a[o]=t[o]===void 0&&s!==void 0?s[o]:t[o];return arguments.length>2&&(a.children=arguments.length>3?as.call(arguments,2):e),ko(n.type,a,r||n.key,i||n.ref,null)}as=Pp.slice,Rt={__e:function(n,t,e,r){for(var i,o,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&o.getDerivedStateFromError!=null&&(i.setState(o.getDerivedStateFromError(n)),s=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(n,r||{}),s=i.__d),s)return i.__E=i}catch(a){n=a}throw n}},Ap=0,Zn.prototype.setState=function(n,t){var e;e=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=mr({},this.state),typeof n=="function"&&(n=n(mr({},e),this.props)),n&&mr(e,n),n!=null&&this.__v&&(t&&this._sb.push(t),Rl(this))},Zn.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),Rl(this))},Zn.prototype.render=_r,Xr=[],Tp=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Fl=function(n,t){return n.__v.__b-t.__v.__b},Ea.__r=0,Bp=/(PointerCapture)$|Capture$/i,Ic=0,jl=Gu(!1),Ll=Gu(!0),Mp=0;var jr,ge,sl,Xu,Wi=0,Up=[],Se=Rt,Zu=Se.__b,tf=Se.__r,ef=Se.diffed,nf=Se.__c,rf=Se.unmount,of=Se.__;function Ki(n,t){Se.__h&&Se.__h(ge,n,Wi||t),Wi=0;var e=ge.__H||(ge.__H={__:[],__h:[]});return n>=e.__.length&&e.__.push({}),e.__[n]}function ls(n){return Wi=1,Ec(Yp,n)}function Ec(n,t,e){var r=Ki(jr++,2);if(r.t=n,!r.__c&&(r.__=[e?e(t):Yp(void 0,t),function(a){var l=r.__N?r.__N[0]:r.__[0],c=r.t(l,a);l!==c&&(r.__N=[c,r.__[1]],r.__c.setState({}))}],r.__c=ge,!ge.u)){var i=function(a,l,c){if(!r.__c.__H)return!0;var u=r.__c.__H.__.filter(function(h){return!!h.__c});if(u.every(function(h){return!h.__N}))return!o||o.call(this,a,l,c);var f=r.__c.props!==a;return u.forEach(function(h){if(h.__N){var d=h.__[0];h.__=h.__N,h.__N=void 0,d!==h.__[0]&&(f=!0)}}),o&&o.call(this,a,l,c)||f};ge.u=!0;var o=ge.shouldComponentUpdate,s=ge.componentWillUpdate;ge.componentWillUpdate=function(a,l,c){if(this.__e){var u=o;o=void 0,i(a,l,c),o=u}s&&s.call(this,a,l,c)},ge.shouldComponentUpdate=i}return r.__N||r.__}function cs(n,t){var e=Ki(jr++,3);!Se.__s&&Ac(e.__H,t)&&(e.__=n,e.i=t,ge.__H.__h.push(e))}function us(n,t){var e=Ki(jr++,4);!Se.__s&&Ac(e.__H,t)&&(e.__=n,e.i=t,ge.__h.push(e))}function Vp(n){return Wi=5,La(function(){return{current:n}},[])}function xp(n,t,e){Wi=6,us(function(){return typeof n=="function"?(n(t()),function(){return n(null)}):n?(n.current=t(),function(){return n.current=null}):void 0},e==null?e:e.concat(n))}function La(n,t){var e=Ki(jr++,7);return Ac(e.__H,t)&&(e.__=n(),e.__H=t,e.__h=n),e.__}function zp(n,t){return Wi=8,La(function(){return n},t)}function Wp(n){var t=ge.context[n.__c],e=Ki(jr++,9);return e.c=n,t?(e.__==null&&(e.__=!0,t.sub(ge)),t.props.value):n.__}function Hp(n,t){Se.useDebugValue&&Se.useDebugValue(t?t(n):n)}function qp(){var n=Ki(jr++,11);if(!n.__){for(var t=ge.__v;t!==null&&!t.__m&&t.__!==null;)t=t.__;var e=t.__m||(t.__m=[0,0]);n.__="P"+e[0]+"-"+e[1]++}return n.__}function A0(){for(var n;n=Up.shift();)if(n.__P&&n.__H)try{n.__H.__h.forEach(qs),n.__H.__h.forEach(Ul),n.__H.__h=[]}catch(t){n.__H.__h=[],Se.__e(t,n.__v)}}Se.__b=function(n){ge=null,Zu&&Zu(n)},Se.__=function(n,t){n&&t.__k&&t.__k.__m&&(n.__m=t.__k.__m),of&&of(n,t)},Se.__r=function(n){tf&&tf(n),jr=0;var t=(ge=n.__c).__H;t&&(sl===ge?(t.__h=[],ge.__h=[],t.__.forEach(function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0})):(t.__h.forEach(qs),t.__h.forEach(Ul),t.__h=[],jr=0)),sl=ge},Se.diffed=function(n){ef&&ef(n);var t=n.__c;t&&t.__H&&(t.__H.__h.length&&(Up.push(t)!==1&&Xu===Se.requestAnimationFrame||((Xu=Se.requestAnimationFrame)||T0)(A0)),t.__H.__.forEach(function(e){e.i&&(e.__H=e.i),e.i=void 0})),sl=ge=null},Se.__c=function(n,t){t.some(function(e){try{e.__h.forEach(qs),e.__h=e.__h.filter(function(r){return!r.__||Ul(r)})}catch(r){t.some(function(i){i.__h&&(i.__h=[])}),t=[],Se.__e(r,e.__v)}}),nf&&nf(n,t)},Se.unmount=function(n){rf&&rf(n);var t,e=n.__c;e&&e.__H&&(e.__H.__.forEach(function(r){try{qs(r)}catch(i){t=i}}),e.__H=void 0,t&&Se.__e(t,e.__v))};var sf=typeof requestAnimationFrame=="function";function T0(n){var t,e=function(){clearTimeout(r),sf&&cancelAnimationFrame(t),setTimeout(n)},r=setTimeout(e,100);sf&&(t=requestAnimationFrame(e))}function qs(n){var t=ge,e=n.__c;typeof e=="function"&&(n.__c=void 0,e()),ge=t}function Ul(n){var t=ge;n.__c=n.__(),ge=t}function Ac(n,t){return!n||n.length!==t.length||t.some(function(e,r){return e!==n[r]})}function Yp(n,t){return typeof t=="function"?t(n):t}function Kp(n,t){for(var e in t)n[e]=t[e];return n}function Vl(n,t){for(var e in n)if(e!=="__source"&&!(e in t))return!0;for(var r in t)if(r!=="__source"&&n[r]!==t[r])return!0;return!1}function $p(n,t){var e=t(),r=ls({t:{__:e,u:t}}),i=r[0].t,o=r[1];return us(function(){i.__=e,i.u=t,al(i)&&o({t:i})},[n,e,t]),cs(function(){return al(i)&&o({t:i}),n(function(){al(i)&&o({t:i})})},[n]),e}function al(n){var t,e,r=n.u,i=n.__;try{var o=r();return!((t=i)===(e=o)&&(t!==0||1/t==1/e)||t!=t&&e!=e)}catch{return!0}}function Jp(n){n()}function Qp(n){return n}function Gp(){return[!1,Jp]}var Xp=us;function xl(n,t){this.props=n,this.context=t}(xl.prototype=new Zn).isPureReactComponent=!0,xl.prototype.shouldComponentUpdate=function(n,t){return Vl(this.props,n)||Vl(this.state,t)};var af=Rt.__b;Rt.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),af&&af(n)};var B0=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911,lf=function(n,t){return n==null?null:br(br(n).map(t))},M0={map:lf,forEach:lf,count:function(n){return n?br(n).length:0},only:function(n){var t=br(n);if(t.length!==1)throw"Children.only";return t[0]},toArray:br},P0=Rt.__e;Rt.__e=function(n,t,e,r){if(n.then){for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return t.__e==null&&(t.__e=e.__e,t.__k=e.__k),i.__c(n,t)}P0(n,t,e,r)};var cf=Rt.unmount;function Zp(n,t,e){return n&&(n.__c&&n.__c.__H&&(n.__c.__H.__.forEach(function(r){typeof r.__c=="function"&&r.__c()}),n.__c.__H=null),(n=Kp({},n)).__c!=null&&(n.__c.__P===e&&(n.__c.__P=t),n.__c=null),n.__k=n.__k&&n.__k.map(function(r){return Zp(r,t,e)})),n}function tg(n,t,e){return n&&e&&(n.__v=null,n.__k=n.__k&&n.__k.map(function(r){return tg(r,t,e)}),n.__c&&n.__c.__P===t&&(n.__e&&e.appendChild(n.__e),n.__c.__e=!0,n.__c.__P=e)),n}function Ys(){this.__u=0,this.o=null,this.__b=null}function eg(n){var t=n.__.__c;return t&&t.__a&&t.__a(n)}function wo(){this.i=null,this.l=null}Rt.unmount=function(n){var t=n.__c;t&&t.__R&&t.__R(),t&&32&n.__u&&(n.type=null),cf&&cf(n)},(Ys.prototype=new Zn).__c=function(n,t){var e=t.__c,r=this;r.o==null&&(r.o=[]),r.o.push(e);var i=eg(r.__v),o=!1,s=function(){o||(o=!0,e.__R=null,i?i(a):a())};e.__R=s;var a=function(){if(!--r.__u){if(r.state.__a){var l=r.state.__a;r.__v.__k[0]=tg(l,l.__c.__P,l.__c.__O)}var c;for(r.setState({__a:r.__b=null});c=r.o.pop();)c.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),n.then(s,s)},Ys.prototype.componentWillUnmount=function(){this.o=[]},Ys.prototype.render=function(n,t){if(this.__b){if(this.__v.__k){var e=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Zp(this.__b,e,r.__O=r.__P)}this.__b=null}var i=t.__a&&Xn(_r,null,n.fallback);return i&&(i.__u&=-33),[Xn(_r,null,t.__a?null:n.children),i]};var uf=function(n,t,e){if(++e[1]===e[0]&&n.l.delete(t),n.props.revealOrder&&(n.props.revealOrder[0]!=="t"||!n.l.size))for(e=n.i;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.i=e=e[2]}};function N0(n){return this.getChildContext=function(){return n.context},n.children}function C0(n){var t=this,e=n.h;t.componentWillUnmount=function(){$o(null,t.v),t.v=null,t.h=null},t.h&&t.h!==e&&t.componentWillUnmount(),t.v||(t.h=e,t.v={nodeType:1,parentNode:e,childNodes:[],contains:function(){return!0},appendChild:function(r){this.childNodes.push(r),t.h.appendChild(r)},insertBefore:function(r,i){this.childNodes.push(r),t.h.insertBefore(r,i)},removeChild:function(r){this.childNodes.splice(this.childNodes.indexOf(r)>>>1,1),t.h.removeChild(r)}}),$o(Xn(N0,{context:t.context},n.__v),t.v)}function ng(n,t){var e=Xn(C0,{__v:n,h:t});return e.containerInfo=t,e}(wo.prototype=new Zn).__a=function(n){var t=this,e=eg(t.__v),r=t.l.get(n);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),uf(t,n,r)):i()};e?e(o):o()}},wo.prototype.render=function(n){this.i=null,this.l=new Map;var t=br(n.children);n.revealOrder&&n.revealOrder[0]==="b"&&t.reverse();for(var e=t.length;e--;)this.l.set(t[e],this.i=[1,0,this.i]);return n.children},wo.prototype.componentDidUpdate=wo.prototype.componentDidMount=function(){var n=this;this.l.forEach(function(t,e){uf(n,e,t)})};var rg=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,F0=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,j0=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,L0=/[A-Z0-9]/g,R0=typeof document<"u",U0=function(n){return(typeof Symbol<"u"&&mt(Symbol())=="symbol"?/fil|che|rad/:/fil|che|ra/).test(n)};function ig(n,t,e){return t.__k==null&&(t.textContent=""),$o(n,t),typeof e=="function"&&e(),n?n.__c:null}Zn.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(n){Object.defineProperty(Zn.prototype,n,{configurable:!0,get:function(){return this["UNSAFE_"+n]},set:function(t){Object.defineProperty(this,n,{configurable:!0,writable:!0,value:t})}})});var ff=Rt.event;function V0(){}function x0(){return this.cancelBubble}function z0(){return this.defaultPrevented}Rt.event=function(n){return ff&&(n=ff(n)),n.persist=V0,n.isPropagationStopped=x0,n.isDefaultPrevented=z0,n.nativeEvent=n};var Tc,W0={enumerable:!1,configurable:!0,get:function(){return this.class}},df=Rt.vnode;Rt.vnode=function(n){typeof n.type=="string"&&function(t){var e=t.props,r=t.type,i={},o=r.indexOf("-")===-1;for(var s in e){var a=e[s];if(!(s==="value"&&"defaultValue"in e&&a==null||R0&&s==="children"&&r==="noscript"||s==="class"||s==="className")){var l=s.toLowerCase();s==="defaultValue"&&"value"in e&&e.value==null?s="value":s==="download"&&a===!0?a="":l==="translate"&&a==="no"?a=!1:l[0]==="o"&&l[1]==="n"?l==="ondoubleclick"?s="ondblclick":l!=="onchange"||r!=="input"&&r!=="textarea"||U0(e.type)?l==="onfocus"?s="onfocusin":l==="onblur"?s="onfocusout":j0.test(s)&&(s=l):l=s="oninput":o&&F0.test(s)?s=s.replace(L0,"-$&").toLowerCase():a===null&&(a=void 0),l==="oninput"&&i[s=l]&&(s="oninputCapture"),i[s]=a}}r=="select"&&i.multiple&&Array.isArray(i.value)&&(i.value=br(e.children).forEach(function(c){c.props.selected=i.value.indexOf(c.props.value)!=-1})),r=="select"&&i.defaultValue!=null&&(i.value=br(e.children).forEach(function(c){c.props.selected=i.multiple?i.defaultValue.indexOf(c.props.value)!=-1:i.defaultValue==c.props.value})),e.class&&!e.className?(i.class=e.class,Object.defineProperty(i,"className",W0)):(e.className&&!e.class||e.class&&e.className)&&(i.class=i.className=e.className),t.props=i}(n),n.$$typeof=rg,df&&df(n)};var hf=Rt.__r;Rt.__r=function(n){hf&&hf(n),Tc=n.__c};var mf=Rt.diffed;Rt.diffed=function(n){mf&&mf(n);var t=n.props,e=n.__e;e!=null&&n.type==="textarea"&&"value"in t&&t.value!==e.value&&(e.value=t.value==null?"":t.value),Tc=null};var H0={ReactCurrentDispatcher:{current:{readContext:function(n){return Tc.__n[n.__c].props.value},useCallback:zp,useContext:Wp,useDebugValue:Hp,useDeferredValue:Qp,useEffect:cs,useId:qp,useImperativeHandle:xp,useInsertionEffect:Xp,useLayoutEffect:us,useMemo:La,useReducer:Ec,useRef:Vp,useState:ls,useSyncExternalStore:$p,useTransition:Gp}}};function Os(n){return!!n&&n.$$typeof===rg}var k={useState:ls,useId:qp,useReducer:Ec,useEffect:cs,useLayoutEffect:us,useInsertionEffect:Xp,useTransition:Gp,useDeferredValue:Qp,useSyncExternalStore:$p,startTransition:Jp,useRef:Vp,useImperativeHandle:xp,useMemo:La,useCallback:zp,useContext:Wp,useDebugValue:Hp,version:"18.3.1",Children:M0,render:ig,hydrate:function(n,t,e){return Rp(n,t),typeof e=="function"&&e(),n?n.__c:null},unmountComponentAtNode:function(n){return!!n.__k&&($o(null,n),!0)},createPortal:ng,createElement:Xn,createContext:function(n,t){var e={__c:t="__cC"+Mp++,__:n,Consumer:function(r,i){return r.children(i)},Provider:function(r){var i,o;return this.getChildContext||(i=new Set,(o={})[t]=this,this.getChildContext=function(){return o},this.componentWillUnmount=function(){i=null},this.shouldComponentUpdate=function(s){this.props.value!==s.value&&i.forEach(function(a){a.__e=!0,Rl(a)})},this.sub=function(s){i.add(s);var a=s.componentWillUnmount;s.componentWillUnmount=function(){i&&i.delete(s),a&&a.call(s)}}),r.children}};return e.Provider.__=e.Consumer.contextType=e},createFactory:function(n){return Xn.bind(null,n)},cloneElement:function(n){return Os(n)?E0.apply(null,arguments):n},createRef:function(){return{current:null}},Fragment:_r,isValidElement:Os,isElement:Os,isFragment:function(n){return Os(n)&&n.type===_r},isMemo:function(n){return!!n&&!!n.displayName&&(typeof n.displayName=="string"||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")},findDOMNode:function(n){return n&&(n.base||n.nodeType===1&&n)||null},Component:Zn,PureComponent:xl,memo:function(n,t){function e(i){var o=this.props.ref,s=o==i.ref;return!s&&o&&(o.call?o(null):o.current=null),t?!t(this.props,i)||!s:Vl(this.props,i)}function r(i){return this.shouldComponentUpdate=e,Xn(n,i)}return r.displayName="Memo("+(n.displayName||n.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r},forwardRef:function(n){function t(e){var r=Kp({},e);return delete r.ref,n(r,e.ref||null)}return t.$$typeof=B0,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(n.displayName||n.name)+")",t},flushSync:function(n,t){return n(t)},unstable_batchedUpdates:function(n,t){return n(t)},StrictMode:_r,Suspense:Ys,SuspenseList:wo,lazy:function(n){var t,e,r;function i(o){if(t||(t=n()).then(function(s){e=s.default||s},function(s){r=s}),r)throw r;if(!e)throw t;return Xn(e,o)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:H0};function zl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=Array(t);e<t;e++)r[e]=n[e];return r}function pf(n,t,e,r,i,o,s){try{var a=n[o](s),l=a.value}catch(c){return void e(c)}a.done?t(l):Promise.resolve(l).then(r,i)}function ll(n){return function(){var t=this,e=arguments;return new Promise(function(r,i){var o=n.apply(t,e);function s(l){pf(o,r,i,s,a,"next",l)}function a(l){pf(o,r,i,s,a,"throw",l)}s(void 0)})}}function $i(n,t,e){return t=Jo(t),function(r,i){if(i&&(mt(i)=="object"||typeof i=="function"))return i;if(i!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(o){if(o===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o}(r)}(n,Bc()?Reflect.construct(t,e||[],Jo(n).constructor):t.apply(n,e))}function Ji(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function")}function Qi(n,t,e){return Object.defineProperty(n,"prototype",{writable:!1}),n}function Gi(n,t,e){return(t=function(r){var i=function(o){if(mt(o)!="object"||!o)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(mt(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return mt(i)=="symbol"?i:i+""}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Ke(){return Ke=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var r in e)({}).hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},Ke.apply(null,arguments)}function Jo(n){return Jo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Jo(n)}function Xi(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&Qo(n,t)}function Bc(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Bc=function(){return!!n})()}function gf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Ut(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?gf(Object(e),!0).forEach(function(r){Gi(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):gf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function tr(n,t){if(n==null)return{};var e,r,i=function(s,a){if(s==null)return{};var l={};for(var c in s)if({}.hasOwnProperty.call(s,c)){if(a.includes(c))continue;l[c]=s[c]}return l}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)e=o[r],t.includes(e)||{}.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function Qr(){Qr=function(){return t};var n,t={},e=Object.prototype,r=e.hasOwnProperty,i=Object.defineProperty||function(U,H,C){U[H]=C.value},o=typeof Symbol=="function"?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(U,H,C){return Object.defineProperty(U,H,{value:C,enumerable:!0,configurable:!0,writable:!0}),U[H]}try{c({},"")}catch{c=function(H,C,G){return H[C]=G}}function u(U,H,C,G){var J=H&&H.prototype instanceof v?H:v,X=Object.create(J.prototype),Dt=new j(G||[]);return i(X,"_invoke",{value:P(U,C,Dt)}),X}function f(U,H,C){try{return{type:"normal",arg:U.call(H,C)}}catch(G){return{type:"throw",arg:G}}}t.wrap=u;var h="suspendedStart",d="suspendedYield",g="executing",m="completed",p={};function v(){}function b(){}function _(){}var I={};c(I,s,function(){return this});var y=Object.getPrototypeOf,A=y&&y(y(V([])));A&&A!==e&&r.call(A,s)&&(I=A);var w=_.prototype=v.prototype=Object.create(I);function F(U){["next","throw","return"].forEach(function(H){c(U,H,function(C){return this._invoke(H,C)})})}function T(U,H){function C(J,X,Dt,Bt){var Pt=f(U[J],U,X);if(Pt.type!=="throw"){var K=Pt.arg,ye=K.value;return ye&&mt(ye)=="object"&&r.call(ye,"__await")?H.resolve(ye.__await).then(function(Yt){C("next",Yt,Dt,Bt)},function(Yt){C("throw",Yt,Dt,Bt)}):H.resolve(ye).then(function(Yt){K.value=Yt,Dt(K)},function(Yt){return C("throw",Yt,Dt,Bt)})}Bt(Pt.arg)}var G;i(this,"_invoke",{value:function(J,X){function Dt(){return new H(function(Bt,Pt){C(J,X,Bt,Pt)})}return G=G?G.then(Dt,Dt):Dt()}})}function P(U,H,C){var G=h;return function(J,X){if(G===g)throw Error("Generator is already running");if(G===m){if(J==="throw")throw X;return{value:n,done:!0}}for(C.method=J,C.arg=X;;){var Dt=C.delegate;if(Dt){var Bt=L(Dt,C);if(Bt){if(Bt===p)continue;return Bt}}if(C.method==="next")C.sent=C._sent=C.arg;else if(C.method==="throw"){if(G===h)throw G=m,C.arg;C.dispatchException(C.arg)}else C.method==="return"&&C.abrupt("return",C.arg);G=g;var Pt=f(U,H,C);if(Pt.type==="normal"){if(G=C.done?m:d,Pt.arg===p)continue;return{value:Pt.arg,done:C.done}}Pt.type==="throw"&&(G=m,C.method="throw",C.arg=Pt.arg)}}}function L(U,H){var C=H.method,G=U.iterator[C];if(G===n)return H.delegate=null,C==="throw"&&U.iterator.return&&(H.method="return",H.arg=n,L(U,H),H.method==="throw")||C!=="return"&&(H.method="throw",H.arg=new TypeError("The iterator does not provide a '"+C+"' method")),p;var J=f(G,U.iterator,H.arg);if(J.type==="throw")return H.method="throw",H.arg=J.arg,H.delegate=null,p;var X=J.arg;return X?X.done?(H[U.resultName]=X.value,H.next=U.nextLoc,H.method!=="return"&&(H.method="next",H.arg=n),H.delegate=null,p):X:(H.method="throw",H.arg=new TypeError("iterator result is not an object"),H.delegate=null,p)}function B(U){var H={tryLoc:U[0]};1 in U&&(H.catchLoc=U[1]),2 in U&&(H.finallyLoc=U[2],H.afterLoc=U[3]),this.tryEntries.push(H)}function M(U){var H=U.completion||{};H.type="normal",delete H.arg,U.completion=H}function j(U){this.tryEntries=[{tryLoc:"root"}],U.forEach(B,this),this.reset(!0)}function V(U){if(U||U===""){var H=U[s];if(H)return H.call(U);if(typeof U.next=="function")return U;if(!isNaN(U.length)){var C=-1,G=function J(){for(;++C<U.length;)if(r.call(U,C))return J.value=U[C],J.done=!1,J;return J.value=n,J.done=!0,J};return G.next=G}}throw new TypeError(mt(U)+" is not iterable")}return b.prototype=_,i(w,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:b,configurable:!0}),b.displayName=c(_,l,"GeneratorFunction"),t.isGeneratorFunction=function(U){var H=typeof U=="function"&&U.constructor;return!!H&&(H===b||(H.displayName||H.name)==="GeneratorFunction")},t.mark=function(U){return Object.setPrototypeOf?Object.setPrototypeOf(U,_):(U.__proto__=_,c(U,l,"GeneratorFunction")),U.prototype=Object.create(w),U},t.awrap=function(U){return{__await:U}},F(T.prototype),c(T.prototype,a,function(){return this}),t.AsyncIterator=T,t.async=function(U,H,C,G,J){J===void 0&&(J=Promise);var X=new T(u(U,H,C,G),J);return t.isGeneratorFunction(H)?X:X.next().then(function(Dt){return Dt.done?Dt.value:X.next()})},F(w),c(w,l,"Generator"),c(w,s,function(){return this}),c(w,"toString",function(){return"[object Generator]"}),t.keys=function(U){var H=Object(U),C=[];for(var G in H)C.push(G);return C.reverse(),function J(){for(;C.length;){var X=C.pop();if(X in H)return J.value=X,J.done=!1,J}return J.done=!0,J}},t.values=V,j.prototype={constructor:j,reset:function(U){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(M),!U)for(var H in this)H.charAt(0)==="t"&&r.call(this,H)&&!isNaN(+H.slice(1))&&(this[H]=n)},stop:function(){this.done=!0;var U=this.tryEntries[0].completion;if(U.type==="throw")throw U.arg;return this.rval},dispatchException:function(U){if(this.done)throw U;var H=this;function C(Pt,K){return X.type="throw",X.arg=U,H.next=Pt,K&&(H.method="next",H.arg=n),!!K}for(var G=this.tryEntries.length-1;G>=0;--G){var J=this.tryEntries[G],X=J.completion;if(J.tryLoc==="root")return C("end");if(J.tryLoc<=this.prev){var Dt=r.call(J,"catchLoc"),Bt=r.call(J,"finallyLoc");if(Dt&&Bt){if(this.prev<J.catchLoc)return C(J.catchLoc,!0);if(this.prev<J.finallyLoc)return C(J.finallyLoc)}else if(Dt){if(this.prev<J.catchLoc)return C(J.catchLoc,!0)}else{if(!Bt)throw Error("try statement without catch or finally");if(this.prev<J.finallyLoc)return C(J.finallyLoc)}}}},abrupt:function(U,H){for(var C=this.tryEntries.length-1;C>=0;--C){var G=this.tryEntries[C];if(G.tryLoc<=this.prev&&r.call(G,"finallyLoc")&&this.prev<G.finallyLoc){var J=G;break}}J&&(U==="break"||U==="continue")&&J.tryLoc<=H&&H<=J.finallyLoc&&(J=null);var X=J?J.completion:{};return X.type=U,X.arg=H,J?(this.method="next",this.next=J.finallyLoc,p):this.complete(X)},complete:function(U,H){if(U.type==="throw")throw U.arg;return U.type==="break"||U.type==="continue"?this.next=U.arg:U.type==="return"?(this.rval=this.arg=U.arg,this.method="return",this.next="end"):U.type==="normal"&&H&&(this.next=H),p},finish:function(U){for(var H=this.tryEntries.length-1;H>=0;--H){var C=this.tryEntries[H];if(C.finallyLoc===U)return this.complete(C.completion,C.afterLoc),M(C),p}},catch:function(U){for(var H=this.tryEntries.length-1;H>=0;--H){var C=this.tryEntries[H];if(C.tryLoc===U){var G=C.completion;if(G.type==="throw"){var J=G.arg;M(C)}return J}}throw Error("illegal catch attempt")},delegateYield:function(U,H,C){return this.delegate={iterator:V(U),resultName:H,nextLoc:C},this.method==="next"&&(this.arg=n),p}},t}function Qo(n,t){return Qo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},Qo(n,t)}function an(n,t){return function(e){if(Array.isArray(e))return e}(n)||function(e,r){var i=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(i!=null){var o,s,a,l,c=[],u=!0,f=!1;try{if(a=(i=i.call(e)).next,r===0){if(Object(i)!==i)return;u=!1}else for(;!(u=(o=a.call(i)).done)&&(c.push(o.value),c.length!==r);u=!0);}catch(h){f=!0,s=h}finally{try{if(!u&&i.return!=null&&(l=i.return(),Object(l)!==l))return}finally{if(f)throw s}}return c}}(n,t)||og(n,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Do(n){return function(t){if(Array.isArray(t))return zl(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||og(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function og(n,t){if(n){if(typeof n=="string")return zl(n,t);var e={}.toString.call(n).slice(8,-1);return e==="Object"&&n.constructor&&(e=n.constructor.name),e==="Map"||e==="Set"?Array.from(n):e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?zl(n,t):void 0}}function Wl(n){var t=typeof Map=="function"?new Map:void 0;return Wl=function(e){if(e===null||!function(i){try{return Function.toString.call(i).indexOf("[native code]")!==-1}catch{return typeof i=="function"}}(e))return e;if(typeof e!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(i,o,s){if(Bc())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,o);var l=new(i.bind.apply(i,a));return s&&Qo(l,s.prototype),l}(e,arguments,Jo(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Qo(r,e)},Wl(n)}function q0(){return k.createElement("svg",{width:"15",height:"15",className:"DocSearch-Control-Key-Icon"},k.createElement("path",{d:"M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953",strokeWidth:"1.2",stroke:"currentColor",fill:"none",strokeLinecap:"square"}))}function sg(){return k.createElement("svg",{width:"20",height:"20",className:"DocSearch-Search-Icon",viewBox:"0 0 20 20","aria-hidden":"true"},k.createElement("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}var Y0=["translations"],cl="Ctrl",K0=k.forwardRef(function(n,t){var e=n.translations,r=e===void 0?{}:e,i=tr(n,Y0),o=r.buttonText,s=o===void 0?"Search":o,a=r.buttonAriaLabel,l=a===void 0?"Search":a,c=an(ls(null),2),u=c[0],f=c[1];cs(function(){typeof navigator<"u"&&(/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?f("⌘"):f(cl))},[]);var h=an(u===cl?[cl,"Ctrl",k.createElement(q0,null)]:["Meta","Command",u],3),d=h[0],g=h[1],m=h[2];return k.createElement("button",Ke({type:"button",className:"DocSearch DocSearch-Button","aria-label":"".concat(l," (").concat(g,"+K)")},i,{ref:t}),k.createElement("span",{className:"DocSearch-Button-Container"},k.createElement(sg,null),k.createElement("span",{className:"DocSearch-Button-Placeholder"},s)),k.createElement("span",{className:"DocSearch-Button-Keys"},u!==null&&k.createElement(k.Fragment,null,k.createElement(_f,{reactsToKey:d},m),k.createElement(_f,{reactsToKey:"k"},"K"))))});function _f(n){var t=n.reactsToKey,e=n.children,r=an(ls(!1),2),i=r[0],o=r[1];return cs(function(){if(t)return window.addEventListener("keydown",s),window.addEventListener("keyup",a),function(){window.removeEventListener("keydown",s),window.removeEventListener("keyup",a)};function s(l){l.key===t&&o(!0)}function a(l){l.key!==t&&l.key!=="Meta"||o(!1)}},[t]),k.createElement("kbd",{className:i?"DocSearch-Button-Key DocSearch-Button-Key--pressed":"DocSearch-Button-Key"},e)}function ag(n,t){var e=void 0;return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];e&&clearTimeout(e),e=setTimeout(function(){return n.apply(void 0,i)},t)}}function Go(n){return n.reduce(function(t,e){return t.concat(e)},[])}var $0=0;function Hl(n){return n.collections.length===0?0:n.collections.reduce(function(t,e){return t+e.items.length},0)}function bf(n){return n!==Object(n)}function lg(n,t){if(n===t)return!0;if(bf(n)||bf(t)||typeof n=="function"||typeof t=="function")return n===t;if(Object.keys(n).length!==Object.keys(t).length)return!1;for(var e=0,r=Object.keys(n);e<r.length;e++){var i=r[e];if(!(i in t)||!lg(n[i],t[i]))return!1}return!0}var Ks=function(){},J0=[{segment:"autocomplete-core",version:"1.17.9"}];function yf(n){var t=n.item,e=n.items,r=e===void 0?[]:e;return{index:t.__autocomplete_indexName,items:[t],positions:[1+r.findIndex(function(i){return i.objectID===t.objectID})],queryID:t.__autocomplete_queryID,algoliaSource:["autocomplete"]}}function vf(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}var Q0=["items"],G0=["items"];function Eo(n){return Eo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Eo(n)}function ks(n){return function(t){if(Array.isArray(t))return ul(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||function(t,e){if(t){if(typeof t=="string")return ul(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ul(t,e):void 0}}(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function ul(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function cg(n,t){if(n==null)return{};var e,r,i=function(s,a){if(s==null)return{};var l,c,u={},f=Object.keys(s);for(c=0;c<f.length;c++)l=f[c],a.indexOf(l)>=0||(u[l]=s[l]);return u}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)e=o[r],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function wf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Ei(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?wf(Object(e),!0).forEach(function(r){X0(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):wf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function X0(n,t,e){return(t=function(r){var i=function(o){if(Eo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Eo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Eo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Ds(n){return n.map(function(t){var e=t.items,r=cg(t,Q0);return Ei(Ei({},r),{},{objectIDs:(e==null?void 0:e.map(function(i){return i.objectID}))||r.objectIDs})})}function Z0(n){var t=n.items.reduce(function(e,r){var i;return e[r.__autocomplete_indexName]=((i=e[r.__autocomplete_indexName])!==null&&i!==void 0?i:[]).concat(r),e},{});return Object.keys(t).map(function(e){return{index:e,items:t[e],algoliaSource:["autocomplete"]}})}function ao(n){return n.objectID&&n.__autocomplete_indexName&&n.__autocomplete_queryID}function Ao(n){return Ao=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Ao(n)}function Ai(n){return function(t){if(Array.isArray(t))return fl(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||function(t,e){if(t){if(typeof t=="string")return fl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fl(t,e):void 0}}(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function fl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function Sf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Nn(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Sf(Object(e),!0).forEach(function(r){t1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Sf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function t1(n,t,e){return(t=function(r){var i=function(o){if(Ao(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Ao(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Ao(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}var ug="2.15.0",e1="https://cdn.jsdelivr.net/npm/search-insights@".concat(ug,"/dist/search-insights.min.js"),n1=ag(function(n){var t=n.onItemsChange,e=n.items,r=n.insights,i=n.state;t({insights:r,insightsEvents:Z0({items:e}).map(function(o){return Nn({eventName:"Items Viewed"},o)}),state:i})},400);function If(n){var t=function(h){return Nn({onItemsChange:function(d){var g=d.insights,m=d.insightsEvents,p=d.state;g.viewedObjectIDs.apply(g,Ai(m.map(function(v){return Nn(Nn({},v),{},{algoliaSource:Of(v.algoliaSource,p.context)})})))},onSelect:function(d){var g=d.insights,m=d.insightsEvents,p=d.state;g.clickedObjectIDsAfterSearch.apply(g,Ai(m.map(function(v){return Nn(Nn({},v),{},{algoliaSource:Of(v.algoliaSource,p.context)})})))},onActive:Ks,__autocomplete_clickAnalytics:!0},h)}(n),e=t.insightsClient,r=t.insightsInitParams,i=t.onItemsChange,o=t.onSelect,s=t.onActive,a=t.__autocomplete_clickAnalytics,l=e;if(e||typeof window<"u"&&function(){var h=window,d=h.AlgoliaAnalyticsObject||"aa";typeof d=="string"&&(l=h[d]),l||(h.AlgoliaAnalyticsObject=d,h[d]||(h[d]=function(){h[d].queue||(h[d].queue=[]);for(var g=arguments.length,m=new Array(g),p=0;p<g;p++)m[p]=arguments[p];h[d].queue.push(m)}),h[d].version=ug,l=h[d],function(g){var m="[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";try{var p=g.document.createElement("script");p.async=!0,p.src=e1,p.onerror=function(){console.error(m)},document.body.appendChild(p)}catch{console.error(m)}}(h))}(),!l)return{};r&&l("init",Nn({partial:!0},r));var c=function(h){var d,g,m,p=(d=function(b){return function(_){if(Array.isArray(_))return _}(b)||function(_){var I=_==null?null:typeof Symbol<"u"&&_[Symbol.iterator]||_["@@iterator"];if(I!=null){var y,A,w,F,T=[],P=!0,L=!1;try{for(w=(I=I.call(_)).next;!(P=(y=w.call(I)).done)&&(T.push(y.value),T.length!==2);P=!0);}catch(B){L=!0,A=B}finally{try{if(!P&&I.return!=null&&(F=I.return(),Object(F)!==F))return}finally{if(L)throw A}}return T}}(b)||function(_){if(_){if(typeof _=="string")return vf(_,2);var I=Object.prototype.toString.call(_).slice(8,-1);return I==="Object"&&_.constructor&&(I=_.constructor.name),I==="Map"||I==="Set"?Array.from(_):I==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(I)?vf(_,2):void 0}}(b)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}((h.version||"").split(".").map(Number)),g=d[0],m=d[1],g>=3||g===2&&m>=4||g===1&&m>=10);function v(b,_,I){if(p&&I!==void 0){var y=I[0].__autocomplete_algoliaCredentials,A={"X-Algolia-Application-Id":y.appId,"X-Algolia-API-Key":y.apiKey};h.apply(void 0,[b].concat(ks(_),[{headers:A}]))}else h.apply(void 0,[b].concat(ks(_)))}return{init:function(b,_){h("init",{appId:b,apiKey:_})},setAuthenticatedUserToken:function(b){h("setAuthenticatedUserToken",b)},setUserToken:function(b){h("setUserToken",b)},clickedObjectIDsAfterSearch:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&v("clickedObjectIDsAfterSearch",Ds(_),_[0].items)},clickedObjectIDs:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&v("clickedObjectIDs",Ds(_),_[0].items)},clickedFilters:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&h.apply(void 0,["clickedFilters"].concat(_))},convertedObjectIDsAfterSearch:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&v("convertedObjectIDsAfterSearch",Ds(_),_[0].items)},convertedObjectIDs:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&v("convertedObjectIDs",Ds(_),_[0].items)},convertedFilters:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&h.apply(void 0,["convertedFilters"].concat(_))},viewedObjectIDs:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&_.reduce(function(y,A){var w=A.items,F=cg(A,G0);return[].concat(ks(y),ks(function(T){for(var P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:20,L=[],B=0;B<T.objectIDs.length;B+=P)L.push(Ei(Ei({},T),{},{objectIDs:T.objectIDs.slice(B,B+P)}));return L}(Ei(Ei({},F),{},{objectIDs:(w==null?void 0:w.map(function(T){return T.objectID}))||F.objectIDs})).map(function(T){return{items:w,payload:T}})))},[]).forEach(function(y){var A=y.items;return v("viewedObjectIDs",[y.payload],A)})},viewedFilters:function(){for(var b=arguments.length,_=new Array(b),I=0;I<b;I++)_[I]=arguments[I];_.length>0&&h.apply(void 0,["viewedFilters"].concat(_))}}}(l),u={current:[]},f=ag(function(h){var d=h.state;if(d.isOpen){var g=d.collections.reduce(function(m,p){return[].concat(Ai(m),Ai(p.items))},[]).filter(ao);lg(u.current.map(function(m){return m.objectID}),g.map(function(m){return m.objectID}))||(u.current=g,g.length>0&&n1({onItemsChange:i,items:g,insights:c,state:d}))}},0);return{name:"aa.algoliaInsightsPlugin",subscribe:function(h){var d=h.setContext,g=h.onSelect,m=h.onActive;function p(v){d({algoliaInsightsPlugin:{__algoliaSearchParameters:Nn(Nn({},a?{clickAnalytics:!0}:{}),v?{userToken:r1(v)}:{}),insights:c}})}l("addAlgoliaAgent","insights-plugin"),p(),l("onUserTokenChange",function(v){p(v)}),l("getUserToken",null,function(v,b){p(b)}),g(function(v){var b=v.item,_=v.state,I=v.event,y=v.source;ao(b)&&o({state:_,event:I,insights:c,item:b,insightsEvents:[Nn({eventName:"Item Selected"},yf({item:b,items:y.getItems().filter(ao)}))]})}),m(function(v){var b=v.item,_=v.source,I=v.state,y=v.event;ao(b)&&s({state:I,event:y,insights:c,item:b,insightsEvents:[Nn({eventName:"Item Active"},yf({item:b,items:_.getItems().filter(ao)}))]})})},onStateChange:function(h){var d=h.state;f({state:d})},__autocomplete_pluginOptions:n}}function Of(){var n,t=arguments.length>1?arguments[1]:void 0;return[].concat(Ai(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]),["autocomplete-internal"],Ai((n=t.algoliaInsightsPlugin)!==null&&n!==void 0&&n.__automaticInsights?["autocomplete-automatic"]:[]))}function r1(n){return typeof n=="number"?n.toString():n}function $s(n,t){var e=t;return{then:function(r,i){return $s(n.then(Es(r,e,n),Es(i,e,n)),e)},catch:function(r){return $s(n.catch(Es(r,e,n)),e)},finally:function(r){return r&&e.onCancelList.push(r),$s(n.finally(Es(r&&function(){return e.onCancelList=[],r()},e,n)),e)},cancel:function(){e.isCanceled=!0;var r=e.onCancelList;e.onCancelList=[],r.forEach(function(i){i()})},isCanceled:function(){return e.isCanceled===!0}}}function kf(n){return $s(n,{isCanceled:!1,onCancelList:[]})}function Es(n,t,e){return n?function(r){return t.isCanceled?r:n(r)}:e}function Df(n,t,e,r){if(!e)return null;if(n<0&&(t===null||r!==null&&t===0))return e+n;var i=(t===null?-1:t)+n;return i<=-1||i>=e?r===null?null:0:i}function Ef(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Af(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Ef(Object(e),!0).forEach(function(r){i1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Ef(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function i1(n,t,e){return(t=function(r){var i=function(o){if(To(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(To(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return To(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function To(n){return To=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},To(n)}function Zr(n){var t=function(i){var o=i.collections.map(function(s){return s.items.length}).reduce(function(s,a,l){var c=(s[l-1]||0)+a;return s.push(c),s},[]).reduce(function(s,a){return a<=i.activeItemId?s+1:s},0);return i.collections[o]}(n);if(!t)return null;var e=t.items[function(i){for(var o=i.state,s=i.collection,a=!1,l=0,c=0;a===!1;){var u=o.collections[l];if(u===s){a=!0;break}c+=u.items.length,l++}return o.activeItemId-c}({state:n,collection:t})],r=t.source;return{item:e,itemInputValue:r.getItemInputValue({item:e,state:n}),itemUrl:r.getItemUrl({item:e,state:n}),source:r}}function _n(n,t,e){return[n,e==null?void 0:e.sourceId,t].filter(Boolean).join("-").replace(/\s/g,"")}var o1=/((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;function Tf(n){return n.nativeEvent||n}function Bo(n){return Bo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Bo(n)}function Bf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function s1(n,t,e){return(t=function(r){var i=function(o){if(Bo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Bo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Bo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Mo(n){return Mo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Mo(n)}function Mf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function As(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Mf(Object(e),!0).forEach(function(r){a1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Mf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function a1(n,t,e){return(t=function(r){var i=function(o){if(Mo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Mo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Mo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Po(n){return Po=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Po(n)}function dl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function Pf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function ci(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Pf(Object(e),!0).forEach(function(r){l1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Pf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function l1(n,t,e){return(t=function(r){var i=function(o){if(Po(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Po(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Po(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function No(n){return No=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},No(n)}function Nf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Ts(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Nf(Object(e),!0).forEach(function(r){fg(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Nf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function fg(n,t,e){return(t=function(r){var i=function(o){if(No(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(No(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return No(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Co(n){return Co=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Co(n)}function Cf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function ui(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Cf(Object(e),!0).forEach(function(r){c1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Cf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function c1(n,t,e){return(t=function(r){var i=function(o){if(Co(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Co(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Co(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function dg(n){return function(t){if(Array.isArray(t))return hl(t)}(n)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(n)||function(t,e){if(t){if(typeof t=="string")return hl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?hl(t,e):void 0}}(n)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function hl(n,t){(t==null||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function Bs(n){return!!n.execute}function u1(n){var t=n.reduce(function(e,r){if(!Bs(r))return e.push(r),e;var i=r.searchClient,o=r.execute,s=r.requesterId,a=r.requests,l=e.find(function(f){return Bs(r)&&Bs(f)&&f.searchClient===i&&!!s&&f.requesterId===s});if(l){var c;(c=l.items).push.apply(c,dg(a))}else{var u={execute:o,requesterId:s,items:a,searchClient:i};e.push(u)}return e},[]).map(function(e){if(!Bs(e))return Promise.resolve(e);var r=e,i=r.execute,o=r.items;return i({searchClient:r.searchClient,requests:o})});return Promise.all(t).then(function(e){return Go(e)})}function Fo(n){return Fo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Fo(n)}var f1=["event","nextState","props","query","refresh","store"];function Ff(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function qr(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Ff(Object(e),!0).forEach(function(r){d1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Ff(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function d1(n,t,e){return(t=function(r){var i=function(o){if(Fo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Fo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Fo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}var jf,ml,Ms,lo=null,Lf=(jf=-1,ml=-1,Ms=void 0,function(n){var t=++jf;return Promise.resolve(n).then(function(e){return Ms&&t<ml?Ms:(ml=t,Ms=e,e)})});function Yr(n){var t=n.event,e=n.nextState,r=e===void 0?{}:e,i=n.props,o=n.query,s=n.refresh,a=n.store,l=function(_,I){if(_==null)return{};var y,A,w=function(T,P){if(T==null)return{};var L,B,M={},j=Object.keys(T);for(B=0;B<j.length;B++)L=j[B],P.indexOf(L)>=0||(M[L]=T[L]);return M}(_,I);if(Object.getOwnPropertySymbols){var F=Object.getOwnPropertySymbols(_);for(A=0;A<F.length;A++)y=F[A],I.indexOf(y)>=0||Object.prototype.propertyIsEnumerable.call(_,y)&&(w[y]=_[y])}return w}(n,f1);lo&&i.environment.clearTimeout(lo);var c=l.setCollections,u=l.setIsOpen,f=l.setQuery,h=l.setActiveItemId,d=l.setStatus,g=l.setContext;if(f(o),h(i.defaultActiveItemId),!o&&i.openOnFocus===!1){var m,p=a.getState().collections.map(function(_){return qr(qr({},_),{},{items:[]})});d("idle"),c(p),u((m=r.isOpen)!==null&&m!==void 0?m:i.shouldPanelOpen({state:a.getState()}));var v=kf(Lf(p).then(function(){return Promise.resolve()}));return a.pendingRequests.add(v)}d("loading"),lo=i.environment.setTimeout(function(){d("stalled")},i.stallThreshold);var b=kf(Lf(i.getSources(qr({query:o,refresh:s,state:a.getState()},l)).then(function(_){return Promise.all(_.map(function(I){return Promise.resolve(I.getItems(qr({query:o,refresh:s,state:a.getState()},l))).then(function(y){return function(A,w,F){if(P=A,!!(P!=null&&P.execute)){var T=A.requesterId==="algolia"?Object.assign.apply(Object,[{}].concat(dg(Object.keys(F.context).map(function(L){var B;return(B=F.context[L])===null||B===void 0?void 0:B.__algoliaSearchParameters})))):{};return ui(ui({},A),{},{requests:A.queries.map(function(L){return{query:A.requesterId==="algolia"?ui(ui({},L),{},{params:ui(ui({},T),L.params)}):L,sourceId:w,transformResponse:A.transformResponse}})})}var P;return{items:A,sourceId:w}}(y,I.sourceId,a.getState())})})).then(u1).then(function(I){var y,A=I.some(function(w){return function(F){return!Array.isArray(F)&&!!(F!=null&&F._automaticInsights)}(w.items)});return A&&g({algoliaInsightsPlugin:qr(qr({},((y=a.getState().context)===null||y===void 0?void 0:y.algoliaInsightsPlugin)||{}),{},{__automaticInsights:A})}),function(w,F,T){return F.map(function(P){var L,B=w.filter(function(U){return U.sourceId===P.sourceId}),M=B.map(function(U){return U.items}),j=B[0].transformResponse,V=j?j({results:L=M,hits:L.map(function(U){return U.hits}).filter(Boolean),facetHits:L.map(function(U){var H;return(H=U.facetHits)===null||H===void 0?void 0:H.map(function(C){return{label:C.value,count:C.count,_highlightResult:{label:{value:C.highlighted}}}})}).filter(Boolean)}):M;return P.onResolve({source:P,results:M,items:V,state:T.getState()}),V.every(Boolean),'The `getItems` function from source "'.concat(P.sourceId,'" must return an array of items but returned ').concat(JSON.stringify(void 0),`.

Did you forget to return items?

See: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems`),{source:P,items:V}})}(I,_,a)}).then(function(I){return function(y){var A=y.props,w=y.state,F=y.collections.reduce(function(P,L){return Ts(Ts({},P),{},fg({},L.source.sourceId,Ts(Ts({},L.source),{},{getItems:function(){return Go(L.items)}})))},{}),T=A.plugins.reduce(function(P,L){return L.reshape?L.reshape(P):P},{sourcesBySourceId:F,state:w}).sourcesBySourceId;return Go(A.reshape({sourcesBySourceId:T,sources:Object.values(T),state:w})).filter(Boolean).map(function(P){return{source:P,items:P.getItems()}})}({collections:I,props:i,state:a.getState()})})}))).then(function(_){var I;d("idle"),c(_);var y=i.shouldPanelOpen({state:a.getState()});u((I=r.isOpen)!==null&&I!==void 0?I:i.openOnFocus&&!o&&y||y);var A=Zr(a.getState());if(a.getState().activeItemId!==null&&A){var w=A.item,F=A.itemInputValue,T=A.itemUrl,P=A.source;P.onActive(qr({event:t,item:w,itemInputValue:F,itemUrl:T,refresh:s,source:P,state:a.getState()},l))}}).finally(function(){d("idle"),lo&&i.environment.clearTimeout(lo)});return a.pendingRequests.add(b)}function jo(n){return jo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},jo(n)}var h1=["event","props","refresh","store"];function Rf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function Kr(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Rf(Object(e),!0).forEach(function(r){m1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Rf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function m1(n,t,e){return(t=function(r){var i=function(o){if(jo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(jo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return jo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Lo(n){return Lo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Lo(n)}var p1=["props","refresh","store"],g1=["inputElement","formElement","panelElement"],_1=["inputElement"],b1=["inputElement","maxLength"],y1=["source"],v1=["item","source"];function Uf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function xe(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Uf(Object(e),!0).forEach(function(r){w1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Uf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function w1(n,t,e){return(t=function(r){var i=function(o){if(Lo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Lo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Lo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function fi(n,t){if(n==null)return{};var e,r,i=function(s,a){if(s==null)return{};var l,c,u={},f=Object.keys(s);for(c=0;c<f.length;c++)l=f[c],a.indexOf(l)>=0||(u[l]=s[l]);return u}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)e=o[r],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function Ro(n){return Ro=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Ro(n)}function Vf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function S1(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Vf(Object(e),!0).forEach(function(r){hg(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Vf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function hg(n,t,e){return(t=function(r){var i=function(o){if(Ro(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Ro(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Ro(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function I1(n){var t,e,r,i,o=n.plugins,s=n.options,a=(t=(((e=s.__autocomplete_metadata)===null||e===void 0?void 0:e.userAgents)||[])[0])===null||t===void 0?void 0:t.segment,l=a?hg({},a,Object.keys(((r=s.__autocomplete_metadata)===null||r===void 0?void 0:r.options)||{})):{};return{plugins:o.map(function(c){return{name:c.name,options:Object.keys(c.__autocomplete_pluginOptions||[])}}),options:S1({"autocomplete-core":Object.keys(s)},l),ua:J0.concat(((i=s.__autocomplete_metadata)===null||i===void 0?void 0:i.userAgents)||[])}}function xf(n){var t,e=n.state;return e.isOpen===!1||e.activeItemId===null?null:((t=Zr(e))===null||t===void 0?void 0:t.itemInputValue)||null}function Uo(n){return Uo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Uo(n)}function zf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function qt(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?zf(Object(e),!0).forEach(function(r){O1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):zf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function O1(n,t,e){return(t=function(r){var i=function(o){if(Uo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Uo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Uo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}var k1=function(n,t){switch(t.type){case"setActiveItemId":case"mousemove":return qt(qt({},n),{},{activeItemId:t.payload});case"setQuery":return qt(qt({},n),{},{query:t.payload,completion:null});case"setCollections":return qt(qt({},n),{},{collections:t.payload});case"setIsOpen":return qt(qt({},n),{},{isOpen:t.payload});case"setStatus":return qt(qt({},n),{},{status:t.payload});case"setContext":return qt(qt({},n),{},{context:qt(qt({},n.context),t.payload)});case"ArrowDown":var e=qt(qt({},n),{},{activeItemId:t.payload.hasOwnProperty("nextActiveItemId")?t.payload.nextActiveItemId:Df(1,n.activeItemId,Hl(n),t.props.defaultActiveItemId)});return qt(qt({},e),{},{completion:xf({state:e})});case"ArrowUp":var r=qt(qt({},n),{},{activeItemId:Df(-1,n.activeItemId,Hl(n),t.props.defaultActiveItemId)});return qt(qt({},r),{},{completion:xf({state:r})});case"Escape":return n.isOpen?qt(qt({},n),{},{activeItemId:null,isOpen:!1,completion:null}):qt(qt({},n),{},{activeItemId:null,query:"",status:"idle",collections:[]});case"submit":return qt(qt({},n),{},{activeItemId:null,isOpen:!1,status:"idle"});case"reset":return qt(qt({},n),{},{activeItemId:t.props.openOnFocus===!0?t.props.defaultActiveItemId:null,status:"idle",completion:null,query:""});case"focus":return qt(qt({},n),{},{activeItemId:t.props.defaultActiveItemId,isOpen:(t.props.openOnFocus||!!n.query)&&t.props.shouldPanelOpen({state:n})});case"blur":return t.props.debug?n:qt(qt({},n),{},{isOpen:!1,activeItemId:null});case"mouseleave":return qt(qt({},n),{},{activeItemId:t.props.defaultActiveItemId});default:return"The reducer action ".concat(JSON.stringify(t.type)," is not supported."),n}};function Vo(n){return Vo=typeof Symbol=="function"&&mt(Symbol.iterator)=="symbol"?function(t){return mt(t)}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)},Vo(n)}function Wf(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,r)}return e}function $r(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Wf(Object(e),!0).forEach(function(r){D1(n,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):Wf(Object(e)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(e,r))})}return n}function D1(n,t,e){return(t=function(r){var i=function(o){if(Vo(o)!=="object"||o===null)return o;var s=o[Symbol.toPrimitive];if(s!==void 0){var a=s.call(o,"string");if(Vo(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}(r);return Vo(i)==="symbol"?i:String(i)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function E1(n){var t=[],e=function(u,f){var h,d=typeof window<"u"?window:{},g=u.plugins||[];return ci(ci({debug:!1,openOnFocus:!1,enterKeyHint:void 0,ignoreCompositionEvents:!1,placeholder:"",autoFocus:!1,defaultActiveItemId:null,stallThreshold:300,insights:void 0,environment:d,shouldPanelOpen:function(m){return Hl(m.state)>0},reshape:function(m){return m.sources}},u),{},{id:(h=u.id)!==null&&h!==void 0?h:"autocomplete-".concat($0++),plugins:g,initialState:ci({activeItemId:null,query:"",completion:null,collections:[],isOpen:!1,status:"idle",context:{}},u.initialState),onStateChange:function(m){var p;(p=u.onStateChange)===null||p===void 0||p.call(u,m),g.forEach(function(v){var b;return(b=v.onStateChange)===null||b===void 0?void 0:b.call(v,m)})},onSubmit:function(m){var p;(p=u.onSubmit)===null||p===void 0||p.call(u,m),g.forEach(function(v){var b;return(b=v.onSubmit)===null||b===void 0?void 0:b.call(v,m)})},onReset:function(m){var p;(p=u.onReset)===null||p===void 0||p.call(u,m),g.forEach(function(v){var b;return(b=v.onReset)===null||b===void 0?void 0:b.call(v,m)})},getSources:function(m){return Promise.all([].concat(function(p){return function(v){if(Array.isArray(v))return dl(v)}(p)||function(v){if(typeof Symbol<"u"&&v[Symbol.iterator]!=null||v["@@iterator"]!=null)return Array.from(v)}(p)||function(v,b){if(v){if(typeof v=="string")return dl(v,b);var _=Object.prototype.toString.call(v).slice(8,-1);return _==="Object"&&v.constructor&&(_=v.constructor.name),_==="Map"||_==="Set"?Array.from(v):_==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?dl(v,b):void 0}}(p)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}(g.map(function(p){return p.getSources})),[u.getSources]).filter(Boolean).map(function(p){return function(v,b){var _=[];return Promise.resolve(v(b)).then(function(I){return Promise.all(I.filter(function(y){return!!y}).map(function(y){if(y.sourceId,_.includes(y.sourceId))throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(y.sourceId)," is not unique."));_.push(y.sourceId);var A={getItemInputValue:function(F){return F.state.query},getItemUrl:function(){},onSelect:function(F){(0,F.setIsOpen)(!1)},onActive:Ks,onResolve:Ks};Object.keys(A).forEach(function(F){A[F].__default=!0});var w=Af(Af({},A),y);return Promise.resolve(w)}))})}(p,m)})).then(function(p){return Go(p)}).then(function(p){return p.map(function(v){return ci(ci({},v),{},{onSelect:function(b){v.onSelect(b),f.forEach(function(_){var I;return(I=_.onSelect)===null||I===void 0?void 0:I.call(_,b)})},onActive:function(b){v.onActive(b),f.forEach(function(_){var I;return(I=_.onActive)===null||I===void 0?void 0:I.call(_,b)})},onResolve:function(b){v.onResolve(b),f.forEach(function(_){var I;return(I=_.onResolve)===null||I===void 0?void 0:I.call(_,b)})}})})})},navigator:ci({navigate:function(m){var p=m.itemUrl;d.location.assign(p)},navigateNewTab:function(m){var p=m.itemUrl,v=d.open(p,"_blank","noopener");v==null||v.focus()},navigateNewWindow:function(m){var p=m.itemUrl;d.open(p,"_blank","noopener")}},u.navigator)})}(n,t),r=function(u,f,h){var d,g=f.initialState;return{getState:function(){return g},dispatch:function(m,p){var v=function(b){for(var _=1;_<arguments.length;_++){var I=arguments[_]!=null?arguments[_]:{};_%2?Bf(Object(I),!0).forEach(function(y){s1(b,y,I[y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(b,Object.getOwnPropertyDescriptors(I)):Bf(Object(I)).forEach(function(y){Object.defineProperty(b,y,Object.getOwnPropertyDescriptor(I,y))})}return b}({},g);g=u(g,{type:m,props:f,payload:p}),h({state:g,prevState:v})},pendingRequests:(d=[],{add:function(m){return d.push(m),m.finally(function(){d=d.filter(function(p){return p!==m})})},cancelAll:function(){d.forEach(function(m){return m.cancel()})},isEmpty:function(){return d.length===0}})}}(k1,e,function(u){var f,h,d=u.prevState,g=u.state;if(e.onStateChange($r({prevState:d,state:g,refresh:s,navigator:e.navigator},i)),!l()&&(f=g.context)!==null&&f!==void 0&&(h=f.algoliaInsightsPlugin)!==null&&h!==void 0&&h.__automaticInsights&&e.insights!==!1){var m=If({__autocomplete_clickAnalytics:!1});e.plugins.push(m),a([m])}}),i=function(u){var f=u.store;return{setActiveItemId:function(h){f.dispatch("setActiveItemId",h)},setQuery:function(h){f.dispatch("setQuery",h)},setCollections:function(h){var d=0,g=h.map(function(m){return As(As({},m),{},{items:Go(m.items).map(function(p){return As(As({},p),{},{__autocomplete_id:d++})})})});f.dispatch("setCollections",g)},setIsOpen:function(h){f.dispatch("setIsOpen",h)},setStatus:function(h){f.dispatch("setStatus",h)},setContext:function(h){f.dispatch("setContext",h)}}}({store:r}),o=function(u){var f=u.props,h=u.refresh,d=u.store,g=fi(u,p1);return{getEnvironmentProps:function(m){var p=m.inputElement,v=m.formElement,b=m.panelElement;function _(I){!d.getState().isOpen&&d.pendingRequests.isEmpty()||I.target===p||[v,b].some(function(y){return(A=y)===(w=I.target)||A.contains(w);var A,w})===!1&&(d.dispatch("blur",null),f.debug||d.pendingRequests.cancelAll())}return xe({onTouchStart:_,onMouseDown:_,onTouchMove:function(I){d.getState().isOpen!==!1&&p===f.environment.document.activeElement&&I.target!==p&&p.blur()}},fi(m,g1))},getRootProps:function(m){return xe({role:"combobox","aria-expanded":d.getState().isOpen,"aria-haspopup":"listbox","aria-controls":d.getState().isOpen?d.getState().collections.map(function(p){var v=p.source;return _n(f.id,"list",v)}).join(" "):void 0,"aria-labelledby":_n(f.id,"label")},m)},getFormProps:function(m){return m.inputElement,xe({action:"",noValidate:!0,role:"search",onSubmit:function(p){var v;p.preventDefault(),f.onSubmit(xe({event:p,refresh:h,state:d.getState()},g)),d.dispatch("submit",null),(v=m.inputElement)===null||v===void 0||v.blur()},onReset:function(p){var v;p.preventDefault(),f.onReset(xe({event:p,refresh:h,state:d.getState()},g)),d.dispatch("reset",null),(v=m.inputElement)===null||v===void 0||v.focus()}},fi(m,_1))},getLabelProps:function(m){return xe({htmlFor:_n(f.id,"input"),id:_n(f.id,"label")},m)},getInputProps:function(m){var p;function v(T){(f.openOnFocus||d.getState().query)&&Yr(xe({event:T,props:f,query:d.getState().completion||d.getState().query,refresh:h,store:d},g)),d.dispatch("focus",null)}var b=m||{};b.inputElement;var _=b.maxLength,I=_===void 0?512:_,y=fi(b,b1),A=Zr(d.getState()),w=function(T){return!!(T&&T.match(o1))}(((p=f.environment.navigator)===null||p===void 0?void 0:p.userAgent)||""),F=f.enterKeyHint||(A!=null&&A.itemUrl&&!w?"go":"search");return xe({"aria-autocomplete":"both","aria-activedescendant":d.getState().isOpen&&d.getState().activeItemId!==null?_n(f.id,"item-".concat(d.getState().activeItemId),A==null?void 0:A.source):void 0,"aria-controls":d.getState().isOpen?d.getState().collections.map(function(T){var P=T.source;return _n(f.id,"list",P)}).join(" "):void 0,"aria-labelledby":_n(f.id,"label"),value:d.getState().completion||d.getState().query,id:_n(f.id,"input"),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",enterKeyHint:F,spellCheck:"false",autoFocus:f.autoFocus,placeholder:f.placeholder,maxLength:I,type:"search",onChange:function(T){var P=T.currentTarget.value;f.ignoreCompositionEvents&&Tf(T).isComposing?g.setQuery(P):Yr(xe({event:T,props:f,query:P.slice(0,I),refresh:h,store:d},g))},onCompositionEnd:function(T){Yr(xe({event:T,props:f,query:T.currentTarget.value.slice(0,I),refresh:h,store:d},g))},onKeyDown:function(T){Tf(T).isComposing||function(P){var L=P.event,B=P.props,M=P.refresh,j=P.store,V=function(Bt,Pt){if(Bt==null)return{};var K,ye,Yt=function(We,pn){if(We==null)return{};var Re,Mn,fn={},Vn=Object.keys(We);for(Mn=0;Mn<Vn.length;Mn++)Re=Vn[Mn],pn.indexOf(Re)>=0||(fn[Re]=We[Re]);return fn}(Bt,Pt);if(Object.getOwnPropertySymbols){var Te=Object.getOwnPropertySymbols(Bt);for(ye=0;ye<Te.length;ye++)K=Te[ye],Pt.indexOf(K)>=0||Object.prototype.propertyIsEnumerable.call(Bt,K)&&(Yt[K]=Bt[K])}return Yt}(P,h1);if(L.key==="ArrowUp"||L.key==="ArrowDown"){var U=function(){var Bt=Zr(j.getState()),Pt=B.environment.document.getElementById(_n(B.id,"item-".concat(j.getState().activeItemId),Bt==null?void 0:Bt.source));Pt&&(Pt.scrollIntoViewIfNeeded?Pt.scrollIntoViewIfNeeded(!1):Pt.scrollIntoView(!1))},H=function(){var Bt=Zr(j.getState());if(j.getState().activeItemId!==null&&Bt){var Pt=Bt.item,K=Bt.itemInputValue,ye=Bt.itemUrl,Yt=Bt.source;Yt.onActive(Kr({event:L,item:Pt,itemInputValue:K,itemUrl:ye,refresh:M,source:Yt,state:j.getState()},V))}};L.preventDefault(),j.getState().isOpen===!1&&(B.openOnFocus||j.getState().query)?Yr(Kr({event:L,props:B,query:j.getState().query,refresh:M,store:j},V)).then(function(){j.dispatch(L.key,{nextActiveItemId:B.defaultActiveItemId}),H(),setTimeout(U,0)}):(j.dispatch(L.key,{}),H(),U())}else if(L.key==="Escape")L.preventDefault(),j.dispatch(L.key,null),j.pendingRequests.cancelAll();else if(L.key==="Tab")j.dispatch("blur",null),j.pendingRequests.cancelAll();else if(L.key==="Enter"){if(j.getState().activeItemId===null||j.getState().collections.every(function(Bt){return Bt.items.length===0}))return void(B.debug||j.pendingRequests.cancelAll());L.preventDefault();var C=Zr(j.getState()),G=C.item,J=C.itemInputValue,X=C.itemUrl,Dt=C.source;if(L.metaKey||L.ctrlKey)X!==void 0&&(Dt.onSelect(Kr({event:L,item:G,itemInputValue:J,itemUrl:X,refresh:M,source:Dt,state:j.getState()},V)),B.navigator.navigateNewTab({itemUrl:X,item:G,state:j.getState()}));else if(L.shiftKey)X!==void 0&&(Dt.onSelect(Kr({event:L,item:G,itemInputValue:J,itemUrl:X,refresh:M,source:Dt,state:j.getState()},V)),B.navigator.navigateNewWindow({itemUrl:X,item:G,state:j.getState()}));else if(!L.altKey){if(X!==void 0)return Dt.onSelect(Kr({event:L,item:G,itemInputValue:J,itemUrl:X,refresh:M,source:Dt,state:j.getState()},V)),void B.navigator.navigate({itemUrl:X,item:G,state:j.getState()});Yr(Kr({event:L,nextState:{isOpen:!1},props:B,query:J,refresh:M,store:j},V)).then(function(){Dt.onSelect(Kr({event:L,item:G,itemInputValue:J,itemUrl:X,refresh:M,source:Dt,state:j.getState()},V))})}}}(xe({event:T,props:f,refresh:h,store:d},g))},onFocus:v,onBlur:Ks,onClick:function(T){m.inputElement!==f.environment.document.activeElement||d.getState().isOpen||v(T)}},y)},getPanelProps:function(m){return xe({onMouseDown:function(p){p.preventDefault()},onMouseLeave:function(){d.dispatch("mouseleave",null)}},m)},getListProps:function(m){var p=m||{},v=p.source,b=fi(p,y1);return xe({role:"listbox","aria-labelledby":_n(f.id,"label"),id:_n(f.id,"list",v)},b)},getItemProps:function(m){var p=m.item,v=m.source,b=fi(m,v1);return xe({id:_n(f.id,"item-".concat(p.__autocomplete_id),v),role:"option","aria-selected":d.getState().activeItemId===p.__autocomplete_id,onMouseMove:function(_){if(p.__autocomplete_id!==d.getState().activeItemId){d.dispatch("mousemove",p.__autocomplete_id);var I=Zr(d.getState());if(d.getState().activeItemId!==null&&I){var y=I.item,A=I.itemInputValue,w=I.itemUrl,F=I.source;F.onActive(xe({event:_,item:y,itemInputValue:A,itemUrl:w,refresh:h,source:F,state:d.getState()},g))}}},onMouseDown:function(_){_.preventDefault()},onClick:function(_){var I=v.getItemInputValue({item:p,state:d.getState()}),y=v.getItemUrl({item:p,state:d.getState()});(y?Promise.resolve():Yr(xe({event:_,nextState:{isOpen:!1},props:f,query:I,refresh:h,store:d},g))).then(function(){v.onSelect(xe({event:_,item:p,itemInputValue:I,itemUrl:y,refresh:h,source:v,state:d.getState()},g))})}},b)}}}($r({props:e,refresh:s,store:r,navigator:e.navigator},i));function s(){return Yr($r({event:new Event("input"),nextState:{isOpen:r.getState().isOpen},props:e,navigator:e.navigator,query:r.getState().query,refresh:s,store:r},i))}function a(u){u.forEach(function(f){var h;return(h=f.subscribe)===null||h===void 0?void 0:h.call(f,$r($r({},i),{},{navigator:e.navigator,refresh:s,onSelect:function(d){t.push({onSelect:d})},onActive:function(d){t.push({onActive:d})},onResolve:function(d){t.push({onResolve:d})}}))})}function l(){return e.plugins.some(function(u){return u.name==="aa.algoliaInsightsPlugin"})}if(e.insights&&!l()){var c=typeof e.insights=="boolean"?{}:e.insights;e.plugins.push(If(c))}return a(e.plugins),function(u){var f,h,d=u.metadata,g=u.environment;if(!((f=g.navigator)===null||f===void 0||(h=f.userAgent)===null||h===void 0)&&h.includes("Algolia Crawler")){var m=g.document.createElement("meta"),p=g.document.querySelector("head");m.name="algolia:metadata",setTimeout(function(){m.content=JSON.stringify(d),p.appendChild(m)},0)}}({metadata:I1({plugins:e.plugins,options:n}),environment:e.environment}),$r($r({refresh:s,navigator:e.navigator},o),i)}function A1(n){var t=n.translations,e=(t===void 0?{}:t).searchByText,r=e===void 0?"Search by":e;return k.createElement("a",{href:"https://www.algolia.com/ref/docsearch/?utm_source=".concat(window.location.hostname,"&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"),target:"_blank",rel:"noopener noreferrer"},k.createElement("span",{className:"DocSearch-Label"},r),k.createElement("svg",{width:"77",height:"19","aria-label":"Algolia",role:"img",id:"Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 2196.2 500"},k.createElement("defs",null,k.createElement("style",null,".cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}")),k.createElement("path",{className:"cls-2",d:"M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("rect",{className:"cls-1",x:"1845.88",y:"104.73",width:"62.58",height:"277.9",rx:"5.9",ry:"5.9"}),k.createElement("path",{className:"cls-2",d:"M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z"}),k.createElement("path",{className:"cls-2",d:"M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("path",{className:"cls-2",d:"M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z"}),k.createElement("path",{className:"cls-2",d:"M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z"}),k.createElement("path",{className:"cls-1",d:"M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z"})))}function Ps(n){return k.createElement("svg",{width:"15",height:"15","aria-label":n.ariaLabel,role:"img"},k.createElement("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.2"},n.children))}function T1(n){var t=n.translations,e=t===void 0?{}:t,r=e.selectText,i=r===void 0?"to select":r,o=e.selectKeyAriaLabel,s=o===void 0?"Enter key":o,a=e.navigateText,l=a===void 0?"to navigate":a,c=e.navigateUpKeyAriaLabel,u=c===void 0?"Arrow up":c,f=e.navigateDownKeyAriaLabel,h=f===void 0?"Arrow down":f,d=e.closeText,g=d===void 0?"to close":d,m=e.closeKeyAriaLabel,p=m===void 0?"Escape key":m,v=e.searchByText,b=v===void 0?"Search by":v;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Logo"},k.createElement(A1,{translations:{searchByText:b}})),k.createElement("ul",{className:"DocSearch-Commands"},k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:s},k.createElement("path",{d:"M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"}))),k.createElement("span",{className:"DocSearch-Label"},i)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:h},k.createElement("path",{d:"M7.5 3.5v8M10.5 8.5l-3 3-3-3"}))),k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:u},k.createElement("path",{d:"M7.5 11.5v-8M10.5 6.5l-3-3-3 3"}))),k.createElement("span",{className:"DocSearch-Label"},l)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:p},k.createElement("path",{d:"M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"}))),k.createElement("span",{className:"DocSearch-Label"},g))))}function B1(n){var t=n.hit,e=n.children;return k.createElement("a",{href:t.url},e)}function M1(){return k.createElement("svg",{viewBox:"0 0 38 38",stroke:"currentColor",strokeOpacity:".5"},k.createElement("g",{fill:"none",fillRule:"evenodd"},k.createElement("g",{transform:"translate(1 1)",strokeWidth:"2"},k.createElement("circle",{strokeOpacity:".3",cx:"18",cy:"18",r:"18"}),k.createElement("path",{d:"M36 18c0-9.94-8.06-18-18-18"},k.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})))))}function P1(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0"}),k.createElement("path",{d:"M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13"})))}function ql(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function N1(){return k.createElement("svg",{className:"DocSearch-Hit-Select-Icon",width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M18 3v4c0 2-2 4-4 4H2"}),k.createElement("path",{d:"M8 17l-6-6 6-6"})))}var C1=function(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))};function F1(n){switch(n.type){case"lvl1":return k.createElement(C1,null);case"content":return k.createElement(L1,null);default:return k.createElement(j1,null)}}function j1(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function L1(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 5H3h14zm0 5H3h14zm0 5H3h14z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function Hf(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function R1(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0"}))}function U1(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2"}))}function V1(n){var t=n.translations,e=t===void 0?{}:t,r=e.titleText,i=r===void 0?"Unable to fetch results":r,o=e.helpText,s=o===void 0?"You might want to check your network connection.":o;return k.createElement("div",{className:"DocSearch-ErrorScreen"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(R1,null)),k.createElement("p",{className:"DocSearch-Title"},i),k.createElement("p",{className:"DocSearch-Help"},s))}var x1=["translations"];function z1(n){var t=n.translations,e=t===void 0?{}:t,r=tr(n,x1),i=e.noResultsText,o=i===void 0?"No results for":i,s=e.suggestedQueryText,a=s===void 0?"Try searching for":s,l=e.reportMissingResultsText,c=l===void 0?"Believe this query should return results?":l,u=e.reportMissingResultsLinkText,f=u===void 0?"Let us know.":u,h=r.state.context.searchSuggestions;return k.createElement("div",{className:"DocSearch-NoResults"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(U1,null)),k.createElement("p",{className:"DocSearch-Title"},o,' "',k.createElement("strong",null,r.state.query),'"'),h&&h.length>0&&k.createElement("div",{className:"DocSearch-NoResults-Prefill-List"},k.createElement("p",{className:"DocSearch-Help"},a,":"),k.createElement("ul",null,h.slice(0,3).reduce(function(d,g){return[].concat(Do(d),[k.createElement("li",{key:g},k.createElement("button",{className:"DocSearch-Prefill",key:g,type:"button",onClick:function(){r.setQuery(g.toLowerCase()+" "),r.refresh(),r.inputRef.current.focus()}},g))])},[]))),r.getMissingResultsUrl&&k.createElement("p",{className:"DocSearch-Help"},"".concat(c," "),k.createElement("a",{href:r.getMissingResultsUrl({query:r.state.query}),target:"_blank",rel:"noopener noreferrer"},f)))}var W1=["hit","attribute","tagName"];function qf(n,t){return t.split(".").reduce(function(e,r){return e!=null&&e[r]?e[r]:null},n)}function di(n){var t=n.hit,e=n.attribute,r=n.tagName;return Xn(r===void 0?"span":r,Ut(Ut({},tr(n,W1)),{},{dangerouslySetInnerHTML:{__html:qf(t,"_snippetResult.".concat(e,".value"))||qf(t,e)}}))}function Yl(n){return n.collection&&n.collection.items.length!==0?k.createElement("section",{className:"DocSearch-Hits"},k.createElement("div",{className:"DocSearch-Hit-source"},n.title),k.createElement("ul",n.getListProps(),n.collection.items.map(function(t,e){return k.createElement(H1,Ke({key:[n.title,t.objectID].join(":"),item:t,index:e},n))}))):null}function H1(n){var t=n.item,e=n.index,r=n.renderIcon,i=n.renderAction,o=n.getItemProps,s=n.onItemClick,a=n.collection,l=n.hitComponent,c=an(k.useState(!1),2),u=c[0],f=c[1],h=an(k.useState(!1),2),d=h[0],g=h[1],m=k.useRef(null),p=l;return k.createElement("li",Ke({className:["DocSearch-Hit",t.__docsearch_parent&&"DocSearch-Hit--Child",u&&"DocSearch-Hit--deleting",d&&"DocSearch-Hit--favoriting"].filter(Boolean).join(" "),onTransitionEnd:function(){m.current&&m.current()}},o({item:t,source:a.source,onClick:function(v){s(t,v)}})),k.createElement(p,{hit:t},k.createElement("div",{className:"DocSearch-Hit-Container"},r({item:t,index:e}),t.hierarchy[t.type]&&t.type==="lvl1"&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(di,{className:"DocSearch-Hit-title",hit:t,attribute:"hierarchy.lvl1"}),t.content&&k.createElement(di,{className:"DocSearch-Hit-path",hit:t,attribute:"content"})),t.hierarchy[t.type]&&(t.type==="lvl2"||t.type==="lvl3"||t.type==="lvl4"||t.type==="lvl5"||t.type==="lvl6")&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(di,{className:"DocSearch-Hit-title",hit:t,attribute:"hierarchy.".concat(t.type)}),k.createElement(di,{className:"DocSearch-Hit-path",hit:t,attribute:"hierarchy.lvl1"})),t.type==="content"&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(di,{className:"DocSearch-Hit-title",hit:t,attribute:"content"}),k.createElement(di,{className:"DocSearch-Hit-path",hit:t,attribute:"hierarchy.lvl1"})),i({item:t,runDeleteTransition:function(v){f(!0),m.current=v},runFavoriteTransition:function(v){g(!0),m.current=v}}))))}function Yf(n,t,e){return n.reduce(function(r,i){var o=t(i);return r.hasOwnProperty(o)||(r[o]=[]),r[o].length<(e||5)&&r[o].push(i),r},{})}function Kf(n){return n}function Ns(n){return n.button===1||n.altKey||n.ctrlKey||n.metaKey||n.shiftKey}function q1(){}var mg=/(<mark>|<\/mark>)/g,Y1=RegExp(mg.source);function pg(n){var t,e,r=n;if(!r.__docsearch_parent&&!n._highlightResult)return n.hierarchy.lvl0;var i=r.__docsearch_parent?(t=r.__docsearch_parent)===null||t===void 0||(t=t._highlightResult)===null||t===void 0||(t=t.hierarchy)===null||t===void 0?void 0:t.lvl0:(e=n._highlightResult)===null||e===void 0||(e=e.hierarchy)===null||e===void 0?void 0:e.lvl0;return i?i.value&&Y1.test(i.value)?i.value.replace(mg,""):i.value:n.hierarchy.lvl0}function K1(n){return k.createElement("div",{className:"DocSearch-Dropdown-Container"},n.state.collections.map(function(t){if(t.items.length===0)return null;var e=pg(t.items[0]);return k.createElement(Yl,Ke({},n,{key:t.source.sourceId,title:e,collection:t,renderIcon:function(r){var i,o=r.item,s=r.index;return k.createElement(k.Fragment,null,o.__docsearch_parent&&k.createElement("svg",{className:"DocSearch-Hit-Tree",viewBox:"0 0 24 54"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},o.__docsearch_parent!==((i=t.items[s+1])===null||i===void 0?void 0:i.__docsearch_parent)?k.createElement("path",{d:"M8 6v21M20 27H8.3"}):k.createElement("path",{d:"M8 6v42M20 27H8.3"}))),k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(F1,{type:o.type})))},renderAction:function(){return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement(N1,null))}}))}),n.resultsFooterComponent&&k.createElement("section",{className:"DocSearch-HitsFooter"},k.createElement(n.resultsFooterComponent,{state:n.state})))}var $1=["translations"];function J1(n){var t=n.translations,e=t===void 0?{}:t,r=tr(n,$1),i=e.recentSearchesTitle,o=i===void 0?"Recent":i,s=e.noRecentSearchesText,a=s===void 0?"No recent searches":s,l=e.saveRecentSearchButtonTitle,c=l===void 0?"Save this search":l,u=e.removeRecentSearchButtonTitle,f=u===void 0?"Remove this search from history":u,h=e.favoriteSearchesTitle,d=h===void 0?"Favorite":h,g=e.removeFavoriteSearchButtonTitle,m=g===void 0?"Remove this search from favorites":g;return r.state.status==="idle"&&r.hasCollections===!1?r.disableUserPersonalization?null:k.createElement("div",{className:"DocSearch-StartScreen"},k.createElement("p",{className:"DocSearch-Help"},a)):r.hasCollections===!1?null:k.createElement("div",{className:"DocSearch-Dropdown-Container"},k.createElement(Yl,Ke({},r,{title:o,collection:r.state.collections[0],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(P1,null))},renderAction:function(p){var v=p.item,b=p.runFavoriteTransition,_=p.runDeleteTransition;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:c,type:"submit",onClick:function(I){I.preventDefault(),I.stopPropagation(),b(function(){r.favoriteSearches.add(v),r.recentSearches.remove(v),r.refresh()})}},k.createElement(Hf,null))),k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:f,type:"submit",onClick:function(I){I.preventDefault(),I.stopPropagation(),_(function(){r.recentSearches.remove(v),r.refresh()})}},k.createElement(ql,null))))}})),k.createElement(Yl,Ke({},r,{title:d,collection:r.state.collections[1],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(Hf,null))},renderAction:function(p){var v=p.item,b=p.runDeleteTransition;return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:m,type:"submit",onClick:function(_){_.preventDefault(),_.stopPropagation(),b(function(){r.favoriteSearches.remove(v),r.refresh()})}},k.createElement(ql,null)))}})))}var Q1=["translations"],G1=k.memo(function(n){var t=n.translations,e=t===void 0?{}:t,r=tr(n,Q1);if(r.state.status==="error")return k.createElement(V1,{translations:e==null?void 0:e.errorScreen});var i=r.state.collections.some(function(o){return o.items.length>0});return r.state.query?i===!1?k.createElement(z1,Ke({},r,{translations:e==null?void 0:e.noResultsScreen})):k.createElement(K1,r):k.createElement(J1,Ke({},r,{hasCollections:i,translations:e==null?void 0:e.startScreen}))},function(n,t){return t.state.status==="loading"||t.state.status==="stalled"}),X1=["translations"];function Z1(n){var t=n.translations,e=t===void 0?{}:t,r=tr(n,X1),i=e.resetButtonTitle,o=i===void 0?"Clear the query":i,s=e.resetButtonAriaLabel,a=s===void 0?"Clear the query":s,l=e.cancelButtonText,c=l===void 0?"Cancel":l,u=e.cancelButtonAriaLabel,f=u===void 0?"Cancel":u,h=e.searchInputLabel,d=h===void 0?"Search":h,g=r.getFormProps({inputElement:r.inputRef.current}).onReset;return k.useEffect(function(){r.autoFocus&&r.inputRef.current&&r.inputRef.current.focus()},[r.autoFocus,r.inputRef]),k.useEffect(function(){r.isFromSelection&&r.inputRef.current&&r.inputRef.current.select()},[r.isFromSelection,r.inputRef]),k.createElement(k.Fragment,null,k.createElement("form",{className:"DocSearch-Form",onSubmit:function(m){m.preventDefault()},onReset:g},k.createElement("label",Ke({className:"DocSearch-MagnifierLabel"},r.getLabelProps()),k.createElement(sg,null),k.createElement("span",{className:"DocSearch-VisuallyHiddenForAccessibility"},d)),k.createElement("div",{className:"DocSearch-LoadingIndicator"},k.createElement(M1,null)),k.createElement("input",Ke({className:"DocSearch-Input",ref:r.inputRef},r.getInputProps({inputElement:r.inputRef.current,autoFocus:r.autoFocus,maxLength:64}))),k.createElement("button",{type:"reset",title:o,className:"DocSearch-Reset","aria-label":a,hidden:!r.state.query},k.createElement(ql,null))),k.createElement("button",{className:"DocSearch-Cancel",type:"reset","aria-label":f,onClick:r.onClose},c))}var tw=["_highlightResult","_snippetResult"];function $f(n){var t=n.key,e=n.limit,r=e===void 0?5:e,i=function(s){return function(){var a="__TEST_KEY__";try{return localStorage.setItem(a,""),localStorage.removeItem(a),!0}catch{return!1}}()===!1?{setItem:function(){},getItem:function(){return[]}}:{setItem:function(a){return window.localStorage.setItem(s,JSON.stringify(a))},getItem:function(){var a=window.localStorage.getItem(s);return a?JSON.parse(a):[]}}}(t),o=i.getItem().slice(0,r);return{add:function(s){var a=s;a._highlightResult,a._snippetResult;var l=tr(a,tw),c=o.findIndex(function(u){return u.objectID===l.objectID});c>-1&&o.splice(c,1),o.unshift(l),o=o.slice(0,r),i.setItem(o)},remove:function(s){o=o.filter(function(a){return a.objectID!==s.objectID}),i.setItem(o)},getAll:function(){return o}}}function ew(n){var t,e="algolia-client-js-".concat(n.key);function r(){return t===void 0&&(t=n.localStorage||window.localStorage),t}function i(){return JSON.parse(r().getItem(e)||"{}")}function o(s){r().setItem(e,JSON.stringify(s))}return{get:function(s,a){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}};return Promise.resolve().then(function(){var c,u,f;return c=n.timeToLive?1e3*n.timeToLive:null,u=i(),o(f=Object.fromEntries(Object.entries(u).filter(function(h){return an(h,2)[1].timestamp!==void 0}))),c&&o(Object.fromEntries(Object.entries(f).filter(function(h){var d=an(h,2)[1],g=new Date().getTime();return!(d.timestamp+c<g)}))),i()[JSON.stringify(s)]}).then(function(c){return Promise.all([c?c.value:a(),c!==void 0])}).then(function(c){var u=an(c,2),f=u[0],h=u[1];return Promise.all([f,h||l.miss(f)])}).then(function(c){return an(c,1)[0]})},set:function(s,a){return Promise.resolve().then(function(){var l=i();return l[JSON.stringify(s)]={timestamp:new Date().getTime(),value:a},r().setItem(e,JSON.stringify(l)),a})},delete:function(s){return Promise.resolve().then(function(){var a=i();delete a[JSON.stringify(s)],r().setItem(e,JSON.stringify(a))})},clear:function(){return Promise.resolve().then(function(){r().removeItem(e)})}}}function So(n){var t=Do(n.caches),e=t.shift();return e===void 0?{get:function(r,i){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}};return i().then(function(s){return Promise.all([s,o.miss(s)])}).then(function(s){return an(s,1)[0]})},set:function(r,i){return Promise.resolve(i)},delete:function(r){return Promise.resolve()},clear:function(){return Promise.resolve()}}:{get:function(r,i){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}};return e.get(r,i,o).catch(function(){return So({caches:t}).get(r,i,o)})},set:function(r,i){return e.set(r,i).catch(function(){return So({caches:t}).set(r,i)})},delete:function(r){return e.delete(r).catch(function(){return So({caches:t}).delete(r)})},clear:function(){return e.clear().catch(function(){return So({caches:t}).clear()})}}}function pl(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{serializable:!0},t={};return{get:function(e,r){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{miss:function(){return Promise.resolve()}},o=JSON.stringify(e);if(o in t)return Promise.resolve(n.serializable?JSON.parse(t[o]):t[o]);var s=r();return s.then(function(a){return i.miss(a)}).then(function(){return s})},set:function(e,r){return t[JSON.stringify(e)]=n.serializable?JSON.stringify(r):r,Promise.resolve(r)},delete:function(e){return delete t[JSON.stringify(e)],Promise.resolve()},clear:function(){return t={},Promise.resolve()}}}function nw(n){var t=n.algoliaAgents,e=n.client,r=n.version,i=function(o){var s={value:"Algolia for JavaScript (".concat(o,")"),add:function(a){var l="; ".concat(a.segment).concat(a.version!==void 0?" (".concat(a.version,")"):"");return s.value.indexOf(l)===-1&&(s.value="".concat(s.value).concat(l)),s}};return s}(r).add({segment:e,version:r});return t.forEach(function(o){return i.add(o)}),i}var Jf=12e4;function Qf(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"up",e=Date.now();return Ut(Ut({},n),{},{status:t,lastUpdate:e,isUp:function(){return t==="up"||Date.now()-e>Jf},isTimedOut:function(){return t==="timed out"&&Date.now()-e<=Jf}})}var gg=function(){function n(t,e){var r;return Ji(this,n),Gi(r=$i(this,n,[t]),"name","AlgoliaError"),e&&(r.name=e),r}return Xi(n,Wl(Error)),Qi(n)}(),_g=function(){function n(t,e,r){var i;return Ji(this,n),Gi(i=$i(this,n,[t,r]),"stackTrace",void 0),i.stackTrace=e,i}return Xi(n,gg),Qi(n)}(),rw=function(){function n(t){return Ji(this,n),$i(this,n,["Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",t,"RetryError"])}return Xi(n,_g),Qi(n)}(),Kl=function(){function n(t,e,r){var i,o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"ApiError";return Ji(this,n),Gi(i=$i(this,n,[t,r,o]),"status",void 0),i.status=e,i}return Xi(n,_g),Qi(n)}(),iw=function(){function n(t,e){var r;return Ji(this,n),Gi(r=$i(this,n,[t,"DeserializationError"]),"response",void 0),r.response=e,r}return Xi(n,gg),Qi(n)}(),ow=function(){function n(t,e,r,i){var o;return Ji(this,n),Gi(o=$i(this,n,[t,e,i,"DetailedApiError"]),"error",void 0),o.error=r,o}return Xi(n,Kl),Qi(n)}();function sw(n,t,e){var r,i=(r=e,Object.keys(r).filter(function(s){return r[s]!==void 0}).sort().map(function(s){return"".concat(s,"=").concat(encodeURIComponent(Object.prototype.toString.call(r[s])==="[object Array]"?r[s].join(","):r[s]).replace(/\+/g,"%20"))}).join("&")),o="".concat(n.protocol,"://").concat(n.url).concat(n.port?":".concat(n.port):"","/").concat(t.charAt(0)==="/"?t.substring(1):t);return i.length&&(o+="?".concat(i)),o}function aw(n,t){if(n.method!=="GET"&&(n.data!==void 0||t.data!==void 0)){var e=Array.isArray(n.data)?n.data:Ut(Ut({},n.data),t.data);return JSON.stringify(e)}}function lw(n,t,e){var r=Ut(Ut(Ut({Accept:"application/json"},n),t),e),i={};return Object.keys(r).forEach(function(o){var s=r[o];i[o.toLowerCase()]=s}),i}function cw(n){try{return JSON.parse(n.content)}catch(t){throw new iw(t.message,n)}}function uw(n,t){var e=n.content,r=n.status;try{var i=JSON.parse(e);return"error"in i?new ow(i.message,r,i.error,t):new Kl(i.message,r,t)}catch{}return new Kl(e,r,t)}function fw(n){return n.map(function(t){return bg(t)})}function bg(n){var t=n.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return Ut(Ut({},n),{},{request:Ut(Ut({},n.request),{},{headers:Ut(Ut({},n.request.headers),t)})})}var dw=["appId","apiKey","authMode","algoliaAgents"],hw=["params"],Gf="5.19.0";function mw(n){return[{url:"".concat(n,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(n,".algolia.net"),accept:"write",protocol:"https"}].concat(function(t){for(var e=t,r=t.length-1;r>0;r--){var i=Math.floor(Math.random()*(r+1)),o=t[r];e[r]=t[i],e[i]=o}return e}([{url:"".concat(n,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(n,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(n,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}]))}var $l="3.8.3",pw=["footer","searchBox"];function gw(n){var t=n.appId,e=n.apiKey,r=n.indexName,i=n.placeholder,o=i===void 0?"Search docs":i,s=n.searchParameters,a=n.maxResultsPerGroup,l=n.onClose,c=l===void 0?q1:l,u=n.transformItems,f=u===void 0?Kf:u,h=n.hitComponent,d=h===void 0?B1:h,g=n.resultsFooterComponent,m=g===void 0?function(){return null}:g,p=n.navigator,v=n.initialScrollY,b=v===void 0?0:v,_=n.transformSearchClient,I=_===void 0?Kf:_,y=n.disableUserPersonalization,A=y!==void 0&&y,w=n.initialQuery,F=w===void 0?"":w,T=n.translations,P=T===void 0?{}:T,L=n.getMissingResultsUrl,B=n.insights,M=B!==void 0&&B,j=P.footer,V=P.searchBox,U=tr(P,pw),H=an(k.useState({query:"",collections:[],completion:null,context:{},isOpen:!1,activeItemId:null,status:"idle"}),2),C=H[0],G=H[1],J=k.useRef(null),X=k.useRef(null),Dt=k.useRef(null),Bt=k.useRef(null),Pt=k.useRef(null),K=k.useRef(10),ye=k.useRef(typeof window<"u"?window.getSelection().toString().slice(0,64):"").current,Yt=k.useRef(F||ye).current,Te=function(dt,Kt,Ne){return k.useMemo(function(){var Ue=function(x,Z){if(!x||typeof x!="string")throw new Error("`appId` is missing.");if(!Z||typeof Z!="string")throw new Error("`apiKey` is missing.");return function(W){var tt=W.appId,it=W.apiKey,$t=W.authMode,ve=W.algoliaAgents,Ht=tr(W,dw),Et=function(Q,ot){var fe=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"WithinHeaders",Je={"x-algolia-api-key":ot,"x-algolia-application-id":Q};return{headers:function(){return fe==="WithinHeaders"?Je:{}},queryParameters:function(){return fe==="WithinQueryParameters"?Je:{}}}}(tt,it,$t),At=function(Q){var ot=Q.hosts,fe=Q.hostsCache,Je=Q.baseHeaders,gn=Q.logger,xn=Q.baseQueryParameters,Nt=Q.algoliaAgent,Be=Q.timeouts,Ve=Q.requester,Ft=Q.requestsCache,ht=Q.responsesCache;function Xt(Fe){return pe.apply(this,arguments)}function pe(){return(pe=ll(Qr().mark(function Fe(Me){var Pe,rr,He,nn,Or;return Qr().wrap(function(ir){for(;;)switch(ir.prev=ir.next){case 0:return ir.next=2,Promise.all(Me.map(function(Qe){return fe.get(Qe,function(){return Promise.resolve(Qf(Qe))})}));case 2:return Pe=ir.sent,rr=Pe.filter(function(Qe){return Qe.isUp()}),He=Pe.filter(function(Qe){return Qe.isTimedOut()}),nn=[].concat(Do(rr),Do(He)),Or=nn.length>0?nn:Me,ir.abrupt("return",{hosts:Or,getTimeout:function(Qe,oi){return(He.length===0&&Qe===0?1:He.length+3+Qe)*oi}});case 8:case"end":return ir.stop()}},Fe)}))).apply(this,arguments)}function we(Fe,Me){return zn.apply(this,arguments)}function zn(){return zn=ll(Qr().mark(function Fe(Me,Pe){var rr,He,nn,Or,ir,Qe,oi,Ra,xr,ds,Ua,Pc,Va,xa=arguments;return Qr().wrap(function(si){for(;;)switch(si.prev=si.next){case 0:if(rr=!(xa.length>2&&xa[2]!==void 0)||xa[2],He=[],nn=aw(Me,Pe),Or=lw(Je,Me.headers,Pe.headers),ir=Me.method==="GET"?Ut(Ut({},Me.data),Pe.data):{},Qe=Ut(Ut(Ut({},xn),Me.queryParameters),ir),Nt.value&&(Qe["x-algolia-agent"]=Nt.value),Pe&&Pe.queryParameters)for(oi=0,Ra=Object.keys(Pe.queryParameters);oi<Ra.length;oi++)xr=Ra[oi],Pe.queryParameters[xr]&&Object.prototype.toString.call(Pe.queryParameters[xr])!=="[object Object]"?Qe[xr]=Pe.queryParameters[xr].toString():Qe[xr]=Pe.queryParameters[xr];return ds=0,Ua=function(){var Zi=ll(Qr().mark(function Nc(hs,za){var to,ms,Wa,Ha,kr,Cc;return Qr().wrap(function(Pn){for(;;)switch(Pn.prev=Pn.next){case 0:if((to=hs.pop())!==void 0){Pn.next=3;break}throw new rw(fw(He));case 3:return ms=Ut(Ut({},Be),Pe.timeouts),Wa={data:nn,headers:Or,method:Me.method,url:sw(to,Me.path,Qe),connectTimeout:za(ds,ms.connect),responseTimeout:za(ds,rr?ms.read:ms.write)},Ha=function(gs){var jc={request:Wa,response:gs,host:to,triesLeft:hs.length};return He.push(jc),jc},Pn.next=8,Ve.send(Wa);case 8:if(qa=(Fc=kr=Pn.sent).isTimedOut,ps=Fc.status,!(qa||function(gs){return!gs.isTimedOut&&!~~gs.status}({isTimedOut:qa,status:ps})||~~(ps/100)!=2&&~~(ps/100)!=4)){Pn.next=16;break}return Cc=Ha(kr),kr.isTimedOut&&ds++,gn.info("Retryable failure",bg(Cc)),Pn.next=15,fe.set(to,Qf(to,kr.isTimedOut?"timed out":"down"));case 15:return Pn.abrupt("return",Ua(hs,za));case 16:if(~~(kr.status/100)!=2){Pn.next=18;break}return Pn.abrupt("return",cw(kr));case 18:throw Ha(kr),uw(kr,He);case 20:case"end":return Pn.stop()}var Fc,qa,ps},Nc)}));return function(Nc,hs){return Zi.apply(this,arguments)}}(),Pc=ot.filter(function(Zi){return Zi.accept==="readWrite"||(rr?Zi.accept==="read":Zi.accept==="write")}),si.next=13,Xt(Pc);case 13:return Va=si.sent,si.abrupt("return",Ua(Do(Va.hosts).reverse(),Va.getTimeout));case 15:case"end":return si.stop()}},Fe)})),zn.apply(this,arguments)}return{hostsCache:fe,requester:Ve,timeouts:Be,logger:gn,algoliaAgent:Nt,baseHeaders:Je,baseQueryParameters:xn,hosts:ot,request:function(Fe){var Me=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Pe=Fe.useReadTransporter||Fe.method==="GET";if(!Pe)return we(Fe,Me,Pe);var rr=function(){return we(Fe,Me)};if((Me.cacheable||Fe.cacheable)!==!0)return rr();var He={request:Fe,requestOptions:Me,transporter:{queryParameters:xn,headers:Je}};return ht.get(He,function(){return Ft.get(He,function(){return Ft.set(He,rr()).then(function(nn){return Promise.all([Ft.delete(He),nn])},function(nn){return Promise.all([Ft.delete(He),Promise.reject(nn)])}).then(function(nn){var Or=an(nn,2);return Or[0],Or[1]})})},{miss:function(nn){return ht.set(He,nn)}})},requestsCache:Ft,responsesCache:ht}}(Ut(Ut({hosts:mw(tt)},Ht),{},{algoliaAgent:nw({algoliaAgents:ve,client:"Lite",version:Gf}),baseHeaders:Ut(Ut({"content-type":"text/plain"},Et.headers()),Ht.baseHeaders),baseQueryParameters:Ut(Ut({},Et.queryParameters()),Ht.baseQueryParameters)}));return{transporter:At,appId:tt,apiKey:it,clearCache:function(){return Promise.all([At.requestsCache.clear(),At.responsesCache.clear()]).then(function(){})},get _ua(){return At.algoliaAgent.value},addAlgoliaAgent:function(Q,ot){At.algoliaAgent.add({segment:Q,version:ot})},setClientApiKey:function(Q){var ot=Q.apiKey;$t&&$t!=="WithinHeaders"?At.baseQueryParameters["x-algolia-api-key"]=ot:At.baseHeaders["x-algolia-api-key"]=ot},searchForHits:function(Q,ot){return this.search(Q,ot)},searchForFacets:function(Q,ot){return this.search(Q,ot)},customPost:function(Q,ot){var fe=Q.path,Je=Q.parameters,gn=Q.body;if(!fe)throw new Error("Parameter `path` is required when calling `customPost`.");var xn={method:"POST",path:"/{path}".replace("{path}",fe),queryParameters:Je||{},headers:{},data:gn||{}};return At.request(xn,ot)},getRecommendations:function(Q,ot){if(Q&&Array.isArray(Q)&&(Q={requests:Q}),!Q)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!Q.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");var fe={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:Q,useReadTransporter:!0,cacheable:!0};return At.request(fe,ot)},search:function(Q,ot){if(Q&&Array.isArray(Q)){var fe={requests:Q.map(function(gn){var xn=gn.params,Nt=tr(gn,hw);return Nt.type==="facet"?Ut(Ut(Ut({},Nt),xn),{},{type:"facet"}):Ut(Ut(Ut({},Nt),xn),{},{facet:void 0,maxFacetHits:void 0,facetQuery:void 0})})};Q=fe}if(!Q)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!Q.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");var Je={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:Q,useReadTransporter:!0,cacheable:!0};return At.request(Je,ot)}}}(Ut({appId:x,apiKey:Z,timeouts:{connect:1e3,read:2e3,write:3e4},logger:{debug:function(W,tt){return Promise.resolve()},info:function(W,tt){return Promise.resolve()},error:function(W,tt){return Promise.resolve()}},requester:{send:function(W){return new Promise(function(tt){var it=new XMLHttpRequest;it.open(W.method,W.url,!0),Object.keys(W.headers).forEach(function(Et){return it.setRequestHeader(Et,W.headers[Et])});var $t,ve=function(Et,At){return setTimeout(function(){it.abort(),tt({status:0,content:At,isTimedOut:!0})},Et)},Ht=ve(W.connectTimeout,"Connection timeout");it.onreadystatechange=function(){it.readyState>it.OPENED&&$t===void 0&&(clearTimeout(Ht),$t=ve(W.responseTimeout,"Socket timeout"))},it.onerror=function(){it.status===0&&(clearTimeout(Ht),clearTimeout($t),tt({content:it.responseText||"Network request failed",status:it.status,isTimedOut:!1}))},it.onload=function(){clearTimeout(Ht),clearTimeout($t),tt({content:it.responseText,status:it.status,isTimedOut:!1})},it.send(W.data)})}},algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:pl(),requestsCache:pl({serializable:!1}),hostsCache:So({caches:[ew({key:"".concat(Gf,"-").concat(x)}),pl()]})},void 0))}(dt,Kt);return Ue.addAlgoliaAgent("docsearch",$l),/docsearch.js \(.*\)/.test(Ue.transporter.algoliaAgent.value)===!1&&Ue.addAlgoliaAgent("docsearch-react",$l),Ne(Ue)},[dt,Kt,Ne])}(t,e,I),We=k.useRef($f({key:"__DOCSEARCH_FAVORITE_SEARCHES__".concat(r),limit:10})).current,pn=k.useRef($f({key:"__DOCSEARCH_RECENT_SEARCHES__".concat(r),limit:We.getAll().length===0?7:4})).current,Re=k.useCallback(function(dt){if(!A){var Kt=dt.type==="content"?dt.__docsearch_parent:dt;Kt&&We.getAll().findIndex(function(Ne){return Ne.objectID===Kt.objectID})===-1&&pn.add(Kt)}},[We,pn,A]),Mn=k.useCallback(function(dt){if(C.context.algoliaInsightsPlugin&&dt.__autocomplete_id){var Kt=dt,Ne={eventName:"Item Selected",index:Kt.__autocomplete_indexName,items:[Kt],positions:[dt.__autocomplete_id],queryID:Kt.__autocomplete_queryID};C.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(Ne)}},[C.context.algoliaInsightsPlugin]),fn=k.useMemo(function(){return E1({id:"docsearch",defaultActiveItemId:0,placeholder:o,openOnFocus:!0,initialState:{query:Yt,context:{searchSuggestions:[]}},insights:M,navigator:p,onStateChange:function(dt){G(dt.state)},getSources:function(dt){var Kt=dt.query,Ne=dt.state,Ue=dt.setContext,x=dt.setStatus;if(!Kt)return A?[]:[{sourceId:"recentSearches",onSelect:function(W){var tt=W.item,it=W.event;Re(tt),Ns(it)||c()},getItemUrl:function(W){return W.item.url},getItems:function(){return pn.getAll()}},{sourceId:"favoriteSearches",onSelect:function(W){var tt=W.item,it=W.event;Re(tt),Ns(it)||c()},getItemUrl:function(W){return W.item.url},getItems:function(){return We.getAll()}}];var Z=!!M;return Te.search({requests:[Ut({query:Kt,indexName:r,attributesToRetrieve:["hierarchy.lvl0","hierarchy.lvl1","hierarchy.lvl2","hierarchy.lvl3","hierarchy.lvl4","hierarchy.lvl5","hierarchy.lvl6","content","type","url"],attributesToSnippet:["hierarchy.lvl1:".concat(K.current),"hierarchy.lvl2:".concat(K.current),"hierarchy.lvl3:".concat(K.current),"hierarchy.lvl4:".concat(K.current),"hierarchy.lvl5:".concat(K.current),"hierarchy.lvl6:".concat(K.current),"content:".concat(K.current)],snippetEllipsisText:"…",highlightPreTag:"<mark>",highlightPostTag:"</mark>",hitsPerPage:20,clickAnalytics:Z},s)]}).catch(function(W){throw W.name==="RetryError"&&x("error"),W}).then(function(W){var tt=W.results[0],it=tt.hits,$t=tt.nbHits,ve=Yf(it,function(Et){return pg(Et)},a);Ne.context.searchSuggestions.length<Object.keys(ve).length&&Ue({searchSuggestions:Object.keys(ve)}),Ue({nbHits:$t});var Ht={};return Z&&(Ht={__autocomplete_indexName:r,__autocomplete_queryID:tt.queryID,__autocomplete_algoliaCredentials:{appId:t,apiKey:e}}),Object.values(ve).map(function(Et,At){return{sourceId:"hits".concat(At),onSelect:function(Q){var ot=Q.item,fe=Q.event;Re(ot),Ns(fe)||c()},getItemUrl:function(Q){return Q.item.url},getItems:function(){return Object.values(Yf(Et,function(Q){return Q.hierarchy.lvl1},a)).map(f).map(function(Q){return Q.map(function(ot){var fe=null,Je=Q.find(function(gn){return gn.type==="lvl1"&&gn.hierarchy.lvl1===ot.hierarchy.lvl1});return ot.type!=="lvl1"&&Je&&(fe=Je),Ut(Ut({},ot),{},{__docsearch_parent:fe},Ht)})}).flat()}}})})}})},[r,s,a,Te,c,pn,We,Re,Yt,o,p,f,A,M,t,e]),Vn=fn.getEnvironmentProps,Vr=fn.getRootProps,et=fn.refresh;return function(dt){var Kt=dt.getEnvironmentProps,Ne=dt.panelElement,Ue=dt.formElement,x=dt.inputElement;k.useEffect(function(){if(Ne&&Ue&&x){var Z=Kt({panelElement:Ne,formElement:Ue,inputElement:x}),W=Z.onTouchStart,tt=Z.onTouchMove;return window.addEventListener("touchstart",W),window.addEventListener("touchmove",tt),function(){window.removeEventListener("touchstart",W),window.removeEventListener("touchmove",tt)}}},[Kt,Ne,Ue,x])}({getEnvironmentProps:Vn,panelElement:Bt.current,formElement:Dt.current,inputElement:Pt.current}),function(dt){var Kt=dt.container;k.useEffect(function(){if(Kt){var Ne=Kt.querySelectorAll("a[href]:not([disabled]), button:not([disabled]), input:not([disabled])"),Ue=Ne[0],x=Ne[Ne.length-1];return Kt.addEventListener("keydown",Z),function(){Kt.removeEventListener("keydown",Z)}}function Z(W){W.key==="Tab"&&(W.shiftKey?document.activeElement===Ue&&(W.preventDefault(),x.focus()):document.activeElement===x&&(W.preventDefault(),Ue.focus()))}},[Kt])}({container:J.current}),k.useEffect(function(){return document.body.classList.add("DocSearch--active"),function(){var dt,Kt;document.body.classList.remove("DocSearch--active"),(dt=(Kt=window).scrollTo)===null||dt===void 0||dt.call(Kt,0,b)}},[]),k.useLayoutEffect(function(){var dt=window.innerWidth-document.body.clientWidth;return document.body.style.marginRight="".concat(dt,"px"),function(){document.body.style.marginRight="0px"}},[]),k.useEffect(function(){window.matchMedia("(max-width: 768px)").matches&&(K.current=5)},[]),k.useEffect(function(){Bt.current&&(Bt.current.scrollTop=0)},[C.query]),k.useEffect(function(){Yt.length>0&&(et(),Pt.current&&Pt.current.focus())},[Yt,et]),k.useEffect(function(){function dt(){if(X.current){var Kt=.01*window.innerHeight;X.current.style.setProperty("--docsearch-vh","".concat(Kt,"px"))}}return dt(),window.addEventListener("resize",dt),function(){window.removeEventListener("resize",dt)}},[]),k.createElement("div",Ke({ref:J},Vr({"aria-expanded":!0}),{className:["DocSearch","DocSearch-Container",C.status==="stalled"&&"DocSearch-Container--Stalled",C.status==="error"&&"DocSearch-Container--Errored"].filter(Boolean).join(" "),role:"button",tabIndex:0,onMouseDown:function(dt){dt.target===dt.currentTarget&&c()}}),k.createElement("div",{className:"DocSearch-Modal",ref:X},k.createElement("header",{className:"DocSearch-SearchBar",ref:Dt},k.createElement(Z1,Ke({},fn,{state:C,autoFocus:Yt.length===0,inputRef:Pt,isFromSelection:!!Yt&&Yt===ye,translations:V,onClose:c}))),k.createElement("div",{className:"DocSearch-Dropdown",ref:Bt},k.createElement(G1,Ke({},fn,{indexName:r,state:C,hitComponent:d,resultsFooterComponent:m,disableUserPersonalization:A,recentSearches:pn,favoriteSearches:We,inputRef:Pt,translations:U,getMissingResultsUrl:L,onItemClick:function(dt,Kt){Mn(dt),Re(dt),Ns(Kt)||c()}}))),k.createElement("footer",{className:"DocSearch-Footer"},k.createElement(T1,{translations:j}))))}function _w(n){var t,e,r=k.useRef(null),i=an(k.useState(!1),2),o=i[0],s=i[1],a=an(k.useState((n==null?void 0:n.initialQuery)||void 0),2),l=a[0],c=a[1],u=k.useCallback(function(){s(!0)},[s]),f=k.useCallback(function(){s(!1),c(n==null?void 0:n.initialQuery)},[s,n.initialQuery]);return function(h){var d=h.isOpen,g=h.onOpen,m=h.onClose,p=h.onInput,v=h.searchButtonRef;k.useEffect(function(){function b(_){var I;if(_.code==="Escape"&&d||((I=_.key)===null||I===void 0?void 0:I.toLowerCase())==="k"&&(_.metaKey||_.ctrlKey)||!function(y){var A=y.target,w=A.tagName;return A.isContentEditable||w==="INPUT"||w==="SELECT"||w==="TEXTAREA"}(_)&&_.key==="/"&&!d)return _.preventDefault(),void(d?m():document.body.classList.contains("DocSearch--active")||g());v&&v.current===document.activeElement&&p&&/[a-zA-Z0-9]/.test(String.fromCharCode(_.keyCode))&&p(_)}return window.addEventListener("keydown",b),function(){window.removeEventListener("keydown",b)}},[d,g,m,p,v])}({isOpen:o,onOpen:u,onClose:f,onInput:k.useCallback(function(h){s(!0),c(h.key)},[s,c]),searchButtonRef:r}),k.createElement(k.Fragment,null,k.createElement(K0,{ref:r,translations:n==null||(t=n.translations)===null||t===void 0?void 0:t.button,onClick:u}),o&&ng(k.createElement(gw,Ke({},n,{initialScrollY:window.scrollY,initialQuery:l,translations:n==null||(e=n.translations)===null||e===void 0?void 0:e.modal,onClose:f})),document.body))}function bw(n){ig(k.createElement(_w,Cl({},n,{transformSearchClient:function(t){return t.addAlgoliaAgent("docsearch.js",$l),n.transformSearchClient?n.transformSearchClient(t):t}})),function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;return typeof t=="string"?e.document.querySelector(t):t}(n.container,n.environment))}function yw(n){let t;return{c(){t=Y("div"),this.h()},l(e){t=q(e,"DIV",{id:!0,class:!0}),$(t).forEach(S),this.h()},h(){N(t,"id","docsearch"),N(t,"class",n[0]+" "+n[1]+" "+n[2]+" "+n[3])},m(e,r){R(e,t,r)},p:Qt,i:Qt,o:Qt,d(e){e&&S(t)}}}function vw(n,t,e){let{algolia:r}=t;Pr(()=>{bw({container:"#docsearch",appId:r==null?void 0:r.appId,apiKey:r==null?void 0:r.apiKey,indexName:r==null?void 0:r.indexName})});const i=`
	**:[&.DocSearch-Button]:bg-base-100
	**:[&.DocSearch-Button]:border-base-300
	**:[&.DocSearch-Button]:hover:bg-base-200/40
	**:[&.DocSearch-Button]:transition-colors
	**:[&.DocSearch-Button]:duration-200
	**:[&.DocSearch-Button]:rounded-md
	**:[&.DocSearch-Button]:flex
	**:[&.DocSearch-Button]:gap-16
	**:[&.DocSearch-Button]:cursor-pointer
	**:[&.DocSearch-Button]:py-1
	**:[&.DocSearch-Button]:pl-2
	**:[&.DocSearch-Button]:sm:pr-1
	**:[&.DocSearch-Button]:pr-20
	**:[&.DocSearch-Button]:sm:text-xs
	**:[&.DocSearch-Button]:border
	**:[&.DocSearch-Button]:font-sans
	**:[&.DocSearch-Button]:font-medium
	**:[&.DocSearch-Button]:items-center;
	`,o=`
	**:[&DocSearch-Button-Placeholder]:text-base-content-muted
	`,s=`
	**:[&.DocSearch-Search-Icon]:hidden
	`,a=`
	**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:text-base-content-muted
	**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:*:text-base-content-muted
	`;return n.$$set=l=>{"algolia"in l&&e(4,r=l.algolia)},[i,o,s,a,r]}class ww extends ae{constructor(t){super(),le(this,t,vw,yw,ee,{algolia:4})}}function Sw(n){let t;const e=n[3].default,r=Oe(e,n,n[11],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&2048)&&ke(r,e,i,i[11],t?Ee(e,i[11],o,null):De(i[11]),null)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function Iw(n){let t,e;const r=[{class:Fi("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",n[1]&&"pl-8",n[0])},n[2]];let i={$$slots:{default:[Sw]},$$scope:{ctx:n}};for(let o=0;o<r.length;o+=1)i=me(i,r[o]);return t=new Tv({props:i}),t.$on("click",n[4]),t.$on("keydown",n[5]),t.$on("focusin",n[6]),t.$on("focusout",n[7]),t.$on("pointerdown",n[8]),t.$on("pointerleave",n[9]),t.$on("pointermove",n[10]),{c(){ct(t.$$.fragment)},l(o){lt(t.$$.fragment,o)},m(o,s){at(t,o,s),e=!0},p(o,[s]){const a=s&7?An(r,[s&3&&{class:Fi("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",o[1]&&"pl-8",o[0])},s&4&&Ql(o[2])]):{};s&2048&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(o){e||(O(t.$$.fragment,o),e=!0)},o(o){D(t.$$.fragment,o),e=!1},d(o){st(t,o)}}}function Ow(n,t,e){const r=["class","inset"];let i=ln(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t,{inset:l=void 0}=t;function c(p){lr.call(this,n,p)}function u(p){lr.call(this,n,p)}function f(p){lr.call(this,n,p)}function h(p){lr.call(this,n,p)}function d(p){lr.call(this,n,p)}function g(p){lr.call(this,n,p)}function m(p){lr.call(this,n,p)}return n.$$set=p=>{t=me(me({},t),ri(p)),e(2,i=ln(t,r)),"class"in p&&e(0,a=p.class),"inset"in p&&e(1,l=p.inset),"$$scope"in p&&e(11,s=p.$$scope)},[a,l,i,o,c,u,f,h,d,g,m,s]}class Mc extends ae{constructor(t){super(),le(this,t,Ow,Iw,ee,{class:0,inset:1})}}function kw(n){let t;const e=n[5].default,r=Oe(e,n,n[7],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&128)&&ke(r,e,i,i[7],t?Ee(e,i[7],o,null):De(i[7]),null)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function Dw(n){let t,e;const r=[{transition:n[2]},{transitionConfig:n[3]},{sideOffset:n[1]},{class:Fi("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",n[0])},n[4]];let i={$$slots:{default:[kw]},$$scope:{ctx:n}};for(let o=0;o<r.length;o+=1)i=me(i,r[o]);return t=new e0({props:i}),t.$on("keydown",n[6]),{c(){ct(t.$$.fragment)},l(o){lt(t.$$.fragment,o)},m(o,s){at(t,o,s),e=!0},p(o,[s]){const a=s&31?An(r,[s&4&&{transition:o[2]},s&8&&{transitionConfig:o[3]},s&2&&{sideOffset:o[1]},s&1&&{class:Fi("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",o[0])},s&16&&Ql(o[4])]):{};s&128&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(o){e||(O(t.$$.fragment,o),e=!0)},o(o){D(t.$$.fragment,o),e=!1},d(o){st(t,o)}}}function Ew(n,t,e){const r=["class","sideOffset","transition","transitionConfig"];let i=ln(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t,{sideOffset:l=4}=t,{transition:c=qg}=t,{transitionConfig:u=void 0}=t;function f(h){lr.call(this,n,h)}return n.$$set=h=>{t=me(me({},t),ri(h)),e(4,i=ln(t,r)),"class"in h&&e(0,a=h.class),"sideOffset"in h&&e(1,l=h.sideOffset),"transition"in h&&e(2,c=h.transition),"transitionConfig"in h&&e(3,u=h.transitionConfig),"$$scope"in h&&e(7,s=h.$$scope)},[a,l,c,u,i,o,f,s]}class Aw extends ae{constructor(t){super(),le(this,t,Ew,Dw,ee,{class:0,sideOffset:1,transition:2,transitionConfig:3})}}function Tw(n){let t,e,r;const i=n[3].default,o=Oe(i,n,n[2],null);let s=[{class:e=Fi("ml-auto text-xs tracking-widest opacity-60",n[0])},n[1]],a={};for(let l=0;l<s.length;l+=1)a=me(a,s[l]);return{c(){t=Y("span"),o&&o.c(),this.h()},l(l){t=q(l,"SPAN",{class:!0});var c=$(t);o&&o.l(c),c.forEach(S),this.h()},h(){$e(t,a)},m(l,c){R(l,t,c),o&&o.m(t,null),r=!0},p(l,[c]){o&&o.p&&(!r||c&4)&&ke(o,i,l,l[2],r?Ee(i,l[2],c,null):De(l[2]),null),$e(t,a=An(s,[(!r||c&1&&e!==(e=Fi("ml-auto text-xs tracking-widest opacity-60",l[0])))&&{class:e},c&2&&l[1]]))},i(l){r||(O(o,l),r=!0)},o(l){D(o,l),r=!1},d(l){l&&S(t),o&&o.d(l)}}}function Bw(n,t,e){const r=["class"];let i=ln(t,r),{$$slots:o={},$$scope:s}=t,{class:a=void 0}=t;return n.$$set=l=>{t=me(me({},t),ri(l)),e(1,i=ln(t,r)),"class"in l&&e(0,a=l.class),"$$scope"in l&&e(2,s=l.$$scope)},[a,i,s,o]}class yg extends ae{constructor(t){super(),le(this,t,Bw,Tw,ee,{class:0})}}const Mw=Vv,Pw=l0,Nw=jv;function Cw(n){let t,e;return t=new Tn({props:{src:$g,class:"h-6 w-6"}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p:Qt,i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function Fw(n){let t,e;return t=new u_({props:{builders:[n[15]],variant:"ghost",size:"sm",class:"px-1","aria-label":"Menu",$$slots:{default:[Cw]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i&32768&&(o.builders=[r[15]]),i&65536&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function jw(n){let t;return{c(){t=wt("⌘P")},l(e){t=vt(e,"⌘P")},m(e,r){R(e,t,r)},d(e){e&&S(t)}}}function Lw(n){let t,e,r;return e=new yg({props:{$$slots:{default:[jw]},$$scope:{ctx:n}}}),{c(){t=wt(`Print PDF
				`),ct(e.$$.fragment)},l(i){t=vt(i,`Print PDF
				`),lt(e.$$.fragment,i)},m(i,o){R(i,t,o),at(e,i,o),r=!0},p(i,o){const s={};o&65536&&(s.$$scope={dirty:o,ctx:i}),e.$set(s)},i(i){r||(O(e.$$.fragment,i),r=!0)},o(i){D(e.$$.fragment,i),r=!1},d(i){i&&S(t),st(e,i)}}}function Xf(n){let t,e;return t=new Mc({props:{$$slots:{default:[Rw]},$$scope:{ctx:n}}}),t.$on("click",n[11]),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i&65544&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function Rw(n){let t=n[3]?"Hide ":"Show ",e,r;return{c(){e=wt(t),r=wt(" Queries")},l(i){e=vt(i,t),r=vt(i," Queries")},m(i,o){R(i,e,o),R(i,r,o)},p(i,o){o&8&&t!==(t=i[3]?"Hide ":"Show ")&&Le(e,t)},d(i){i&&(S(e),S(r))}}}function Uw(n){let t,e;return t=new Mc({props:{$$slots:{default:[xw]},$$scope:{ctx:n}}}),t.$on("click",n[12]),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i&65542&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function Vw(n){let t,e,r,i,o;return i=new Tn({props:{src:n[1],class:"h-4 w-4 ml-1"}}),{c(){t=Y("span"),e=wt(n[2]),r=rt(),ct(i.$$.fragment),this.h()},l(s){t=q(s,"SPAN",{class:!0});var a=$(t);e=vt(a,n[2]),a.forEach(S),r=nt(s),lt(i.$$.fragment,s),this.h()},h(){N(t,"class","text-xs leading-none")},m(s,a){R(s,t,a),z(t,e),R(s,r,a),at(i,s,a),o=!0},p(s,a){(!o||a&4)&&Le(e,s[2]);const l={};a&2&&(l.src=s[1]),i.$set(l)},i(s){o||(O(i.$$.fragment,s),o=!0)},o(s){D(i.$$.fragment,s),o=!1},d(s){s&&(S(t),S(r)),st(i,s)}}}function xw(n){let t,e,r;return e=new yg({props:{class:"tracking-normal flex flex-row items-center",$$slots:{default:[Vw]},$$scope:{ctx:n}}}),{c(){t=wt(`Appearance
					`),ct(e.$$.fragment)},l(i){t=vt(i,`Appearance
					`),lt(e.$$.fragment,i)},m(i,o){R(i,t,o),at(e,i,o),r=!0},p(i,o){const s={};o&65542&&(s.$$scope={dirty:o,ctx:i}),e.$set(s)},i(i){r||(O(e.$$.fragment,i),r=!0)},o(i){D(e.$$.fragment,i),r=!1},d(i){i&&S(t),st(e,i)}}}function zw(n){let t,e,r,i,o;t=new Mc({props:{$$slots:{default:[Lw]},$$scope:{ctx:n}}}),t.$on("click",n[4]);let s=!n[0]&&Xf(n),a=n[8].appearance.switcher&&Uw(n);return{c(){ct(t.$$.fragment),e=rt(),s&&s.c(),r=rt(),a&&a.c(),i=ft()},l(l){lt(t.$$.fragment,l),e=nt(l),s&&s.l(l),r=nt(l),a&&a.l(l),i=ft()},m(l,c){at(t,l,c),R(l,e,c),s&&s.m(l,c),R(l,r,c),a&&a.m(l,c),R(l,i,c),o=!0},p(l,c){const u={};c&65536&&(u.$$scope={dirty:c,ctx:l}),t.$set(u),l[0]?s&&(jt(),D(s,1,1,()=>{s=null}),Lt()):s?(s.p(l,c),c&1&&O(s,1)):(s=Xf(l),s.c(),O(s,1),s.m(r.parentNode,r)),l[8].appearance.switcher&&a.p(l,c)},i(l){o||(O(t.$$.fragment,l),O(s),O(a),o=!0)},o(l){D(t.$$.fragment,l),D(s),D(a),o=!1},d(l){l&&(S(e),S(r),S(i)),st(t,l),s&&s.d(l),a&&a.d(l)}}}function Ww(n){let t,e,r,i;t=new Nw({props:{$$slots:{default:[zw]},$$scope:{ctx:n}}});let o=vl;return{c(){ct(t.$$.fragment),e=rt(),r=ft()},l(s){lt(t.$$.fragment,s),e=nt(s),r=ft()},m(s,a){at(t,s,a),R(s,e,a),R(s,r,a),i=!0},p(s,a){const l={};a&65551&&(l.$$scope={dirty:a,ctx:s}),t.$set(l)},i(s){i||(O(t.$$.fragment,s),O(o),i=!0)},o(s){D(t.$$.fragment,s),D(o),i=!1},d(s){s&&(S(e),S(r)),st(t,s)}}}function Hw(n){let t,e,r,i;return t=new Pw({props:{asChild:!0,$$slots:{default:[Fw,({builder:o})=>({15:o}),({builder:o})=>o?32768:0]},$$scope:{ctx:n}}}),r=new Aw({props:{class:"w-52 text-xs",$$slots:{default:[Ww]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment),e=rt(),ct(r.$$.fragment)},l(o){lt(t.$$.fragment,o),e=nt(o),lt(r.$$.fragment,o)},m(o,s){at(t,o,s),R(o,e,s),at(r,o,s),i=!0},p(o,s){const a={};s&98304&&(a.$$scope={dirty:s,ctx:o}),t.$set(a);const l={};s&65551&&(l.$$scope={dirty:s,ctx:o}),r.$set(l)},i(o){i||(O(t.$$.fragment,o),O(r.$$.fragment,o),i=!0)},o(o){D(t.$$.fragment,o),D(r.$$.fragment,o),i=!1},d(o){o&&S(e),st(t,o),st(r,o)}}}function qw(n){let t,e;return t=new Mw({props:{$$slots:{default:[Hw]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,[i]){const o={};i&65551&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function Yw(n,t,e){let r,i,o,s,a;Ae(n,Kc,b=>e(3,a=b));const l=new Event("export-beforeprint"),c=new Event("export-afterprint");function u(){window.dispatchEvent(l),setTimeout(()=>window.print(),0),setTimeout(()=>window.dispatchEvent(c),0)}const{selectedAppearance:f,activeAppearance:h,cycleAppearance:d,themesConfig:g}=hh();Ae(n,f,b=>e(10,s=b)),Ae(n,h,b=>e(9,o=b));let{neverShowQueries:m}=t;const p=b=>{b.preventDefault(),Kc.update(_=>!_)},v=b=>{b.preventDefault(),d()};return n.$$set=b=>{"neverShowQueries"in b&&e(0,m=b.neverShowQueries)},n.$$.update=()=>{n.$$.dirty&1024&&e(2,r=s==="system"?"System":s==="light"?"Light":"Dark"),n.$$.dirty&512&&e(1,i=o==="light"?Yg:Kg)},[m,i,r,a,u,f,h,d,g,o,s,p,v]}class Kw extends ae{constructor(t){super(),le(this,t,Yw,qw,ee,{neverShowQueries:0})}}function $w(n){let t,e,r,i,o,s,a,l,c,u,f;const h=[Gw,Qw],d=[];function g(m,p){return m[0]?0:1}return r=g(n),i=d[r]=h[r](n),l=new Sc({props:{logo:n[2],lightLogo:n[3],darkLogo:n[4],title:n[1]}}),{c(){t=Y("div"),e=Y("button"),i.c(),s=rt(),a=Y("a"),ct(l.$$.fragment),this.h()},l(m){t=q(m,"DIV",{class:!0});var p=$(t);e=q(p,"BUTTON",{type:!0,class:!0});var v=$(e);i.l(v),v.forEach(S),s=nt(p),a=q(p,"A",{href:!0,class:!0});var b=$(a);lt(l.$$.fragment,b),b.forEach(S),p.forEach(S),this.h()},h(){N(e,"type","button"),N(e,"class",o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+(n[9]==="hide"?"block":"md:hidden")),N(a,"href",xt("/")),N(a,"class","text-sm font-bold text-base-content hidden md:block"),N(t,"class","flex gap-x-4 items-center")},m(m,p){R(m,t,p),z(t,e),d[r].m(e,null),z(t,s),z(t,a),at(l,a,null),c=!0,u||(f=he(e,"click",n[15]),u=!0)},p(m,p){let v=r;r=g(m),r!==v&&(jt(),D(d[v],1,1,()=>{d[v]=null}),Lt(),i=d[r],i||(i=d[r]=h[r](m),i.c()),O(i,1),i.m(e,null)),(!c||p&512&&o!==(o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+(m[9]==="hide"?"block":"md:hidden")))&&N(e,"class",o);const b={};p&4&&(b.logo=m[2]),p&8&&(b.lightLogo=m[3]),p&16&&(b.darkLogo=m[4]),p&2&&(b.title=m[1]),l.$set(b)},i(m){c||(O(i),O(l.$$.fragment,m),c=!0)},o(m){D(i),D(l.$$.fragment,m),c=!1},d(m){m&&S(t),d[r].d(),st(l),u=!1,f()}}}function Jw(n){let t,e,r;return e=new Sc({props:{logo:n[2],lightLogo:n[3],darkLogo:n[4],title:n[1]}}),{c(){t=Y("a"),ct(e.$$.fragment),this.h()},l(i){t=q(i,"A",{href:!0,class:!0});var o=$(t);lt(e.$$.fragment,o),o.forEach(S),this.h()},h(){N(t,"href",xt("/")),N(t,"class","block text-sm font-bold text-base-content")},m(i,o){R(i,t,o),at(e,t,null),r=!0},p(i,o){const s={};o&4&&(s.logo=i[2]),o&8&&(s.lightLogo=i[3]),o&16&&(s.darkLogo=i[4]),o&2&&(s.title=i[1]),e.$set(s)},i(i){r||(O(e.$$.fragment,i),r=!0)},o(i){D(e.$$.fragment,i),r=!1},d(i){i&&S(t),st(e)}}}function Qw(n){let t,e="Open sidebar",r,i,o;return i=new Tn({props:{class:"w-5 h-5",src:Jg}}),{c(){t=Y("span"),t.textContent=e,r=rt(),ct(i.$$.fragment),this.h()},l(s){t=q(s,"SPAN",{class:!0,"data-svelte-h":!0}),yr(t)!=="svelte-73kebv"&&(t.textContent=e),r=nt(s),lt(i.$$.fragment,s),this.h()},h(){N(t,"class","sr-only")},m(s,a){R(s,t,a),R(s,r,a),at(i,s,a),o=!0},i(s){o||(O(i.$$.fragment,s),o=!0)},o(s){D(i.$$.fragment,s),o=!1},d(s){s&&(S(t),S(r)),st(i,s)}}}function Gw(n){let t,e="Close sidebar",r,i,o;return i=new Tn({props:{class:"w-5 h-5",src:Js}}),{c(){t=Y("span"),t.textContent=e,r=rt(),ct(i.$$.fragment),this.h()},l(s){t=q(s,"SPAN",{class:!0,"data-svelte-h":!0}),yr(t)!=="svelte-13q18xv"&&(t.textContent=e),r=nt(s),lt(i.$$.fragment,s),this.h()},h(){N(t,"class","sr-only")},m(s,a){R(s,t,a),R(s,r,a),at(i,s,a),o=!0},i(s){o||(O(i.$$.fragment,s),o=!0)},o(s){D(i.$$.fragment,s),o=!1},d(s){s&&(S(t),S(r)),st(i,s)}}}function Zf(n){let t,e;return t=new ww({props:{algolia:n[10]}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i&1024&&(o.algolia=r[10]),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function td(n){let t,e,r,i;return e=new Tn({props:{src:S_,class:"w-4 h-4 text-base-content"}}),{c(){t=Y("a"),ct(e.$$.fragment),this.h()},l(o){t=q(o,"A",{href:!0,class:!0,target:!0,rel:!0});var s=$(t);lt(e.$$.fragment,s),s.forEach(S),this.h()},h(){N(t,"href",r=xt(n[11])),N(t,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),N(t,"target","_blank"),N(t,"rel","noreferrer")},m(o,s){R(o,t,s),at(e,t,null),i=!0},p(o,s){(!i||s&2048&&r!==(r=xt(o[11])))&&N(t,"href",r)},i(o){i||(O(e.$$.fragment,o),i=!0)},o(o){D(e.$$.fragment,o),i=!1},d(o){o&&S(t),st(e)}}}function ed(n){let t,e,r,i;return e=new Tn({props:{src:I_,class:"w-4 h-4 text-base-content"}}),{c(){t=Y("a"),ct(e.$$.fragment),this.h()},l(o){t=q(o,"A",{href:!0,class:!0,target:!0,rel:!0});var s=$(t);lt(e.$$.fragment,s),s.forEach(S),this.h()},h(){N(t,"href",r=xt(n[12])),N(t,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),N(t,"target","_blank"),N(t,"rel","noreferrer")},m(o,s){R(o,t,s),at(e,t,null),i=!0},p(o,s){(!i||s&4096&&r!==(r=xt(o[12])))&&N(t,"href",r)},i(o){i||(O(e.$$.fragment,o),i=!0)},o(o){D(e.$$.fragment,o),i=!1},d(o){o&&S(t),st(e)}}}function nd(n){let t,e,r;return e=new Tn({props:{src:O_,fill:"currentColor",class:"w-4 h-4 text-base-content "}}),{c(){t=Y("a"),ct(e.$$.fragment),this.h()},l(i){t=q(i,"A",{href:!0,class:!0,target:!0,rel:!0});var o=$(t);lt(e.$$.fragment,o),o.forEach(S),this.h()},h(){N(t,"href",n[13]),N(t,"class","hover:bg-gray-50 rounded-lg p-2 transition-all duration-200"),N(t,"target","_blank"),N(t,"rel","noreferrer")},m(i,o){R(i,t,o),at(e,t,null),r=!0},p(i,o){(!r||o&8192)&&N(t,"href",i[13])},i(i){r||(O(e.$$.fragment,i),r=!0)},o(i){D(e.$$.fragment,i),r=!1},d(i){i&&S(t),st(e)}}}function rd(n){let t,e,r,i;return e=new Tn({props:{src:k_,class:"w-4 h-4 text-base-content "}}),{c(){t=Y("a"),ct(e.$$.fragment),this.h()},l(o){t=q(o,"A",{href:!0,class:!0,target:!0,rel:!0});var s=$(t);lt(e.$$.fragment,s),s.forEach(S),this.h()},h(){N(t,"href",r=xt(n[14])),N(t,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),N(t,"target","_blank"),N(t,"rel","noreferrer")},m(o,s){R(o,t,s),at(e,t,null),i=!0},p(o,s){(!i||s&16384&&r!==(r=xt(o[14])))&&N(t,"href",r)},i(o){i||(O(e.$$.fragment,o),i=!0)},o(o){D(e.$$.fragment,o),i=!1},d(o){o&&S(t),st(e)}}}function Xw(n){let t,e,r,i,o,s,a,l,c,u,f,h,d,g,m,p;const v=[Jw,$w],b=[];function _(T,P){return T[8]||T[9]==="never"?0:1}r=_(n),i=b[r]=v[r](n);let I=n[10]&&Zf(n),y=n[11]&&td(n),A=n[12]&&ed(n),w=n[13]&&nd(n),F=n[14]&&rd(n);return g=new Kw({props:{neverShowQueries:n[5]}}),{c(){t=Y("header"),e=Y("div"),i.c(),o=rt(),s=Y("div"),I&&I.c(),a=rt(),l=Y("div"),y&&y.c(),c=rt(),A&&A.c(),u=rt(),w&&w.c(),f=rt(),F&&F.c(),h=rt(),d=Y("div"),ct(g.$$.fragment),this.h()},l(T){t=q(T,"HEADER",{class:!0});var P=$(t);e=q(P,"DIV",{class:!0,style:!0});var L=$(e);i.l(L),o=nt(L),s=q(L,"DIV",{class:!0});var B=$(s);I&&I.l(B),a=nt(B),l=q(B,"DIV",{class:!0});var M=$(l);y&&y.l(M),c=nt(M),A&&A.l(M),u=nt(M),w&&w.l(M),f=nt(M),F&&F.l(M),M.forEach(S),h=nt(B),d=q(B,"DIV",{class:!0});var j=$(d);lt(g.$$.fragment,j),j.forEach(S),B.forEach(S),L.forEach(S),P.forEach(S),this.h()},h(){N(l,"class","flex gap-2 items-center"),N(d,"class","relative"),N(s,"class","flex gap-2 text-sm items-center"),N(e,"class",m=(n[6]?"max-w-full ":n[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"),Qs(e,"max-width",n[7]+"px"),N(t,"class","fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden")},m(T,P){R(T,t,P),z(t,e),b[r].m(e,null),z(e,o),z(e,s),I&&I.m(s,null),z(s,a),z(s,l),y&&y.m(l,null),z(l,c),A&&A.m(l,null),z(l,u),w&&w.m(l,null),z(l,f),F&&F.m(l,null),z(s,h),z(s,d),at(g,d,null),p=!0},p(T,[P]){let L=r;r=_(T),r===L?b[r].p(T,P):(jt(),D(b[L],1,1,()=>{b[L]=null}),Lt(),i=b[r],i?i.p(T,P):(i=b[r]=v[r](T),i.c()),O(i,1),i.m(e,o)),T[10]?I?(I.p(T,P),P&1024&&O(I,1)):(I=Zf(T),I.c(),O(I,1),I.m(s,a)):I&&(jt(),D(I,1,1,()=>{I=null}),Lt()),T[11]?y?(y.p(T,P),P&2048&&O(y,1)):(y=td(T),y.c(),O(y,1),y.m(l,c)):y&&(jt(),D(y,1,1,()=>{y=null}),Lt()),T[12]?A?(A.p(T,P),P&4096&&O(A,1)):(A=ed(T),A.c(),O(A,1),A.m(l,u)):A&&(jt(),D(A,1,1,()=>{A=null}),Lt()),T[13]?w?(w.p(T,P),P&8192&&O(w,1)):(w=nd(T),w.c(),O(w,1),w.m(l,f)):w&&(jt(),D(w,1,1,()=>{w=null}),Lt()),T[14]?F?(F.p(T,P),P&16384&&O(F,1)):(F=rd(T),F.c(),O(F,1),F.m(l,null)):F&&(jt(),D(F,1,1,()=>{F=null}),Lt());const B={};P&32&&(B.neverShowQueries=T[5]),g.$set(B),(!p||P&192&&m!==(m=(T[6]?"max-w-full ":T[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"))&&N(e,"class",m),(!p||P&128)&&Qs(e,"max-width",T[7]+"px")},i(T){p||(O(i),O(I),O(y),O(A),O(w),O(F),O(g.$$.fragment,T),p=!0)},o(T){D(i),D(I),D(y),D(A),D(w),D(F),D(g.$$.fragment,T),p=!1},d(T){T&&S(t),b[r].d(),I&&I.d(),y&&y.d(),A&&A.d(),w&&w.d(),F&&F.d(),st(g)}}}function Zw(n,t,e){let{mobileSidebarOpen:r=void 0}=t,{title:i=void 0}=t,{logo:o=void 0}=t,{lightLogo:s=void 0}=t,{darkLogo:a=void 0}=t,{neverShowQueries:l=void 0}=t,{fullWidth:c=void 0}=t,{maxWidth:u=void 0}=t,{hideSidebar:f=void 0}=t,{sidebarFrontMatter:h=void 0}=t,{algolia:d=void 0}=t,{githubRepo:g=void 0}=t,{xProfile:m=void 0}=t,{blueskyProfile:p=void 0}=t,{slackCommunity:v=void 0}=t;const b=()=>{e(0,r=!r)};return n.$$set=_=>{"mobileSidebarOpen"in _&&e(0,r=_.mobileSidebarOpen),"title"in _&&e(1,i=_.title),"logo"in _&&e(2,o=_.logo),"lightLogo"in _&&e(3,s=_.lightLogo),"darkLogo"in _&&e(4,a=_.darkLogo),"neverShowQueries"in _&&e(5,l=_.neverShowQueries),"fullWidth"in _&&e(6,c=_.fullWidth),"maxWidth"in _&&e(7,u=_.maxWidth),"hideSidebar"in _&&e(8,f=_.hideSidebar),"sidebarFrontMatter"in _&&e(9,h=_.sidebarFrontMatter),"algolia"in _&&e(10,d=_.algolia),"githubRepo"in _&&e(11,g=_.githubRepo),"xProfile"in _&&e(12,m=_.xProfile),"blueskyProfile"in _&&e(13,p=_.blueskyProfile),"slackCommunity"in _&&e(14,v=_.slackCommunity)},[r,i,o,s,a,l,c,u,f,h,d,g,m,p,v,b]}class t2 extends ae{constructor(t){super(),le(this,t,Zw,Xw,ee,{mobileSidebarOpen:0,title:1,logo:2,lightLogo:3,darkLogo:4,neverShowQueries:5,fullWidth:6,maxWidth:7,hideSidebar:8,sidebarFrontMatter:9,algolia:10,githubRepo:11,xProfile:12,blueskyProfile:13,slackCommunity:14})}}function e2(n){return Pr(()=>{}),[]}class n2 extends ae{constructor(t){super(),le(this,t,e2,null,ee,{})}}function r2(n){let t,e='<span class="sr-only">Loading...</span> <div class="h-8 rounded-full bg-base-200 w-48 mb-8"></div> <div class="flex gap-3"><div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[65%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[100%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[75%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div>',r;return{c(){t=Y("div"),t.innerHTML=e,this.h()},l(i){t=q(i,"DIV",{role:!0,class:!0,"data-svelte-h":!0}),yr(t)!=="svelte-1u7962h"&&(t.innerHTML=e),this.h()},h(){N(t,"role","status"),N(t,"class","animate-pulse")},m(i,o){R(i,t,o)},p:Qt,i(i){i&&(r||On(()=>{r=Hi(t,xo,{}),r.start()}))},o:Qt,d(i){i&&S(t)}}}class i2 extends ae{constructor(t){super(),le(this,t,null,r2,ee,{})}}function o2(n){let t,e;const r=n[1].default,i=Oe(r,n,n[0],null);return{c(){t=Y("span"),i&&i.c(),this.h()},l(o){t=q(o,"SPAN",{class:!0});var s=$(t);i&&i.l(s),s.forEach(S),this.h()},h(){N(t,"class","rounded-sm px-0.5 py-[1px] bg-positive/10 border border-positive/20 text-positive text-base sm:text-xs")},m(o,s){R(o,t,s),i&&i.m(t,null),e=!0},p(o,[s]){i&&i.p&&(!e||s&1)&&ke(i,r,o,o[0],e?Ee(r,o[0],s,null):De(o[0]),null)},i(o){e||(O(i,o),e=!0)},o(o){D(i,o),e=!1},d(o){o&&S(t),i&&i.d(o)}}}function s2(n,t,e){let{$$slots:r={},$$scope:i}=t;return n.$$set=o=>{"$$scope"in o&&e(0,i=o.$$scope)},[i,r]}class Bn extends ae{constructor(t){super(),le(this,t,s2,o2,ee,{})}}function id(n,t,e){const r=n.slice();return r[18]=t[e],r}function od(n,t,e){const r=n.slice();return r[21]=t[e],r}function sd(n,t,e){const r=n.slice();return r[24]=t[e],r}function ad(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[24].href.toUpperCase()+"/";return t[27]=e,t}function a2(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[21].href.toUpperCase()+"/";return t[27]=e,t}function ld(n,t,e){const r=n.slice();return r[18]=t[e],r}function cd(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[18].href.toUpperCase()+"/";return t[27]=e,t}function ud(n,t,e){const r=n.slice();return r[18]=t[e],r}function fd(n,t,e){const r=n.slice();return r[21]=t[e],r}function dd(n,t,e){const r=n.slice();return r[24]=t[e],r}function hd(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[24].href.toUpperCase()+"/";return t[27]=e,t}function l2(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[21].href.toUpperCase()+"/";return t[27]=e,t}function md(n,t,e){const r=n.slice();return r[18]=t[e],r}function pd(n){const t=n.slice(),e=t[8].url.pathname.toUpperCase()===t[18].href.toUpperCase()+"/";return t[27]=e,t}function gd(n){let t,e,r,i,o,s,a,l,c,u,f,h,d="Close sidebar",g,m,p,v,b,_,I,y,A,w,F,T,P,L;l=new Sc({props:{logo:n[2],title:n[1]}}),m=new Tn({props:{src:Js,class:"w-5 h-5"}});let B=ue(n[11]),M=[];for(let C=0;C<B.length;C+=1)M[C]=_d(md(n,B,C));const j=C=>D(M[C],1,1,()=>{M[C]=null});let V=ue(n[11]),U=[];for(let C=0;C<V.length;C+=1)U[C]=wd(ud(n,V,C));const H=C=>D(U[C],1,1,()=>{U[C]=null});return{c(){t=Y("div"),r=rt(),i=Y("div"),o=Y("div"),s=Y("div"),a=Y("a"),ct(l.$$.fragment),c=rt(),u=Y("span"),f=Y("button"),h=Y("span"),h.textContent=d,g=rt(),ct(m.$$.fragment),p=rt(),v=Y("div"),b=Y("div"),_=Y("a"),I=wt(n[3]),y=rt();for(let C=0;C<M.length;C+=1)M[C].c();A=rt();for(let C=0;C<U.length;C+=1)U[C].c();this.h()},l(C){t=q(C,"DIV",{class:!0,role:!0,tabindex:!0}),$(t).forEach(S),r=nt(C),i=q(C,"DIV",{class:!0});var G=$(i);o=q(G,"DIV",{class:!0});var J=$(o);s=q(J,"DIV",{class:!0});var X=$(s);a=q(X,"A",{href:!0,class:!0});var Dt=$(a);lt(l.$$.fragment,Dt),Dt.forEach(S),c=nt(X),u=q(X,"SPAN",{role:!0,tabindex:!0});var Bt=$(u);f=q(Bt,"BUTTON",{type:!0,class:!0});var Pt=$(f);h=q(Pt,"SPAN",{class:!0,"data-svelte-h":!0}),yr(h)!=="svelte-13q18xv"&&(h.textContent=d),g=nt(Pt),lt(m.$$.fragment,Pt),Pt.forEach(S),Bt.forEach(S),X.forEach(S),p=nt(J),v=q(J,"DIV",{class:!0,id:!0});var K=$(v);b=q(K,"DIV",{class:!0});var ye=$(b);_=q(ye,"A",{class:!0,href:!0});var Yt=$(_);I=vt(Yt,n[3]),Yt.forEach(S),y=nt(ye);for(let Te=0;Te<M.length;Te+=1)M[Te].l(ye);ye.forEach(S),A=nt(K);for(let Te=0;Te<U.length;Te+=1)U[Te].l(K);K.forEach(S),J.forEach(S),G.forEach(S),this.h()},h(){N(t,"class","fixed inset-0 bg-base-100/80 z-50 backdrop-blur-sm"),N(t,"role","button"),N(t,"tabindex","-1"),N(a,"href",xt("/")),N(a,"class","block mt-1 text-sm font-bold"),N(h,"class","sr-only"),N(f,"type","button"),N(f,"class","hover:bg-base-200 rounded-lg p-1 transition-all duration-500"),N(u,"role","button"),N(u,"tabindex","-1"),N(s,"class","py-3 px-8 mb-3 flex items-start justify-between"),N(_,"class","sticky top-0 bg-base-100 shadow shadow-base-100 text-base-heading font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100"),N(_,"href",xt("/")),N(b,"class","flex flex-col pb-6"),N(v,"class","flex-1 px-8 sm:pb-0 pb-4 overflow-auto text-base sm:text-sm pretty-scrollbar"),N(v,"id","mobileScrollable"),N(o,"class","flex flex-col h-full pb-4"),N(i,"class","bg-base-100 border-r border-base-200 shadow-lg fixed inset-0 z-50 flex sm:w-72 h-screen w-screen flex-col overflow-hidden select-none")},m(C,G){R(C,t,G),R(C,r,G),R(C,i,G),z(i,o),z(o,s),z(s,a),at(l,a,null),z(s,c),z(s,u),z(u,f),z(f,h),z(f,g),at(m,f,null),z(o,p),z(o,v),z(v,b),z(b,_),z(_,I),z(b,y);for(let J=0;J<M.length;J+=1)M[J]&&M[J].m(b,null);z(v,A);for(let J=0;J<U.length;J+=1)U[J]&&U[J].m(v,null);T=!0,P||(L=[he(t,"click",n[13]),he(t,"keypress",n[14]),he(f,"click",n[15]),he(u,"click",n[16]),he(u,"keypress",n[17])],P=!0)},p(C,G){const J={};if(G[0]&4&&(J.logo=C[2]),G[0]&2&&(J.title=C[1]),l.$set(J),(!T||G[0]&8)&&Le(I,C[3]),G[0]&2304){B=ue(C[11]);let X;for(X=0;X<B.length;X+=1){const Dt=md(C,B,X);M[X]?(M[X].p(Dt,G),O(M[X],1)):(M[X]=_d(Dt),M[X].c(),O(M[X],1),M[X].m(b,null))}for(jt(),X=B.length;X<M.length;X+=1)j(X);Lt()}if(G[0]&2432){V=ue(C[11]);let X;for(X=0;X<V.length;X+=1){const Dt=ud(C,V,X);U[X]?(U[X].p(Dt,G),O(U[X],1)):(U[X]=wd(Dt),U[X].c(),O(U[X],1),U[X].m(v,null))}for(jt(),X=V.length;X<U.length;X+=1)H(X);Lt()}},i(C){if(!T){C&&On(()=>{T&&(e||(e=In(t,xo,{duration:100},!0)),e.run(1))}),O(l.$$.fragment,C),O(m.$$.fragment,C);for(let G=0;G<B.length;G+=1)O(M[G]);for(let G=0;G<V.length;G+=1)O(U[G]);C&&On(()=>{T&&(F&&F.end(1),w=Hi(i,ei,{x:-50,duration:300}),w.start())}),T=!0}},o(C){C&&(e||(e=In(t,xo,{duration:100},!1)),e.run(0)),D(l.$$.fragment,C),D(m.$$.fragment,C),M=M.filter(Boolean);for(let G=0;G<M.length;G+=1)D(M[G]);U=U.filter(Boolean);for(let G=0;G<U.length;G+=1)D(U[G]);w&&w.invalidate(),C&&(F=es(i,ei,{x:-100,duration:200})),T=!1},d(C){C&&(S(t),S(r),S(i)),C&&e&&e.end(),st(l),st(m),kn(M,C),kn(U,C),C&&F&&F.end(),P=!1,wr(L)}}}function c2(n){var c,u;let t,e=(((c=n[18].frontMatter)==null?void 0:c.title)??n[18].label)+"",r,i,o,s,a,l=((u=n[18].frontMatter)==null?void 0:u.sidebar_badge)&&u2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),l&&l.c(),o=rt(),this.h()},l(f){t=q(f,"A",{class:!0,href:!0});var h=$(t);r=vt(h,e),i=nt(h),l&&l.l(h),o=nt(h),h.forEach(S),this.h()},h(){N(t,"class",s="group inline-block py-1 capitalize transition-colors duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(t,"href",xt(n[18].href))},m(f,h){R(f,t,h),z(t,r),z(t,i),l&&l.m(t,null),z(t,o),a=!0},p(f,h){var d;(d=f[18].frontMatter)!=null&&d.sidebar_badge&&l.p(f,h),(!a||h[0]&256&&s!==(s="group inline-block py-1 capitalize transition-colors duration-100 "+(f[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(t,"class",s)},i(f){a||(O(l),a=!0)},o(f){D(l),a=!1},d(f){f&&S(t),l&&l.d()}}}function u2(n){let t,e;return t=new Bn({props:{$$slots:{default:[f2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function f2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function _d(n){var i,o;let t,e,r=n[18].children.length===0&&n[18].href&&(((i=n[18].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[18].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&c2(pd(n));return{c(){r&&r.c(),t=ft()},l(s){r&&r.l(s),t=ft()},m(s,a){r&&r.m(s,a),R(s,t,a),e=!0},p(s,a){var l,c;s[18].children.length===0&&s[18].href&&(((l=s[18].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[18].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(pd(s),a)},i(s){e||(O(r),e=!0)},o(s){D(r),e=!1},d(s){s&&S(t),r&&r.d(s)}}}function d2(n){let t,e,r,i,o,s;const a=[m2,h2],l=[];function c(d,g){var m,p;return d[18].href&&(((m=d[18].frontMatter)==null?void 0:m.sidebar_link)!==!1||((p=d[18].frontMatter)==null?void 0:p.sidebar_link)===void 0)?0:1}e=c(n),r=l[e]=a[e](n);let u=ue(n[18].children),f=[];for(let d=0;d<u.length;d+=1)f[d]=vd(fd(n,u,d));const h=d=>D(f[d],1,1,()=>{f[d]=null});return{c(){t=Y("div"),r.c(),i=rt();for(let d=0;d<f.length;d+=1)f[d].c();o=rt(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=$(t);r.l(g),i=nt(g);for(let m=0;m<f.length;m+=1)f[m].l(g);o=nt(g),g.forEach(S),this.h()},h(){N(t,"class","flex flex-col pb-6")},m(d,g){R(d,t,g),l[e].m(t,null),z(t,i);for(let m=0;m<f.length;m+=1)f[m]&&f[m].m(t,null);z(t,o),s=!0},p(d,g){if(r.p(d,g),g[0]&2432){u=ue(d[18].children);let m;for(m=0;m<u.length;m+=1){const p=fd(d,u,m);f[m]?(f[m].p(p,g),O(f[m],1)):(f[m]=vd(p),f[m].c(),O(f[m],1),f[m].m(t,o))}for(jt(),m=u.length;m<f.length;m+=1)h(m);Lt()}},i(d){if(!s){O(r);for(let g=0;g<u.length;g+=1)O(f[g]);s=!0}},o(d){D(r),f=f.filter(Boolean);for(let g=0;g<f.length;g+=1)D(f[g]);s=!1},d(d){d&&S(t),l[e].d(),kn(f,d)}}}function h2(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&p2(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0,href:!0});var u=$(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(S),this.h()},h(){N(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),N(t,"href",xt(n[18].href))},m(c,u){R(c,t,u),z(t,r),z(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(O(s),o=!0)},o(c){D(s),o=!1},d(c){c&&S(t),s&&s.d()}}}function m2(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&_2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"A",{class:!0,href:!0});var u=$(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(S),this.h()},h(){N(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),N(t,"href",xt(n[18].href))},m(c,u){R(c,t,u),z(t,r),z(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(O(s),o=!0)},o(c){D(s),o=!1},d(c){c&&S(t),s&&s.d()}}}function p2(n){let t,e;return t=new Bn({props:{$$slots:{default:[g2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function g2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function _2(n){let t,e;return t=new Bn({props:{$$slots:{default:[b2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function b2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function y2(n){var a,l;let t,e=(((a=n[21].frontMatter)==null?void 0:a.title)??n[21].label)+"",r,i,o,s=((l=n[21].frontMatter)==null?void 0:l.sidebar_badge)&&w2(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0});var u=$(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(S),this.h()},h(){N(t,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(c,u){R(c,t,u),z(t,r),z(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[21].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(O(s),o=!0)},o(c){D(s),o=!1},d(c){c&&S(t),s&&s.d()}}}function v2(n){var l,c;let t,e=(((l=n[21].frontMatter)==null?void 0:l.title)??n[21].label)+"",r,i,o,s,a=((c=n[21].frontMatter)==null?void 0:c.sidebar_badge)&&I2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),a&&a.c(),this.h()},l(u){t=q(u,"A",{class:!0,href:!0});var f=$(t);r=vt(f,e),i=nt(f),a&&a.l(f),f.forEach(S),this.h()},h(){N(t,"class",o="group inline-block py-1 capitalize transition-colors duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(t,"href",xt(n[21].href))},m(u,f){R(u,t,f),z(t,r),z(t,i),a&&a.m(t,null),s=!0},p(u,f){var h;(h=u[21].frontMatter)!=null&&h.sidebar_badge&&a.p(u,f),(!s||f[0]&256&&o!==(o="group inline-block py-1 capitalize transition-colors duration-100 "+(u[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(t,"class",o)},i(u){s||(O(a),s=!0)},o(u){D(a),s=!1},d(u){u&&S(t),a&&a.d()}}}function w2(n){let t,e;return t=new Bn({props:{$$slots:{default:[S2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function S2(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function I2(n){let t,e;return t=new Bn({props:{$$slots:{default:[O2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function O2(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function bd(n){let t,e,r=ue(n[21].children),i=[];for(let s=0;s<r.length;s+=1)i[s]=yd(dd(n,r,s));const o=s=>D(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ft()},l(s){for(let a=0;a<i.length;a+=1)i[a].l(s);t=ft()},m(s,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(s,a);R(s,t,a),e=!0},p(s,a){if(a[0]&2304){r=ue(s[21].children);let l;for(l=0;l<r.length;l+=1){const c=dd(s,r,l);i[l]?(i[l].p(c,a),O(i[l],1)):(i[l]=yd(c),i[l].c(),O(i[l],1),i[l].m(t.parentNode,t))}for(jt(),l=r.length;l<i.length;l+=1)o(l);Lt()}},i(s){if(!e){for(let a=0;a<r.length;a+=1)O(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)D(i[a]);e=!1},d(s){s&&S(t),kn(i,s)}}}function k2(n){var l,c;let t,e=(((l=n[24].frontMatter)==null?void 0:l.title)??n[24].label)+"",r,i,o,s,a=((c=n[24].frontMatter)==null?void 0:c.sidebar_badge)&&D2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),a&&a.c(),this.h()},l(u){t=q(u,"A",{href:!0,class:!0});var f=$(t);r=vt(f,e),i=nt(f),a&&a.l(f),f.forEach(S),this.h()},h(){N(t,"href",xt(n[24].href)),N(t,"class",o="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(n[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content"))},m(u,f){R(u,t,f),z(t,r),z(t,i),a&&a.m(t,null),s=!0},p(u,f){var h;(h=u[24].frontMatter)!=null&&h.sidebar_badge&&a.p(u,f),(!s||f[0]&256&&o!==(o="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(u[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content")))&&N(t,"class",o)},i(u){s||(O(a),s=!0)},o(u){D(a),s=!1},d(u){u&&S(t),a&&a.d()}}}function D2(n){let t,e;return t=new Bn({props:{$$slots:{default:[E2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function E2(n){let t=n[24].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function yd(n){var i,o;let t,e,r=n[24].href&&(((i=n[24].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[24].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&k2(hd(n));return{c(){r&&r.c(),t=ft()},l(s){r&&r.l(s),t=ft()},m(s,a){r&&r.m(s,a),R(s,t,a),e=!0},p(s,a){var l,c;s[24].href&&(((l=s[24].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[24].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(hd(s),a)},i(s){e||(O(r),e=!0)},o(s){D(r),e=!1},d(s){s&&S(t),r&&r.d(s)}}}function vd(n){let t,e,r,i,o;const s=[v2,y2],a=[];function l(f,h){var d,g;return f[21].href&&(((d=f[21].frontMatter)==null?void 0:d.sidebar_link)!==!1||((g=f[21].frontMatter)==null?void 0:g.sidebar_link)===void 0)?0:1}function c(f,h){return h===0?l2(f):f}t=l(n),e=a[t]=s[t](c(n,t));let u=n[21].children.length>0&&n[7]>2&&bd(n);return{c(){e.c(),r=rt(),u&&u.c(),i=ft()},l(f){e.l(f),r=nt(f),u&&u.l(f),i=ft()},m(f,h){a[t].m(f,h),R(f,r,h),u&&u.m(f,h),R(f,i,h),o=!0},p(f,h){e.p(c(f,t),h),f[21].children.length>0&&f[7]>2?u?(u.p(f,h),h[0]&128&&O(u,1)):(u=bd(f),u.c(),O(u,1),u.m(i.parentNode,i)):u&&(jt(),D(u,1,1,()=>{u=null}),Lt())},i(f){o||(O(e),O(u),o=!0)},o(f){D(e),D(u),o=!1},d(f){f&&(S(r),S(i)),a[t].d(f),u&&u.d(f)}}}function wd(n){let t,e,r=n[18].children.length>0&&d2(n);return{c(){r&&r.c(),t=ft()},l(i){r&&r.l(i),t=ft()},m(i,o){r&&r.m(i,o),R(i,t,o),e=!0},p(i,o){i[18].children.length>0&&r.p(i,o)},i(i){e||(O(r),e=!0)},o(i){D(r),e=!1},d(i){i&&S(t),r&&r.d(i)}}}function Sd(n){let t,e,r,i,o,s,a,l=ue(n[11]),c=[];for(let g=0;g<l.length;g+=1)c[g]=Id(ld(n,l,g));const u=g=>D(c[g],1,1,()=>{c[g]=null});let f=ue(n[11]),h=[];for(let g=0;g<f.length;g+=1)h[g]=Ad(id(n,f,g));const d=g=>D(h[g],1,1,()=>{h[g]=null});return{c(){t=Y("div"),e=Y("div"),r=Y("a"),i=wt(n[3]),o=rt();for(let g=0;g<c.length;g+=1)c[g].c();s=rt();for(let g=0;g<h.length;g+=1)h[g].c();this.h()},l(g){t=q(g,"DIV",{class:!0});var m=$(t);e=q(m,"DIV",{class:!0});var p=$(e);r=q(p,"A",{class:!0,href:!0});var v=$(r);i=vt(v,n[3]),v.forEach(S),o=nt(p);for(let b=0;b<c.length;b+=1)c[b].l(p);p.forEach(S),s=nt(m);for(let b=0;b<h.length;b+=1)h[b].l(m);m.forEach(S),this.h()},h(){N(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading"),N(r,"href",xt("/")),N(e,"class","flex flex-col pb-6"),N(t,"class","hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"),Nr(t,"top-8",n[5])},m(g,m){R(g,t,m),z(t,e),z(e,r),z(r,i),z(e,o);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(e,null);z(t,s);for(let p=0;p<h.length;p+=1)h[p]&&h[p].m(t,null);a=!0},p(g,m){if((!a||m[0]&8)&&Le(i,g[3]),m[0]&2304){l=ue(g[11]);let p;for(p=0;p<l.length;p+=1){const v=ld(g,l,p);c[p]?(c[p].p(v,m),O(c[p],1)):(c[p]=Id(v),c[p].c(),O(c[p],1),c[p].m(e,null))}for(jt(),p=l.length;p<c.length;p+=1)u(p);Lt()}if(m[0]&2432){f=ue(g[11]);let p;for(p=0;p<f.length;p+=1){const v=id(g,f,p);h[p]?(h[p].p(v,m),O(h[p],1)):(h[p]=Ad(v),h[p].c(),O(h[p],1),h[p].m(t,null))}for(jt(),p=f.length;p<h.length;p+=1)d(p);Lt()}(!a||m[0]&32)&&Nr(t,"top-8",g[5])},i(g){if(!a){for(let m=0;m<l.length;m+=1)O(c[m]);for(let m=0;m<f.length;m+=1)O(h[m]);a=!0}},o(g){c=c.filter(Boolean);for(let m=0;m<c.length;m+=1)D(c[m]);h=h.filter(Boolean);for(let m=0;m<h.length;m+=1)D(h[m]);a=!1},d(g){g&&S(t),kn(c,g),kn(h,g)}}}function A2(n){var c,u;let t,e=(((c=n[18].frontMatter)==null?void 0:c.title)??n[18].label)+"",r,i,o,s,a,l=((u=n[18].frontMatter)==null?void 0:u.sidebar_badge)&&T2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),l&&l.c(),o=rt(),this.h()},l(f){t=q(f,"A",{class:!0,href:!0});var h=$(t);r=vt(h,e),i=nt(h),l&&l.l(h),o=nt(h),h.forEach(S),this.h()},h(){N(t,"class",s="group inline-block py-1 capitalize transition-all duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(t,"href",xt(n[18].href))},m(f,h){R(f,t,h),z(t,r),z(t,i),l&&l.m(t,null),z(t,o),a=!0},p(f,h){var d;(d=f[18].frontMatter)!=null&&d.sidebar_badge&&l.p(f,h),(!a||h[0]&256&&s!==(s="group inline-block py-1 capitalize transition-all duration-100 "+(f[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(t,"class",s)},i(f){a||(O(l),a=!0)},o(f){D(l),a=!1},d(f){f&&S(t),l&&l.d()}}}function T2(n){let t,e;return t=new Bn({props:{$$slots:{default:[B2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function B2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function Id(n){var i,o;let t,e,r=n[18].children.length===0&&n[18].href&&(((i=n[18].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[18].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&A2(cd(n));return{c(){r&&r.c(),t=ft()},l(s){r&&r.l(s),t=ft()},m(s,a){r&&r.m(s,a),R(s,t,a),e=!0},p(s,a){var l,c;s[18].children.length===0&&s[18].href&&(((l=s[18].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[18].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(cd(s),a)},i(s){e||(O(r),e=!0)},o(s){D(r),e=!1},d(s){s&&S(t),r&&r.d(s)}}}function M2(n){let t,e,r,i,o,s;const a=[N2,P2],l=[];function c(d,g){var m,p;return d[18].href&&(((m=d[18].frontMatter)==null?void 0:m.sidebar_link)!==!1||((p=d[18].frontMatter)==null?void 0:p.sidebar_link)===void 0)?0:1}e=c(n),r=l[e]=a[e](n);let u=ue(n[18].children),f=[];for(let d=0;d<u.length;d+=1)f[d]=Ed(od(n,u,d));const h=d=>D(f[d],1,1,()=>{f[d]=null});return{c(){t=Y("div"),r.c(),i=rt();for(let d=0;d<f.length;d+=1)f[d].c();o=rt(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=$(t);r.l(g),i=nt(g);for(let m=0;m<f.length;m+=1)f[m].l(g);o=nt(g),g.forEach(S),this.h()},h(){N(t,"class","flex flex-col pb-6")},m(d,g){R(d,t,g),l[e].m(t,null),z(t,i);for(let m=0;m<f.length;m+=1)f[m]&&f[m].m(t,null);z(t,o),s=!0},p(d,g){if(r.p(d,g),g[0]&2432){u=ue(d[18].children);let m;for(m=0;m<u.length;m+=1){const p=od(d,u,m);f[m]?(f[m].p(p,g),O(f[m],1)):(f[m]=Ed(p),f[m].c(),O(f[m],1),f[m].m(t,o))}for(jt(),m=u.length;m<f.length;m+=1)h(m);Lt()}},i(d){if(!s){O(r);for(let g=0;g<u.length;g+=1)O(f[g]);s=!0}},o(d){D(r),f=f.filter(Boolean);for(let g=0;g<f.length;g+=1)D(f[g]);s=!1},d(d){d&&S(t),l[e].d(),kn(f,d)}}}function P2(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&C2(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0,href:!0});var u=$(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(S),this.h()},h(){N(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize text-base-heading z-10"),N(t,"href",xt(n[18].href))},m(c,u){R(c,t,u),z(t,r),z(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(O(s),o=!0)},o(c){D(s),o=!1},d(c){c&&S(t),s&&s.d()}}}function N2(n){var a,l;let t,e=(((a=n[18].frontMatter)==null?void 0:a.title)??n[18].label)+"",r,i,o,s=((l=n[18].frontMatter)==null?void 0:l.sidebar_badge)&&j2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"A",{class:!0,href:!0});var u=$(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(S),this.h()},h(){N(t,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group block capitalize hover:underline text-base-heading z-10"),N(t,"href",xt(n[18].href))},m(c,u){R(c,t,u),z(t,r),z(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[18].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(O(s),o=!0)},o(c){D(s),o=!1},d(c){c&&S(t),s&&s.d()}}}function C2(n){let t,e;return t=new Bn({props:{$$slots:{default:[F2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function F2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function j2(n){let t,e;return t=new Bn({props:{$$slots:{default:[L2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function L2(n){let t=n[18].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function R2(n){var a,l;let t,e=(((a=n[21].frontMatter)==null?void 0:a.title)??n[21].label)+"",r,i,o,s=((l=n[21].frontMatter)==null?void 0:l.sidebar_badge)&&V2(n);return{c(){t=Y("span"),r=wt(e),i=rt(),s&&s.c(),this.h()},l(c){t=q(c,"SPAN",{class:!0});var u=$(t);r=vt(u,e),i=nt(u),s&&s.l(u),u.forEach(S),this.h()},h(){N(t,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(c,u){R(c,t,u),z(t,r),z(t,i),s&&s.m(t,null),o=!0},p(c,u){var f;(f=c[21].frontMatter)!=null&&f.sidebar_badge&&s.p(c,u)},i(c){o||(O(s),o=!0)},o(c){D(s),o=!1},d(c){c&&S(t),s&&s.d()}}}function U2(n){var l,c;let t,e=(((l=n[21].frontMatter)==null?void 0:l.title)??n[21].label)+"",r,i,o,s,a=((c=n[21].frontMatter)==null?void 0:c.sidebar_badge)&&z2(n);return{c(){t=Y("a"),r=wt(e),i=rt(),a&&a.c(),this.h()},l(u){t=q(u,"A",{href:!0,class:!0});var f=$(t);r=vt(f,e),i=nt(f),a&&a.l(f),f.forEach(S),this.h()},h(){N(t,"href",xt(n[21].href)),N(t,"class",o="group inline-block py-1 capitalize transition-all duration-100 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content"))},m(u,f){R(u,t,f),z(t,r),z(t,i),a&&a.m(t,null),s=!0},p(u,f){var h;(h=u[21].frontMatter)!=null&&h.sidebar_badge&&a.p(u,f),(!s||f[0]&256&&o!==(o="group inline-block py-1 capitalize transition-all duration-100 "+(u[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(t,"class",o)},i(u){s||(O(a),s=!0)},o(u){D(a),s=!1},d(u){u&&S(t),a&&a.d()}}}function V2(n){let t,e;return t=new Bn({props:{$$slots:{default:[x2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function x2(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function z2(n){let t,e;return t=new Bn({props:{$$slots:{default:[W2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function W2(n){let t=n[21].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function Od(n){let t,e,r=ue(n[21].children),i=[];for(let s=0;s<r.length;s+=1)i[s]=Dd(sd(n,r,s));const o=s=>D(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ft()},l(s){for(let a=0;a<i.length;a+=1)i[a].l(s);t=ft()},m(s,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(s,a);R(s,t,a),e=!0},p(s,a){if(a[0]&2304){r=ue(s[21].children);let l;for(l=0;l<r.length;l+=1){const c=sd(s,r,l);i[l]?(i[l].p(c,a),O(i[l],1)):(i[l]=Dd(c),i[l].c(),O(i[l],1),i[l].m(t.parentNode,t))}for(jt(),l=r.length;l<i.length;l+=1)o(l);Lt()}},i(s){if(!e){for(let a=0;a<r.length;a+=1)O(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)D(i[a]);e=!1},d(s){s&&S(t),kn(i,s)}}}function H2(n){var f,h;let t,e,r=(((f=n[24].frontMatter)==null?void 0:f.title)??n[24].label)+"",i,o,s,a,l,c=((h=n[24].frontMatter)==null?void 0:h.sidebar_badge)&&q2(n),u=n[27]&&kd(n);return{c(){t=Y("div"),e=Y("a"),i=wt(r),o=rt(),c&&c.c(),a=rt(),u&&u.c(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=$(t);e=q(g,"A",{href:!0,class:!0});var m=$(e);i=vt(m,r),o=nt(m),c&&c.l(m),m.forEach(S),a=nt(g),u&&u.l(g),g.forEach(S),this.h()},h(){N(e,"href",xt(n[24].href)),N(e,"class",s="group inline-block w-full capitalize transition-all duration-200 "+(n[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(t,"class","relative py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 border-l ml-[1px] transition-all duration-200 hover:border-base-content")},m(d,g){R(d,t,g),z(t,e),z(e,i),z(e,o),c&&c.m(e,null),z(t,a),u&&u.m(t,null),l=!0},p(d,g){var m;(m=d[24].frontMatter)!=null&&m.sidebar_badge&&c.p(d,g),(!l||g[0]&256&&s!==(s="group inline-block w-full capitalize transition-all duration-200 "+(d[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(e,"class",s),d[27]?u?g[0]&256&&O(u,1):(u=kd(d),u.c(),O(u,1),u.m(t,null)):u&&(jt(),D(u,1,1,()=>{u=null}),Lt())},i(d){l||(O(c),O(u),l=!0)},o(d){D(c),D(u),l=!1},d(d){d&&S(t),c&&c.d(),u&&u.d()}}}function q2(n){let t,e;return t=new Bn({props:{$$slots:{default:[Y2]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i[1]&128&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function Y2(n){let t=n[24].frontMatter.sidebar_badge+"",e;return{c(){e=wt(t)},l(r){e=vt(r,t)},m(r,i){R(r,e,i)},p:Qt,d(r){r&&S(e)}}}function kd(n){let t,e,r,i;return{c(){t=Y("div"),this.h()},l(o){t=q(o,"DIV",{class:!0}),$(t).forEach(S),this.h()},h(){N(t,"class","absolute top-0 -left-[1px] w-[1px] h-full bg-primary")},m(o,s){R(o,t,s),i=!0},i(o){i||(o&&On(()=>{i&&(r&&r.end(1),e=Hi(t,n[9],{key:"trigger"}),e.start())}),i=!0)},o(o){e&&e.invalidate(),o&&(r=es(t,n[10],{key:"trigger"})),i=!1},d(o){o&&S(t),o&&r&&r.end()}}}function Dd(n){var i,o;let t,e,r=n[24].href&&(((i=n[24].frontMatter)==null?void 0:i.sidebar_link)!==!1||((o=n[24].frontMatter)==null?void 0:o.sidebar_link)===void 0)&&H2(ad(n));return{c(){r&&r.c(),t=ft()},l(s){r&&r.l(s),t=ft()},m(s,a){r&&r.m(s,a),R(s,t,a),e=!0},p(s,a){var l,c;s[24].href&&(((l=s[24].frontMatter)==null?void 0:l.sidebar_link)!==!1||((c=s[24].frontMatter)==null?void 0:c.sidebar_link)===void 0)&&r.p(ad(s),a)},i(s){e||(O(r),e=!0)},o(s){D(r),e=!1},d(s){s&&S(t),r&&r.d(s)}}}function Ed(n){let t,e,r,i,o;const s=[U2,R2],a=[];function l(f,h){var d,g;return f[21].href&&(((d=f[21].frontMatter)==null?void 0:d.sidebar_link)!==!1||((g=f[21].frontMatter)==null?void 0:g.sidebar_link)===void 0)?0:1}function c(f,h){return h===0?a2(f):f}t=l(n),e=a[t]=s[t](c(n,t));let u=n[21].children.length>0&&n[7]>2&&Od(n);return{c(){e.c(),r=rt(),u&&u.c(),i=ft()},l(f){e.l(f),r=nt(f),u&&u.l(f),i=ft()},m(f,h){a[t].m(f,h),R(f,r,h),u&&u.m(f,h),R(f,i,h),o=!0},p(f,h){e.p(c(f,t),h),f[21].children.length>0&&f[7]>2?u?(u.p(f,h),h[0]&128&&O(u,1)):(u=Od(f),u.c(),O(u,1),u.m(i.parentNode,i)):u&&(jt(),D(u,1,1,()=>{u=null}),Lt())},i(f){o||(O(e),O(u),o=!0)},o(f){D(e),D(u),o=!1},d(f){f&&(S(r),S(i)),a[t].d(f),u&&u.d(f)}}}function Ad(n){let t,e,r=n[18].children.length>0&&M2(n);return{c(){r&&r.c(),t=ft()},l(i){r&&r.l(i),t=ft()},m(i,o){r&&r.m(i,o),R(i,t,o),e=!0},p(i,o){i[18].children.length>0&&r.p(i,o)},i(i){e||(O(r),e=!0)},o(i){D(r),e=!1},d(i){i&&S(t),r&&r.d(i)}}}function Td(n){let t,e='<a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a>';return{c(){t=Y("div"),t.innerHTML=e,this.h()},l(r){t=q(r,"DIV",{class:!0,"data-svelte-h":!0}),yr(t)!=="svelte-fworv4"&&(t.innerHTML=e),this.h()},h(){N(t,"class","fixed bottom-0 text-xs py-2")},m(r,i){R(r,t,i)},d(r){r&&S(t)}}}function K2(n){let t,e,r,i,o,s=n[0]&&gd(n),a=!n[0]&&Sd(n),l=n[4]&&Td();return{c(){s&&s.c(),t=rt(),e=Y("aside"),a&&a.c(),r=rt(),l&&l.c(),this.h()},l(c){s&&s.l(c),t=nt(c),e=q(c,"ASIDE",{class:!0});var u=$(e);a&&a.l(u),r=nt(u),l&&l.l(u),u.forEach(S),this.h()},h(){N(e,"class",i="w-48 flex-none "+(n[6]==="hide"?"hidden":"hidden md:flex"))},m(c,u){s&&s.m(c,u),R(c,t,u),R(c,e,u),a&&a.m(e,null),z(e,r),l&&l.m(e,null),o=!0},p(c,u){c[0]?s?(s.p(c,u),u[0]&1&&O(s,1)):(s=gd(c),s.c(),O(s,1),s.m(t.parentNode,t)):s&&(jt(),D(s,1,1,()=>{s=null}),Lt()),c[0]?a&&(jt(),D(a,1,1,()=>{a=null}),Lt()):a?(a.p(c,u),u[0]&1&&O(a,1)):(a=Sd(c),a.c(),O(a,1),a.m(e,r)),c[4]?l||(l=Td(),l.c(),l.m(e,null)):l&&(l.d(1),l=null),(!o||u[0]&64&&i!==(i="w-48 flex-none "+(c[6]==="hide"?"hidden":"hidden md:flex")))&&N(e,"class",i)},i(c){o||(O(s),O(a),o=!0)},o(c){D(s),D(a),o=!1},d(c){c&&(S(t),S(e)),s&&s.d(c),a&&a.d(),l&&l.d()}}}function vg(n){Object.keys(n.children).forEach(function(t){var r;const e=n.children[t];vg(e),(!e.label&&!e.href||e.children.length===0&&((r=e.frontMatter)==null?void 0:r.sidebar_link)===!1)&&delete n.children[t]})}function wg(n){return n.children=Object.values(n.children).sort((t,e)=>{var r,i,o,s;return!isNaN((r=t.frontMatter)==null?void 0:r.sidebar_position)&&!isNaN((i=e.frontMatter)==null?void 0:i.sidebar_position)?t.frontMatter.sidebar_position-e.frontMatter.sidebar_position||t.label.localeCompare(e.label):isNaN((o=t.frontMatter)==null?void 0:o.sidebar_position)?isNaN((s=e.frontMatter)==null?void 0:s.sidebar_position)?t.label.localeCompare(e.label):1:-1}),n.children.forEach(wg),n}function $2(n,t,e){let r;Ae(n,Ma,y=>e(8,r=y));let{fileTree:i=void 0}=t,{title:o=void 0}=t,{logo:s=void 0}=t,{homePageName:a=void 0}=t,{builtWithEvidence:l=void 0}=t,{hideHeader:c=!1}=t,{sidebarFrontMatter:u=void 0}=t,{sidebarDepth:f=3}=t;const[h,d]=Qg({duration:200,easing:Gg});i=structuredClone(i),vg(i),i=wg(i);let g=i==null?void 0:i.children,{mobileSidebarOpen:m=!1}=t;h_(()=>{{let y=document.querySelector("#mobileScrollable");m?Zg(y):Xg(y)}});const p=()=>e(0,m=!1),v=()=>e(0,m=!1),b=()=>{e(0,m=!1)},_=()=>e(0,m=!1),I=()=>e(0,m=!1);return n.$$set=y=>{"fileTree"in y&&e(12,i=y.fileTree),"title"in y&&e(1,o=y.title),"logo"in y&&e(2,s=y.logo),"homePageName"in y&&e(3,a=y.homePageName),"builtWithEvidence"in y&&e(4,l=y.builtWithEvidence),"hideHeader"in y&&e(5,c=y.hideHeader),"sidebarFrontMatter"in y&&e(6,u=y.sidebarFrontMatter),"sidebarDepth"in y&&e(7,f=y.sidebarDepth),"mobileSidebarOpen"in y&&e(0,m=y.mobileSidebarOpen)},[m,o,s,a,l,c,u,f,r,h,d,g,i,p,v,b,_,I]}class J2 extends ae{constructor(t){super(),le(this,t,$2,K2,ee,{fileTree:12,title:1,logo:2,homePageName:3,builtWithEvidence:4,hideHeader:5,sidebarFrontMatter:6,sidebarDepth:7,mobileSidebarOpen:0},null,[-1,-1])}}function Bd(n,t,e){const r=n.slice();return r[5]=t[e],r}function Md(n){let t,e="On this page",r,i,o=ue(n[0]),s=[];for(let a=0;a<o.length;a+=1)s[a]=Pd(Bd(n,o,a));return{c(){t=Y("span"),t.textContent=e,r=rt();for(let a=0;a<s.length;a+=1)s[a].c();i=ft(),this.h()},l(a){t=q(a,"SPAN",{class:!0,"data-svelte-h":!0}),yr(t)!=="svelte-14mun4z"&&(t.textContent=e),r=nt(a);for(let l=0;l<s.length;l+=1)s[l].l(a);i=ft(),this.h()},h(){N(t,"class","block text-xs sticky top-0 mb-2 bg-base-100 shadow-base-100 font-medium")},m(a,l){R(a,t,l),R(a,r,l);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(a,l);R(a,i,l)},p(a,l){if(l&3){o=ue(a[0]);let c;for(c=0;c<o.length;c+=1){const u=Bd(a,o,c);s[c]?s[c].p(u,l):(s[c]=Pd(u),s[c].c(),s[c].m(i.parentNode,i))}for(;c<s.length;c+=1)s[c].d(1);s.length=o.length}},d(a){a&&(S(t),S(r),S(i)),kn(s,a)}}}function Pd(n){let t,e=n[5].innerText+"",r,i,o,s;return{c(){t=Y("a"),r=wt(e),i=rt(),this.h()},l(a){t=q(a,"A",{href:!0,class:!0});var l=$(t);r=vt(l,e),i=nt(l),l.forEach(S),this.h()},h(){N(t,"href",o="#"+n[5].id),N(t,"class",s=n[1][n[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")},m(a,l){R(a,t,l),z(t,r),z(t,i)},p(a,l){l&1&&e!==(e=a[5].innerText+"")&&Le(r,e),l&1&&o!==(o="#"+a[5].id)&&N(t,"href",o),l&1&&s!==(s=a[1][a[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")&&N(t,"class",s)},d(a){a&&S(t)}}}function Q2(n){let t,e=n[0]&&n[0].length>1&&Md(n);return{c(){e&&e.c(),t=ft()},l(r){e&&e.l(r),t=ft()},m(r,i){e&&e.m(r,i),R(r,t,i)},p(r,[i]){r[0]&&r[0].length>1?e?e.p(r,i):(e=Md(r),e.c(),e.m(t.parentNode,t)):e&&(e.d(1),e=null)},i:Qt,o:Qt,d(r){r&&S(t),e&&e.d(r)}}}function G2(n,t,e){let r=[],i;function o(){e(0,r=Array.from(document.querySelectorAll("h1.markdown, h2.markdown, h3.markdown")))}function s(){return i=new MutationObserver(()=>{o()}),r.forEach(l=>{i.observe(l,{subtree:!0,characterData:!0,childList:!0})}),i}return Pr(()=>{o(),i=s()}),m_(()=>{i==null||i.disconnect()}),[r,{h1:"mt-3 font-semibold block bg-base-100 shadow shadow-base-100",h2:"pl-0 text-base-content-muted",h3:"pl-4 text-base-content-muted"}]}class X2 extends ae{constructor(t){super(),le(this,t,G2,Q2,ee,{})}}function Nd(n){let t,e,r;return e=new X2({}),{c(){t=Y("div"),ct(e.$$.fragment),this.h()},l(i){t=q(i,"DIV",{class:!0});var o=$(t);lt(e.$$.fragment,o),o.forEach(S),this.h()},h(){N(t,"class","fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"),Nr(t,"top-8",n[0])},m(i,o){R(i,t,o),at(e,t,null),r=!0},p(i,o){(!r||o&1)&&Nr(t,"top-8",i[0])},i(i){r||(O(e.$$.fragment,i),r=!0)},o(i){D(e.$$.fragment,i),r=!1},d(i){i&&S(t),st(e)}}}function Z2(n){let t,e,r=!n[1]&&n[2].data.isUserPage&&Nd(n);return{c(){t=Y("aside"),r&&r.c(),this.h()},l(i){t=q(i,"ASIDE",{class:!0});var o=$(t);r&&r.l(o),o.forEach(S),this.h()},h(){N(t,"class","hidden lg:block w-48")},m(i,o){R(i,t,o),r&&r.m(t,null),e=!0},p(i,[o]){!i[1]&&i[2].data.isUserPage?r?(r.p(i,o),o&6&&O(r,1)):(r=Nd(i),r.c(),O(r,1),r.m(t,null)):r&&(jt(),D(r,1,1,()=>{r=null}),Lt())},i(i){e||(O(r),e=!0)},o(i){D(r),e=!1},d(i){i&&S(t),r&&r.d()}}}function tS(n,t,e){let r,i;Ae(n,yh,s=>e(1,r=s)),Ae(n,Ma,s=>e(2,i=s));let{hideHeader:o=!1}=t;return n.$$set=s=>{"hideHeader"in s&&e(0,o=s.hideHeader)},[o,r,i]}class eS extends ae{constructor(t){super(),le(this,t,tS,Z2,ee,{hideHeader:0})}}function Cd(n,t,e){const r=n.slice();return r[3]=t[e],r[5]=e,r}function nS(n){let t,e=n[3].title+"",r,i,o;return{c(){t=Y("a"),r=wt(e),i=rt(),this.h()},l(s){t=q(s,"A",{href:!0,class:!0});var a=$(t);r=vt(a,e),i=nt(a),a.forEach(S),this.h()},h(){N(t,"href",o=xt(n[3].href)),N(t,"class","hover:underline")},m(s,a){R(s,t,a),z(t,r),z(t,i)},p(s,a){a&1&&e!==(e=s[3].title+"")&&Le(r,e),a&1&&o!==(o=xt(s[3].href))&&N(t,"href",o)},i:Qt,o:Qt,d(s){s&&S(t)}}}function rS(n){let t,e,r,i;t=new Tn({props:{src:t_,size:"12px",theme:"solid"}});function o(l,c){return l[3].href?oS:iS}let s=o(n),a=s(n);return{c(){ct(t.$$.fragment),e=rt(),a.c(),r=ft()},l(l){lt(t.$$.fragment,l),e=nt(l),a.l(l),r=ft()},m(l,c){at(t,l,c),R(l,e,c),a.m(l,c),R(l,r,c),i=!0},p(l,c){s===(s=o(l))&&a?a.p(l,c):(a.d(1),a=s(l),a&&(a.c(),a.m(r.parentNode,r)))},i(l){i||(O(t.$$.fragment,l),i=!0)},o(l){D(t.$$.fragment,l),i=!1},d(l){l&&(S(e),S(r)),st(t,l),a.d(l)}}}function iS(n){let t,e=n[3].title+"",r;return{c(){t=Y("span"),r=wt(e),this.h()},l(i){t=q(i,"SPAN",{class:!0});var o=$(t);r=vt(o,e),o.forEach(S),this.h()},h(){N(t,"class","cursor-default")},m(i,o){R(i,t,o),z(t,r)},p(i,o){o&1&&e!==(e=i[3].title+"")&&Le(r,e)},d(i){i&&S(t)}}}function oS(n){let t,e=n[3].title+"",r,i;return{c(){t=Y("a"),r=wt(e),this.h()},l(o){t=q(o,"A",{href:!0,class:!0});var s=$(t);r=vt(s,e),s.forEach(S),this.h()},h(){N(t,"href",i=xt(n[3].href)),N(t,"class","hover:underline")},m(o,s){R(o,t,s),z(t,r)},p(o,s){s&1&&e!==(e=o[3].title+"")&&Le(r,e),s&1&&i!==(i=xt(o[3].href))&&N(t,"href",i)},d(o){o&&S(t)}}}function Fd(n){let t,e,r,i;const o=[rS,nS],s=[];function a(l,c){return l[5]>0?0:1}return t=a(n),e=s[t]=o[t](n),{c(){e.c(),r=ft()},l(l){e.l(l),r=ft()},m(l,c){s[t].m(l,c),R(l,r,c),i=!0},p(l,c){e.p(l,c)},i(l){i||(O(e),i=!0)},o(l){D(e),i=!1},d(l){l&&S(r),s[t].d(l)}}}function sS(n){let t,e,r,i=ue(n[0]),o=[];for(let a=0;a<i.length;a+=1)o[a]=Fd(Cd(n,i,a));const s=a=>D(o[a],1,1,()=>{o[a]=null});return{c(){t=Y("div"),e=Y("div");for(let a=0;a<o.length;a+=1)o[a].c();this.h()},l(a){t=q(a,"DIV",{class:!0});var l=$(t);e=q(l,"DIV",{class:!0});var c=$(e);for(let u=0;u<o.length;u+=1)o[u].l(c);c.forEach(S),l.forEach(S),this.h()},h(){N(e,"class","inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"),N(t,"class","flex items-start mt-0 whitespace-nowrap overflow-auto")},m(a,l){R(a,t,l),z(t,e);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(e,null);r=!0},p(a,[l]){if(l&1){i=ue(a[0]);let c;for(c=0;c<i.length;c+=1){const u=Cd(a,i,c);o[c]?(o[c].p(u,l),O(o[c],1)):(o[c]=Fd(u),o[c].c(),O(o[c],1),o[c].m(e,null))}for(jt(),c=i.length;c<o.length;c+=1)s(c);Lt()}},i(a){if(!r){for(let l=0;l<i.length;l+=1)O(o[l]);r=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)D(o[l]);r=!1},d(a){a&&S(t),kn(o,a)}}}function aS(n,t){if(n==="/")return t;const e=n.replace(mh.deployment.basePath,"").split("/").slice(1);let r=t;for(let i of e)if(r.children[i]?r=r.children[i]:r=Object.values(r.children).find(o=>o.isTemplated),!r)return null;return r}function lS(n,t){const e=[{href:"/",title:"Home"}];n.forEach((r,i)=>{r!=""&&`/${r}`!==mh.deployment.basePath&&e.push({href:"/"+n.slice(0,i+1).join("/"),title:decodeURIComponent(r.replace(/_/g," ").replace(/-/g," "))})}),e.length>3&&e.splice(1,e.length-3,{href:e.slice(-3)[0].href,title:"..."});for(const r of e)if(r.href==="/")r.href=xt("/"),r.title="Home";else{const i=aS(r.href,t);!i||!i.isPage?r.href=null:r.title=i.title??r.title}return e}function cS(n,t,e){let r,i;Ae(n,Ma,s=>e(2,i=s));let{fileTree:o}=t;return n.$$set=s=>{"fileTree"in s&&e(1,o=s.fileTree)},n.$$.update=()=>{n.$$.dirty&6&&e(0,r=lS(i.url.pathname.split("/").slice(1),o))},[r,o,i]}class uS extends ae{constructor(t){super(),le(this,t,cS,sS,ee,{fileTree:1})}}function jd(n){let t,e,r,i="Error",o,s,a,l,c,u='<a href="https://docs.evidence.dev" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">docs</a> <a href="https://evidencedev.slack.com/join/shared_invite/zt-uda6wp6a-hP6Qyz0LUOddwpXW5qG03Q#/shared-invite/email" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">slack</a> <a href="mailto:<EMAIL>" class="hover:text-base-content-muted transition-colors duration-200">email</a>',f,h,d;return{c(){t=Y("div"),e=Y("div"),r=Y("h1"),r.textContent=i,o=rt(),s=Y("p"),a=wt(n[1]),l=rt(),c=Y("div"),c.innerHTML=u,this.h()},l(g){t=q(g,"DIV",{class:!0});var m=$(t);e=q(m,"DIV",{class:!0});var p=$(e);r=q(p,"H1",{class:!0,"data-svelte-h":!0}),yr(r)!=="svelte-1wczc15"&&(r.textContent=i),o=nt(p),s=q(p,"P",{class:!0});var v=$(s);a=vt(v,n[1]),v.forEach(S),l=nt(p),c=q(p,"DIV",{class:!0,"data-svelte-h":!0}),yr(c)!=="svelte-vfh8n7"&&(c.innerHTML=u),p.forEach(S),m.forEach(S),this.h()},h(){N(r,"class","text-2xl font-bold tracking-wide border-b pb-4 border-base-300"),N(s,"class","text-xl mt-6 leading-relaxed select-text"),N(c,"class","absolute bottom-0 flex items-end gap-4 text-lg mb-6"),N(e,"class","relative min-w-full h-screen bg-gradient-to-b from-base-200 to-base-300 rounded-lg border-t-8 border-negative shadow-xl p-8"),N(t,"class","fixed flex flex-col z-50 h-screen w-screen bg-base-100/50 justify-center items-center py-20 px-10 sm:px-20 select-none backdrop-blur-sm")},m(g,m){R(g,t,m),z(t,e),z(e,r),z(e,o),z(e,s),z(s,a),z(e,l),z(e,c),d=!0},p(g,m){(!d||m&2)&&Le(a,g[1])},i(g){d||(g&&On(()=>{d&&(f||(f=In(e,ei,{y:100,duration:300},!0)),f.run(1))}),g&&On(()=>{d&&(h||(h=In(t,xo,{duration:100},!0)),h.run(1))}),d=!0)},o(g){g&&(f||(f=In(e,ei,{y:100,duration:300},!1)),f.run(0)),g&&(h||(h=In(t,xo,{duration:100},!1)),h.run(0)),d=!1},d(g){g&&S(t),g&&f&&f.end(),g&&h&&h.end()}}}function fS(n){let t,e=n[0]&&jd(n);return{c(){e&&e.c(),t=ft()},l(r){e&&e.l(r),t=ft()},m(r,i){e&&e.m(r,i),R(r,t,i)},p(r,[i]){r[0]?e?(e.p(r,i),i&1&&O(e,1)):(e=jd(r),e.c(),O(e,1),e.m(t.parentNode,t)):e&&(jt(),D(e,1,1,()=>{e=null}),Lt())},i(r){O(e)},o(r){D(e)},d(r){r&&S(t),e&&e.d(r)}}}function dS(n,t,e){return[!1,void 0]}class hS extends ae{constructor(t){super(),le(this,t,dS,fS,ee,{})}}function mS(n){let t,e=JSON.stringify(n[0],null,2)+"",r;return{c(){t=Y("pre"),r=wt(e),this.h()},l(i){t=q(i,"PRE",{class:!0});var o=$(t);r=vt(o,e),o.forEach(S),this.h()},h(){N(t,"class","text-xs px-2 py-2 bg-base-200 my-2")},m(i,o){R(i,t,o),z(t,r)},p(i,[o]){o&1&&e!==(e=JSON.stringify(i[0],null,2)+"")&&Le(r,e)},i:Qt,o:Qt,d(i){i&&S(t)}}}function pS(n,t,e){let r;const i=ph();return Ae(n,i,o=>e(0,r=o)),[r,i]}class gS extends ae{constructor(t){super(),le(this,t,pS,mS,ee,{})}}function _S(n){const t=[{type:"unchanged",content:"{"}],e=e_(n.before,n.after);function r(o,s){return s.reduce((a,l)=>a==null?void 0:a[l],o)}function i(o,s){const a=Object.keys(o);a.forEach((l,c)=>{const u=r(n.added,s)??{},f=r(n.deleted,s)??{},h=r(n.updated,s)??{};let d="unchanged";l in u&&(d="added"),l in f&&(d="deleted"),l in h&&(d="updated");const g=(m,p=!1)=>{const v=`"${l}": `;let b=`${"  ".repeat(s.length+1)}${p?"":v}${m}`;return c<a.length-1&&(b+=","),b};if(typeof o[l]=="object"){t.push({type:d==="updated"?"unchanged":d,content:g("{")}),i(o[l],s.concat(l)),t.push({type:d==="updated"?"unchanged":d,content:g("}",!0)});return}else{const m=d==="deleted"?r(n.before,s)[l]:o[l];t.push({type:d,content:g(JSON.stringify(m))});return}})}return i(e,[]),t.push({type:"unchanged",content:"}"}),t}const bS={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},yS=(n,t,e)=>{let r;const i=bS[n];return typeof i=="string"?r=i:t===1?r=i.one:r=i.other.replace("{{count}}",t.toString()),e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"in "+r:r+" ago":r};function gl(n){return(t={})=>{const e=t.width?String(t.width):n.defaultWidth;return n.formats[e]||n.formats[n.defaultWidth]}}const vS={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},wS={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},SS={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},IS={date:gl({formats:vS,defaultWidth:"full"}),time:gl({formats:wS,defaultWidth:"full"}),dateTime:gl({formats:SS,defaultWidth:"full"})},OS={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},kS=(n,t,e,r)=>OS[n];function co(n){return(t,e)=>{const r=e!=null&&e.context?String(e.context):"standalone";let i;if(r==="formatting"&&n.formattingValues){const s=n.defaultFormattingWidth||n.defaultWidth,a=e!=null&&e.width?String(e.width):s;i=n.formattingValues[a]||n.formattingValues[s]}else{const s=n.defaultWidth,a=e!=null&&e.width?String(e.width):n.defaultWidth;i=n.values[a]||n.values[s]}const o=n.argumentCallback?n.argumentCallback(t):t;return i[o]}}const DS={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},ES={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},AS={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},TS={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},BS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},MS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},PS=(n,t)=>{const e=Number(n),r=e%100;if(r>20||r<10)switch(r%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},NS={ordinalNumber:PS,era:co({values:DS,defaultWidth:"wide"}),quarter:co({values:ES,defaultWidth:"wide",argumentCallback:n=>n-1}),month:co({values:AS,defaultWidth:"wide"}),day:co({values:TS,defaultWidth:"wide"}),dayPeriod:co({values:BS,defaultWidth:"wide",formattingValues:MS,defaultFormattingWidth:"wide"})};function uo(n){return(t,e={})=>{const r=e.width,i=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],o=t.match(i);if(!o)return null;const s=o[0],a=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],l=Array.isArray(a)?FS(a,f=>f.test(s)):CS(a,f=>f.test(s));let c;c=n.valueCallback?n.valueCallback(l):l,c=e.valueCallback?e.valueCallback(c):c;const u=t.slice(s.length);return{value:c,rest:u}}}function CS(n,t){for(const e in n)if(Object.prototype.hasOwnProperty.call(n,e)&&t(n[e]))return e}function FS(n,t){for(let e=0;e<n.length;e++)if(t(n[e]))return e}function jS(n){return(t,e={})=>{const r=t.match(n.matchPattern);if(!r)return null;const i=r[0],o=t.match(n.parsePattern);if(!o)return null;let s=n.valueCallback?n.valueCallback(o[0]):o[0];s=e.valueCallback?e.valueCallback(s):s;const a=t.slice(i.length);return{value:s,rest:a}}}const LS=/^(\d+)(th|st|nd|rd)?/i,RS=/\d+/i,US={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},VS={any:[/^b/i,/^(a|c)/i]},xS={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},zS={any:[/1/i,/2/i,/3/i,/4/i]},WS={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},HS={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},qS={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},YS={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},KS={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},$S={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},JS={ordinalNumber:jS({matchPattern:LS,parsePattern:RS,valueCallback:n=>parseInt(n,10)}),era:uo({matchPatterns:US,defaultMatchWidth:"wide",parsePatterns:VS,defaultParseWidth:"any"}),quarter:uo({matchPatterns:xS,defaultMatchWidth:"wide",parsePatterns:zS,defaultParseWidth:"any",valueCallback:n=>n+1}),month:uo({matchPatterns:WS,defaultMatchWidth:"wide",parsePatterns:HS,defaultParseWidth:"any"}),day:uo({matchPatterns:qS,defaultMatchWidth:"wide",parsePatterns:YS,defaultParseWidth:"any"}),dayPeriod:uo({matchPatterns:KS,defaultMatchWidth:"any",parsePatterns:$S,defaultParseWidth:"any"})},Sg={code:"en-US",formatDistance:yS,formatLong:IS,formatRelative:kS,localize:NS,match:JS,options:{weekStartsOn:0,firstWeekContainsDate:1}};let QS={};function fs(){return QS}function GS(n){return t=>{const r=(n?Math[n]:Math.trunc)(t);return r===0?0:r}}function ze(n){const t=Object.prototype.toString.call(n);return n instanceof Date||typeof n=="object"&&t==="[object Date]"?new n.constructor(+n):typeof n=="number"||t==="[object Number]"||typeof n=="string"||t==="[object String]"?new Date(n):new Date(NaN)}function Aa(n){const t=ze(n),e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),+n-+e}function XS(n,t){const e=ze(n),r=ze(t),i=e.getTime()-r.getTime();return i<0?-1:i>0?1:i}const Ig=6048e5,ZS=864e5,Ld=6e4,Rd=525600,Ud=43200,Vd=1440;function tI(n,t,e){const r=fs(),i=(e==null?void 0:e.locale)??r.locale??Sg,o=XS(n,t);if(isNaN(o))throw new RangeError("Invalid time value");const s=Object.assign({},e,{addSuffix:e==null?void 0:e.addSuffix,comparison:o});let a,l;o>0?(a=ze(t),l=ze(n)):(a=ze(n),l=ze(t));const c=GS((e==null?void 0:e.roundingMethod)??"round"),u=l.getTime()-a.getTime(),f=u/Ld,h=Aa(l)-Aa(a),d=(u-h)/Ld,g=e==null?void 0:e.unit;let m;if(g?m=g:f<1?m="second":f<60?m="minute":f<Vd?m="hour":d<Ud?m="day":d<Rd?m="month":m="year",m==="second"){const p=c(u/1e3);return i.formatDistance("xSeconds",p,s)}else if(m==="minute"){const p=c(f);return i.formatDistance("xMinutes",p,s)}else if(m==="hour"){const p=c(f/60);return i.formatDistance("xHours",p,s)}else if(m==="day"){const p=c(d/Vd);return i.formatDistance("xDays",p,s)}else if(m==="month"){const p=c(d/Ud);return p===12&&g!=="month"?i.formatDistance("xYears",1,s):i.formatDistance("xMonths",p,s)}else{const p=c(d/Rd);return i.formatDistance("xYears",p,s)}}function Lr(n,t){return n instanceof Date?new n.constructor(t):new Date(t)}function eI(n){return Lr(n,Date.now())}function xd(n,t){return tI(n,eI(n),t)}function zd(n){const t=ze(n);return t.setHours(0,0,0,0),t}function nI(n,t){const e=zd(n),r=zd(t),i=+e-Aa(e),o=+r-Aa(r);return Math.round((i-o)/ZS)}function rI(n){const t=ze(n),e=Lr(n,0);return e.setFullYear(t.getFullYear(),0,1),e.setHours(0,0,0,0),e}function iI(n){const t=ze(n);return nI(t,rI(t))+1}function Xo(n,t){var a,l,c,u;const e=fs(),r=(t==null?void 0:t.weekStartsOn)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.weekStartsOn)??e.weekStartsOn??((u=(c=e.locale)==null?void 0:c.options)==null?void 0:u.weekStartsOn)??0,i=ze(n),o=i.getDay(),s=(o<r?7:0)+o-r;return i.setDate(i.getDate()-s),i.setHours(0,0,0,0),i}function Ta(n){return Xo(n,{weekStartsOn:1})}function Og(n){const t=ze(n),e=t.getFullYear(),r=Lr(n,0);r.setFullYear(e+1,0,4),r.setHours(0,0,0,0);const i=Ta(r),o=Lr(n,0);o.setFullYear(e,0,4),o.setHours(0,0,0,0);const s=Ta(o);return t.getTime()>=i.getTime()?e+1:t.getTime()>=s.getTime()?e:e-1}function oI(n){const t=Og(n),e=Lr(n,0);return e.setFullYear(t,0,4),e.setHours(0,0,0,0),Ta(e)}function sI(n){const t=ze(n),e=+Ta(t)-+oI(t);return Math.round(e/Ig)+1}function kg(n,t){var u,f,h,d;const e=ze(n),r=e.getFullYear(),i=fs(),o=(t==null?void 0:t.firstWeekContainsDate)??((f=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??i.firstWeekContainsDate??((d=(h=i.locale)==null?void 0:h.options)==null?void 0:d.firstWeekContainsDate)??1,s=Lr(n,0);s.setFullYear(r+1,0,o),s.setHours(0,0,0,0);const a=Xo(s,t),l=Lr(n,0);l.setFullYear(r,0,o),l.setHours(0,0,0,0);const c=Xo(l,t);return e.getTime()>=a.getTime()?r+1:e.getTime()>=c.getTime()?r:r-1}function aI(n,t){var a,l,c,u;const e=fs(),r=(t==null?void 0:t.firstWeekContainsDate)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.firstWeekContainsDate)??e.firstWeekContainsDate??((u=(c=e.locale)==null?void 0:c.options)==null?void 0:u.firstWeekContainsDate)??1,i=kg(n,t),o=Lr(n,0);return o.setFullYear(i,0,r),o.setHours(0,0,0,0),Xo(o,t)}function lI(n,t){const e=ze(n),r=+Xo(e,t)-+aI(e,t);return Math.round(r/Ig)+1}function oe(n,t){const e=n<0?"-":"",r=Math.abs(n).toString().padStart(t,"0");return e+r}const Ar={y(n,t){const e=n.getFullYear(),r=e>0?e:1-e;return oe(t==="yy"?r%100:r,t.length)},M(n,t){const e=n.getMonth();return t==="M"?String(e+1):oe(e+1,2)},d(n,t){return oe(n.getDate(),t.length)},a(n,t){const e=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];case"aaaa":default:return e==="am"?"a.m.":"p.m."}},h(n,t){return oe(n.getHours()%12||12,t.length)},H(n,t){return oe(n.getHours(),t.length)},m(n,t){return oe(n.getMinutes(),t.length)},s(n,t){return oe(n.getSeconds(),t.length)},S(n,t){const e=t.length,r=n.getMilliseconds(),i=Math.trunc(r*Math.pow(10,e-3));return oe(i,t.length)}},hi={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Wd={G:function(n,t,e){const r=n.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return e.era(r,{width:"abbreviated"});case"GGGGG":return e.era(r,{width:"narrow"});case"GGGG":default:return e.era(r,{width:"wide"})}},y:function(n,t,e){if(t==="yo"){const r=n.getFullYear(),i=r>0?r:1-r;return e.ordinalNumber(i,{unit:"year"})}return Ar.y(n,t)},Y:function(n,t,e,r){const i=kg(n,r),o=i>0?i:1-i;if(t==="YY"){const s=o%100;return oe(s,2)}return t==="Yo"?e.ordinalNumber(o,{unit:"year"}):oe(o,t.length)},R:function(n,t){const e=Og(n);return oe(e,t.length)},u:function(n,t){const e=n.getFullYear();return oe(e,t.length)},Q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return oe(r,2);case"Qo":return e.ordinalNumber(r,{unit:"quarter"});case"QQQ":return e.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return e.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return oe(r,2);case"qo":return e.ordinalNumber(r,{unit:"quarter"});case"qqq":return e.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return e.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,t,e){const r=n.getMonth();switch(t){case"M":case"MM":return Ar.M(n,t);case"Mo":return e.ordinalNumber(r+1,{unit:"month"});case"MMM":return e.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return e.month(r,{width:"wide",context:"formatting"})}},L:function(n,t,e){const r=n.getMonth();switch(t){case"L":return String(r+1);case"LL":return oe(r+1,2);case"Lo":return e.ordinalNumber(r+1,{unit:"month"});case"LLL":return e.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return e.month(r,{width:"wide",context:"standalone"})}},w:function(n,t,e,r){const i=lI(n,r);return t==="wo"?e.ordinalNumber(i,{unit:"week"}):oe(i,t.length)},I:function(n,t,e){const r=sI(n);return t==="Io"?e.ordinalNumber(r,{unit:"week"}):oe(r,t.length)},d:function(n,t,e){return t==="do"?e.ordinalNumber(n.getDate(),{unit:"date"}):Ar.d(n,t)},D:function(n,t,e){const r=iI(n);return t==="Do"?e.ordinalNumber(r,{unit:"dayOfYear"}):oe(r,t.length)},E:function(n,t,e){const r=n.getDay();switch(t){case"E":case"EE":case"EEE":return e.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(r,{width:"short",context:"formatting"});case"EEEE":default:return e.day(r,{width:"wide",context:"formatting"})}},e:function(n,t,e,r){const i=n.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return oe(o,2);case"eo":return e.ordinalNumber(o,{unit:"day"});case"eee":return e.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(i,{width:"short",context:"formatting"});case"eeee":default:return e.day(i,{width:"wide",context:"formatting"})}},c:function(n,t,e,r){const i=n.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return oe(o,t.length);case"co":return e.ordinalNumber(o,{unit:"day"});case"ccc":return e.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(i,{width:"narrow",context:"standalone"});case"cccccc":return e.day(i,{width:"short",context:"standalone"});case"cccc":default:return e.day(i,{width:"wide",context:"standalone"})}},i:function(n,t,e){const r=n.getDay(),i=r===0?7:r;switch(t){case"i":return String(i);case"ii":return oe(i,t.length);case"io":return e.ordinalNumber(i,{unit:"day"});case"iii":return e.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(r,{width:"short",context:"formatting"});case"iiii":default:return e.day(r,{width:"wide",context:"formatting"})}},a:function(n,t,e){const i=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(i,{width:"narrow",context:"formatting"});case"aaaa":default:return e.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(n,t,e){const r=n.getHours();let i;switch(r===12?i=hi.noon:r===0?i=hi.midnight:i=r/12>=1?"pm":"am",t){case"b":case"bb":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(i,{width:"narrow",context:"formatting"});case"bbbb":default:return e.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(n,t,e){const r=n.getHours();let i;switch(r>=17?i=hi.evening:r>=12?i=hi.afternoon:r>=4?i=hi.morning:i=hi.night,t){case"B":case"BB":case"BBB":return e.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(i,{width:"narrow",context:"formatting"});case"BBBB":default:return e.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(n,t,e){if(t==="ho"){let r=n.getHours()%12;return r===0&&(r=12),e.ordinalNumber(r,{unit:"hour"})}return Ar.h(n,t)},H:function(n,t,e){return t==="Ho"?e.ordinalNumber(n.getHours(),{unit:"hour"}):Ar.H(n,t)},K:function(n,t,e){const r=n.getHours()%12;return t==="Ko"?e.ordinalNumber(r,{unit:"hour"}):oe(r,t.length)},k:function(n,t,e){let r=n.getHours();return r===0&&(r=24),t==="ko"?e.ordinalNumber(r,{unit:"hour"}):oe(r,t.length)},m:function(n,t,e){return t==="mo"?e.ordinalNumber(n.getMinutes(),{unit:"minute"}):Ar.m(n,t)},s:function(n,t,e){return t==="so"?e.ordinalNumber(n.getSeconds(),{unit:"second"}):Ar.s(n,t)},S:function(n,t){return Ar.S(n,t)},X:function(n,t,e){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return qd(r);case"XXXX":case"XX":return Gr(r);case"XXXXX":case"XXX":default:return Gr(r,":")}},x:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"x":return qd(r);case"xxxx":case"xx":return Gr(r);case"xxxxx":case"xxx":default:return Gr(r,":")}},O:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Hd(r,":");case"OOOO":default:return"GMT"+Gr(r,":")}},z:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Hd(r,":");case"zzzz":default:return"GMT"+Gr(r,":")}},t:function(n,t,e){const r=Math.trunc(n.getTime()/1e3);return oe(r,t.length)},T:function(n,t,e){const r=n.getTime();return oe(r,t.length)}};function Hd(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),i=Math.trunc(r/60),o=r%60;return o===0?e+String(i):e+String(i)+t+oe(o,2)}function qd(n,t){return n%60===0?(n>0?"-":"+")+oe(Math.abs(n)/60,2):Gr(n,t)}function Gr(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),i=oe(Math.trunc(r/60),2),o=oe(r%60,2);return e+i+t+o}const Yd=(n,t)=>{switch(n){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},Dg=(n,t)=>{switch(n){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},cI=(n,t)=>{const e=n.match(/(P+)(p+)?/)||[],r=e[1],i=e[2];if(!i)return Yd(n,t);let o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",Yd(r,t)).replace("{{time}}",Dg(i,t))},uI={p:Dg,P:cI},fI=/^D+$/,dI=/^Y+$/,hI=["D","DD","YY","YYYY"];function mI(n){return fI.test(n)}function pI(n){return dI.test(n)}function gI(n,t,e){const r=_I(n,t,e);if(console.warn(r),hI.includes(n))throw new RangeError(r)}function _I(n,t,e){const r=n[0]==="Y"?"years":"days of the month";return`Use \`${n.toLowerCase()}\` instead of \`${n}\` (in \`${t}\`) for formatting ${r} to the input \`${e}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function bI(n){return n instanceof Date||typeof n=="object"&&Object.prototype.toString.call(n)==="[object Date]"}function yI(n){if(!bI(n)&&typeof n!="number")return!1;const t=ze(n);return!isNaN(Number(t))}const vI=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,wI=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,SI=/^'([^]*?)'?$/,II=/''/g,OI=/[a-zA-Z]/;function Kd(n,t,e){var u,f,h,d;const r=fs(),i=r.locale??Sg,o=r.firstWeekContainsDate??((f=(u=r.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??1,s=r.weekStartsOn??((d=(h=r.locale)==null?void 0:h.options)==null?void 0:d.weekStartsOn)??0,a=ze(n);if(!yI(a))throw new RangeError("Invalid time value");let l=t.match(wI).map(g=>{const m=g[0];if(m==="p"||m==="P"){const p=uI[m];return p(g,i.formatLong)}return g}).join("").match(vI).map(g=>{if(g==="''")return{isToken:!1,value:"'"};const m=g[0];if(m==="'")return{isToken:!1,value:kI(g)};if(Wd[m])return{isToken:!0,value:g};if(m.match(OI))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return{isToken:!1,value:g}});i.localize.preprocessor&&(l=i.localize.preprocessor(a,l));const c={firstWeekContainsDate:o,weekStartsOn:s,locale:i};return l.map(g=>{if(!g.isToken)return g.value;const m=g.value;(pI(m)||mI(m))&&gI(m,t,String(n));const p=Wd[m[0]];return p(a,m,i.localize,c)}).join("")}function kI(n){const t=n.match(SI);return t?t[1].replace(II,"'"):n}function $d(n,t,e){const r=n.slice();return r[6]=t[e],r}function Jd(n){let t,e,r=n[4](n[6].type)+"",i,o,s,a,l,c=n[6].content+"",u,f,h;return{c(){t=Y("div"),e=Y("span"),i=wt(r),s=rt(),a=Y("div"),l=Y("pre"),u=wt(c),h=rt(),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=$(t);e=q(g,"SPAN",{class:!0});var m=$(e);i=vt(m,r),m.forEach(S),s=nt(g),a=q(g,"DIV",{class:!0});var p=$(a);l=q(p,"PRE",{class:!0});var v=$(l);u=vt(v,c),v.forEach(S),p.forEach(S),h=nt(g),g.forEach(S),this.h()},h(){N(e,"class",o=n[3][n[6].type]+" px-1 select-none"),N(l,"class","whitespace-pre-wrap"),N(a,"class",f=n[3][n[6].type]+" selection:bg-black/15"),N(t,"class","group contents")},m(d,g){R(d,t,g),z(t,e),z(e,i),z(t,s),z(t,a),z(a,l),z(l,u),z(t,h)},p(d,g){g&2&&r!==(r=d[4](d[6].type)+"")&&Le(i,r),g&2&&o!==(o=d[3][d[6].type]+" px-1 select-none")&&N(e,"class",o),g&2&&c!==(c=d[6].content+"")&&Le(u,c),g&2&&f!==(f=d[3][d[6].type]+" selection:bg-black/15")&&N(a,"class",f)},d(d){d&&S(t)}}}function DI(n){let t,e,r,i,o,s=Kd(n[0].asof,"HH:mm:ss")+"",a,l,c,u,f=ue(n[1]),h=[];for(let d=0;d<f.length;d+=1)h[d]=Jd($d(n,f,d));return{c(){t=Y("section"),e=Y("span"),r=wt("About "),i=wt(n[2]),o=wt(" ("),a=wt(s),l=wt(")"),c=rt(),u=Y("div");for(let d=0;d<h.length;d+=1)h[d].c();this.h()},l(d){t=q(d,"SECTION",{});var g=$(t);e=q(g,"SPAN",{});var m=$(e);r=vt(m,"About "),i=vt(m,n[2]),o=vt(m," ("),a=vt(m,s),l=vt(m,")"),m.forEach(S),c=nt(g),u=q(g,"DIV",{class:!0});var p=$(u);for(let v=0;v<h.length;v+=1)h[v].l(p);p.forEach(S),g.forEach(S),this.h()},h(){N(u,"class","font-mono text-xs grid grid-cols-[auto,1fr] text-[0.7rem] bg-base-200 p-2 select-text")},m(d,g){R(d,t,g),z(t,e),z(e,r),z(e,i),z(e,o),z(e,a),z(e,l),z(t,c),z(t,u);for(let m=0;m<h.length;m+=1)h[m]&&h[m].m(u,null)},p(d,[g]){if(g&4&&Le(i,d[2]),g&1&&s!==(s=Kd(d[0].asof,"HH:mm:ss")+"")&&Le(a,s),g&26){f=ue(d[1]);let m;for(m=0;m<f.length;m+=1){const p=$d(d,f,m);h[m]?h[m].p(p,g):(h[m]=Jd(p),h[m].c(),h[m].m(u,null))}for(;m<h.length;m+=1)h[m].d(1);h.length=f.length}},i:Qt,o:Qt,d(d){d&&S(t),kn(h,d)}}}function EI(n,t,e){let r,i,{diffData:o={added:{},deleted:{},updated:{},before:{},after:{},asof:new Date}}=t;const s={added:"bg-positive/25",deleted:"bg-negative/25",updated:"bg-warning/25",unchanged:""},a=c=>{switch(c){case"added":return"+";case"deleted":return"-";case"updated":return"~";case"unchanged":return" ";default:return"?"}},l=g_(null,c=>{c(xd(o.asof,{addSuffix:!0,includeSeconds:!0}));const u=setInterval(()=>{c(xd(o.asof,{addSuffix:!0,includeSeconds:!0}))},5e3);return()=>{clearInterval(u)}});return Ae(n,l,c=>e(2,i=c)),n.$$set=c=>{"diffData"in c&&e(0,o=c.diffData)},n.$$.update=()=>{n.$$.dirty&1&&e(1,r=_S(o))},[o,r,i,s,a,l]}class AI extends ae{constructor(t){super(),le(this,t,EI,DI,ee,{diffData:0})}}function Qd(n,t,e){const r=n.slice();return r[2]=t[e],r}function Gd(n){let t,e,r,i;return e=new AI({props:{diffData:n[2]}}),{c(){t=Y("div"),ct(e.$$.fragment),r=rt(),this.h()},l(o){t=q(o,"DIV",{class:!0});var s=$(t);lt(e.$$.fragment,s),r=nt(s),s.forEach(S),this.h()},h(){N(t,"class","my-4")},m(o,s){R(o,t,s),at(e,t,null),z(t,r),i=!0},p(o,s){const a={};s&2&&(a.diffData=o[2]),e.$set(a)},i(o){i||(O(e.$$.fragment,o),i=!0)},o(o){D(e.$$.fragment,o),i=!1},d(o){o&&S(t),st(e)}}}function TI(n){let t,e,r=ue(n[1].reverse()),i=[];for(let s=0;s<r.length;s+=1)i[s]=Gd(Qd(n,r,s));const o=s=>D(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ft()},l(s){for(let a=0;a<i.length;a+=1)i[a].l(s);t=ft()},m(s,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(s,a);R(s,t,a),e=!0},p(s,[a]){if(a&2){r=ue(s[1].reverse());let l;for(l=0;l<r.length;l+=1){const c=Qd(s,r,l);i[l]?(i[l].p(c,a),O(i[l],1)):(i[l]=Gd(c),i[l].c(),O(i[l],1),i[l].m(t.parentNode,t))}for(jt(),l=r.length;l<i.length;l+=1)o(l);Lt()}},i(s){if(!e){for(let a=0;a<r.length;a+=1)O(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)D(i[a]);e=!1},d(s){s&&S(t),kn(i,s)}}}function BI(n,t,e){let r,i=Qt,o=()=>(i(),i=p_(s,a=>e(1,r=a)),s);n.$$.on_destroy.push(()=>i());let{history:s}=t;return o(),n.$$set=a=>{"history"in a&&o(e(0,s=a.history))},[s,r]}class MI extends ae{constructor(t){super(),le(this,t,BI,TI,ee,{history:0})}}function Xd(n,t,e){const r=n.slice();return r[12]=t[e][0],r[13]=t[e][1],r}function Zd(n){let t,e,r,i,o,s="Evidence Dev Tools",a,l,c,u,f,h;return r=new Tn({props:{src:n[0]?Js:yl,class:"w-4 h-4"}}),l=new E_({props:{$$slots:{default:[FI]},$$scope:{ctx:n}}}),{c(){t=Y("div"),e=Y("button"),ct(r.$$.fragment),i=rt(),o=Y("header"),o.textContent=s,a=rt(),ct(l.$$.fragment),this.h()},l(d){t=q(d,"DIV",{class:!0});var g=$(t);e=q(g,"BUTTON",{class:!0});var m=$(e);lt(r.$$.fragment,m),m.forEach(S),i=nt(g),o=q(g,"HEADER",{class:!0,"data-svelte-h":!0}),yr(o)!=="svelte-ekyf9x"&&(o.textContent=s),a=nt(g),lt(l.$$.fragment,g),g.forEach(S),this.h()},h(){N(e,"class","absolute right-4 top-4 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-30"),N(o,"class","text-xl font-bold mb-4"),N(t,"class","h-[calc(100vh-3rem)] w-96 bg-base-100 fixed overflow-auto right-0 top-12 px-4 py-4 z-10")},m(d,g){R(d,t,g),z(t,e),at(r,e,null),z(t,i),z(t,o),z(t,a),at(l,t,null),u=!0,f||(h=he(e,"click",n[7]),f=!0)},p(d,g){const m={};g&1&&(m.src=d[0]?Js:yl),r.$set(m);const p={};g&2054&&(p.$$scope={dirty:g,ctx:d}),l.$set(p)},i(d){u||(O(r.$$.fragment,d),O(l.$$.fragment,d),d&&On(()=>{u&&(c||(c=In(t,ei,{x:384,duration:250,delay:0},!0)),c.run(1))}),u=!0)},o(d){D(r.$$.fragment,d),D(l.$$.fragment,d),d&&(c||(c=In(t,ei,{x:384,duration:250,delay:0},!1)),c.run(0)),u=!1},d(d){d&&S(t),st(r),st(l),d&&c&&c.end(),f=!1,h()}}}function th(n,t){let e,r,i=t[13].id+"",o,s,a,l=t[13].hash+"",c,u,f,h;function d(){return t[8](t[13])}return{key:n,first:null,c(){e=Y("button"),r=Y("p"),o=wt(i),s=rt(),a=Y("p"),c=wt(l),u=rt(),this.h()},l(g){e=q(g,"BUTTON",{class:!0});var m=$(e);r=q(m,"P",{class:!0});var p=$(r);o=vt(p,i),p.forEach(S),s=nt(m),a=q(m,"P",{class:!0});var v=$(a);c=vt(v,l),v.forEach(S),u=nt(m),m.forEach(S),this.h()},h(){var g;N(r,"class","w-full text-left truncate"),N(a,"class","w-full text-right"),N(e,"class","flex justify-between w-full odd:bg-base-200/40 hover:bg-base-200"),Nr(e,"bg-negative",t[13].error),Nr(e,"bg-warning",(g=t[13].opts)==null?void 0:g.noResolve),this.first=e},m(g,m){R(g,e,m),z(e,r),z(r,o),z(e,s),z(e,a),z(a,c),z(e,u),f||(h=he(e,"click",d),f=!0)},p(g,m){var p;t=g,m&4&&i!==(i=t[13].id+"")&&Le(o,i),m&4&&l!==(l=t[13].hash+"")&&Le(c,l),m&4&&Nr(e,"bg-negative",t[13].error),m&4&&Nr(e,"bg-warning",(p=t[13].opts)==null?void 0:p.noResolve)},d(g){g&&S(e),f=!1,h()}}}function eh(n){let t,e;return t=new s_({props:{query:n[1]}}),t.$on("close",n[9]),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i&2&&(o.query=r[1]),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function PI(n){let t,e=[],r=new Map,i,o=$c.isQuery(n[1]),s,a,l=ue(n[2].entries());const c=f=>f[12];for(let f=0;f<l.length;f+=1){let h=Xd(n,l,f),d=c(h);r.set(d,e[f]=th(d,h))}let u=o&&eh(n);return{c(){t=Y("section");for(let f=0;f<e.length;f+=1)e[f].c();i=rt(),u&&u.c(),s=ft(),this.h()},l(f){t=q(f,"SECTION",{class:!0});var h=$(t);for(let d=0;d<e.length;d+=1)e[d].l(h);h.forEach(S),i=nt(f),u&&u.l(f),s=ft(),this.h()},h(){N(t,"class","")},m(f,h){R(f,t,h);for(let d=0;d<e.length;d+=1)e[d]&&e[d].m(t,null);R(f,i,h),u&&u.m(f,h),R(f,s,h),a=!0},p(f,h){h&6&&(l=ue(f[2].entries()),e=dh(e,h,c,1,f,l,r,t,o_,th,null,Xd)),h&2&&(o=$c.isQuery(f[1])),o?u?(u.p(f,h),h&2&&O(u,1)):(u=eh(f),u.c(),O(u,1),u.m(s.parentNode,s)):u&&(jt(),D(u,1,1,()=>{u=null}),Lt())},i(f){a||(O(u),a=!0)},o(f){D(u),a=!1},d(f){f&&(S(t),S(i),S(s));for(let h=0;h<e.length;h+=1)e[h].d();u&&u.d(f)}}}function NI(n){let t,e;return t=new gS({props:{history:n[4]}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p:Qt,i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function CI(n){let t,e;return t=new MI({props:{history:n[4]}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p:Qt,i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function FI(n){let t,e,r,i,o,s;return t=new Ja({props:{title:"Inspect Queries",compact:!0,$$slots:{default:[PI]},$$scope:{ctx:n}}}),r=new Ja({props:{title:"Inspect Inputs",compact:!0,$$slots:{default:[NI]},$$scope:{ctx:n}}}),o=new Ja({props:{title:"View Input History",compact:!0,$$slots:{default:[CI]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment),e=rt(),ct(r.$$.fragment),i=rt(),ct(o.$$.fragment)},l(a){lt(t.$$.fragment,a),e=nt(a),lt(r.$$.fragment,a),i=nt(a),lt(o.$$.fragment,a)},m(a,l){at(t,a,l),R(a,e,l),at(r,a,l),R(a,i,l),at(o,a,l),s=!0},p(a,l){const c={};l&2054&&(c.$$scope={dirty:l,ctx:a}),t.$set(c);const u={};l&2048&&(u.$$scope={dirty:l,ctx:a}),r.$set(u);const f={};l&2048&&(f.$$scope={dirty:l,ctx:a}),o.$set(f)},i(a){s||(O(t.$$.fragment,a),O(r.$$.fragment,a),O(o.$$.fragment,a),s=!0)},o(a){D(t.$$.fragment,a),D(r.$$.fragment,a),D(o.$$.fragment,a),s=!1},d(a){a&&(S(e),S(i)),st(t,a),st(r,a),st(o,a)}}}function jI(n){let t,e,r,i,o,s;return e=new Tn({props:{src:yl,class:"w-4 h-4"}}),{c(){t=Y("button"),ct(e.$$.fragment),this.h()},l(a){t=q(a,"BUTTON",{class:!0});var l=$(t);lt(e.$$.fragment,l),l.forEach(S),this.h()},h(){N(t,"class","fixed right-4 top-16 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-0")},m(a,l){R(a,t,l),at(e,t,null),i=!0,o||(s=he(t,"click",n[10]),o=!0)},p:Qt,i(a){i||(O(e.$$.fragment,a),a&&On(()=>{i&&(r||(r=In(t,bl,{axis:"x"},!0)),r.run(1))}),i=!0)},o(a){D(e.$$.fragment,a),a&&(r||(r=In(t,bl,{axis:"x"},!1)),r.run(0)),i=!1},d(a){a&&S(t),st(e),a&&r&&r.end(),o=!1,s()}}}function LI(n){let t,e=n_(),r,i,o=n[0]&&Zd(n),s=e&&jI(n);const a=n[6].default,l=Oe(a,n,n[11],null);return{c(){o&&o.c(),t=rt(),s&&s.c(),r=rt(),l&&l.c()},l(c){o&&o.l(c),t=nt(c),s&&s.l(c),r=nt(c),l&&l.l(c)},m(c,u){o&&o.m(c,u),R(c,t,u),s&&s.m(c,u),R(c,r,u),l&&l.m(c,u),i=!0},p(c,[u]){c[0]?o?(o.p(c,u),u&1&&O(o,1)):(o=Zd(c),o.c(),O(o,1),o.m(t.parentNode,t)):o&&(jt(),D(o,1,1,()=>{o=null}),Lt()),e&&s.p(c,u),l&&l.p&&(!i||u&2048)&&ke(l,a,c,c[11],i?Ee(a,c[11],u,null):De(c[11]),null)},i(c){i||(O(o),O(s),O(l,c),i=!0)},o(c){D(o),D(s),D(l,c),i=!1},d(c){c&&(S(t),S(r)),o&&o.d(c),s&&s.d(c),l&&l.d(c)}}}function RI(n,t,e){let r,i;Ae(n,r_,m=>e(2,i=m));let{$$slots:o={},$$scope:s}=t;i_(qe({}));let a=!1,l;__(()=>{}),Pr(()=>{const m=p=>{p.key==="Escape"&&(e(0,a=!1),p.stopPropagation()),p.key.toLowerCase()==="e"&&p.shiftKey&&(p.ctrlKey||p.metaKey)&&(e(0,a=!0),p.stopPropagation())};return window.addEventListener("keydown",m),()=>window.removeEventListener("keydown",m)});const c=ph();Ae(n,c,m=>e(5,r=m));const u=new u0,f=()=>e(0,a=!a),h=m=>e(1,l=m),d=()=>e(1,l=null),g=()=>e(0,a=!a);return n.$$set=m=>{"$$scope"in m&&e(11,s=m.$$scope)},n.$$.update=()=>{n.$$.dirty&32&&u.push(r)},[a,l,i,c,u,r,o,f,h,d,g,s]}class UI extends ae{constructor(t){super(),le(this,t,RI,LI,ee,{})}}function VI(n){let t;const e=n[0].default,r=Oe(e,n,n[1],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&2)&&ke(r,e,i,i[1],t?Ee(e,i[1],o,null):De(i[1]),null)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function xI(n){let t,e;return t=new UI({props:{$$slots:{default:[zI]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,i){const o={};i&2&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function zI(n){let t;const e=n[0].default,r=Oe(e,n,n[1],null);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&2)&&ke(r,e,i,i[1],t?Ee(e,i[1],o,null):De(i[1]),null)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function WI(n){let t,e,r,i;const o=[xI,VI],s=[];function a(l,c){return 1}return t=a(),e=s[t]=o[t](n),{c(){e.c(),r=ft()},l(l){e.l(l),r=ft()},m(l,c){s[t].m(l,c),R(l,r,c),i=!0},p(l,[c]){e.p(l,c)},i(l){i||(O(e),i=!0)},o(l){D(e),i=!1},d(l){l&&S(r),s[t].d(l)}}}function HI(n,t,e){let{$$slots:r={},$$scope:i}=t;return n.$$set=o=>{"$$scope"in o&&e(1,i=o.$$scope)},[r,i]}class qI extends ae{constructor(t){super(),le(this,t,HI,WI,ee,{})}}const YI=n=>({}),nh=n=>({});function rh(n){let t,e,r;function i(s){n[41](s)}let o={title:n[0],logo:n[1],lightLogo:n[2],darkLogo:n[3],neverShowQueries:n[4],fullWidth:n[21],maxWidth:n[20],hideSidebar:n[5],githubRepo:n[8],slackCommunity:n[11],xProfile:n[9],blueskyProfile:n[10],algolia:n[7],sidebarFrontMatter:n[14]};return n[17]!==void 0&&(o.mobileSidebarOpen=n[17]),t=new t2({props:o}),Kn.push(()=>bh(t,"mobileSidebarOpen",i)),{c(){ct(t.$$.fragment)},l(s){lt(t.$$.fragment,s)},m(s,a){at(t,s,a),r=!0},p(s,a){const l={};a[0]&1&&(l.title=s[0]),a[0]&2&&(l.logo=s[1]),a[0]&4&&(l.lightLogo=s[2]),a[0]&8&&(l.darkLogo=s[3]),a[0]&16&&(l.neverShowQueries=s[4]),a[0]&2097152&&(l.fullWidth=s[21]),a[0]&1048576&&(l.maxWidth=s[20]),a[0]&32&&(l.hideSidebar=s[5]),a[0]&256&&(l.githubRepo=s[8]),a[0]&2048&&(l.slackCommunity=s[11]),a[0]&512&&(l.xProfile=s[9]),a[0]&1024&&(l.blueskyProfile=s[10]),a[0]&128&&(l.algolia=s[7]),a[0]&16384&&(l.sidebarFrontMatter=s[14]),!e&&a[0]&131072&&(e=!0,l.mobileSidebarOpen=s[17],_h(()=>e=!1)),t.$set(l)},i(s){r||(O(t.$$.fragment,s),r=!0)},o(s){D(t.$$.fragment,s),r=!1},d(s){st(t,s)}}}function ih(n){let t,e,r,i;function o(a){n[42](a)}let s={fileTree:n[24],title:n[0],logo:n[1],homePageName:n[12],builtWithEvidence:n[6],hideHeader:n[19],sidebarFrontMatter:n[14],sidebarDepth:n[13]};return n[17]!==void 0&&(s.mobileSidebarOpen=n[17]),e=new J2({props:s}),Kn.push(()=>bh(e,"mobileSidebarOpen",o)),{c(){t=Y("div"),ct(e.$$.fragment),this.h()},l(a){t=q(a,"DIV",{class:!0});var l=$(t);lt(e.$$.fragment,l),l.forEach(S),this.h()},h(){N(t,"class","print:hidden")},m(a,l){R(a,t,l),at(e,t,null),i=!0},p(a,l){const c={};l[0]&1&&(c.title=a[0]),l[0]&2&&(c.logo=a[1]),l[0]&4096&&(c.homePageName=a[12]),l[0]&64&&(c.builtWithEvidence=a[6]),l[0]&524288&&(c.hideHeader=a[19]),l[0]&16384&&(c.sidebarFrontMatter=a[14]),l[0]&8192&&(c.sidebarDepth=a[13]),!r&&l[0]&131072&&(r=!0,c.mobileSidebarOpen=a[17],_h(()=>r=!1)),e.$set(c)},i(a){i||(O(e.$$.fragment,a),i=!0)},o(a){D(e.$$.fragment,a),i=!1},d(a){a&&S(t),st(e)}}}function oh(n){let t,e,r=n[15].route.id!=="/settings"&&sh(n);return{c(){t=Y("div"),r&&r.c(),this.h()},l(i){t=q(i,"DIV",{class:!0});var o=$(t);r&&r.l(o),o.forEach(S),this.h()},h(){N(t,"class","print:hidden")},m(i,o){R(i,t,o),r&&r.m(t,null),e=!0},p(i,o){i[15].route.id!=="/settings"?r?(r.p(i,o),o[0]&32768&&O(r,1)):(r=sh(i),r.c(),O(r,1),r.m(t,null)):r&&(jt(),D(r,1,1,()=>{r=null}),Lt())},i(i){e||(O(r),e=!0)},o(i){D(r),e=!1},d(i){i&&S(t),r&&r.d()}}}function sh(n){let t,e;return t=new uS({props:{fileTree:n[24]}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p:Qt,i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function KI(n){let t,e;return t=new i2({}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p:Qt,i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function $I(n){let t,e;const r=n[40].content,i=Oe(r,n,n[43],nh);return{c(){t=Y("article"),i&&i.c(),this.h()},l(o){t=q(o,"ARTICLE",{id:!0,class:!0});var s=$(t);i&&i.l(s),s.forEach(S),this.h()},h(){N(t,"id","evidence-main-article"),N(t,"class","select-text markdown pb-10")},m(o,s){R(o,t,s),i&&i.m(t,null),e=!0},p(o,s){i&&i.p&&(!e||s[1]&4096)&&ke(i,r,o,o[43],e?Ee(r,o[43],s,YI):De(o[43]),nh)},i(o){e||(O(i,o),e=!0)},o(o){D(i,o),e=!1},d(o){o&&S(t),i&&i.d(o)}}}function ah(n){let t,e,r;return e=new eS({props:{hideHeader:n[19]}}),{c(){t=Y("div"),ct(e.$$.fragment),this.h()},l(i){t=q(i,"DIV",{class:!0});var o=$(t);lt(e.$$.fragment,o),o.forEach(S),this.h()},h(){N(t,"class","print:hidden")},m(i,o){R(i,t,o),at(e,t,null),r=!0},p(i,o){const s={};o[0]&524288&&(s.hideHeader=i[19]),e.$set(s)},i(i){r||(O(e.$$.fragment,i),r=!0)},o(i){D(e.$$.fragment,i),r=!1},d(i){i&&S(t),st(e)}}}function lh(n){let t,e;return t=new n2({}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function JI(n){let t,e,r,i,o,s,a,l,c,u,f,h,d,g,m=!n[16]&&vl,p,v;e=new hS({});let b=!n[19]&&rh(n),_=!n[5]&&n[14]!=="never"&&n[15].route.id!=="/settings"&&ih(n),I=!n[22]&&n[15].route.id!=="/settings"&&oh(n);const y=[$I,KI],A=[];function w(P,L){return P[16]?1:0}c=w(n),u=A[c]=y[c](n);let F=!n[18]&&n[15].route.id!=="/settings"&&ah(n),T=m&&lh();return{c(){t=Y("div"),ct(e.$$.fragment),r=rt(),b&&b.c(),i=rt(),o=Y("div"),_&&_.c(),s=rt(),a=Y("main"),I&&I.c(),l=rt(),u.c(),h=rt(),F&&F.c(),g=rt(),T&&T.c(),p=ft(),this.h()},l(P){t=q(P,"DIV",{"data-sveltekit-preload-data":!0,class:!0});var L=$(t);lt(e.$$.fragment,L),r=nt(L),b&&b.l(L),i=nt(L),o=q(L,"DIV",{class:!0,style:!0});var B=$(o);_&&_.l(B),s=nt(B),a=q(B,"MAIN",{class:!0});var M=$(a);I&&I.l(M),l=nt(M),u.l(M),M.forEach(S),h=nt(B),F&&F.l(B),B.forEach(S),L.forEach(S),g=nt(P),T&&T.l(P),p=ft(),this.h()},h(){N(a,"class",f=(n[15].route.id==="/settings"?"w-full mt-16 sm:mt-20 ":(!n[5]&&!["hide","never"].includes(n[14])?"md:pl-8 ":"")+(n[18]?"":"md:pr-8 ")+(n[19]?n[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":n[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"),N(o,"class",d=(n[21]?"max-w-full ":n[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"),Qs(o,"max-width",n[20]+"px"),N(t,"data-sveltekit-preload-data",n[23]),N(t,"class","antialiased")},m(P,L){R(P,t,L),at(e,t,null),z(t,r),b&&b.m(t,null),z(t,i),z(t,o),_&&_.m(o,null),z(o,s),z(o,a),I&&I.m(a,null),z(a,l),A[c].m(a,null),z(o,h),F&&F.m(o,null),R(P,g,L),T&&T.m(P,L),R(P,p,L),v=!0},p(P,L){P[19]?b&&(jt(),D(b,1,1,()=>{b=null}),Lt()):b?(b.p(P,L),L[0]&524288&&O(b,1)):(b=rh(P),b.c(),O(b,1),b.m(t,i)),!P[5]&&P[14]!=="never"&&P[15].route.id!=="/settings"?_?(_.p(P,L),L[0]&49184&&O(_,1)):(_=ih(P),_.c(),O(_,1),_.m(o,s)):_&&(jt(),D(_,1,1,()=>{_=null}),Lt()),!P[22]&&P[15].route.id!=="/settings"?I?(I.p(P,L),L[0]&4227072&&O(I,1)):(I=oh(P),I.c(),O(I,1),I.m(a,l)):I&&(jt(),D(I,1,1,()=>{I=null}),Lt());let B=c;c=w(P),c===B?A[c].p(P,L):(jt(),D(A[B],1,1,()=>{A[B]=null}),Lt(),u=A[c],u?u.p(P,L):(u=A[c]=y[c](P),u.c()),O(u,1),u.m(a,null)),(!v||L[0]&5029920&&f!==(f=(P[15].route.id==="/settings"?"w-full mt-16 sm:mt-20 ":(!P[5]&&!["hide","never"].includes(P[14])?"md:pl-8 ":"")+(P[18]?"":"md:pr-8 ")+(P[19]?P[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":P[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"))&&N(a,"class",f),!P[18]&&P[15].route.id!=="/settings"?F?(F.p(P,L),L[0]&294912&&O(F,1)):(F=ah(P),F.c(),O(F,1),F.m(o,null)):F&&(jt(),D(F,1,1,()=>{F=null}),Lt()),(!v||L[0]&3145728&&d!==(d=(P[21]?"max-w-full ":P[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"))&&N(o,"class",d),(!v||L[0]&1048576)&&Qs(o,"max-width",P[20]+"px"),L[0]&98304&&(m=!P[16]&&vl),m?T?L[0]&98304&&O(T,1):(T=lh(),T.c(),O(T,1),T.m(p.parentNode,p)):T&&(jt(),D(T,1,1,()=>{T=null}),Lt())},i(P){v||(O(e.$$.fragment,P),O(b),O(_),O(I),O(u),O(F),O(T),v=!0)},o(P){D(e.$$.fragment,P),D(b),D(_),D(I),D(u),D(F),D(T),v=!1},d(P){P&&(S(t),S(g),S(p)),st(e),b&&b.d(),_&&_.d(),I&&I.d(),A[c].d(),F&&F.d(),T&&T.d(P)}}}function QI(n){let t,e,r,i,o;const s=n[40].default,a=Oe(s,n,n[43],null);return e=new b0({}),i=new qI({props:{$$slots:{default:[JI]},$$scope:{ctx:n}}}),{c(){a&&a.c(),t=rt(),ct(e.$$.fragment),r=rt(),ct(i.$$.fragment)},l(l){a&&a.l(l),t=nt(l),lt(e.$$.fragment,l),r=nt(l),lt(i.$$.fragment,l)},m(l,c){a&&a.m(l,c),R(l,t,c),at(e,l,c),R(l,r,c),at(i,l,c),o=!0},p(l,c){a&&a.p&&(!o||c[1]&4096)&&ke(a,s,l,l[43],o?Ee(s,l[43],c,null):De(l[43]),null);const u={};c[0]&8388607|c[1]&4096&&(u.$$scope={dirty:c,ctx:l}),i.$set(u)},i(l){o||(O(a,l),O(e.$$.fragment,l),O(i.$$.fragment,l),o=!0)},o(l){D(a,l),D(e.$$.fragment,l),D(i.$$.fragment,l),o=!1},d(l){l&&(S(t),S(r)),a&&a.d(l),st(e,l),st(i,l)}}}function GI(n){const t=new Map;function e(r,i=""){const o=r.href||i;r.isPage&&t.set(decodeURI(o),r),r.children&&Object.entries(r.children).forEach(([s,a])=>{const l=`${o}/${s}`;e(a,l)})}return e(n),t}function XI(n,t,e){let r,i,o,s,a,l,c,u,f,h,d,g,m,p,v,b,_;Ae(n,Ma,et=>e(15,b=et)),Ae(n,yh,et=>e(16,_=et));let{$$slots:I={},$$scope:y}=t;{const et=document.getElementById("__evidence_project_splash");et==null||et.remove()}let{data:A}=t,{title:w=void 0}=t,{logo:F=void 0}=t,{lightLogo:T=void 0}=t,{darkLogo:P=void 0}=t,{neverShowQueries:L=!1}=t,{fullWidth:B=!1}=t,{hideSidebar:M=!1}=t,{builtWithEvidence:j=!0}=t,{algolia:V=void 0}=t,{githubRepo:U=void 0}=t,{xProfile:H=void 0}=t,{blueskyProfile:C=void 0}=t,{slackCommunity:G=void 0}=t,{maxWidth:J=void 0}=t,{homePageName:X="Home"}=t,{hideBreadcrumbs:Dt=!1}=t,{hideHeader:Bt=!1}=t,{hideTOC:Pt=!1}=t,{sidebarDepth:K=3}=t;const ye="hover";let Yt=!1,Te=A==null?void 0:A.pagesManifest;Pr(async()=>{if(!("serviceWorker"in navigator))return;const et=await navigator.serviceWorker.register(xt("/fix-tprotocol-service-worker.js"),{scope:xt("/"),type:"classic"});console.debug("[fix-tprotocol-service-worker] Service Worker registered",{registration:et})});const{syncThemeAttribute:We,cycleAppearance:pn,selectedAppearance:Re,setAppearance:Mn,activeAppearance:fn}=hh();Ae(n,Re,et=>e(44,p=et)),Ae(n,fn,et=>e(45,v=et)),Pr(()=>{const et=dt=>{dt.key.toLowerCase()==="l"&&dt.shiftKey&&(dt.ctrlKey||dt.metaKey)&&pn()};return window.addEventListener("keydown",et),()=>window.removeEventListener("keydown",et)}),Pr(()=>We(document.querySelector("html"))),Pr(()=>{let et;const dt=()=>{et=v,p==="dark"&&Mn("light")},Kt=()=>{et==="dark"&&Mn("dark")};return window.addEventListener("beforeprint",dt),window.addEventListener("afterprint",Kt),()=>{window.removeEventListener("beforeprint",dt),window.removeEventListener("afterprint",Kt)}});function Vn(et){Yt=et,e(17,Yt),e(16,_)}function Vr(et){Yt=et,e(17,Yt),e(16,_)}return n.$$set=et=>{"data"in et&&e(27,A=et.data),"title"in et&&e(0,w=et.title),"logo"in et&&e(1,F=et.logo),"lightLogo"in et&&e(2,T=et.lightLogo),"darkLogo"in et&&e(3,P=et.darkLogo),"neverShowQueries"in et&&e(4,L=et.neverShowQueries),"fullWidth"in et&&e(28,B=et.fullWidth),"hideSidebar"in et&&e(5,M=et.hideSidebar),"builtWithEvidence"in et&&e(6,j=et.builtWithEvidence),"algolia"in et&&e(7,V=et.algolia),"githubRepo"in et&&e(8,U=et.githubRepo),"xProfile"in et&&e(9,H=et.xProfile),"blueskyProfile"in et&&e(10,C=et.blueskyProfile),"slackCommunity"in et&&e(11,G=et.slackCommunity),"maxWidth"in et&&e(29,J=et.maxWidth),"homePageName"in et&&e(12,X=et.homePageName),"hideBreadcrumbs"in et&&e(30,Dt=et.hideBreadcrumbs),"hideHeader"in et&&e(31,Bt=et.hideHeader),"hideTOC"in et&&e(32,Pt=et.hideTOC),"sidebarDepth"in et&&e(13,K=et.sidebarDepth),"$$scope"in et&&e(43,y=et.$$scope)},n.$$.update=()=>{var et;n.$$.dirty[0]&65536&&_&&e(17,Yt=!1),n.$$.dirty[0]&32768|n.$$.dirty[1]&256&&e(34,i=(et=r.get(b.route.id))==null?void 0:et.frontMatter),n.$$.dirty[1]&8&&e(14,o=i==null?void 0:i.sidebar),n.$$.dirty[0]&16384&&(["show","hide","never"].includes(o)||e(14,o=void 0)),n.$$.dirty[1]&8&&e(38,s=i==null?void 0:i.hide_breadcrumbs),n.$$.dirty[0]&1073741824|n.$$.dirty[1]&128&&e(22,a=s??Dt),n.$$.dirty[1]&8&&e(37,l=i==null?void 0:i.full_width),n.$$.dirty[0]&268435456|n.$$.dirty[1]&64&&e(21,c=l??B),n.$$.dirty[1]&8&&e(36,u=i==null?void 0:i.max_width),n.$$.dirty[0]&536870912|n.$$.dirty[1]&32&&e(20,f=u??J),n.$$.dirty[1]&8&&e(35,h=i==null?void 0:i.hide_header),n.$$.dirty[1]&17&&e(19,d=h??Bt),n.$$.dirty[1]&8&&e(33,g=i==null?void 0:i.hide_toc),n.$$.dirty[1]&6&&e(18,m=g??Pt)},e(39,r=GI(Te)),[w,F,T,P,L,M,j,V,U,H,C,G,X,K,o,b,_,Yt,m,d,f,c,a,ye,Te,Re,fn,A,B,J,Dt,Bt,Pt,g,i,h,u,l,s,r,I,Vn,Vr,y]}class ZI extends ae{constructor(t){super(),le(this,t,XI,QI,ee,{data:27,title:0,logo:1,lightLogo:2,darkLogo:3,neverShowQueries:4,fullWidth:28,hideSidebar:5,builtWithEvidence:6,algolia:7,githubRepo:8,xProfile:9,blueskyProfile:10,slackCommunity:11,maxWidth:29,homePageName:12,hideBreadcrumbs:30,hideHeader:31,hideTOC:32,sidebarDepth:13},null,[-1,-1])}}const tO=n=>({}),ch=n=>({slot:"content"});function eO(n){let t;const e=n[1].default,r=Oe(e,n,n[2],ch);return{c(){r&&r.c()},l(i){r&&r.l(i)},m(i,o){r&&r.m(i,o),t=!0},p(i,o){r&&r.p&&(!t||o&4)&&ke(r,e,i,i[2],t?Ee(e,i[2],o,tO):De(i[2]),ch)},i(i){t||(O(r,i),t=!0)},o(i){D(r,i),t=!1},d(i){r&&r.d(i)}}}function nO(n){let t,e;return t=new ZI({props:{data:n[0],$$slots:{content:[eO]},$$scope:{ctx:n}}}),{c(){ct(t.$$.fragment)},l(r){lt(t.$$.fragment,r)},m(r,i){at(t,r,i),e=!0},p(r,[i]){const o={};i&1&&(o.data=r[0]),i&4&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(r){e||(O(t.$$.fragment,r),e=!0)},o(r){D(t.$$.fragment,r),e=!1},d(r){st(t,r)}}}function rO(n,t,e){let{$$slots:r={},$$scope:i}=t,{data:o}=t;return n.$$set=s=>{"data"in s&&e(0,o=s.data),"$$scope"in s&&e(2,i=s.$$scope)},[o,r,i]}class _O extends ae{constructor(t){super(),le(this,t,rO,nO,ee,{data:0})}}export{_O as component,gO as universal};
