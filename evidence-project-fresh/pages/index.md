---
title: Domo Data Loader
---

# Domo Data Loader

Load datasets from Domo into DuckDB for analysis.


<!-- Domo Dataset Loader -->
<div class="workflow-picker-section">
  <div id="domo-workflow-picker" class="workflow-picker">
    <div class="workflow-step">
      <label for="dataset-selector">Select Dataset:</label>
      <select id="dataset-selector" class="dataset-dropdown">
        <option value="">Choose a dataset...</option>
      </select>
    </div>

    <div id="dataset-preview" class="dataset-preview" style="display: none;">
      <h4>Dataset Information</h4>
      <div id="preview-content" class="preview-content">
        <div id="dataset-info" class="dataset-info"></div>

        <h5>Schema</h5>
        <div id="schema-table" class="schema-table"></div>

        <div class="preview-actions">
          <button id="preview-btn" class="btn btn-secondary">Preview Data</button>
        </div>

        <div id="data-preview" class="data-preview" style="display: none;"></div>
      </div>
    </div>

    <div id="loading-config" class="workflow-step" style="display: none;">
      <h4>Loading Configuration</h4>
      <div class="config-grid">
        <div class="config-item">
          <label for="table-name">Table Name in DuckDB:</label>
          <input id="table-name" type="text" placeholder="Enter table name" />
        </div>

        <div class="config-item">
          <label for="refresh-mode">Refresh Mode:</label>
          <select id="refresh-mode">
            <option value="replace">Replace existing data</option>
            <option value="append">Append to existing data</option>
          </select>
        </div>
      </div>
    </div>

    <div id="workflow-actions" class="workflow-actions" style="display: none;">
      <button id="load-dataset-btn" class="btn btn-primary">📊 Load Dataset into DuckDB</button>
    </div>

    <div id="loading-status" class="loading-status" style="display: none;">
      <div class="loading-spinner"></div>
      <p>Loading...</p>
    </div>
  </div>
</div>

## Loaded Datasets

<div id="loaded-datasets-section">
  <p>Once you load datasets from Domo, they will appear here and you can query them using SQL.</p>
</div>





<style>
  .domo-banner {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #00d4aa;
    border-radius: 12px;
    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
    color: #065f46;
    text-align: center;
  }

  .domo-banner h3 {
    margin: 0 0 10px 0;
    color: #047857;
  }

  .dev-banner {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #f59e0b;
    border-radius: 12px;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    text-align: center;
  }

  .dev-banner h3 {
    margin: 0 0 10px 0;
    color: #d97706;
  }

  .quick-start-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 30px 0;
  }

  .quick-start-card {
    padding: 25px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .quick-start-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .quick-start-card h4 {
    margin: 0 0 15px 0;
    color: #1f2937;
    font-size: 18px;
  }

  .quick-start-card p {
    margin: 0 0 20px 0;
    color: #6b7280;
    line-height: 1.5;
  }

  .btn-primary, .btn-secondary {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-primary {
    background-color: #2563eb;
    color: white;
  }

  .btn-primary:hover {
    background-color: #1d4ed8;
    text-decoration: none;
    color: white;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover {
    background-color: #e5e7eb;
    text-decoration: none;
    color: #374151;
  }



  .next-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
  }

  .step {
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .step h4 {
    margin: 0 0 10px 0;
    color: #1f2937;
    font-size: 16px;
  }

  .step p {
    margin: 0;
    color: #6b7280;
    line-height: 1.5;
  }

  .step a {
    color: #2563eb;
    text-decoration: none;
  }

  .step a:hover {
    text-decoration: underline;
  }

  /* Workflow Toggle */
  .workflow-toggle {
    margin: 30px 0;
    text-align: center;
  }

  .workflow-toggle button {
    margin: 0 10px;
  }

  /* Workflow Picker Section */
  .workflow-picker-section {
    margin: 30px 0;
    padding: 30px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background-color: #f9fafb;
  }

  .workflow-picker-section h3 {
    margin: 0 0 10px 0;
    color: #1f2937;
  }

  .workflow-picker-section p {
    margin: 0 0 20px 0;
    color: #6b7280;
  }

  /* Workflow Picker Styles */
  .workflow-picker {
    max-width: 800px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .workflow-step {
    margin: 25px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: white;
  }

  .workflow-step label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
  }

  .dataset-dropdown,
  .workflow-step input,
  .workflow-step select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
  }

  .dataset-dropdown:focus,
  .workflow-step input:focus,
  .workflow-step select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .dataset-preview {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: white;
  }

  .dataset-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
  }

  .info-item {
    padding: 10px;
    background-color: #f3f4f6;
    border-radius: 4px;
  }

  .schema-table table,
  .data-preview table,
  .data-preview-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }

  .schema-table th,
  .schema-table td,
  .data-preview th,
  .data-preview td,
  .data-preview-table th,
  .data-preview-table td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #e5e7eb;
  }

  .schema-table th,
  .data-preview th,
  .data-preview-table th {
    background-color: #f3f4f6;
    font-weight: 600;
  }

  .data-preview {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #f9fafb;
  }

  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .workflow-actions {
    text-align: center;
    margin: 30px 0;
  }

  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin: 0 5px;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #e5e7eb;
  }

  .loading-status {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
    text-align: center;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-overlay p {
    color: white;
    font-size: 16px;
    margin: 0;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .preview-actions {
    margin: 15px 0;
  }

  .data-preview {
    margin-top: 20px;
    max-height: 300px;
    overflow: auto;
  }

  /* Analysis Section */
  #analysis-section {
    margin: 40px 0;
    padding: 30px;
    border: 2px solid #10b981;
    border-radius: 12px;
    background-color: #f0fdf4;
  }

  #analysis-section h2 {
    margin: 0 0 20px 0;
    color: #047857;
  }

  .loaded-datasets-summary {
    margin: 15px 0;
  }

  .loaded-datasets-summary ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .loaded-datasets-summary li {
    margin: 5px 0;
  }

  .analysis-examples {
    margin: 30px 0;
  }

  .analysis-examples h3 {
    color: #047857;
    margin-bottom: 15px;
  }

  .query-example {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #d1fae5;
    border-radius: 8px;
    background-color: white;
  }

  .query-example h4 {
    margin: 0 0 10px 0;
    color: #065f46;
  }

  .query-example pre {
    margin: 10px 0 0 0;
    padding: 15px;
    background-color: #f3f4f6;
    border-radius: 6px;
    overflow-x: auto;
  }

  .query-example code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
  }
</style>
