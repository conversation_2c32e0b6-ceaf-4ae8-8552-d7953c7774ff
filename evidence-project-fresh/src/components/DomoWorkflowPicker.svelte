<script>
  import { onMount } from 'svelte';
  import { writable } from 'svelte/store';

  // Component props
  export let onDatasetLoaded = () => {};
  export let showPreview = true;
  export let allowCustomTableName = true;

  // Reactive state
  let availableDatasets = [];
  let selectedDataset = null;
  let selectedDatasetId = '';
  let tableName = '';
  let refreshMode = 'replace';
  let isLoading = false;
  let loadingMessage = '';
  let previewData = null;
  let showDatasetPreview = false;

  // Error and success messages
  let errorMessage = '';
  let successMessage = '';

  onMount(async () => {
    try {
      await initializeComponent();
    } catch (error) {
      console.error('Failed to initialize DomoWorkflowPicker:', error);
      errorMessage = 'Failed to initialize component. Please refresh the page.';
    }
  });

  async function initializeComponent() {
    // Check if we're in Domo environment
    if (typeof window.domo !== 'undefined') {
      await loadDomoDatasets();
    } else {
      // Load mock datasets for development
      loadMockDatasets();
    }
  }

  async function loadDomoDatasets() {
    try {
      isLoading = true;
      loadingMessage = 'Loading available datasets...';
      
      const datasets = await window.domo.get('/data/v1/datasets');
      availableDatasets = datasets.map(dataset => ({
        id: dataset.id,
        name: dataset.name,
        description: dataset.description || 'No description available',
        schema: dataset.schema || [],
        rowCount: dataset.rowCount || 0,
        lastUpdated: dataset.lastUpdated || 'Unknown'
      }));
    } catch (error) {
      console.error('Failed to load Domo datasets:', error);
      errorMessage = 'Failed to load datasets from Domo. Please check your connection.';
    } finally {
      isLoading = false;
    }
  }

  function loadMockDatasets() {
    availableDatasets = [
      {
        id: 'sales-data-2024',
        name: 'Sales Data 2024',
        description: 'Comprehensive sales data including revenue, units sold, and customer information',
        schema: [
          { name: 'date', type: 'DATE' },
          { name: 'region', type: 'STRING' },
          { name: 'product_category', type: 'STRING' },
          { name: 'revenue', type: 'DECIMAL' },
          { name: 'units_sold', type: 'LONG' }
        ],
        rowCount: 15420,
        lastUpdated: '2024-01-16'
      },
      {
        id: 'customer-analytics',
        name: 'Customer Analytics',
        description: 'Customer behavior and demographic data for segmentation analysis',
        schema: [
          { name: 'customer_id', type: 'STRING' },
          { name: 'age_group', type: 'STRING' },
          { name: 'segment', type: 'STRING' },
          { name: 'lifetime_value', type: 'DECIMAL' },
          { name: 'acquisition_channel', type: 'STRING' }
        ],
        rowCount: 8750,
        lastUpdated: '2024-01-15'
      },
      {
        id: 'marketing-campaigns',
        name: 'Marketing Campaigns',
        description: 'Campaign performance metrics and ROI analysis',
        schema: [
          { name: 'campaign_id', type: 'STRING' },
          { name: 'campaign_name', type: 'STRING' },
          { name: 'start_date', type: 'DATE' },
          { name: 'end_date', type: 'DATE' },
          { name: 'spend', type: 'DECIMAL' },
          { name: 'impressions', type: 'LONG' },
          { name: 'clicks', type: 'LONG' },
          { name: 'conversions', type: 'LONG' }
        ],
        rowCount: 342,
        lastUpdated: '2024-01-14'
      }
    ];
  }

  function onDatasetChange() {
    selectedDataset = availableDatasets.find(d => d.id === selectedDatasetId);
    if (selectedDataset) {
      // Auto-generate table name
      tableName = selectedDataset.name.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_');
      
      showDatasetPreview = true;
      previewData = null;
      clearMessages();
    } else {
      showDatasetPreview = false;
      tableName = '';
    }
  }

  async function previewDataset() {
    if (!selectedDataset) return;

    try {
      isLoading = true;
      loadingMessage = 'Loading data preview...';
      
      // Fetch sample data
      const sampleData = await fetchSampleData(selectedDataset.id);
      previewData = sampleData;
      
    } catch (error) {
      console.error('Preview failed:', error);
      errorMessage = 'Failed to preview dataset data.';
    } finally {
      isLoading = false;
    }
  }

  async function loadDatasetIntoDuckDB() {
    if (!selectedDataset || !tableName.trim()) {
      errorMessage = 'Please select a dataset and provide a table name.';
      return;
    }

    try {
      isLoading = true;
      loadingMessage = 'Loading dataset into DuckDB...';
      clearMessages();

      // Validate table name
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tableName)) {
        throw new Error('Table name must start with a letter or underscore and contain only letters, numbers, and underscores.');
      }

      // Fetch full dataset
      const datasetData = await fetchDatasetData(selectedDataset.id);
      
      // Create DuckDB table
      await createDuckDBTable(tableName, selectedDataset, datasetData, refreshMode);
      
      successMessage = `Dataset "${selectedDataset.name}" loaded successfully into table "${tableName}"`;
      
      // Notify parent component
      onDatasetLoaded({
        datasetId: selectedDataset.id,
        datasetName: selectedDataset.name,
        tableName: tableName,
        rowCount: selectedDataset.rowCount
      });

    } catch (error) {
      console.error('Load failed:', error);
      errorMessage = `Failed to load dataset: ${error.message}`;
    } finally {
      isLoading = false;
    }
  }

  async function fetchSampleData(datasetId) {
    if (typeof window.domo !== 'undefined' && window.domo.get) {
      // Fetch sample from Domo API
      const response = await window.domo.get(`/data/v1/datasets/${datasetId}/data?limit=5`);
      return response;
    } else {
      // Mock sample data
      return {
        columns: selectedDataset.schema.map(col => col.name),
        rows: [
          ['2024-01-01', 'North America', 'Electronics', 15000, 45],
          ['2024-01-01', 'Europe', 'Clothing', 8500, 120],
          ['2024-01-02', 'Asia', 'Electronics', 12000, 35],
          ['2024-01-02', 'North America', 'Home & Garden', 6500, 80],
          ['2024-01-03', 'Europe', 'Electronics', 18000, 55]
        ]
      };
    }
  }

  async function fetchDatasetData(datasetId) {
    if (typeof window.domo !== 'undefined' && window.domo.get) {
      return await window.domo.get(`/data/v1/datasets/${datasetId}/data`);
    } else {
      // Return mock data for development
      return await fetchSampleData(datasetId);
    }
  }

  async function createDuckDBTable(tableName, dataset, data, mode) {
    // This would integrate with DuckDB-WASM in a real implementation
    console.log(`Creating DuckDB table: ${tableName}`);
    console.log('Dataset schema:', dataset.schema);
    console.log('Data rows:', data.rows?.length || 0);
    console.log('Refresh mode:', mode);
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In a real implementation, you would:
    // 1. Create the table schema based on dataset.schema
    // 2. Insert the data from data.rows
    // 3. Handle refresh mode (replace vs append)
    
    return Promise.resolve();
  }

  function clearMessages() {
    errorMessage = '';
    successMessage = '';
  }

  function formatRowCount(count) {
    return count.toLocaleString();
  }
</script>

<div class="workflow-picker">
  <div class="picker-header">
    <h3>Domo Dataset Workflow</h3>
    <p>Select and load Domo datasets into DuckDB for analysis</p>
  </div>

  {#if errorMessage}
    <div class="alert alert-error">
      <strong>Error:</strong> {errorMessage}
      <button on:click={clearMessages} class="alert-close">&times;</button>
    </div>
  {/if}

  {#if successMessage}
    <div class="alert alert-success">
      <strong>Success:</strong> {successMessage}
      <button on:click={clearMessages} class="alert-close">&times;</button>
    </div>
  {/if}

  <div class="workflow-step">
    <label for="dataset-select">Select Dataset:</label>
    <select 
      id="dataset-select" 
      bind:value={selectedDatasetId} 
      on:change={onDatasetChange}
      disabled={isLoading}
    >
      <option value="">Choose a dataset...</option>
      {#each availableDatasets as dataset}
        <option value={dataset.id}>
          {dataset.name} ({formatRowCount(dataset.rowCount)} rows)
        </option>
      {/each}
    </select>
  </div>

  {#if showDatasetPreview && selectedDataset}
    <div class="dataset-preview">
      <h4>Dataset Information</h4>
      <div class="dataset-info">
        <div class="info-item">
          <strong>Name:</strong> {selectedDataset.name}
        </div>
        <div class="info-item">
          <strong>Description:</strong> {selectedDataset.description}
        </div>
        <div class="info-item">
          <strong>Rows:</strong> {formatRowCount(selectedDataset.rowCount)}
        </div>
        <div class="info-item">
          <strong>Last Updated:</strong> {selectedDataset.lastUpdated}
        </div>
      </div>

      <h5>Schema</h5>
      <div class="schema-table">
        <table>
          <thead>
            <tr>
              <th>Column</th>
              <th>Type</th>
            </tr>
          </thead>
          <tbody>
            {#each selectedDataset.schema as column}
              <tr>
                <td>{column.name}</td>
                <td>{column.type}</td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>

      {#if showPreview}
        <div class="preview-actions">
          <button 
            on:click={previewDataset} 
            disabled={isLoading}
            class="btn btn-secondary"
          >
            Preview Data
          </button>
        </div>

        {#if previewData}
          <div class="data-preview">
            <h5>Data Preview (First 5 rows)</h5>
            <table>
              <thead>
                <tr>
                  {#each previewData.columns as column}
                    <th>{column}</th>
                  {/each}
                </tr>
              </thead>
              <tbody>
                {#each previewData.rows as row}
                  <tr>
                    {#each row as cell}
                      <td>{cell}</td>
                    {/each}
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {/if}
      {/if}
    </div>
  {/if}

  {#if selectedDataset}
    <div class="workflow-step">
      <h4>Loading Configuration</h4>
      <div class="config-grid">
        {#if allowCustomTableName}
          <div class="config-item">
            <label for="table-name">Table Name in DuckDB:</label>
            <input 
              id="table-name"
              type="text" 
              bind:value={tableName}
              placeholder="Enter table name"
              disabled={isLoading}
            />
          </div>
        {/if}
        
        <div class="config-item">
          <label for="refresh-mode">Refresh Mode:</label>
          <select id="refresh-mode" bind:value={refreshMode} disabled={isLoading}>
            <option value="replace">Replace existing data</option>
            <option value="append">Append to existing data</option>
          </select>
        </div>
      </div>
    </div>

    <div class="workflow-actions">
      <button 
        on:click={loadDatasetIntoDuckDB}
        disabled={isLoading || !tableName.trim()}
        class="btn btn-primary"
      >
        {isLoading ? 'Loading...' : 'Load Dataset into DuckDB'}
      </button>
    </div>
  {/if}

  {#if isLoading}
    <div class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{loadingMessage}</p>
    </div>
  {/if}
</div>

<style>
  .workflow-picker {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .picker-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .picker-header h3 {
    color: #1f2937;
    margin-bottom: 8px;
  }

  .picker-header p {
    color: #6b7280;
    margin: 0;
  }

  .workflow-step {
    margin: 25px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
  }

  .workflow-step label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
  }

  .workflow-step select,
  .workflow-step input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
  }

  .workflow-step select:focus,
  .workflow-step input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .dataset-preview {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: white;
  }

  .dataset-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
  }

  .info-item {
    padding: 10px;
    background-color: #f3f4f6;
    border-radius: 4px;
  }

  .schema-table table,
  .data-preview table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }

  .schema-table th,
  .schema-table td,
  .data-preview th,
  .data-preview td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #e5e7eb;
  }

  .schema-table th,
  .data-preview th {
    background-color: #f3f4f6;
    font-weight: 600;
  }

  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .workflow-actions {
    text-align: center;
    margin: 30px 0;
  }

  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #e5e7eb;
  }

  .alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    position: relative;
  }

  .alert-error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
  }

  .alert-success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
  }

  .alert-close {
    position: absolute;
    top: 8px;
    right: 12px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-overlay p {
    color: white;
    font-size: 16px;
    margin: 0;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .preview-actions {
    margin: 15px 0;
  }

  .data-preview {
    margin-top: 20px;
    max-height: 300px;
    overflow: auto;
  }
</style>
