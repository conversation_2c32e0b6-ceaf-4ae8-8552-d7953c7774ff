/**
 * Domo to DuckDB Connector
 * Handles the integration between Domo datasets and DuckDB for Evidence
 */

import * as duckdb from '@duckdb/duckdb-wasm';

export class DomoDuckDBConnector {
    constructor() {
        this.db = null;
        this.connection = null;
        this.isInitialized = false;
        this.loadedTables = new Map();
    }

    /**
     * Initialize DuckDB-WASM
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            // Select appropriate DuckDB bundle
            const JSDELIVR_BUNDLES = duckdb.getJsDelivrBundles();
            const bundle = await duckdb.selectBundle(JSDELIVR_BUNDLES);

            // Create worker
            const worker_url = URL.createObjectURL(
                new Blob([`importScripts("${bundle.mainWorker}");`], { type: 'text/javascript' })
            );

            const worker = new Worker(worker_url);
            const logger = new duckdb.ConsoleLogger();
            
            // Initialize DuckDB
            this.db = new duckdb.AsyncDuckDB(logger, worker);
            await this.db.instantiate(bundle.mainModule, bundle.pthreadWorker);
            
            // Create connection
            this.connection = await this.db.connect();
            
            URL.revokeObjectURL(worker_url);
            this.isInitialized = true;
            
            console.log('DuckDB-WASM initialized successfully');
        } catch (error) {
            console.error('Failed to initialize DuckDB-WASM:', error);
            throw new Error(`DuckDB initialization failed: ${error.message}`);
        }
    }

    /**
     * Create a table in DuckDB from Domo dataset
     */
    async createTableFromDomoDataset(tableName, dataset, data, refreshMode = 'replace') {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Validate inputs
            if (!tableName || !dataset || !data) {
                throw new Error('Missing required parameters: tableName, dataset, or data');
            }

            // Sanitize table name
            const sanitizedTableName = this.sanitizeTableName(tableName);

            // Handle refresh mode
            if (refreshMode === 'replace') {
                await this.dropTableIfExists(sanitizedTableName);
            }

            // Create table schema
            const createTableSQL = this.generateCreateTableSQL(sanitizedTableName, dataset.schema);
            await this.connection.query(createTableSQL);

            // Insert data
            await this.insertDataIntoTable(sanitizedTableName, dataset.schema, data);

            // Track loaded table
            this.loadedTables.set(sanitizedTableName, {
                originalName: tableName,
                datasetId: dataset.id,
                datasetName: dataset.name,
                rowCount: data.rows ? data.rows.length : 0,
                schema: dataset.schema,
                loadedAt: new Date().toISOString()
            });

            console.log(`Table ${sanitizedTableName} created successfully with ${data.rows?.length || 0} rows`);
            return sanitizedTableName;

        } catch (error) {
            console.error('Failed to create table from Domo dataset:', error);
            throw new Error(`Table creation failed: ${error.message}`);
        }
    }

    /**
     * Generate CREATE TABLE SQL from dataset schema
     */
    generateCreateTableSQL(tableName, schema) {
        const columns = schema.map(column => {
            const columnName = this.sanitizeColumnName(column.name);
            const columnType = this.mapDomoTypeToDuckDB(column.type);
            return `${columnName} ${columnType}`;
        }).join(', ');

        return `CREATE TABLE ${tableName} (${columns})`;
    }

    /**
     * Map Domo data types to DuckDB types
     */
    mapDomoTypeToDuckDB(domoType) {
        const typeMapping = {
            'STRING': 'VARCHAR',
            'LONG': 'BIGINT',
            'DOUBLE': 'DOUBLE',
            'DECIMAL': 'DECIMAL(18,2)',
            'DATE': 'DATE',
            'DATETIME': 'TIMESTAMP',
            'BOOLEAN': 'BOOLEAN'
        };

        return typeMapping[domoType.toUpperCase()] || 'VARCHAR';
    }

    /**
     * Insert data into DuckDB table
     */
    async insertDataIntoTable(tableName, schema, data) {
        if (!data.rows || data.rows.length === 0) {
            console.warn('No data rows to insert');
            return;
        }

        // Prepare column names
        const columnNames = schema.map(col => this.sanitizeColumnName(col.name));
        const placeholders = columnNames.map(() => '?').join(', ');
        
        const insertSQL = `INSERT INTO ${tableName} (${columnNames.join(', ')}) VALUES (${placeholders})`;

        // Prepare statement
        const stmt = await this.connection.prepare(insertSQL);

        try {
            // Insert data in batches for better performance
            const batchSize = 1000;
            for (let i = 0; i < data.rows.length; i += batchSize) {
                const batch = data.rows.slice(i, i + batchSize);
                
                for (const row of batch) {
                    // Convert row data to appropriate types
                    const convertedRow = this.convertRowData(row, schema);
                    await stmt.run(...convertedRow);
                }
            }
        } finally {
            await stmt.finalize();
        }
    }

    /**
     * Convert row data to appropriate types for DuckDB
     */
    convertRowData(row, schema) {
        return row.map((value, index) => {
            if (value === null || value === undefined) {
                return null;
            }

            const columnType = schema[index]?.type?.toUpperCase();
            
            switch (columnType) {
                case 'LONG':
                case 'BIGINT':
                    return parseInt(value, 10);
                case 'DOUBLE':
                case 'DECIMAL':
                    return parseFloat(value);
                case 'BOOLEAN':
                    return Boolean(value);
                case 'DATE':
                case 'DATETIME':
                    return new Date(value).toISOString();
                default:
                    return String(value);
            }
        });
    }

    /**
     * Drop table if it exists
     */
    async dropTableIfExists(tableName) {
        try {
            await this.connection.query(`DROP TABLE IF EXISTS ${tableName}`);
        } catch (error) {
            console.warn(`Failed to drop table ${tableName}:`, error);
        }
    }

    /**
     * Sanitize table name for DuckDB
     */
    sanitizeTableName(name) {
        return name
            .toLowerCase()
            .replace(/[^a-z0-9_]/g, '_')
            .replace(/^[0-9]/, '_$&')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    }

    /**
     * Sanitize column name for DuckDB
     */
    sanitizeColumnName(name) {
        return name
            .toLowerCase()
            .replace(/[^a-z0-9_]/g, '_')
            .replace(/^[0-9]/, '_$&')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    }

    /**
     * Execute a query against DuckDB
     */
    async query(sql) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const result = await this.connection.query(sql);
            return result;
        } catch (error) {
            console.error('Query execution failed:', error);
            throw new Error(`Query failed: ${error.message}`);
        }
    }

    /**
     * Get information about loaded tables
     */
    getLoadedTables() {
        return Array.from(this.loadedTables.entries()).map(([tableName, info]) => ({
            tableName,
            ...info
        }));
    }

    /**
     * Check if a table exists
     */
    async tableExists(tableName) {
        try {
            const result = await this.connection.query(`
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_name = '${tableName.toLowerCase()}'
            `);
            return result.toArray()[0].count > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get table schema information
     */
    async getTableSchema(tableName) {
        try {
            const result = await this.connection.query(`
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = '${tableName.toLowerCase()}'
                ORDER BY ordinal_position
            `);
            return result.toArray();
        } catch (error) {
            console.error('Failed to get table schema:', error);
            return [];
        }
    }

    /**
     * Get table row count
     */
    async getTableRowCount(tableName) {
        try {
            const result = await this.connection.query(`SELECT COUNT(*) as count FROM ${tableName}`);
            return result.toArray()[0].count;
        } catch (error) {
            console.error('Failed to get table row count:', error);
            return 0;
        }
    }

    /**
     * Close the DuckDB connection
     */
    async close() {
        try {
            if (this.connection) {
                await this.connection.close();
            }
            if (this.db) {
                await this.db.terminate();
            }
            this.isInitialized = false;
            console.log('DuckDB connection closed');
        } catch (error) {
            console.error('Error closing DuckDB connection:', error);
        }
    }
}

// Export singleton instance
export const domoDuckDBConnector = new DomoDuckDBConnector();
