/**
 * Domo API Client
 * Handles communication with Domo APIs for dataset access
 */

export class DomoAPIClient {
    constructor() {
        this.baseURL = '';
        this.isDomoEnvironment = typeof window !== 'undefined' && typeof window.domo !== 'undefined';
        this.mockMode = !this.isDomoEnvironment;
    }

    /**
     * Initialize the API client
     */
    async initialize() {
        if (this.isDomoEnvironment) {
            // In Domo environment, use the domo object
            console.log('Initializing Domo API client in DDX environment');
            this.baseURL = '/data/v1';
        } else {
            // Development mode with mock data
            console.log('Initializing Domo API client in mock mode');
        }
    }

    /**
     * Get list of available datasets
     */
    async getDatasets() {
        if (this.mockMode) {
            return this.getMockDatasets();
        }

        try {
            const response = await window.domo.get('/data/v1/datasets');
            return response.map(dataset => this.normalizeDataset(dataset));
        } catch (error) {
            console.error('Failed to fetch datasets from Domo:', error);
            throw new Error(`Failed to fetch datasets: ${error.message}`);
        }
    }

    /**
     * Get dataset metadata by ID
     */
    async getDataset(datasetId) {
        if (this.mockMode) {
            const mockDatasets = this.getMockDatasets();
            const dataset = mockDatasets.find(d => d.id === datasetId);
            if (!dataset) {
                throw new Error(`Dataset ${datasetId} not found`);
            }
            return dataset;
        }

        try {
            const response = await window.domo.get(`/data/v1/datasets/${datasetId}`);
            return this.normalizeDataset(response);
        } catch (error) {
            console.error(`Failed to fetch dataset ${datasetId}:`, error);
            throw new Error(`Failed to fetch dataset: ${error.message}`);
        }
    }

    /**
     * Get dataset data
     */
    async getDatasetData(datasetId, options = {}) {
        const { limit, offset, includeHeader = true } = options;

        if (this.mockMode) {
            return this.getMockDatasetData(datasetId, options);
        }

        try {
            let url = `/data/v1/datasets/${datasetId}/data`;
            const params = new URLSearchParams();
            
            if (limit) params.append('limit', limit.toString());
            if (offset) params.append('offset', offset.toString());
            if (includeHeader) params.append('includeHeader', 'true');
            
            if (params.toString()) {
                url += `?${params.toString()}`;
            }

            const response = await window.domo.get(url);
            return this.normalizeDatasetData(response);
        } catch (error) {
            console.error(`Failed to fetch data for dataset ${datasetId}:`, error);
            throw new Error(`Failed to fetch dataset data: ${error.message}`);
        }
    }

    /**
     * Get sample data from dataset (first N rows)
     */
    async getSampleData(datasetId, sampleSize = 5) {
        return this.getDatasetData(datasetId, { limit: sampleSize });
    }

    /**
     * Normalize dataset object from Domo API
     */
    normalizeDataset(dataset) {
        return {
            id: dataset.id,
            name: dataset.name || 'Unnamed Dataset',
            description: dataset.description || 'No description available',
            schema: this.normalizeSchema(dataset.schema || []),
            rowCount: dataset.rows || dataset.rowCount || 0,
            columnCount: dataset.columns || dataset.columnCount || 0,
            lastUpdated: dataset.lastUpdated || dataset.updatedAt || new Date().toISOString(),
            createdAt: dataset.createdAt || new Date().toISOString(),
            owner: dataset.owner || 'Unknown',
            tags: dataset.tags || [],
            dataCurrentAt: dataset.dataCurrentAt || dataset.lastUpdated
        };
    }

    /**
     * Normalize schema from Domo API
     */
    normalizeSchema(schema) {
        if (Array.isArray(schema)) {
            return schema.map(column => ({
                name: column.name || column.columnName,
                type: this.normalizeColumnType(column.type || column.columnType),
                description: column.description || ''
            }));
        }
        return [];
    }

    /**
     * Normalize column types from Domo to standard types
     */
    normalizeColumnType(domoType) {
        const typeMapping = {
            'STRING': 'STRING',
            'LONG': 'LONG',
            'DOUBLE': 'DOUBLE',
            'DECIMAL': 'DECIMAL',
            'DATE': 'DATE',
            'DATETIME': 'DATETIME',
            'BOOLEAN': 'BOOLEAN'
        };

        const upperType = domoType?.toString().toUpperCase();
        return typeMapping[upperType] || 'STRING';
    }

    /**
     * Normalize dataset data response
     */
    normalizeDatasetData(response) {
        if (Array.isArray(response)) {
            // If response is just an array of rows
            return {
                rows: response,
                columns: [], // Will need to be inferred or provided separately
                totalRows: response.length
            };
        }

        return {
            rows: response.rows || response.data || [],
            columns: response.columns || response.headers || [],
            totalRows: response.totalRows || response.count || (response.rows?.length || 0)
        };
    }

    /**
     * Get mock datasets for development
     */
    getMockDatasets() {
        return [
            {
                id: 'sales-data-2024',
                name: 'Sales Data 2024',
                description: 'Comprehensive sales data including revenue, units sold, and customer information',
                schema: [
                    { name: 'date', type: 'DATE', description: 'Transaction date' },
                    { name: 'region', type: 'STRING', description: 'Sales region' },
                    { name: 'product_category', type: 'STRING', description: 'Product category' },
                    { name: 'product_name', type: 'STRING', description: 'Product name' },
                    { name: 'revenue', type: 'DECIMAL', description: 'Revenue amount' },
                    { name: 'units_sold', type: 'LONG', description: 'Number of units sold' },
                    { name: 'customer_id', type: 'STRING', description: 'Customer identifier' }
                ],
                rowCount: 15420,
                columnCount: 7,
                lastUpdated: '2024-01-16T10:30:00Z',
                createdAt: '2024-01-01T00:00:00Z',
                owner: 'Sales Team',
                tags: ['sales', 'revenue', 'quarterly']
            },
            {
                id: 'customer-analytics',
                name: 'Customer Analytics',
                description: 'Customer behavior and demographic data for segmentation analysis',
                schema: [
                    { name: 'customer_id', type: 'STRING', description: 'Unique customer identifier' },
                    { name: 'age_group', type: 'STRING', description: 'Customer age group' },
                    { name: 'segment', type: 'STRING', description: 'Customer segment' },
                    { name: 'lifetime_value', type: 'DECIMAL', description: 'Customer lifetime value' },
                    { name: 'acquisition_channel', type: 'STRING', description: 'How customer was acquired' },
                    { name: 'last_purchase_date', type: 'DATE', description: 'Date of last purchase' },
                    { name: 'total_orders', type: 'LONG', description: 'Total number of orders' }
                ],
                rowCount: 8750,
                columnCount: 7,
                lastUpdated: '2024-01-15T14:20:00Z',
                createdAt: '2023-12-01T00:00:00Z',
                owner: 'Marketing Team',
                tags: ['customers', 'analytics', 'segmentation']
            },
            {
                id: 'marketing-campaigns',
                name: 'Marketing Campaigns',
                description: 'Campaign performance metrics and ROI analysis',
                schema: [
                    { name: 'campaign_id', type: 'STRING', description: 'Campaign identifier' },
                    { name: 'campaign_name', type: 'STRING', description: 'Campaign name' },
                    { name: 'start_date', type: 'DATE', description: 'Campaign start date' },
                    { name: 'end_date', type: 'DATE', description: 'Campaign end date' },
                    { name: 'spend', type: 'DECIMAL', description: 'Campaign spend' },
                    { name: 'impressions', type: 'LONG', description: 'Number of impressions' },
                    { name: 'clicks', type: 'LONG', description: 'Number of clicks' },
                    { name: 'conversions', type: 'LONG', description: 'Number of conversions' },
                    { name: 'channel', type: 'STRING', description: 'Marketing channel' }
                ],
                rowCount: 342,
                columnCount: 9,
                lastUpdated: '2024-01-14T09:15:00Z',
                createdAt: '2023-11-01T00:00:00Z',
                owner: 'Marketing Team',
                tags: ['marketing', 'campaigns', 'roi']
            },
            {
                id: 'inventory-data',
                name: 'Inventory Management',
                description: 'Product inventory levels, costs, and warehouse information',
                schema: [
                    { name: 'product_id', type: 'STRING', description: 'Product identifier' },
                    { name: 'product_name', type: 'STRING', description: 'Product name' },
                    { name: 'category', type: 'STRING', description: 'Product category' },
                    { name: 'quantity_on_hand', type: 'LONG', description: 'Current inventory quantity' },
                    { name: 'unit_cost', type: 'DECIMAL', description: 'Cost per unit' },
                    { name: 'warehouse_location', type: 'STRING', description: 'Warehouse location' },
                    { name: 'last_restocked', type: 'DATE', description: 'Last restock date' },
                    { name: 'reorder_point', type: 'LONG', description: 'Reorder threshold' }
                ],
                rowCount: 1250,
                columnCount: 8,
                lastUpdated: '2024-01-16T16:45:00Z',
                createdAt: '2023-10-15T00:00:00Z',
                owner: 'Operations Team',
                tags: ['inventory', 'operations', 'supply-chain']
            }
        ];
    }

    /**
     * Get mock dataset data
     */
    getMockDatasetData(datasetId, options = {}) {
        const { limit = 100, offset = 0 } = options;
        
        const mockData = {
            'sales-data-2024': {
                columns: ['date', 'region', 'product_category', 'product_name', 'revenue', 'units_sold', 'customer_id'],
                rows: [
                    ['2024-01-01', 'North America', 'Electronics', 'Smartphone X', 899.99, 1, 'CUST001'],
                    ['2024-01-01', 'Europe', 'Clothing', 'Winter Jacket', 129.99, 2, 'CUST002'],
                    ['2024-01-02', 'Asia', 'Electronics', 'Laptop Pro', 1299.99, 1, 'CUST003'],
                    ['2024-01-02', 'North America', 'Home & Garden', 'Coffee Maker', 89.99, 1, 'CUST004'],
                    ['2024-01-03', 'Europe', 'Electronics', 'Tablet Mini', 399.99, 3, 'CUST005']
                ]
            },
            'customer-analytics': {
                columns: ['customer_id', 'age_group', 'segment', 'lifetime_value', 'acquisition_channel', 'last_purchase_date', 'total_orders'],
                rows: [
                    ['CUST001', '25-34', 'Premium', 2500.50, 'Social Media', '2024-01-15', 12],
                    ['CUST002', '35-44', 'Standard', 1200.25, 'Email', '2024-01-10', 8],
                    ['CUST003', '18-24', 'Premium', 3200.75, 'Search', '2024-01-14', 15],
                    ['CUST004', '45-54', 'Standard', 800.00, 'Referral', '2024-01-12', 5],
                    ['CUST005', '25-34', 'Premium', 1800.30, 'Social Media', '2024-01-16', 10]
                ]
            },
            'marketing-campaigns': {
                columns: ['campaign_id', 'campaign_name', 'start_date', 'end_date', 'spend', 'impressions', 'clicks', 'conversions', 'channel'],
                rows: [
                    ['CAMP001', 'Holiday Sale 2024', '2024-01-01', '2024-01-31', 5000.00, 100000, 2500, 125, 'Social Media'],
                    ['CAMP002', 'New Product Launch', '2024-01-15', '2024-02-15', 8000.00, 150000, 3200, 180, 'Search'],
                    ['CAMP003', 'Email Newsletter', '2024-01-01', '2024-01-31', 1200.00, 25000, 800, 45, 'Email'],
                    ['CAMP004', 'Retargeting Campaign', '2024-01-10', '2024-01-25', 3500.00, 75000, 1800, 95, 'Display'],
                    ['CAMP005', 'Influencer Partnership', '2024-01-05', '2024-01-20', 6000.00, 200000, 4500, 220, 'Social Media']
                ]
            },
            'inventory-data': {
                columns: ['product_id', 'product_name', 'category', 'quantity_on_hand', 'unit_cost', 'warehouse_location', 'last_restocked', 'reorder_point'],
                rows: [
                    ['PROD001', 'Smartphone X', 'Electronics', 150, 450.00, 'Warehouse A', '2024-01-10', 50],
                    ['PROD002', 'Winter Jacket', 'Clothing', 75, 65.00, 'Warehouse B', '2024-01-08', 25],
                    ['PROD003', 'Laptop Pro', 'Electronics', 30, 800.00, 'Warehouse A', '2024-01-12', 10],
                    ['PROD004', 'Coffee Maker', 'Home & Garden', 200, 45.00, 'Warehouse C', '2024-01-14', 75],
                    ['PROD005', 'Tablet Mini', 'Electronics', 120, 200.00, 'Warehouse A', '2024-01-11', 40]
                ]
            }
        };

        const datasetData = mockData[datasetId];
        if (!datasetData) {
            throw new Error(`Mock dataset ${datasetId} not found`);
        }

        // Apply pagination
        const paginatedRows = datasetData.rows.slice(offset, offset + limit);

        return {
            columns: datasetData.columns,
            rows: paginatedRows,
            totalRows: datasetData.rows.length
        };
    }
}

// Export singleton instance
export const domoAPIClient = new DomoAPIClient();
