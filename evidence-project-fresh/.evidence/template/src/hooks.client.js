/** @param {Error | unknown} e  */
const transformError = (e) => {
	if (!(e instanceof Error)) {
		return JSON.parse(JSON.stringify(e));
	} else {
		return {
			message: e.message,
			stack: e.stack,
			name: e.name,
			cause: e.cause ? transformError(e.cause) : undefined
		};
	}
};

/** @type {import("@sveltejs/kit").HandleClientError } */
export const handleError = (e) => {
	console.error(`Error in client-side routing`, e);
	return transformError(e.error);
};
