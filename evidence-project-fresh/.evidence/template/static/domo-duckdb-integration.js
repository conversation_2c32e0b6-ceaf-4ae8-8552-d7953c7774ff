/**
 * Domo DDX to DuckDB Integration Module
 * Handles loading Domo datasets into DuckDB for Evidence analysis in DDX environment
 */

class DomoDuckDBIntegration {
    constructor() {
        this.duckdb = null;
        this.connection = null;
        this.availableDatasets = [];
        this.isInitialized = false;
        this.isDomoEnvironment = typeof window !== 'undefined' && typeof window.domo !== 'undefined';

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    async init() {
        try {
            // Wait for Domo to be ready if in DDX environment
            if (this.isDomoEnvironment) {
                await this.waitForDomo();
            }

            await this.initializeDuckDB();
            await this.loadAvailableDatasets();
            this.setupEventListeners();
            this.isInitialized = true;
            console.log('Domo-DuckDB integration initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Domo-DuckDB integration:', error);
            this.showError('Failed to initialize data integration. Please refresh the page.');
        }
    }

    /**
     * Wait for Domo DDX environment to be ready
     */
    async waitForDomo() {
        return new Promise((resolve, reject) => {
            if (window.domo && window.domo.get) {
                resolve();
                return;
            }

            let attempts = 0;
            const maxAttempts = 50; // 5 seconds max wait

            const checkDomo = () => {
                attempts++;
                if (window.domo && window.domo.get) {
                    resolve();
                } else if (attempts >= maxAttempts) {
                    reject(new Error('Domo DDX environment not available'));
                } else {
                    setTimeout(checkDomo, 100);
                }
            };

            checkDomo();
        });
    }

    async initializeDuckDB() {
        try {
            if (this.isDomoEnvironment) {
                console.log('Running in Domo DDX environment');
                // In DDX, we'll use a simplified approach since we're bundled
                // DuckDB-WASM will be available through the bundled Evidence build
            } else {
                console.log('Running in development environment');
            }

            // Initialize DuckDB connection
            // This will be handled by Evidence's DuckDB integration
            this.duckdb = {
                query: async (sql) => {
                    console.log('Executing SQL:', sql);
                    // In real implementation, this would execute against Evidence's DuckDB instance
                    return { rows: [], columns: [] };
                }
            };

            console.log('DuckDB integration ready');
        } catch (error) {
            throw new Error(`DuckDB initialization failed: ${error.message}`);
        }
    }

    async loadAvailableDatasets() {
        try {
            if (this.isDomoEnvironment && window.domo && window.domo.get) {
                // Use Domo DDX API to get available datasets
                console.log('Loading datasets from Domo DDX...');

                // In DDX, datasets are typically accessed through the data API
                // The exact endpoint may vary based on your Domo setup
                try {
                    const datasets = await window.domo.get('/data/v1/datasets');
                    this.availableDatasets = datasets.map(dataset => this.normalizeDomoDataset(dataset));
                } catch (apiError) {
                    console.warn('Standard dataset API failed, trying alternative approach:', apiError);
                    // Fallback: try to get datasets through different endpoint or method
                    this.availableDatasets = await this.getDatasetsFallback();
                }
            } else {
                // Development mode with mock data
                console.log('Loading mock datasets for development...');
                this.availableDatasets = this.getMockDatasets();
            }

            this.populateDatasetDropdown();
            console.log(`Loaded ${this.availableDatasets.length} datasets`);
        } catch (error) {
            console.error('Failed to load available datasets:', error);
            this.showError('Failed to load available datasets. Please check your Domo connection.');
            // Load mock data as fallback
            this.availableDatasets = this.getMockDatasets();
            this.populateDatasetDropdown();
        }
    }

    /**
     * Normalize dataset from Domo API response
     */
    normalizeDomoDataset(dataset) {
        return {
            id: dataset.id,
            name: dataset.name || dataset.displayName || 'Unnamed Dataset',
            description: dataset.description || 'No description available',
            schema: this.normalizeSchema(dataset.schema || dataset.columns || []),
            rowCount: dataset.rowCount || dataset.rows || 0,
            lastUpdated: dataset.lastUpdated || dataset.updatedAt || new Date().toISOString()
        };
    }

    /**
     * Normalize schema from various Domo formats
     */
    normalizeSchema(schema) {
        if (!Array.isArray(schema)) return [];

        return schema.map(col => ({
            name: col.name || col.columnName || col.field,
            type: this.normalizeColumnType(col.type || col.columnType || col.dataType),
            description: col.description || ''
        }));
    }

    /**
     * Normalize column types
     */
    normalizeColumnType(type) {
        const typeMap = {
            'STRING': 'STRING',
            'TEXT': 'STRING',
            'LONG': 'LONG',
            'INTEGER': 'LONG',
            'DOUBLE': 'DOUBLE',
            'FLOAT': 'DOUBLE',
            'DECIMAL': 'DECIMAL',
            'DATE': 'DATE',
            'DATETIME': 'DATETIME',
            'TIMESTAMP': 'DATETIME',
            'BOOLEAN': 'BOOLEAN'
        };

        return typeMap[type?.toString().toUpperCase()] || 'STRING';
    }

    /**
     * Fallback method to get datasets if standard API fails
     */
    async getDatasetsFallback() {
        // This could try different approaches based on your Domo setup
        // For example, using different API endpoints or methods
        console.log('Attempting fallback dataset loading...');
        return this.getMockDatasets(); // Fallback to mock data
    }

    populateDatasetDropdown() {
        const selector = document.getElementById('dataset-selector');
        if (!selector) return;

        // Clear existing options except the first one
        selector.innerHTML = '<option value="">Select a dataset...</option>';

        this.availableDatasets.forEach(dataset => {
            const option = document.createElement('option');
            option.value = dataset.id;
            option.textContent = `${dataset.name} (${dataset.rowCount} rows)`;
            option.dataset.description = dataset.description;
            selector.appendChild(option);
        });
    }

    setupEventListeners() {
        const selector = document.getElementById('dataset-selector');
        const previewBtn = document.getElementById('preview-btn');
        const loadBtn = document.getElementById('load-dataset-btn');
        const tableNameInput = document.getElementById('table-name');

        if (selector) {
            selector.addEventListener('change', (e) => {
                const selectedId = e.target.value;
                if (selectedId) {
                    this.onDatasetSelected(selectedId);
                    if (previewBtn) previewBtn.disabled = false;
                    if (loadBtn) loadBtn.disabled = false;
                    
                    // Auto-populate table name
                    const dataset = this.availableDatasets.find(d => d.id === selectedId);
                    if (dataset && tableNameInput) {
                        tableNameInput.value = dataset.name.toLowerCase().replace(/\s+/g, '_');
                    }
                } else {
                    if (previewBtn) previewBtn.disabled = true;
                    if (loadBtn) loadBtn.disabled = true;
                    this.hideDatasetPreview();
                }
            });
        }

        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.previewDataset());
        }

        if (loadBtn) {
            loadBtn.addEventListener('click', () => this.loadDatasetIntoDuckDB());
        }
    }

    onDatasetSelected(datasetId) {
        const dataset = this.availableDatasets.find(d => d.id === datasetId);
        if (dataset) {
            this.showDatasetPreview(dataset);
        }
    }

    showDatasetPreview(dataset) {
        const previewContainer = document.getElementById('dataset-preview');
        const previewContent = document.getElementById('preview-content');
        
        if (!previewContainer || !previewContent) return;

        const schemaTable = dataset.schema.map(col => 
            `<tr><td>${col.name}</td><td>${col.type}</td></tr>`
        ).join('');

        previewContent.innerHTML = `
            <div class="dataset-info">
                <h5>${dataset.name}</h5>
                <p><strong>Description:</strong> ${dataset.description}</p>
                <p><strong>Rows:</strong> ${dataset.rowCount.toLocaleString()}</p>
                <p><strong>Last Updated:</strong> ${dataset.lastUpdated}</p>
                
                <h6>Schema:</h6>
                <table class="schema-table">
                    <thead>
                        <tr><th>Column</th><th>Type</th></tr>
                    </thead>
                    <tbody>
                        ${schemaTable}
                    </tbody>
                </table>
            </div>
        `;

        previewContainer.style.display = 'block';
    }

    hideDatasetPreview() {
        const previewContainer = document.getElementById('dataset-preview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    async previewDataset() {
        const selectedId = document.getElementById('dataset-selector').value;
        if (!selectedId) return;

        try {
            this.showLoading('Loading preview...');
            
            // In a real implementation, you'd fetch sample data from Domo
            const sampleData = await this.fetchSampleData(selectedId);
            this.showDataPreview(sampleData);
            
        } catch (error) {
            console.error('Preview failed:', error);
            this.showError('Failed to preview dataset');
        } finally {
            this.hideLoading();
        }
    }

    async loadDatasetIntoDuckDB() {
        const selectedId = document.getElementById('dataset-selector').value;
        const tableName = document.getElementById('table-name').value;
        const refreshMode = document.getElementById('refresh-mode').value;

        if (!selectedId || !tableName) {
            this.showError('Please select a dataset and provide a table name');
            return;
        }

        try {
            this.showLoading('Loading dataset into DuckDB...');
            
            // Fetch the full dataset from Domo
            const dataset = await this.fetchDatasetData(selectedId);
            
            // Load into DuckDB
            await this.createDuckDBTable(tableName, dataset, refreshMode);
            
            this.showSuccess(`Dataset loaded successfully into table: ${tableName}`);
            
            // Optionally refresh the Evidence page to show new data
            setTimeout(() => {
                window.location.reload();
            }, 2000);
            
        } catch (error) {
            console.error('Load failed:', error);
            this.showError(`Failed to load dataset: ${error.message}`);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Get mock datasets for development/fallback
     */
    getMockDatasets() {
        return [
            {
                id: 'domo-sales-2024',
                name: 'Sales Performance 2024',
                description: 'Comprehensive sales data including revenue, units sold, and regional performance',
                schema: [
                    { name: 'transaction_date', type: 'DATE' },
                    { name: 'region', type: 'STRING' },
                    { name: 'product_category', type: 'STRING' },
                    { name: 'revenue', type: 'DECIMAL' },
                    { name: 'units_sold', type: 'LONG' },
                    { name: 'sales_rep', type: 'STRING' }
                ],
                rowCount: 15420,
                lastUpdated: '2024-01-16T10:30:00Z'
            },
            {
                id: 'domo-customers',
                name: 'Customer Analytics',
                description: 'Customer behavior and demographic data for segmentation analysis',
                schema: [
                    { name: 'customer_id', type: 'STRING' },
                    { name: 'age_group', type: 'STRING' },
                    { name: 'segment', type: 'STRING' },
                    { name: 'lifetime_value', type: 'DECIMAL' },
                    { name: 'acquisition_channel', type: 'STRING' }
                ],
                rowCount: 8750,
                lastUpdated: '2024-01-15T14:20:00Z'
            },
            {
                id: 'domo-marketing',
                name: 'Marketing Campaign Performance',
                description: 'Campaign metrics, ROI analysis, and channel performance data',
                schema: [
                    { name: 'campaign_id', type: 'STRING' },
                    { name: 'campaign_name', type: 'STRING' },
                    { name: 'channel', type: 'STRING' },
                    { name: 'spend', type: 'DECIMAL' },
                    { name: 'impressions', type: 'LONG' },
                    { name: 'clicks', type: 'LONG' },
                    { name: 'conversions', type: 'LONG' }
                ],
                rowCount: 342,
                lastUpdated: '2024-01-14T09:15:00Z'
            }
        ];
    }

    async fetchSampleData(datasetId) {
        if (this.isDomoEnvironment && window.domo && window.domo.get) {
            try {
                // Fetch sample data from Domo DDX
                const response = await window.domo.get(`/data/v1/datasets/${datasetId}/data?limit=5`);
                return this.normalizeDataResponse(response);
            } catch (error) {
                console.warn('Failed to fetch sample data from Domo, using mock data:', error);
                return this.getMockSampleData(datasetId);
            }
        } else {
            return this.getMockSampleData(datasetId);
        }
    }

    async fetchDatasetData(datasetId) {
        if (this.isDomoEnvironment && window.domo && window.domo.get) {
            try {
                // Fetch full dataset from Domo DDX
                console.log(`Fetching full dataset: ${datasetId}`);
                const response = await window.domo.get(`/data/v1/datasets/${datasetId}/data`);
                return this.normalizeDataResponse(response);
            } catch (error) {
                console.error('Failed to fetch dataset data from Domo:', error);
                throw new Error(`Failed to fetch dataset data: ${error.message}`);
            }
        } else {
            // Development mode - return mock data
            return this.getMockSampleData(datasetId);
        }
    }

    /**
     * Normalize data response from Domo API
     */
    normalizeDataResponse(response) {
        if (Array.isArray(response)) {
            // Response is just an array of rows
            return {
                rows: response,
                columns: [], // Will need to be inferred from schema
                totalRows: response.length
            };
        }

        return {
            rows: response.rows || response.data || [],
            columns: response.columns || response.headers || [],
            totalRows: response.totalRows || (response.rows?.length || 0)
        };
    }

    /**
     * Get mock sample data for development
     */
    getMockSampleData(datasetId) {
        const mockData = {
            'domo-sales-2024': {
                columns: ['transaction_date', 'region', 'product_category', 'revenue', 'units_sold', 'sales_rep'],
                rows: [
                    ['2024-01-01', 'North America', 'Electronics', 1299.99, 1, 'John Smith'],
                    ['2024-01-01', 'Europe', 'Clothing', 89.99, 2, 'Marie Dubois'],
                    ['2024-01-02', 'Asia Pacific', 'Electronics', 899.99, 1, 'Yuki Tanaka'],
                    ['2024-01-02', 'North America', 'Home & Garden', 159.99, 1, 'Sarah Johnson'],
                    ['2024-01-03', 'Europe', 'Electronics', 599.99, 3, 'Hans Mueller']
                ]
            },
            'domo-customers': {
                columns: ['customer_id', 'age_group', 'segment', 'lifetime_value', 'acquisition_channel'],
                rows: [
                    ['CUST001', '25-34', 'Premium', 2500.50, 'Social Media'],
                    ['CUST002', '35-44', 'Standard', 1200.25, 'Email'],
                    ['CUST003', '18-24', 'Premium', 3200.75, 'Search'],
                    ['CUST004', '45-54', 'Standard', 800.00, 'Referral'],
                    ['CUST005', '25-34', 'Premium', 1800.30, 'Social Media']
                ]
            },
            'domo-marketing': {
                columns: ['campaign_id', 'campaign_name', 'channel', 'spend', 'impressions', 'clicks', 'conversions'],
                rows: [
                    ['CAMP001', 'Holiday Sale 2024', 'Social Media', 5000.00, 100000, 2500, 125],
                    ['CAMP002', 'New Product Launch', 'Search', 8000.00, 150000, 3200, 180],
                    ['CAMP003', 'Email Newsletter', 'Email', 1200.00, 25000, 800, 45],
                    ['CAMP004', 'Retargeting Campaign', 'Display', 3500.00, 75000, 1800, 95],
                    ['CAMP005', 'Influencer Partnership', 'Social Media', 6000.00, 200000, 4500, 220]
                ]
            }
        };

        return mockData[datasetId] || {
            columns: ['id', 'name', 'value'],
            rows: [
                ['1', 'Sample Data', 100],
                ['2', 'Test Record', 200],
                ['3', 'Demo Entry', 300]
            ]
        };
    }

    async createDuckDBTable(tableName, dataset, refreshMode) {
        // In a real implementation, this would use DuckDB-WASM
        console.log(`Creating table ${tableName} with mode ${refreshMode}`);
        console.log('Dataset:', dataset);
        
        // Mock implementation
        return Promise.resolve();
    }

    showLoading(message) {
        const loadingStatus = document.getElementById('loading-status');
        if (loadingStatus) {
            loadingStatus.querySelector('p').textContent = message;
            loadingStatus.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingStatus = document.getElementById('loading-status');
        if (loadingStatus) {
            loadingStatus.style.display = 'none';
        }
    }

    showError(message) {
        alert(`Error: ${message}`);
    }

    showSuccess(message) {
        alert(`Success: ${message}`);
    }

    showDataPreview(data) {
        // Implementation for showing data preview
        console.log('Data preview:', data);
    }
}

// Initialize the integration when the script loads
window.domoDuckDBIntegration = new DomoDuckDBIntegration();
