import{D as ie}from"./chunks/false.js";import{b as D,a as X,r as wt}from"./chunks/paths.js";import{H as V,S as W,j as he,t as T,R as I,A as pe}from"./chunks/index.js";import{m as $t,d as Fe,n as _e,a as Ge,r as $e,b as bt,h as kt,s as be,c as vt}from"./chunks/exports.js";import*as N from"devalue";import{r as xt,w as ke}from"./chunks/index2.js";import{p as Me,s as Et,r as ve,o as jt,g as Rt,a as St,b as At,c as Ot,d as Tt}from"./chunks/internal.js";import{parse as K,serialize as ce}from"cookie";import*as xe from"set-cookie-parser";import{p as Ee}from"./chunks/environment.js";const Je=["GET","POST","PUT","PATCH","DELETE","OPTIONS","HEAD"],qt=["GET","POST","HEAD"];function me(e,t){const r=[];e.split(",").forEach((o,a)=>{const i=/([^/ \t]+)\/([^; \t]+)[ \t]*(?:;[ \t]*q=([0-9.]+))?/.exec(o);if(i){const[,c,u,l="1"]=i;r.push({type:c,subtype:u,q:+l,i:a})}}),r.sort((o,a)=>o.q!==a.q?a.q-o.q:o.subtype==="*"!=(a.subtype==="*")?o.subtype==="*"?1:-1:o.type==="*"!=(a.type==="*")?o.type==="*"?1:-1:o.i-a.i);let s,n=1/0;for(const o of t){const[a,i]=o.split("/"),c=r.findIndex(u=>(u.type===a||u.type==="*")&&(u.subtype===i||u.subtype==="*"));c!==-1&&c<n&&(s=o,n=c)}return s}function Pt(e,...t){const r=e.headers.get("content-type")?.split(";",1)[0].trim()??"";return t.includes(r.toLowerCase())}function Be(e){return Pt(e,"application/x-www-form-urlencoded","multipart/form-data","text/plain")}function Ct(e){return e instanceof Error||e&&e.name&&e.message?e:new Error(JSON.stringify(e))}function G(e){return e instanceof V||e instanceof W?e.status:500}function Ht(e){return e instanceof W?e.text:"Internal Error"}const Ve={"&":"&amp;",'"':"&quot;"},Ke={"&":"&amp;","<":"&lt;"},Xe="[\\ud800-\\udbff](?![\\udc00-\\udfff])|[\\ud800-\\udbff][\\udc00-\\udfff]|[\\udc00-\\udfff]",Nt=new RegExp(`[${Object.keys(Ve).join("")}]|`+Xe,"g"),zt=new RegExp(`[${Object.keys(Ke).join("")}]|`+Xe,"g");function ye(e,t){const r=t?Ve:Ke;return e.replace(t?Nt:zt,n=>n.length===2?n:r[n]??`&#${n.charCodeAt(0)};`)}function Qe(e,t){return T(`${t} method not allowed`,{status:405,headers:{allow:Ut(e).join(", ")}})}function Ut(e){const t=Je.filter(r=>r in e);return("GET"in e||"HEAD"in e)&&t.push("HEAD"),t}function Q(e,t,r){let s=e.templates.error({status:t,message:ye(r)});return T(s,{headers:{"content-type":"text/html; charset=utf-8"},status:t})}async function je(e,t,r){r=r instanceof V?r:Ct(r);const s=G(r),n=await P(e,t,r),o=me(e.request.headers.get("accept")||"text/html",["application/json","text/html"]);return e.isDataRequest||o==="application/json"?he(n,{status:s}):Q(t,s,n.message)}async function P(e,t,r){if(r instanceof V)return r.body;const s=G(r),n=Ht(r);return await t.hooks.handleError({error:r,event:e,status:s,message:n})??{message:n}}function Y(e,t){return new Response(void 0,{status:e,headers:{location:t}})}function Ye(e,t){return t.path?`Data returned from \`load\` while rendering ${e.route.id} is not serializable: ${t.message} (data${t.path})`:t.path===""?`Data returned from \`load\` while rendering ${e.route.id} is not a plain object`:t.message}function Ze(e){const t=[];return e.uses&&e.uses.dependencies.size>0&&t.push(`"dependencies":${JSON.stringify(Array.from(e.uses.dependencies))}`),e.uses&&e.uses.search_params.size>0&&t.push(`"search_params":${JSON.stringify(Array.from(e.uses.search_params))}`),e.uses&&e.uses.params.size>0&&t.push(`"params":${JSON.stringify(Array.from(e.uses.params))}`),e.uses?.parent&&t.push('"parent":1'),e.uses?.route&&t.push('"route":1'),e.uses?.url&&t.push('"url":1'),`"uses":{${t.join(",")}}`}async function Dt(e,t,r){const s=e.request.method;let n=t[s]||t.fallback;if(s==="HEAD"&&t.GET&&!t.HEAD&&(n=t.GET),!n)return Qe(t,s);const o=t.prerender??r.prerender_default;if(o&&(t.POST||t.PATCH||t.PUT||t.DELETE))throw new Error("Cannot prerender endpoints that have mutative methods");if(r.prerendering&&!o){if(r.depth>0)throw new Error(`${e.route.id} is not prerenderable`);return new Response(void 0,{status:204})}try{let a=await n(e);if(!(a instanceof Response))throw new Error(`Invalid response from route ${e.url.pathname}: handler should return a Response object`);return r.prerendering&&(a=new Response(a.body,{status:a.status,statusText:a.statusText,headers:new Headers(a.headers)}),a.headers.set("x-sveltekit-prerender",String(o))),a}catch(a){if(a instanceof I)return new Response(void 0,{status:a.status,headers:{location:a.location}});throw a}}function It(e){const{method:t,headers:r}=e.request;if(Je.includes(t)&&!qt.includes(t))return!0;if(t==="POST"&&r.get("x-sveltekit-action")==="true")return!1;const s=e.request.headers.get("accept")??"*/*";return me(s,["*","text/html"])!=="text/html"}function Re(e){return e.filter(t=>t!=null)}function et(e){return me(e.request.headers.get("accept")??"*/*",["application/json","text/html"])==="application/json"&&e.request.method==="POST"}async function Lt(e,t,r){const s=r?.actions;if(!s){const n=new W(405,"Method Not Allowed","POST method not allowed. No actions exist for this page");return J({type:"error",error:await P(e,t,n)},{status:n.status,headers:{allow:"GET"}})}st(s);try{const n=await nt(e,s);return n instanceof pe?J({type:"failure",status:n.status,data:Se(n.data,e.route.id)}):J({type:"success",status:n?200:204,data:Se(n,e.route.id)})}catch(n){const o=n;return o instanceof I?rt(o):J({type:"error",error:await P(e,t,tt(o))},{status:G(o)})}}function tt(e){return e instanceof pe?new Error('Cannot "throw fail()". Use "return fail()"'):e}function rt(e){return J({type:"redirect",status:e.status,location:e.location})}function J(e,t){return he(e,t)}function Wt(e){return e.request.method==="POST"}async function Ft(e,t){const r=t?.actions;if(!r)return e.setHeaders({allow:"GET"}),{type:"error",error:new W(405,"Method Not Allowed","POST method not allowed. No actions exist for this page")};st(r);try{const s=await nt(e,r);return s instanceof pe?{type:"failure",status:s.status,data:s.data}:{type:"success",status:200,data:s}}catch(s){const n=s;return n instanceof I?{type:"redirect",status:n.status,location:n.location}:{type:"error",error:tt(n)}}}function st(e){if(e.default&&Object.keys(e).length>1)throw new Error("When using named actions, the default action cannot be used. See the docs for more info: https://svelte.dev/docs/kit/form-actions#named-actions")}async function nt(e,t){const r=new URL(e.request.url);let s="default";for(const o of r.searchParams)if(o[0].startsWith("/")){if(s=o[0].slice(1),s==="default")throw new Error('Cannot use reserved action name "default"');break}const n=t[s];if(!n)throw new W(404,"Not Found",`No action with name '${s}' found`);if(!Be(e.request))throw new W(415,"Unsupported Media Type",`Form actions expect form-encoded data — received ${e.request.headers.get("content-type")}`);return n(e)}function Gt(e,t){return at(e,N.uneval,t)}function Se(e,t){return at(e,N.stringify,t)}function at(e,t,r){try{return t(e)}catch(s){const n=s;if(e instanceof Response)throw new Error(`Data returned from action inside ${r} is not serializable. Form actions need to return plain objects or fail(). E.g. return { success: true } or return fail(400, { message: "invalid" });`);if("path"in n){let o=`Data returned from action inside ${r} is not serializable: ${n.message}`;throw n.path!==""&&(o+=` (data.${n.path})`),new Error(o)}throw n}}const Ae="x-sveltekit-invalidated",Oe="x-sveltekit-trailing-slash";function Mt(e){if(globalThis.Buffer)return Buffer.from(e).toString("base64");const t=new Uint8Array(new Uint16Array([1]).buffer)[0]>0;return btoa(new TextDecoder(t?"utf-16le":"utf-16be").decode(new Uint16Array(new Uint8Array(e))))}async function ge({event:e,state:t,node:r,parent:s}){if(!r?.server)return null;let n=!0;const o={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},a=$t(e.url,()=>{n&&(o.url=!0)},c=>{n&&o.search_params.add(c)});return t.prerendering&&Fe(a),{type:"data",data:await r.server.load?.call(null,{...e,fetch:(c,u)=>(new URL(c instanceof Request?c.url:c,e.url),e.fetch(c,u)),depends:(...c)=>{for(const u of c){const{href:l}=new URL(u,e.url);o.dependencies.add(l)}},params:new Proxy(e.params,{get:(c,u)=>(n&&o.params.add(u),c[u])}),parent:async()=>(n&&(o.parent=!0),s()),route:new Proxy(e.route,{get:(c,u)=>(n&&(o.route=!0),c[u])}),url:a,untrack(c){n=!1;try{return c()}finally{n=!0}}})??null,uses:o,slash:r.server.trailingSlash}}async function ot({event:e,fetched:t,node:r,parent:s,server_data_promise:n,state:o,resolve_opts:a,csr:i}){const c=await n;return r?.universal?.load?await r.universal.load.call(null,{url:e.url,params:e.params,data:c?.data??null,route:e.route,fetch:Jt(e,o,t,i,a),setHeaders:e.setHeaders,depends:()=>{},parent:s,untrack:l=>l()})??null:c?.data??null}function Jt(e,t,r,s,n){const o=async(a,i)=>{const c=a instanceof Request&&a.body?a.clone().body:null,u=a instanceof Request&&[...a.headers].length?new Headers(a.headers):i?.headers;let l=await e.fetch(a,i);const d=new URL(a instanceof Request?a.url:a,e.url),m=d.origin===e.url.origin;let f;if(m)t.prerendering&&(f={response:l,body:null},t.prerendering.dependencies.set(d.pathname,f));else if((a instanceof Request?a.mode:i?.mode??"cors")==="no-cors")l=new Response("",{status:l.status,statusText:l.statusText,headers:l.headers});else{const k=l.headers.get("access-control-allow-origin");if(!k||k!==e.url.origin&&k!=="*")throw new Error(`CORS error: ${k?"Incorrect":"No"} 'Access-Control-Allow-Origin' header is present on the requested resource`)}const h=new Proxy(l,{get(y,k,g){async function w(p,x){const _=Number(y.status);if(isNaN(_))throw new Error(`response.status is not a number. value: "${y.status}" type: ${typeof y.status}`);r.push({url:m?d.href.slice(e.url.origin.length):d.href,method:e.request.method,request_body:a instanceof Request&&c?await Bt(c):i?.body,request_headers:u,response_body:p,response:y,is_b64:x})}if(k==="arrayBuffer")return async()=>{const p=await y.arrayBuffer();return f&&(f.body=new Uint8Array(p)),p instanceof ArrayBuffer&&await w(Mt(p),!0),p};async function v(){const p=await y.text();return(!p||typeof p=="string")&&await w(p,!1),f&&(f.body=p),p}return k==="text"?v:k==="json"?async()=>JSON.parse(await v()):Reflect.get(y,k,y)}});if(s){const y=l.headers.get;l.headers.get=k=>{const g=k.toLowerCase(),w=y.call(l.headers,g);if(w&&!g.startsWith("x-sveltekit-")&&!n.filterSerializedResponseHeaders(g,w))throw new Error(`Failed to get response header "${g}" — it must be included by the \`filterSerializedResponseHeaders\` option: https://svelte.dev/docs/kit/hooks#Server-hooks-handle (at ${e.route.id})`);return w}}return h};return(a,i)=>{const c=o(a,i);return c.catch(()=>{}),c}}async function Bt(e){let t="";const r=e.getReader(),s=new TextDecoder;for(;;){const{done:n,value:o}=await r.read();if(n)break;t+=s.decode(o)}return t}function it(...e){let t=5381;for(const r of e)if(typeof r=="string"){let s=r.length;for(;s;)t=t*33^r.charCodeAt(--s)}else if(ArrayBuffer.isView(r)){const s=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);let n=s.length;for(;n;)t=t*33^s[--n]}else throw new TypeError("value must be a string or TypedArray");return(t>>>0).toString(36)}const ct={"<":"\\u003C","\u2028":"\\u2028","\u2029":"\\u2029"},Vt=new RegExp(`[${Object.keys(ct).join("")}]`,"g");function Kt(e,t,r=!1){const s={};let n=null,o=null,a=!1;for(const[l,d]of e.response.headers)t(l,d)&&(s[l]=d),l==="cache-control"?n=d:l==="age"?o=d:l==="vary"&&d.trim()==="*"&&(a=!0);const i={status:e.response.status,statusText:e.response.statusText,headers:s,body:e.response_body},c=JSON.stringify(i).replace(Vt,l=>ct[l]),u=['type="application/json"',"data-sveltekit-fetched",`data-url="${ye(e.url,!0)}"`];if(e.is_b64&&u.push("data-b64"),e.request_headers||e.request_body){const l=[];e.request_headers&&l.push([...new Headers(e.request_headers)].join(",")),e.request_body&&l.push(e.request_body),u.push(`data-hash="${it(...l)}"`)}if(!r&&e.method==="GET"&&n&&!a){const l=/s-maxage=(\d+)/g.exec(n)??/max-age=(\d+)/g.exec(n);if(l){const d=+l[1]-+(o??"0");u.push(`data-ttl="${d}"`)}}return`<script ${u.join(" ")}>${c}<\/script>`}const z=JSON.stringify,Xt=new TextEncoder;function Te(e){le[0]||Qt();const t=lt.slice(0),r=Yt(e);for(let n=0;n<r.length;n+=16){const o=r.subarray(n,n+16);let a,i,c,u=t[0],l=t[1],d=t[2],m=t[3],f=t[4],h=t[5],y=t[6],k=t[7];for(let g=0;g<64;g++)g<16?a=o[g]:(i=o[g+1&15],c=o[g+14&15],a=o[g&15]=(i>>>7^i>>>18^i>>>3^i<<25^i<<14)+(c>>>17^c>>>19^c>>>10^c<<15^c<<13)+o[g&15]+o[g+9&15]|0),a=a+k+(f>>>6^f>>>11^f>>>25^f<<26^f<<21^f<<7)+(y^f&(h^y))+le[g],k=y,y=h,h=f,f=m+a|0,m=d,d=l,l=u,u=a+(l&d^m&(l^d))+(l>>>2^l>>>13^l>>>22^l<<30^l<<19^l<<10)|0;t[0]=t[0]+u|0,t[1]=t[1]+l|0,t[2]=t[2]+d|0,t[3]=t[3]+m|0,t[4]=t[4]+f|0,t[5]=t[5]+h|0,t[6]=t[6]+y|0,t[7]=t[7]+k|0}const s=new Uint8Array(t.buffer);return ut(s),dt(s)}const lt=new Uint32Array(8),le=new Uint32Array(64);function Qt(){function e(r){return(r-Math.floor(r))*4294967296}let t=2;for(let r=0;r<64;t++){let s=!0;for(let n=2;n*n<=t;n++)if(t%n===0){s=!1;break}s&&(r<8&&(lt[r]=e(t**(1/2))),le[r]=e(t**(1/3)),r++)}}function ut(e){for(let t=0;t<e.length;t+=4){const r=e[t+0],s=e[t+1],n=e[t+2],o=e[t+3];e[t+0]=o,e[t+1]=n,e[t+2]=s,e[t+3]=r}}function Yt(e){const t=Xt.encode(e),r=t.length*8,s=512*Math.ceil((r+65)/512),n=new Uint8Array(s/8);n.set(t),n[t.length]=128,ut(n);const o=new Uint32Array(n.buffer);return o[o.length-2]=Math.floor(r/4294967296),o[o.length-1]=r,o}const U="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");function dt(e){const t=e.length;let r="",s;for(s=2;s<t;s+=3)r+=U[e[s-2]>>2],r+=U[(e[s-2]&3)<<4|e[s-1]>>4],r+=U[(e[s-1]&15)<<2|e[s]>>6],r+=U[e[s]&63];return s===t+1&&(r+=U[e[s-2]>>2],r+=U[(e[s-2]&3)<<4],r+="=="),s===t&&(r+=U[e[s-2]>>2],r+=U[(e[s-2]&3)<<4|e[s-1]>>4],r+=U[(e[s-1]&15)<<2],r+="="),r}const qe=new Uint8Array(16);function Zt(){return crypto.getRandomValues(qe),dt(qe)}const er=new Set(["self","unsafe-eval","unsafe-hashes","unsafe-inline","none","strict-dynamic","report-sample","wasm-unsafe-eval","script"]),tr=/^(nonce|sha\d\d\d)-/;class ft{#e;#t;#l;#u;#d;#n;#f;#h;#a;#o;#i;#s;#c;#r;#p;constructor(t,r,s){this.#e=t,this.#a=r;const n=this.#a;this.#o=[],this.#i=[],this.#s=[],this.#c=[],this.#r=[];const o=n["script-src"]||n["default-src"],a=n["script-src-elem"],i=n["style-src"]||n["default-src"],c=n["style-src-attr"],u=n["style-src-elem"],l=d=>!!d&&!d.some(m=>m==="unsafe-inline");this.#l=l(o),this.#u=l(a),this.#n=l(i),this.#f=l(c),this.#h=l(u),this.#t=this.#l||this.#u,this.#d=this.#n||this.#f||this.#h,this.script_needs_nonce=this.#t&&!this.#e,this.style_needs_nonce=this.#d&&!this.#e,this.#p=s}add_script(t){if(!this.#t)return;const r=this.#e?`sha256-${Te(t)}`:`nonce-${this.#p}`;this.#l&&this.#o.push(r),this.#u&&this.#i.push(r)}add_style(t){if(!this.#d)return;const r=this.#e?`sha256-${Te(t)}`:`nonce-${this.#p}`;if(this.#n&&this.#s.push(r),this.#n&&this.#s.push(r),this.#f&&this.#c.push(r),this.#h){const s="sha256-9OlNO0DNEeaVzHL4RZwCLsBHA8WBQ8toBp/4F5XV2nc=",n=this.#a;n["style-src-elem"]&&!n["style-src-elem"].includes(s)&&!this.#r.includes(s)&&this.#r.push(s),r!==s&&this.#r.push(r)}}get_header(t=!1){const r=[],s={...this.#a};this.#s.length>0&&(s["style-src"]=[...s["style-src"]||s["default-src"]||[],...this.#s]),this.#c.length>0&&(s["style-src-attr"]=[...s["style-src-attr"]||[],...this.#c]),this.#r.length>0&&(s["style-src-elem"]=[...s["style-src-elem"]||[],...this.#r]),this.#o.length>0&&(s["script-src"]=[...s["script-src"]||s["default-src"]||[],...this.#o]),this.#i.length>0&&(s["script-src-elem"]=[...s["script-src-elem"]||[],...this.#i]);for(const n in s){if(t&&(n==="frame-ancestors"||n==="report-uri"||n==="sandbox"))continue;const o=s[n];if(!o)continue;const a=[n];Array.isArray(o)&&o.forEach(i=>{er.has(i)||tr.test(i)?a.push(`'${i}'`):a.push(i)}),r.push(a.join(" "))}return r.join("; ")}}class rr extends ft{get_meta(){const t=this.get_header(!0);if(t)return`<meta http-equiv="content-security-policy" content="${ye(t,!0)}">`}}class sr extends ft{constructor(t,r,s){if(super(t,r,s),Object.values(r).filter(n=>!!n).length>0){const n=r["report-to"]?.length??!1,o=r["report-uri"]?.length??!1;if(!n&&!o)throw Error("`content-security-policy-report-only` must be specified with either the `report-to` or `report-uri` directives, or both")}}}class nr{nonce=Zt();csp_provider;report_only_provider;constructor({mode:t,directives:r,reportOnly:s},{prerender:n}){const o=t==="hash"||t==="auto"&&n;this.csp_provider=new rr(o,r,this.nonce),this.report_only_provider=new sr(o,s,this.nonce)}get script_needs_nonce(){return this.csp_provider.script_needs_nonce||this.report_only_provider.script_needs_nonce}get style_needs_nonce(){return this.csp_provider.style_needs_nonce||this.report_only_provider.style_needs_nonce}add_script(t){this.csp_provider.add_script(t),this.report_only_provider.add_script(t)}add_style(t){this.csp_provider.add_style(t),this.report_only_provider.add_style(t)}}function Pe(){let e,t;return{promise:new Promise((s,n)=>{e=s,t=n}),fulfil:e,reject:t}}function ht(){const e=[Pe()];return{iterator:{[Symbol.asyncIterator](){return{next:async()=>{const t=await e[0].promise;return t.done||e.shift(),t}}}},push:t=>{e[e.length-1].fulfil({value:t,done:!1}),e.push(Pe())},done:()=>{e[e.length-1].fulfil({done:!0})}}}const ar={...xt(!1),check:()=>!1},Ce=new TextEncoder;async function B({branch:e,fetched:t,options:r,manifest:s,state:n,page_config:o,status:a,error:i=null,event:c,resolve_opts:u,action_result:l}){if(n.prerendering){if(r.csp.mode==="nonce")throw new Error('Cannot use prerendering if config.kit.csp.mode === "nonce"');if(r.app_template_contains_nonce)throw new Error("Cannot use prerendering if page template contains %sveltekit.nonce%")}const{client:d}=s._,m=new Set(d.imports),f=new Set(d.stylesheets),h=new Set(d.fonts),y=new Set,k=new Map;let g;const w=l?.type==="success"||l?.type==="failure"?l.data??null:null;let v=D,p=X,x=z(D);if(o.ssr){const E={stores:{page:ke(null),navigating:ke(null),updated:ar},constructors:await Promise.all(e.map(({node:S})=>S.component())),form:w};let j={};for(let S=0;S<e.length;S+=1)j={...j,...e[S].data},E[`data_${S}`]=j;E.page={error:i,params:c.params,route:c.route,status:a,url:c.url,data:j,form:w,state:{}};try{g=r.root.render(E)}finally{wt()}for(const{node:S}of e){for(const O of S.imports)m.add(O);for(const O of S.stylesheets)f.add(O);for(const O of S.fonts)h.add(O);S.inline_styles&&Object.entries(await S.inline_styles()).forEach(([O,M])=>k.set(O,M))}}else g={head:"",html:"",css:{code:"",map:null}};let _="",b=g.html;const $=new nr(r.csp,{prerender:!!n.prerendering}),R=E=>E.startsWith("/")?D+E:`${p}/${E}`;if(k.size>0){const E=Array.from(k.values()).join(`
`),j=[];$.style_needs_nonce&&j.push(` nonce="${$.nonce}"`),$.add_style(E),_+=`
	<style${j.join("")}>${E}</style>`}for(const E of f){const j=R(E),S=['rel="stylesheet"'];if(k.has(E))S.push("disabled",'media="(max-width: 0)"');else if(u.preload({type:"css",path:j})){const O=['rel="preload"','as="style"'];y.add(`<${encodeURI(j)}>; ${O.join(";")}; nopush`)}_+=`
		<link href="${j}" ${S.join(" ")}>`}for(const E of h){const j=R(E);if(u.preload({type:"font",path:j})){const O=['rel="preload"','as="font"',`type="font/${E.slice(E.lastIndexOf(".")+1)}"`,`href="${j}"`,"crossorigin"];_+=`
		<link ${O.join(" ")}>`}}const A=`__sveltekit_${r.version_hash}`,{data:F,chunks:C}=or(c,r,e.map(E=>E.server_data),$,A);if(o.ssr&&o.csr&&(b+=`
			${t.map(E=>Kt(E,u.filterSerializedResponseHeaders,!!n.prerendering)).join(`
			`)}`),o.csr){d.uses_env_dynamic_public&&n.prerendering&&m.add(`${r.app_dir}/env.js`);const E=Array.from(m,q=>R(q)).filter(q=>u.preload({type:"js",path:q}));for(const q of E)y.add(`<${encodeURI(q)}>; rel="modulepreload"; nopush`),r.preload_strategy!=="modulepreload"?_+=`
		<link rel="preload" as="script" crossorigin="anonymous" href="${q}">`:n.prerendering&&(_+=`
		<link rel="modulepreload" href="${q}">`);const j=[],S=d.uses_env_dynamic_public&&n.prerendering,O=[`base: ${x}`];X&&O.push(`assets: ${z(X)}`),d.uses_env_dynamic_public&&O.push(`env: ${S?"null":z(Me)}`),C&&(j.push("const deferred = new Map();"),O.push(`defer: (id) => new Promise((fulfil, reject) => {
							deferred.set(id, { fulfil, reject });
						})`),O.push(`resolve: ({ id, data, error }) => {
							const { fulfil, reject } = deferred.get(id);
							deferred.delete(id);

							if (error) reject(error);
							else fulfil(data);
						}`)),j.push(`${A} = {
						${O.join(`,
						`)}
					};`);const M=["app","element"];if(j.push("const element = document.currentScript.parentElement;"),o.ssr){const q={form:"null",error:"null"};j.push(`const data = ${F};`),w&&(q.form=Gt(w,c.route.id)),i&&(q.error=N.uneval(i));const ee=[`node_ids: [${e.map(({node:gt})=>gt.index).join(", ")}]`,"data",`form: ${q.form}`,`error: ${q.error}`];a!==200&&ee.push(`status: ${a}`),r.embedded&&ee.push(`params: ${N.uneval(c.params)}`,`route: ${z(c.route)}`);const te="	".repeat(S?7:6);M.push(`{
${te}	${ee.join(`,
${te}	`)}
${te}}`)}S?j.push(`import(${z(`${v}/${r.app_dir}/env.js`)}).then(({ env }) => {
						${A}.env = env;

						Promise.all([
							import(${z(R(d.start))}),
							import(${z(R(d.app))})
						]).then(([kit, app]) => {
							kit.start(${M.join(", ")});
						});
					});`):j.push(`Promise.all([
						import(${z(R(d.start))}),
						import(${z(R(d.app))})
					]).then(([kit, app]) => {
						kit.start(${M.join(", ")});
					});`),r.service_worker&&j.push(`if ('serviceWorker' in navigator) {
						addEventListener('load', function () {
							navigator.serviceWorker.register('${R("service-worker.js")}');
						});
					}`);const we=`
				{
					${j.join(`

					`)}
				}
			`;$.add_script(we),b+=`
			<script${$.script_needs_nonce?` nonce="${$.nonce}"`:""}>${we}<\/script>
		`}const L=new Headers({"x-sveltekit-page":"true","content-type":"text/html"});if(n.prerendering){const E=[],j=$.csp_provider.get_meta();j&&E.push(j),n.prerendering.cache&&E.push(`<meta http-equiv="cache-control" content="${n.prerendering.cache}">`),E.length>0&&(_=E.join(`
`)+_)}else{const E=$.csp_provider.get_header();E&&L.set("content-security-policy",E);const j=$.report_only_provider.get_header();j&&L.set("content-security-policy-report-only",j),y.size&&L.set("link",Array.from(y).join(", "))}_+=g.head;const yt=r.templates.app({head:_,body:b,assets:p,nonce:$.nonce,env:Et}),Z=await u.transformPageChunk({html:yt,done:!0})||"";return C||L.set("etag",`"${it(Z)}"`),C?new Response(new ReadableStream({async start(E){E.enqueue(Ce.encode(Z+`
`));for await(const j of C)E.enqueue(Ce.encode(j));E.close()},type:"bytes"}),{headers:L}):T(Z,{status:a,headers:L})}function or(e,t,r,s,n){let o=1,a=0;const{iterator:i,push:c,done:u}=ht();function l(d){if(typeof d?.then=="function"){const m=o++;return a+=1,d.then(f=>({data:f})).catch(async f=>({error:await P(e,t,f)})).then(async({data:f,error:h})=>{a-=1;let y;try{y=N.uneval({id:m,data:f,error:h},l)}catch{h=await P(e,t,new Error(`Failed to serialize promise while rendering ${e.route.id}`)),f=void 0,y=N.uneval({id:m,data:f,error:h},l)}const k=s.script_needs_nonce?` nonce="${s.nonce}"`:"";c(`<script${k}>${n}.resolve(${y})<\/script>
`),a===0&&u()}),`${n}.defer(${m})`}}try{return{data:`[${r.map(m=>m?`{"type":"data","data":${N.uneval(m.data,l)},${Ze(m)}${m.slash?`,"slash":${JSON.stringify(m.slash)}`:""}}`:"null").join(",")}]`,chunks:a>0?i:null}}catch(d){throw new Error(Ye(e,d))}}function H(e,t){return e.reduce((r,s)=>s?.universal?.[t]??s?.server?.[t]??r,void 0)}async function pt({event:e,options:t,manifest:r,state:s,status:n,error:o,resolve_opts:a}){if(e.request.headers.get("x-sveltekit-error"))return Q(t,n,o.message);const i=[];try{const c=[],u=await r._.nodes[0](),l=H([u],"ssr")??!0,d=H([u],"csr")??!0;if(l){s.error=!0;const m=ge({event:e,state:s,node:u,parent:async()=>({})}),f=await m,h=await ot({event:e,fetched:i,node:u,parent:async()=>({}),resolve_opts:a,server_data_promise:m,state:s,csr:d});c.push({node:u,server_data:f,data:h},{node:await r._.nodes[1](),data:null,server_data:null})}return await B({options:t,manifest:r,state:s,page_config:{ssr:l,csr:d},status:n,error:await P(e,t,o),branch:c,fetched:i,event:e,resolve_opts:a})}catch(c){return c instanceof I?Y(c.status,c.location):Q(t,G(c),(await P(e,t,c)).message)}}function ir(e){let t=!1,r;return()=>t?r:(t=!0,r=e())}const He=new TextEncoder;async function cr(e,t,r,s,n,o,a){if(!t.page)return new Response(void 0,{status:404});try{const i=[...t.page.layouts,t.page.leaf],c=o??i.map(()=>!0);let u=!1;const l=new URL(e.url);l.pathname=_e(l.pathname,a);const d={...e,url:l},m=i.map((w,v)=>ir(async()=>{try{if(u)return{type:"skip"};const p=w==null?w:await s._.nodes[w]();return ge({event:d,state:n,node:p,parent:async()=>{const x={};for(let _=0;_<v;_+=1){const b=await m[_]();b&&Object.assign(x,b.data)}return x}})}catch(p){throw u=!0,p}})),f=m.map(async(w,v)=>c[v]?w():{type:"skip"});let h=f.length;const y=await Promise.all(f.map((w,v)=>w.catch(async p=>{if(p instanceof I)throw p;return h=Math.min(h,v+1),{type:"error",error:await P(e,r,p),status:p instanceof V||p instanceof W?p.status:void 0}}))),{data:k,chunks:g}=_t(e,r,y);return g?new Response(new ReadableStream({async start(w){w.enqueue(He.encode(k));for await(const v of g)w.enqueue(He.encode(v));w.close()},type:"bytes"}),{headers:{"content-type":"text/sveltekit-data","cache-control":"private, no-store"}}):ue(k)}catch(i){const c=i;return c instanceof I?de(c):ue(await P(e,r,c),500)}}function ue(e,t=200){return T(typeof e=="string"?e:JSON.stringify(e),{status:t,headers:{"content-type":"application/json","cache-control":"private, no-store"}})}function de(e){return ue({type:"redirect",location:e.location})}function _t(e,t,r){let s=1,n=0;const{iterator:o,push:a,done:i}=ht(),c={Promise:u=>{if(typeof u?.then=="function"){const l=s++;n+=1;let d="data";return u.catch(async m=>(d="error",P(e,t,m))).then(async m=>{let f;try{f=N.stringify(m,c)}catch{const h=await P(e,t,new Error(`Failed to serialize promise while rendering ${e.route.id}`));d="error",f=N.stringify(h,c)}n-=1,a(`{"type":"chunk","id":${l},"${d}":${f}}
`),n===0&&i()}),l}}};try{return{data:`{"type":"data","nodes":[${r.map(l=>l?l.type==="error"||l.type==="skip"?JSON.stringify(l):`{"type":"data","data":${N.stringify(l.data,c)},${Ze(l)}${l.slash?`,"slash":${JSON.stringify(l.slash)}`:""}}`:"null").join(",")}]}
`,chunks:n>0?o:null}}catch(u){throw new Error(Ye(e,u))}}function fe(e,t){return Promise.all([...e.layouts.map(r=>r==null?r:t._.nodes[r]()),t._.nodes[e.leaf]()])}const lr=10;async function ur(e,t,r,s,n,o){if(n.depth>lr)return T(`Not found: ${e.url.pathname}`,{status:404});if(et(e)){const a=await s._.nodes[t.leaf]();return Lt(e,r,a?.server)}try{const a=await fe(t,s),i=a.at(-1);let c=200,u;if(Wt(e)){if(u=await Ft(e,i.server),u?.type==="redirect")return Y(u.status,u.location);u?.type==="error"&&(c=G(u.error)),u?.type==="failure"&&(c=u.status)}const l=a.some(p=>p?.server?.load),d=Ge(e.url.pathname),m=H(a,"prerender")??!1;if(m){if(i.server?.actions)throw new Error("Cannot prerender pages with actions")}else if(n.prerendering)return new Response(void 0,{status:204});n.prerender_default=m;const f=[];if(H(a,"ssr")===!1&&!(n.prerendering&&l))return ie&&u&&e.request.headers.has("x-sveltekit-action"),await B({branch:[],fetched:f,page_config:{ssr:!1,csr:H(a,"csr")??!0},status:c,error:null,event:e,options:r,manifest:s,state:n,resolve_opts:o});const h=[];let y=null;const k=a.map((p,x)=>{if(y)throw y;return Promise.resolve().then(async()=>{try{if(p===i&&u?.type==="error")throw u.error;return await ge({event:e,state:n,node:p,parent:async()=>{const _={};for(let b=0;b<x;b+=1){const $=await k[b];$&&Object.assign(_,$.data)}return _}})}catch(_){throw y=_,y}})}),g=H(a,"csr")??!0,w=a.map((p,x)=>{if(y)throw y;return Promise.resolve().then(async()=>{try{return await ot({event:e,fetched:f,node:p,parent:async()=>{const _={};for(let b=0;b<x;b+=1)Object.assign(_,await w[b]);return _},resolve_opts:o,server_data_promise:k[x],state:n,csr:g})}catch(_){throw y=_,y}})});for(const p of k)p.catch(()=>{});for(const p of w)p.catch(()=>{});for(let p=0;p<a.length;p+=1){const x=a[p];if(x)try{const _=await k[p],b=await w[p];h.push({node:x,server_data:_,data:b})}catch(_){const b=_;if(b instanceof I){if(n.prerendering&&l){const A=JSON.stringify({type:"redirect",location:b.location});n.prerendering.dependencies.set(d,{response:T(A),body:A})}return Y(b.status,b.location)}const $=G(b),R=await P(e,r,b);for(;p--;)if(t.errors[p]){const A=t.errors[p],F=await s._.nodes[A]();let C=p;for(;!h[C];)C-=1;return await B({event:e,options:r,manifest:s,state:n,resolve_opts:o,page_config:{ssr:!0,csr:!0},status:$,error:R,branch:Re(h.slice(0,C+1)).concat({node:F,data:null,server_data:null}),fetched:f})}return Q(r,$,R.message)}else h.push(null)}if(n.prerendering&&l){let{data:p,chunks:x}=_t(e,r,h.map(_=>_?.server_data));if(x)for await(const _ of x)p+=_;n.prerendering.dependencies.set(d,{response:T(p),body:p})}const v=H(a,"ssr")??!0;return await B({event:e,options:r,manifest:s,state:n,resolve_opts:o,page_config:{csr:H(a,"csr")??!0,ssr:v},status:c,error:null,branch:v===!1?[]:Re(h),action_result:u,fetched:f})}catch(a){return await pt({event:e,options:r,manifest:s,state:n,status:500,error:a,resolve_opts:o})}}function dr(e,t,r){const s={},n=e.slice(1),o=n.filter(i=>i!==void 0);let a=0;for(let i=0;i<t.length;i+=1){const c=t[i];let u=n[i-a];if(c.chained&&c.rest&&a&&(u=n.slice(i-a,i+1).filter(l=>l).join("/"),a=0),u===void 0){c.rest&&(s[c.name]="");continue}if(!c.matcher||r[c.matcher](u)){s[c.name]=u;const l=t[i+1],d=n[i+1];l&&!l.rest&&l.optional&&d&&c.chained&&(a=0),!l&&!d&&Object.keys(s).length===o.length&&(a=0);continue}if(c.optional&&c.chained){a++;continue}return}if(!a)return s}const fr=/[\x00-\x1F\x7F()<>@,;:"/[\]?={} \t]/;function re(e){if(e?.path===void 0)throw new Error("You must specify a `path` when setting, deleting or serializing cookies")}function hr(e,t,r){const s=e.headers.get("cookie")??"",n=K(s,{decode:d=>d}),o=_e(t.pathname,r),a={},i={httpOnly:!0,sameSite:"lax",secure:!(t.hostname==="localhost"&&t.protocol==="http:")},c={get(d,m){const f=a[d];return f&&se(t.hostname,f.options.domain)&&ne(t.pathname,f.options.path)?f.value:K(s,{decode:m?.decode})[d]},getAll(d){const m=K(s,{decode:d?.decode});for(const f of Object.values(a))se(t.hostname,f.options.domain)&&ne(t.pathname,f.options.path)&&(m[f.name]=f.value);return Object.entries(m).map(([f,h])=>({name:f,value:h}))},set(d,m,f){const h=d.match(fr);h&&console.warn(`The cookie name "${d}" will be invalid in SvelteKit 3.0 as it contains ${h.join(" and ")}. See RFC 2616 for more details https://datatracker.ietf.org/doc/html/rfc2616#section-2.2`),re(f),l(d,m,{...i,...f})},delete(d,m){re(m),c.set(d,"",{...m,maxAge:0})},serialize(d,m,f){re(f);let h=f.path;return(!f.domain||f.domain===t.hostname)&&(h=$e(o,h)),ce(d,m,{...i,...f,path:h})}};function u(d,m){const f={...n};for(const h in a){const y=a[h];if(!se(d.hostname,y.options.domain)||!ne(d.pathname,y.options.path))continue;const k=y.options.encode||encodeURIComponent;f[y.name]=k(y.value)}if(m){const h=K(m,{decode:y=>y});for(const y in h)f[y]=h[y]}return Object.entries(f).map(([h,y])=>`${h}=${y}`).join("; ")}function l(d,m,f){let h=f.path;(!f.domain||f.domain===t.hostname)&&(h=$e(o,h)),a[d]={name:d,value:m,options:{...f,path:h}}}return{cookies:c,new_cookies:a,get_cookie_header:u,set_internal:l}}function se(e,t){if(!t)return!0;const r=t[0]==="."?t.slice(1):t;return e===r?!0:e.endsWith("."+r)}function ne(e,t){if(!t)return!0;const r=t.endsWith("/")?t.slice(0,-1):t;return e===r?!0:e.startsWith(r+"/")}function Ne(e,t){for(const r of t){const{name:s,value:n,options:o}=r;if(e.append("set-cookie",ce(s,n,o)),o.path.endsWith(".html")){const a=Ge(o.path);e.append("set-cookie",ce(s,n,{...o,path:a}))}}}function pr({event:e,options:t,manifest:r,state:s,get_cookie_header:n,set_internal:o}){const a=async(i,c)=>{const u=ze(i,c,e.url);let l=(i instanceof Request?i.mode:c?.mode)??"cors",d=(i instanceof Request?i.credentials:c?.credentials)??"same-origin";return t.hooks.handleFetch({event:e,request:u,fetch:async(m,f)=>{const h=ze(m,f,e.url),y=new URL(h.url);if(h.headers.has("origin")||h.headers.set("origin",e.url.origin),m!==u&&(l=(m instanceof Request?m.mode:f?.mode)??"cors",d=(m instanceof Request?m.credentials:f?.credentials)??"same-origin"),(h.method==="GET"||h.method==="HEAD")&&(l==="no-cors"&&y.origin!==e.url.origin||y.origin===e.url.origin)&&h.headers.delete("origin"),y.origin!==e.url.origin){if(`.${y.hostname}`.endsWith(`.${e.url.hostname}`)&&d!=="omit"){const $=n(y,h.headers.get("cookie"));$&&h.headers.set("cookie",$)}return fetch(h)}const k=X||D,g=decodeURIComponent(y.pathname),w=(g.startsWith(k)?g.slice(k.length):g).slice(1),v=`${w}/index.html`,p=r.assets.has(w)||w in r._.server_assets,x=r.assets.has(v)||v in r._.server_assets;if(p||x){const $=p?w:v;if(s.read){const R=p?r.mimeTypes[w.slice(w.lastIndexOf("."))]:"text/html";return new Response(s.read($),{headers:R?{"content-type":R}:{}})}else if(ve&&$ in r._.server_assets){const R=r._.server_assets[$],A=r.mimeTypes[$.slice($.lastIndexOf("."))];return new Response(ve($),{headers:{"Content-Length":""+R,"Content-Type":A}})}return await fetch(h)}if(d!=="omit"){const $=n(y,h.headers.get("cookie"));$&&h.headers.set("cookie",$);const R=e.request.headers.get("authorization");R&&!h.headers.has("authorization")&&h.headers.set("authorization",R)}h.headers.has("accept")||h.headers.set("accept","*/*"),h.headers.has("accept-language")||h.headers.set("accept-language",e.request.headers.get("accept-language"));const _=await mt(h,t,r,{...s,depth:s.depth+1}),b=_.headers.get("set-cookie");if(b)for(const $ of xe.splitCookiesString(b)){const{name:R,value:A,...F}=xe.parseString($,{decodeValues:!1}),C=F.path??(y.pathname.split("/").slice(0,-1).join("/")||"/");o(R,A,{path:C,encode:L=>L,...F})}return _}})};return(i,c)=>{const u=a(i,c);return u.catch(()=>{}),u}}function ze(e,t,r){return e instanceof Request?e:new Request(typeof e=="string"?new URL(e,r):e,t)}let Ue,ae,oe;function _r(e){return Ue??=`export const env=${JSON.stringify(Me)}`,ae??=`W/${Date.now()}`,oe??=new Headers({"content-type":"application/javascript; charset=utf-8",etag:ae}),e.headers.get("if-none-match")===ae?new Response(void 0,{status:304,headers:oe}):new Response(Ue,{headers:oe})}function mr(e){let t={};for(const r of e)!r?.universal?.config&&!r?.server?.config||(t={...t,...r?.universal?.config,...r?.server?.config});return Object.keys(t).length?t:void 0}const De=({html:e})=>e,Ie=()=>!1,Le=({type:e})=>e==="js"||e==="css",yr=new Set(["GET","HEAD","POST"]),gr=new Set(["GET","HEAD","OPTIONS"]);async function mt(e,t,r,s){const n=new URL(e.url);if(t.csrf_check_origin&&Be(e)&&(e.method==="POST"||e.method==="PUT"||e.method==="PATCH"||e.method==="DELETE")&&e.headers.get("origin")!==n.origin){const w=new V(403,`Cross-site ${e.method} form submissions are forbidden`);return e.headers.get("accept")==="application/json"?he(w.body,{status:w.status}):T(w.body.message,{status:w.status})}let o;try{o=t.hooks.reroute({url:new URL(n)})??n.pathname}catch{return T("Internal Server Error",{status:500})}let a;try{a=bt(o)}catch{return T("Malformed URI",{status:400})}let i=null,c={};if(D&&!s.prerendering?.fallback){if(!a.startsWith(D))return T("Not found",{status:404});a=a.slice(D.length)||"/"}if(a===`/${t.app_dir}/env.js`)return _r(e);if(a.startsWith(`/${t.app_dir}`)){const g=new Headers;return g.set("cache-control","public, max-age=0, must-revalidate"),T("Not found",{status:404,headers:g})}const u=kt(a);let l;if(u&&(a=be(a)||"/",n.pathname=be(n.pathname)+(n.searchParams.get(Oe)==="1"?"/":"")||"/",n.searchParams.delete(Oe),l=n.searchParams.get(Ae)?.split("").map(g=>g==="1"),n.searchParams.delete(Ae)),!s.prerendering?.fallback){const g=await r._.matchers();for(const w of r._.routes){const v=w.pattern.exec(a);if(!v)continue;const p=dr(v,w.params,g);if(p){i=w,c=vt(p);break}}}let d;const m={};let f={};const h={cookies:null,fetch:null,getClientAddress:s.getClientAddress||(()=>{throw new Error("@sveltejs/adapter-static does not specify getClientAddress. Please raise an issue")}),locals:{},params:c,platform:s.platform,request:e,route:{id:i?.id??null},setHeaders:g=>{for(const w in g){const v=w.toLowerCase(),p=g[w];if(v==="set-cookie")throw new Error("Use `event.cookies.set(name, value, options)` instead of `event.setHeaders` to set cookies");if(v in m)throw new Error(`"${w}" header is already set`);m[v]=p,s.prerendering&&v==="cache-control"&&(s.prerendering.cache=p)}},url:n,isDataRequest:u,isSubRequest:s.depth>0};let y={transformPageChunk:De,filterSerializedResponseHeaders:Ie,preload:Le};try{if(i){if(n.pathname===D||n.pathname===D+"/")d="always";else if(i.page){const _=await fe(i.page,r);d=H(_,"trailingSlash")}else i.endpoint&&(d=(await i.endpoint()).trailingSlash);if(!u){const _=_e(n.pathname,d??"never");if(_!==n.pathname&&!s.prerendering?.fallback)return new Response(void 0,{status:308,headers:{"x-sveltekit-normalize":"1",location:(_.startsWith("//")?n.origin+_:_)+(n.search==="?"?"":n.search)}})}if(s.before_handle||s.emulator?.platform){let _={},b=!1;if(i.endpoint){const $=await i.endpoint();_=$.config??_,b=$.prerender??b}else if(i.page){const $=await fe(i.page,r);_=mr($)??_,b=H($,"prerender")??!1}s.before_handle&&s.before_handle(h,_,b),s.emulator?.platform&&(h.platform=await s.emulator.platform({config:_,prerender:b}))}}else s.emulator?.platform&&(h.platform=await s.emulator.platform({config:{},prerender:!!s.prerendering?.fallback}));const{cookies:g,new_cookies:w,get_cookie_header:v,set_internal:p}=hr(e,n,d??"never");f=w,h.cookies=g,h.fetch=pr({event:h,options:t,manifest:r,state:s,get_cookie_header:v,set_internal:p}),s.prerendering&&!s.prerendering.fallback&&Fe(n);const x=await t.hooks.handle({event:h,resolve:(_,b)=>k(_,b).then($=>{for(const R in m){const A=m[R];$.headers.set(R,A)}return Ne($.headers,Object.values(f)),s.prerendering&&_.route.id!==null&&$.headers.set("x-sveltekit-routeid",encodeURI(_.route.id)),$})});if(x.status===200&&x.headers.has("etag")){let _=e.headers.get("if-none-match");_?.startsWith('W/"')&&(_=_.substring(2));const b=x.headers.get("etag");if(_===b){const $=new Headers({etag:b});for(const R of["cache-control","content-location","date","expires","vary","set-cookie"]){const A=x.headers.get(R);A&&$.set(R,A)}return new Response(void 0,{status:304,headers:$})}}if(u&&x.status>=300&&x.status<=308){const _=x.headers.get("location");if(_)return de(new I(x.status,_))}return x}catch(g){if(g instanceof I){const w=u?de(g):i?.page&&et(h)?rt(g):Y(g.status,g.location);return Ne(w.headers,Object.values(f)),w}return await je(h,t,g)}async function k(g,w){try{if(w&&(y={transformPageChunk:w.transformPageChunk||De,filterSerializedResponseHeaders:w.filterSerializedResponseHeaders||Ie,preload:w.preload||Le}),s.prerendering?.fallback)return await B({event:g,options:t,manifest:r,state:s,page_config:{ssr:!1,csr:!0},status:200,error:null,branch:[],fetched:[],resolve_opts:y});if(i){const v=g.request.method;let p;if(u)p=await cr(g,i,t,r,s,l,d??"never");else if(i.endpoint&&(!i.page||It(g)))p=await Dt(g,await i.endpoint(),s);else if(i.page)if(yr.has(v))p=await ur(g,i.page,t,r,s,y);else{const x=new Set(gr);if((await r._.nodes[i.page.leaf]())?.server?.actions&&x.add("POST"),v==="OPTIONS")p=new Response(null,{status:204,headers:{allow:Array.from(x.values()).join(", ")}});else{const b=[...x].reduce(($,R)=>($[R]=!0,$),{});p=Qe(b,v)}}else throw new Error("This should never happen");if(e.method==="GET"&&i.page&&i.endpoint){const x=p.headers.get("vary")?.split(",")?.map(_=>_.trim().toLowerCase());x?.includes("accept")||x?.includes("*")||(p=new Response(p.body,{status:p.status,statusText:p.statusText,headers:new Headers(p.headers)}),p.headers.append("Vary","Accept"))}return p}return s.error&&g.isSubRequest?await fetch(e,{headers:{"x-sveltekit-error":"true"}}):s.error?T("Internal Server Error",{status:500}):s.depth===0?await pt({event:g,options:t,manifest:r,state:s,status:404,error:new W(404,"Not Found",`Not found: ${g.url.pathname}`),resolve_opts:y}):s.prerendering?T("not found",{status:404}):await fetch(e)}catch(v){return await je(g,t,v)}finally{g.cookies.set=()=>{throw new Error("Cannot use `cookies.set(...)` after the response has been generated")},g.setHeaders=()=>{throw new Error("Cannot use `setHeaders(...)` after the response has been generated")}}}}function wr(e,{public_prefix:t,private_prefix:r}){return Object.fromEntries(Object.entries(e).filter(([s])=>s.startsWith(r)&&(t===""||!s.startsWith(t))))}function $r(e,{public_prefix:t,private_prefix:r}){return Object.fromEntries(Object.entries(e).filter(([s])=>s.startsWith(t)&&(r===""||!s.startsWith(r))))}const We={get({type:e},t){throw new Error(`Cannot read values from $env/dynamic/${e} while prerendering (attempted to read env.${t.toString()}). Use $env/static/${e} instead`)}};class Cr{#e;#t;constructor(t){this.#e=jt,this.#t=t}async init({env:t,read:r}){const s={public_prefix:this.#e.env_public_prefix,private_prefix:this.#e.env_private_prefix},n=wr(t,s),o=$r(t,s);if(St(Ee?new Proxy({type:"private"},We):n),At(Ee?new Proxy({type:"public"},We):o),Ot(o),r&&Tt(r),!this.#e.hooks)try{const a=await Rt();this.#e.hooks={handle:a.handle||(({event:i,resolve:c})=>c(i)),handleError:a.handleError||(({error:i})=>console.error(i)),handleFetch:a.handleFetch||(({request:i,fetch:c})=>c(i)),reroute:a.reroute||(()=>{})}}catch(a){throw a}}async respond(t,r){return mt(t,this.#e,this.#t,{...r,error:!1,depth:0})}}export{Cr as Server};
