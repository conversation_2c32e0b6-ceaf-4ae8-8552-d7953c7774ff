import{j as r}from"../../../../chunks/index.js";import s from"fs";import a from"path";const y=!1,i="../customization",g=`${i}/custom-formatting.json`,S={version:"1.0",customFormats:[]};async function O(){let t={};try{t=e()||t}catch{}return r({customFormattingSettings:t})}async function p({request:t}){const{newCustomFormat:n}=await t.json();let o=e()||{};return n&&(o.customFormats||(o.customFormats=[]),n.formatTag&&n.formatCode&&o.customFormats.push(n),c(o)),r(o)}async function C({request:t}){const{formatTag:n}=await t.json();let o=e()||{};if(n){o.customFormats||(o.customFormats=[]);let m=o.customFormats.findIndex(f=>f.formatTag===n);m>=0&&o.customFormats.splice(m,1),c(o)}return r(o)}function e(){let t=u();return JSON.parse(s.readFileSync(t,"utf8"))}function c(t){let n=u();s.writeFileSync(n,JSON.stringify(t,null,2))}function u(){let t=a.join(a.resolve("./"),g);return s.existsSync(t)||(s.existsSync(i)||s.mkdirSync(i),s.writeFileSync(t,JSON.stringify(S,null,2))),t}export{C as DELETE,O as GET,p as POST,y as prerender};
