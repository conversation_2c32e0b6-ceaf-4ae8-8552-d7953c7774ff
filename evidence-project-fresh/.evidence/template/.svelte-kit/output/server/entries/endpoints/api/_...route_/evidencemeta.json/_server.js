import c from"fs/promises";import e from"path";import{$ as a}from"../../../../../chunks/index3.js";import{j as p}from"../../../../../chunks/index.js";const _="EVIDENCE_URL_PREFIX"in process.env?process.env.EVIDENCE_URL_PREFIX:"_evidence",E=process.cwd().includes(e.join(".evidence","template")),l=E?"..":".evidence";"EVIDENCE_DATA_DIR"in process.env&&process.env.EVIDENCE_DATA_DIR?e.resolve(process.env.EVIDENCE_DATA_DIR):e.resolve(l,"data");"EVIDENCE_DATA_URL_PREFIX"in process.env?process.env.EVIDENCE_DATA_URL_PREFIX:`${_}`;e.resolve(l,"meta");const d=e.resolve(...E?["..",".."]:[]);e.resolve(d,"sources");const D="EVIDENCE_PAGES_DIR"in process.env&&process.env.EVIDENCE_PAGES_DIR?e.resolve(process.env.EVIDENCE_PAGES_DIR):e.resolve(d,"pages");let u=!1;process.cwd().endsWith("sites/example-project")&&(u=!0);const f=!0,P=async()=>{const t=D;return(await c.readdir(t,{recursive:!0})).filter(s=>s.endsWith(".md")).map(s=>{const n=u?"/+page.md":".md";let r=s.slice(0,-n.length);return s.endsWith("index.md")&&(r=r.replaceAll(/\/?index/g,"")),{route:r}})};async function g({params:{route:t}}){if(t==="/settings")return p({queries:[]});let o;(await c.readdir(process.cwd())).includes("src")?o=e.join("src","pages"):o=e.join(".evidence","template","src","pages");const i=e.join(process.cwd(),o,t,"+page.md"),s=await c.readFile(i,"utf8"),n=a.injectPartials(s),r=a.extractQueries(n);return p({queries:r})}export{g as GET,P as entries,f as prerender};
