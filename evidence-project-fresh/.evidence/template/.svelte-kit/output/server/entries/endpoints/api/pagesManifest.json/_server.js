import{$ as f}from"../../../../chunks/index3.js";import{e as p}from"../../../../chunks/index.js";import c from"fs/promises";import i from"path";function d(s){const n={label:"Home",href:void 0,children:{},frontMatter:{},isTemplated:!1,isPage:!1};for(const[r,o]of Object.entries(s)){const e=r.replace("/src/pages/","");let t=n;for(const a of e.split("/"))if(a==="+page.md")e==="+page.md"?t.href="/":e.includes("[")||(t.href=encodeURI("/"+e.replace("/+page.md",""))),t.frontMatter=f.parseFrontmatter(o),t.isPage=!0;else{const l=a.includes("[")?void 0:a.replace(/_/g," ").replace(/-/g," ");t=t.children[a]=t.children[a]??{label:l,href:void 0,children:{},frontMatter:{},isTemplated:a.includes("["),isPage:!1}}}return n}const w=!0;async function b(){const s={};async function n(r){const o=await c.readdir(r,{withFileTypes:!0});for(const e of o)if(e.isFile()&&e.name.endsWith(".md")){const t=i.join(e.parentPath??e.path??r,e.name),a=await c.readFile(t,"utf-8"),l="/"+i.normalize(t).split(i.sep).join("/");s[l]=a}else e.isDirectory()&&await n(i.join(r,e.name))}try{await n(i.join("src","pages"));const r=d(s);return new Response(JSON.stringify(r))}catch(r){throw console.log("Failed to build pages manifest with error: ",r),p(500,"Failed to build pages manifest.")}}export{b as GET,d as _buildPageManifest,w as prerender};
