import{s as Pt,g as Qt,c as N,a as ye,b as S,e as Dt,d as X,f as Vt,h as b,i as ue,v as g,o as Gt}from"../../chunks/ssr.js";import"dequal";import{i as _e,a as p,w as Z,m as se,e as he,b as pe,c as W,p as Xt,s as Ve,d as Yt,u as qt,n as Ge,f as wt,t as tt,F as kt,k as ae,g as xe,S as ft,h as lt,o as Bt,j as Se,l as Wt}from"../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import"clsx";import"chroma-js";import{t as Zt,s as Jt}from"../../chunks/Query.js";import"@uwdata/mosaic-sql";import"@evidence-dev/universal-sql/client-duckdb";import"ssf";import"@tidyjs/tidy";import"deep-object-diff";import"../../chunks/index5.js";import"export-to-csv";import"echarts";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import{g as $t,c as Pe,s as G}from"../../chunks/utils.js";import{p as it,n as Ht}from"../../chunks/stores.js";import{a as z,c as Ft}from"../../chunks/index6.js";import{c as vt,f as er,I as ve,D as tr,S as rr,M as nr,X as Kt,a as ar,C as ir}from"../../chunks/index7.js";import{s as Ie,w as or,t as nt,g as Ct,d as Mt,u as Et,a as Tt,b as sr,o as at,r as dr,c as lr,e as ur,f as cr,h as bt,i as fr}from"../../chunks/helpers.js";import{w as q,d as qe}from"../../chunks/index2.js";import{c as mr,h as hr,r as vr,i as Ot,B as br}from"../../chunks/button.js";import{nanoid as gr}from"nanoid/non-secure";import"lodash/merge.js";import"prismjs";import"debounce";import"downloadjs";import"echarts-stat";function pr(r){r.setAttribute("data-highlighted","")}function He(r){r.removeAttribute("data-highlighted")}function xr(r,e=500){let t=null;return function(...n){const a=()=>{t=null,r(...n)};t&&clearTimeout(t),t=setTimeout(a,e)}}function Y(r){_e&&Ie(1).then(()=>{const e=document.activeElement;!p(e)||e===r||(e.tabIndex=-1,r&&(r.tabIndex=0,r.focus()))})}function Nt(){return Array.from(document.querySelectorAll('a[href]:not([tabindex="-1"]), button:not([disabled]):not([tabindex="-1"]), input:not([disabled]):not([tabindex="-1"]), select:not([disabled]):not([tabindex="-1"]), textarea:not([disabled]):not([tabindex="-1"]), [tabindex]:not([tabindex="-1"])'))}function yr(r){const e=Nt(),n=e.indexOf(r)+1,a=e[n];return n<e.length&&p(a)?a:null}function wr(r){const e=Nt(),n=e.indexOf(r)-1,a=e[n];return n>=0&&p(a)?a:null}const kr=new Set(["Shift","Control","Alt","Meta","CapsLock","NumLock"]),Cr={onMatch:Y,getCurrentItem:()=>document.activeElement};function Mr(r={}){const e={...Cr,...r},t=Z(q([])),n=xr(()=>{t.update(()=>[])});return{typed:t,resetTyped:n,handleTypeaheadSearch:(d,s)=>{if(kr.has(d))return;const u=e.getCurrentItem(),l=$t(t);if(!Array.isArray(l))return;l.push(d.toLowerCase()),t.set(l);const v=s.filter(o=>!(o.getAttribute("disabled")==="true"||o.getAttribute("aria-disabled")==="true"||o.hasAttribute("data-disabled"))),C=l.length>1&&l.every(o=>o===l[0])?l[0]:l.join(""),E=p(u)?v.indexOf(u):-1;let _=or(v,Math.max(E,0));C.length===1&&(_=_.filter(o=>o!==u));const O=_.find(o=>o?.innerText&&o.innerText.toLowerCase().startsWith(C.toLowerCase()));p(O)&&O!==u&&e.onMatch(O),n()}}}const Er={ltr:[...ft,ae.ARROW_RIGHT]},Tr={ltr:[ae.ARROW_LEFT]},St=["menu","trigger"],Or={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function Sr(r){const{name:e,selector:t}=Yt(r.selector),{preventScroll:n,arrowSize:a,positioning:d,closeOnEscape:s,closeOnOutsideClick:u,portal:l,forceVisible:v,typeahead:k,loop:C,closeFocus:E,disableFocusFirstItem:_,closeOnItemClick:T,onOutsideClick:O}=r.rootOptions,o=r.rootOpen,f=r.rootActiveTrigger,x=r.nextFocusable,L=r.prevFocusable,F=Z.writable(!1),ie=Z(q(0)),J=Z(q(null)),Q=Z(q("right")),$=Z(q(null)),ce=Z(qe([Q,J],([i,m])=>c=>i===m?.side&&Ar(c,m?.area))),{typed:fe,handleTypeaheadSearch:le}=Mr(),ee=nt({...Ct(St),...r.ids}),te=Mt({open:o,forceVisible:v,activeTrigger:f}),be=se(e(),{stores:[te,l,ee.menu,ee.trigger],returned:([i,m,c,h])=>({role:"menu",hidden:i?void 0:!0,style:Ve({display:i?void 0:"none"}),id:c,"aria-labelledby":h,"data-state":i?"open":"closed","data-portal":Xt(m),tabindex:-1}),action:i=>{let m=Ge;const c=he([te,f,d,u,l,s],([w,R,re,K,A,P])=>{m(),!(!w||!R)&&tt().then(()=>{m(),Ye(i,t),m=Et(i,{anchorElement:R,open:o,options:{floating:re,modal:{closeOnInteractOutside:K,shouldCloseOnInteractOutside:M=>(O.get()?.(M),!(M.defaultPrevented||p(R)&&R.contains(M.target))),onClose:()=>{o.set(!1),R.focus()},open:w},portal:Tt(i,A),escapeKeydown:P?void 0:null}}).destroy})}),h=pe(W(i,"keydown",w=>{const R=w.target,re=w.currentTarget;if(!p(R)||!p(re)||!(R.closest('[role="menu"]')===re))return;if(kt.includes(w.key)&&_t(w,C.get()??!1),w.key===ae.TAB){w.preventDefault(),o.set(!1),At(w,x,L);return}const A=w.key.length===1;!(w.ctrlKey||w.altKey||w.metaKey)&&A&&k.get()===!0&&le(w.key,Ae(re))}));return{destroy(){c(),h(),m()}}}}),Ce=se(e("trigger"),{stores:[o,ee.menu,ee.trigger],returned:([i,m,c])=>({"aria-controls":m,"aria-expanded":i,"data-state":i?"open":"closed",id:c,tabindex:0}),action:i=>(rt(i),f.update(c=>c||i),{destroy:pe(W(i,"click",c=>{const h=o.get(),w=c.currentTarget;p(w)&&(Je(w),h||c.preventDefault())}),W(i,"keydown",c=>{const h=c.currentTarget;if(!p(h)||!(ft.includes(c.key)||c.key===ae.ARROW_DOWN))return;c.preventDefault(),Je(h);const w=h.getAttribute("aria-controls");if(!w)return;const R=document.getElementById(w);if(!R)return;const re=Ae(R);re.length&&Y(re[0])}))})}),Me=se(e("arrow"),{stores:a,returned:i=>({"data-arrow":!0,style:Ve({position:"absolute",width:`var(--arrow-size, ${i}px)`,height:`var(--arrow-size, ${i}px)`})})}),Ee=se(e("overlay"),{stores:[te],returned:([i])=>({hidden:i?void 0:!0,tabindex:-1,style:Ve({display:i?void 0:"none"}),"aria-hidden":"true","data-state":Pr(i)}),action:i=>{let m=Ge;if(s.get()){const h=qt(i,{handler:()=>{o.set(!1);const w=f.get();w&&w.focus()}});h&&h.destroy&&(m=h.destroy)}const c=he([l],([h])=>{if(h===null)return Ge;const w=Tt(i,h);return w===null?Ge:sr(i,w).destroy});return{destroy(){m(),c()}}}}),Te=se(e("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:i=>(Ye(i,t),rt(i),{destroy:pe(W(i,"pointerdown",c=>{const h=c.currentTarget;if(p(h)&&xe(h)){c.preventDefault();return}}),W(i,"click",c=>{const h=c.currentTarget;if(p(h)){if(xe(h)){c.preventDefault();return}if(c.defaultPrevented){Y(h);return}T.get()&&Ie(1).then(()=>{o.set(!1)})}}),W(i,"keydown",c=>{De(c)}),W(i,"pointermove",c=>{Ue(c)}),W(i,"pointerleave",c=>{we(c)}),W(i,"focusin",c=>{me(c)}),W(i,"focusout",c=>{Oe(c)}))})}),ge=se(e("group"),{returned:()=>i=>({role:"group","aria-labelledby":i})}),Fe=se(e("group-label"),{returned:()=>i=>({id:i})}),Ke={defaultChecked:!1,disabled:!1},Ne=i=>{const m={...Ke,...i},c=m.checked??q(m.defaultChecked??null),h=at(c,m.onCheckedChange),w=q(m.disabled),R=se(e("checkbox-item"),{stores:[h,w],returned:([A,P])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":Qe(A)?"mixed":A?"true":"false","data-disabled":lt(P),"data-state":oe(A)}),action:A=>(Ye(A,t),rt(A),{destroy:pe(W(A,"pointerdown",M=>{const D=M.currentTarget;if(p(D)&&xe(D)){M.preventDefault();return}}),W(A,"click",M=>{const D=M.currentTarget;if(p(D)){if(xe(D)){M.preventDefault();return}if(M.defaultPrevented){Y(D);return}h.update(ke=>Qe(ke)?!0:!ke),T.get()&&tt().then(()=>{o.set(!1)})}}),W(A,"keydown",M=>{De(M)}),W(A,"pointermove",M=>{const D=M.currentTarget;if(p(D)){if(xe(D)){$e(M);return}Ue(M,D)}}),W(A,"pointerleave",M=>{we(M)}),W(A,"focusin",M=>{me(M)}),W(A,"focusout",M=>{Oe(M)}))})}),re=qe(h,A=>A===!0),K=qe(h,A=>A==="indeterminate");return{elements:{checkboxItem:R},states:{checked:h},helpers:{isChecked:re,isIndeterminate:K},options:{disabled:w}}},Re=(i={})=>{const m=i.value??q(i.defaultValue??null),c=at(m,i.onValueChange),h=se(e("radio-group"),{returned:()=>({role:"group"})}),w={disabled:!1},R=se(e("radio-item"),{stores:[c],returned:([K])=>A=>{const{value:P,disabled:M}={...w,...A},D=K===P;return{disabled:M,role:"menuitemradio","data-state":D?"checked":"unchecked","aria-checked":D,"data-disabled":lt(M),"data-value":P,"data-orientation":"vertical",tabindex:-1}},action:K=>(Ye(K,t),{destroy:pe(W(K,"pointerdown",P=>{const M=P.currentTarget;if(!p(M))return;const D=K.dataset.value;if(K.dataset.disabled||D===void 0){P.preventDefault();return}}),W(K,"click",P=>{const M=P.currentTarget;if(!p(M))return;const D=K.dataset.value;if(K.dataset.disabled||D===void 0){P.preventDefault();return}if(P.defaultPrevented){if(!p(M))return;Y(M);return}c.set(D),T.get()&&tt().then(()=>{o.set(!1)})}),W(K,"keydown",P=>{De(P)}),W(K,"pointermove",P=>{const M=P.currentTarget;if(!p(M))return;const D=K.dataset.value;if(K.dataset.disabled||D===void 0){$e(P);return}Ue(P,M)}),W(K,"pointerleave",P=>{we(P)}),W(K,"focusin",P=>{me(P)}),W(K,"focusout",P=>{Oe(P)}))})}),re=qe(c,K=>A=>K===A);return{elements:{radioGroup:h,radioItem:R},states:{value:c},helpers:{isChecked:re}}},{elements:{root:ze}}=mr({orientation:"horizontal"}),Le={...Or,disabled:!1,positioning:{placement:"right-start",gutter:8}},je=i=>{const m={...Le,...i},c=m.open??q(!1),h=at(c,m?.onOpenChange),w=nt(Bt(m,"ids")),{positioning:R,arrowSize:re,disabled:K}=w,A=Z(q(null)),P=Z(q(null)),M=Z(q(0)),D=nt({...Ct(St),...m.ids});wt(()=>{const I=document.getElementById(D.trigger.get());I&&A.set(I)});const ke=Mt({open:h,forceVisible:v,activeTrigger:A}),Lt=se(e("submenu"),{stores:[ke,D.menu,D.trigger],returned:([I,V,ne])=>({role:"menu",hidden:I?void 0:!0,style:Ve({display:I?void 0:"none"}),id:V,"aria-labelledby":ne,"data-state":I?"open":"closed","data-id":V,tabindex:-1}),action:I=>{let V=Ge;const ne=he([ke,R],([y,H])=>{if(V(),!y)return;const j=A.get();j&&tt().then(()=>{V();const U=xt(j);V=Et(I,{anchorElement:j,open:h,options:{floating:H,portal:p(U)?U:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy})}),B=pe(W(I,"keydown",y=>{if(y.key===ae.ESCAPE)return;const H=y.target,j=y.currentTarget;if(!p(H)||!p(j)||!(H.closest('[role="menu"]')===j))return;if(kt.includes(y.key)){y.stopImmediatePropagation(),_t(y,C.get()??!1);return}const Be=Tr.ltr.includes(y.key),We=y.ctrlKey||y.altKey||y.metaKey,et=y.key.length===1;if(Be){const yt=A.get();y.preventDefault(),h.update(()=>(yt&&Y(yt),!1));return}if(y.key===ae.TAB){y.preventDefault(),o.set(!1),At(y,x,L);return}!We&&et&&k.get()===!0&&le(y.key,Ae(j))}),W(I,"pointermove",y=>{pt(y)}),W(I,"focusout",y=>{const H=A.get();if(F.get()){const j=y.target,U=document.getElementById(D.menu.get());if(!p(U)||!p(j))return;!U.contains(j)&&j!==H&&h.set(!1)}else{const j=y.currentTarget,U=y.relatedTarget;if(!p(U)||!p(j))return;!j.contains(U)&&U!==H&&h.set(!1)}}));return{destroy(){ne(),V(),B()}}}}),jt=se(e("subtrigger"),{stores:[h,K,D.menu,D.trigger],returned:([I,V,ne,B])=>({role:"menuitem",id:B,tabindex:-1,"aria-controls":ne,"aria-expanded":I,"data-state":I?"open":"closed","data-disabled":lt(V),"aria-haspopop":"menu"}),action:I=>{Ye(I,t),rt(I),A.update(B=>B||I);const V=()=>{ut(P),window.clearTimeout(M.get()),J.set(null)},ne=pe(W(I,"click",B=>{if(B.defaultPrevented)return;const y=B.currentTarget;!p(y)||xe(y)||(Y(y),h.get()||h.update(H=>H||(A.set(y),!H)))}),W(I,"keydown",B=>{const y=fe.get(),H=B.currentTarget;if(!(!p(H)||xe(H)||y.length>0&&B.key===ae.SPACE)&&Er.ltr.includes(B.key)){if(!h.get()){H.click(),B.preventDefault();return}const U=H.getAttribute("aria-controls");if(!U)return;const Be=document.getElementById(U);if(!p(Be))return;const We=Ae(Be)[0];Y(We)}}),W(I,"pointermove",B=>{if(!Xe(B)||(ot(B),B.defaultPrevented))return;const y=B.currentTarget;if(!p(y))return;Ir(D.menu.get())||Y(y);const H=P.get();!h.get()&&!H&&!xe(y)&&P.set(window.setTimeout(()=>{h.update(()=>(A.set(y),!0)),ut(P)},100))}),W(I,"pointerleave",B=>{if(!Xe(B))return;ut(P);const y=document.getElementById(D.menu.get()),H=y?.getBoundingClientRect();if(H){const j=y?.dataset.side,U=j==="right",Be=U?-5:5,We=H[U?"left":"right"],et=H[U?"right":"left"];J.set({area:[{x:B.clientX+Be,y:B.clientY},{x:We,y:H.top},{x:et,y:H.top},{x:et,y:H.bottom},{x:We,y:H.bottom}],side:j}),window.clearTimeout(M.get()),M.set(window.setTimeout(()=>{J.set(null)},300))}else{if(st(B),B.defaultPrevented)return;J.set(null)}}),W(I,"focusout",B=>{const y=B.currentTarget;if(!p(y))return;He(y);const H=B.relatedTarget;if(!p(H))return;const j=y.getAttribute("aria-controls");if(!j)return;const U=document.getElementById(j);U&&!U.contains(H)&&h.set(!1)}),W(I,"focusin",B=>{me(B)}));return{destroy(){V(),ne()}}}}),Ut=se(e("subarrow"),{stores:re,returned:I=>({"data-arrow":!0,style:Ve({position:"absolute",width:`var(--arrow-size, ${I}px)`,height:`var(--arrow-size, ${I}px)`})})});return he([o],([I])=>{I||(A.set(null),h.set(!1))}),he([J],([I])=>{!_e||I||window.clearTimeout(M.get())}),he([h],([I])=>{if(_e&&(I&&F.get()&&Ie(1).then(()=>{const V=document.getElementById(D.menu.get());if(!V)return;const ne=Ae(V);ne.length&&Y(ne[0])}),!I)){const V=$.get(),ne=document.getElementById(D.trigger.get());if(V&&Ie(1).then(()=>{const B=document.getElementById(D.menu.get());B&&B.contains(V)&&He(V)}),!ne||document.activeElement===ne)return;He(ne)}}),{ids:D,elements:{subTrigger:jt,subMenu:Lt,subArrow:Ut},states:{subOpen:h},options:w}};wt(()=>{const i=document.getElementById(ee.trigger.get());p(i)&&o.get()&&f.set(i);const m=[],c=()=>F.set(!1),h=()=>{F.set(!0),m.push(pe(Se(document,"pointerdown",c,{capture:!0,once:!0}),Se(document,"pointermove",c,{capture:!0,once:!0})))},w=R=>{if(R.key===ae.ESCAPE&&s.get()){o.set(!1);return}};return m.push(Se(document,"keydown",h,{capture:!0})),m.push(Se(document,"keydown",w)),()=>{m.forEach(R=>R())}}),he([o,$],([i,m])=>{!i&&m&&He(m)}),he([o],([i])=>{if(_e&&!i){const m=f.get();if(!m)return;const c=E.get();!i&&m&&hr({prop:c,defaultEl:m})}}),he([o,n],([i,m])=>{if(!_e)return;const c=[];return i&&m&&c.push(vr()),Ie(1).then(()=>{const h=document.getElementById(ee.menu.get());if(h&&i&&F.get()){if(_.get()){Y(h);return}const w=Ae(h);if(!w.length)return;Y(w[0])}}),()=>{c.forEach(h=>h())}}),he(o,i=>{if(!_e)return;const m=()=>F.set(!1),c=h=>{if(F.set(!0),h.key===ae.ESCAPE&&i&&s.get()){o.set(!1);return}};return pe(Se(document,"pointerdown",m,{capture:!0,once:!0}),Se(document,"pointermove",m,{capture:!0,once:!0}),Se(document,"keydown",c,{capture:!0}))});function Je(i){o.update(m=>{const c=!m;return c&&(x.set(yr(i)),L.set(wr(i)),f.set(i)),c})}function me(i){const m=i.currentTarget;if(!p(m))return;const c=$.get();c&&He(c),pr(m),$.set(m)}function Oe(i){const m=i.currentTarget;p(m)&&He(m)}function ot(i){dt(i)&&i.preventDefault()}function $e(i){if(dt(i))return;const m=i.target;if(!p(m))return;const c=xt(m);c&&Y(c)}function st(i){dt(i)&&i.preventDefault()}function pt(i){if(!Xe(i))return;const m=i.target,c=i.currentTarget;if(!p(c)||!p(m))return;const h=ie.get(),w=h!==i.clientX;if(c.contains(m)&&w){const R=i.clientX>h?"right":"left";Q.set(R),ie.set(i.clientX)}}function Ue(i,m=null){if(!Xe(i)||(ot(i),i.defaultPrevented))return;if(m){Y(m);return}const c=i.currentTarget;p(c)&&Y(c)}function we(i){Xe(i)&&$e(i)}function De(i){if(fe.get().length>0&&i.key===ae.SPACE){i.preventDefault();return}if(ft.includes(i.key)){i.preventDefault();const h=i.currentTarget;if(!p(h))return;h.click()}}function Qe(i){return i==="indeterminate"}function oe(i){return Qe(i)?"indeterminate":i?"checked":"unchecked"}function dt(i){return ce.get()(i)}function xt(i){const m=i.closest('[role="menu"]');return p(m)?m:null}return{elements:{trigger:Ce,menu:be,overlay:Ee,item:Te,group:ge,groupLabel:Fe,arrow:Me,separator:ze},builders:{createCheckboxItem:Ne,createSubmenu:je,createMenuRadioGroup:Re},states:{open:o},helpers:{handleTypeaheadSearch:le},ids:ee,options:r.rootOptions}}function At(r,e,t){if(r.shiftKey){const n=t.get();n&&(r.preventDefault(),Ie(1).then(()=>n.focus()),t.set(null))}else{const n=e.get();n&&(r.preventDefault(),Ie(1).then(()=>n.focus()),e.set(null))}}function Ae(r){return Array.from(r.querySelectorAll(`[data-melt-menu-id="${r.id}"]`)).filter(e=>p(e))}function rt(r){!r||!xe(r)||(r.setAttribute("data-disabled",""),r.setAttribute("aria-disabled","true"))}function ut(r){if(!_e)return;const e=r.get();e&&(window.clearTimeout(e),r.set(null))}function Xe(r){return r.pointerType==="mouse"}function Ye(r,e){if(!r)return;const t=r.closest(`${e()}, ${e("submenu")}`);p(t)&&r.setAttribute("data-melt-menu-id",t.id)}function _t(r,e){r.preventDefault();const t=document.activeElement,n=r.currentTarget;if(!p(t)||!p(n))return;const a=Ae(n);if(!a.length)return;const d=a.filter(l=>!(l.hasAttribute("data-disabled")||l.getAttribute("disabled")==="true")),s=d.indexOf(t);let u;switch(r.key){case ae.ARROW_DOWN:e?u=s<d.length-1?s+1:0:u=s<d.length-1?s+1:s;break;case ae.ARROW_UP:e?u=s>0?s-1:d.length-1:u=s<0?d.length-1:s>0?s-1:0;break;case ae.HOME:u=0;break;case ae.END:u=d.length-1;break;default:return}Y(d[u])}function Ar(r,e){if(!e)return!1;const t={x:r.clientX,y:r.clientY};return _r(t,e)}function _r(r,e){const{x:t,y:n}=r;let a=!1;for(let d=0,s=e.length-1;d<e.length;s=d++){const u=e[d].x,l=e[d].y,v=e[s].x,k=e[s].y;l>n!=k>n&&t<(v-u)*(n-l)/(k-l)+u&&(a=!a)}return a}function Ir(r){const e=document.activeElement;if(!p(e))return!1;const t=e.closest(`[data-id="${r}"]`);return p(t)}function Pr(r){return r?"open":"closed"}const Dr={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function Br(r){const e={...Dr,...r},t=nt(Bt(e,"ids")),n=e.open??q(e.defaultOpen),a=at(n,e?.onOpenChange),d=Z(q(null)),s=Z(q(null)),u=Z(q(null)),{elements:l,builders:v,ids:k,states:C,options:E}=Sr({rootOptions:t,rootOpen:a,rootActiveTrigger:Z(d),nextFocusable:Z(s),prevFocusable:Z(u),selector:"dropdown-menu",ids:e.ids});return{ids:k,elements:l,states:C,builders:v,options:E}}function Wr(){return gr(10)}function gt(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function Ze(){const{NAME:r}=gt();return Qt(r)}function Hr(r){const{NAME:e,PARTS:t}=gt(),n=lr("menu",t),a={...Br({...dr(r),forceVisible:!0}),getAttrs:n};return Pt(e,a),{...a,updateOption:ur(a.options)}}function Fr(){const{GROUP_NAME:r}=gt(),{elements:{group:e},getAttrs:t}=Ze(),n=Wr();return Pt(r,n),{group:e,id:n,getAttrs:t}}function Kr(r){const t={...{side:"bottom",align:"center"},...r},{options:{positioning:n}}=Ze();cr(n)(t)}const Nr=N((r,e,t,n)=>{let a,d,s=Pe(e,["href","asChild","disabled","el"]),u,l,{href:v=void 0}=e,{asChild:k=!1}=e,{disabled:C=!1}=e,{el:E=void 0}=e;const{elements:{item:_},getAttrs:T}=Ze();return l=G(_,O=>u=O),bt(),e.href===void 0&&t.href&&v!==void 0&&t.href(v),e.asChild===void 0&&t.asChild&&k!==void 0&&t.asChild(k),e.disabled===void 0&&t.disabled&&C!==void 0&&t.disabled(C),e.el===void 0&&t.el&&E!==void 0&&t.el(E),a=u,d={...T("item"),...fr(C)},Object.assign(a,d),l(),`${k?`${n.default?n.default({builder:a}):""}`:`${(O=>O?`<${v?"a":"div"}${ye([{href:Dt(v)},X(a),X(s)],{})}${S("this",E,0)}>${Ot(O)?"":`${n.default?n.default({builder:a}):""}`}${Ot(O)?"":`</${O}>`}`:"")(v?"a":"div")}`}`}),Rr=N((r,e,t,n)=>{let a,d=Pe(e,["asChild","el"]),s,u,{asChild:l=!1}=e,{el:v=void 0}=e;const{group:k,id:C,getAttrs:E}=Fr();u=G(k,T=>s=T);const _=E("group");return e.asChild===void 0&&t.asChild&&l!==void 0&&t.asChild(l),e.el===void 0&&t.el&&v!==void 0&&t.el(v),a=s(C),Object.assign(a,_),u(),`${l?`${n.default?n.default({builder:a}):""}`:`<div${ye([X(a),X(d)],{})}${S("this",v,0)}>${n.default?n.default({builder:a}):""}</div>`}`}),zr=N((r,e,t,n)=>{let a,d,{closeOnOutsideClick:s=void 0}=e,{closeOnEscape:u=void 0}=e,{portal:l=void 0}=e,{open:v=void 0}=e,{onOpenChange:k=void 0}=e,{preventScroll:C=void 0}=e,{loop:E=void 0}=e,{dir:_=void 0}=e,{typeahead:T=void 0}=e,{closeFocus:O=void 0}=e,{disableFocusFirstItem:o=void 0}=e,{closeOnItemClick:f=void 0}=e,{onOutsideClick:x=void 0}=e;const{states:{open:L},updateOption:F,ids:ie}=Hr({closeOnOutsideClick:s,closeOnEscape:u,portal:l,forceVisible:!0,defaultOpen:v,preventScroll:C,loop:E,dir:_,typeahead:T,closeFocus:O,disableFocusFirstItem:o,closeOnItemClick:f,onOutsideClick:x,onOpenChange:({next:Q})=>(v!==Q&&(k?.(Q),v=Q),Q)}),J=qe([ie.menu,ie.trigger],([Q,$])=>({menu:Q,trigger:$}));return d=G(J,Q=>a=Q),e.closeOnOutsideClick===void 0&&t.closeOnOutsideClick&&s!==void 0&&t.closeOnOutsideClick(s),e.closeOnEscape===void 0&&t.closeOnEscape&&u!==void 0&&t.closeOnEscape(u),e.portal===void 0&&t.portal&&l!==void 0&&t.portal(l),e.open===void 0&&t.open&&v!==void 0&&t.open(v),e.onOpenChange===void 0&&t.onOpenChange&&k!==void 0&&t.onOpenChange(k),e.preventScroll===void 0&&t.preventScroll&&C!==void 0&&t.preventScroll(C),e.loop===void 0&&t.loop&&E!==void 0&&t.loop(E),e.dir===void 0&&t.dir&&_!==void 0&&t.dir(_),e.typeahead===void 0&&t.typeahead&&T!==void 0&&t.typeahead(T),e.closeFocus===void 0&&t.closeFocus&&O!==void 0&&t.closeFocus(O),e.disableFocusFirstItem===void 0&&t.disableFocusFirstItem&&o!==void 0&&t.disableFocusFirstItem(o),e.closeOnItemClick===void 0&&t.closeOnItemClick&&f!==void 0&&t.closeOnItemClick(f),e.onOutsideClick===void 0&&t.onOutsideClick&&x!==void 0&&t.onOutsideClick(x),v!==void 0&&L.set(v),F("closeOnOutsideClick",s),F("closeOnEscape",u),F("portal",l),F("preventScroll",C),F("loop",E),F("dir",_),F("closeFocus",O),F("disableFocusFirstItem",o),F("typeahead",T),F("closeOnItemClick",f),F("onOutsideClick",x),d(),`${n.default?n.default({ids:a}):""}`}),Lr=N((r,e,t,n)=>{let a,d=Pe(e,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]),s,u,l,v,{transition:k=void 0}=e,{transitionConfig:C=void 0}=e,{inTransition:E=void 0}=e,{inTransitionConfig:_=void 0}=e,{outTransition:T=void 0}=e,{outTransitionConfig:O=void 0}=e,{asChild:o=!1}=e,{id:f=void 0}=e,{side:x="bottom"}=e,{align:L="center"}=e,{sideOffset:F=0}=e,{alignOffset:ie=0}=e,{collisionPadding:J=8}=e,{avoidCollisions:Q=!0}=e,{collisionBoundary:$=void 0}=e,{sameWidth:ce=!1}=e,{fitViewport:fe=!1}=e,{strategy:le="absolute"}=e,{overlap:ee=!1}=e,{el:te=void 0}=e;const{elements:{menu:be},states:{open:Ce},ids:Me,getAttrs:Ee}=Ze();v=G(be,ge=>l=ge),u=G(Ce,ge=>s=ge),bt();const Te=Ee("content");return e.transition===void 0&&t.transition&&k!==void 0&&t.transition(k),e.transitionConfig===void 0&&t.transitionConfig&&C!==void 0&&t.transitionConfig(C),e.inTransition===void 0&&t.inTransition&&E!==void 0&&t.inTransition(E),e.inTransitionConfig===void 0&&t.inTransitionConfig&&_!==void 0&&t.inTransitionConfig(_),e.outTransition===void 0&&t.outTransition&&T!==void 0&&t.outTransition(T),e.outTransitionConfig===void 0&&t.outTransitionConfig&&O!==void 0&&t.outTransitionConfig(O),e.asChild===void 0&&t.asChild&&o!==void 0&&t.asChild(o),e.id===void 0&&t.id&&f!==void 0&&t.id(f),e.side===void 0&&t.side&&x!==void 0&&t.side(x),e.align===void 0&&t.align&&L!==void 0&&t.align(L),e.sideOffset===void 0&&t.sideOffset&&F!==void 0&&t.sideOffset(F),e.alignOffset===void 0&&t.alignOffset&&ie!==void 0&&t.alignOffset(ie),e.collisionPadding===void 0&&t.collisionPadding&&J!==void 0&&t.collisionPadding(J),e.avoidCollisions===void 0&&t.avoidCollisions&&Q!==void 0&&t.avoidCollisions(Q),e.collisionBoundary===void 0&&t.collisionBoundary&&$!==void 0&&t.collisionBoundary($),e.sameWidth===void 0&&t.sameWidth&&ce!==void 0&&t.sameWidth(ce),e.fitViewport===void 0&&t.fitViewport&&fe!==void 0&&t.fitViewport(fe),e.strategy===void 0&&t.strategy&&le!==void 0&&t.strategy(le),e.overlap===void 0&&t.overlap&&ee!==void 0&&t.overlap(ee),e.el===void 0&&t.el&&te!==void 0&&t.el(te),f&&Me.menu.set(f),a=l,Object.assign(a,Te),s&&Kr({side:x,align:L,sideOffset:F,alignOffset:ie,collisionPadding:J,avoidCollisions:Q,collisionBoundary:$,sameWidth:ce,fitViewport:fe,strategy:le,overlap:ee}),u(),v(),`${o&&s?`${n.default?n.default({builder:a}):""}`:`${k&&s?`<div${ye([X(a),X(d)],{})}${S("this",te,0)}>${n.default?n.default({builder:a}):""}</div>`:`${E&&T&&s?`<div${ye([X(a),X(d)],{})}${S("this",te,0)}>${n.default?n.default({builder:a}):""}</div>`:`${E&&s?`<div${ye([X(a),X(d)],{})}${S("this",te,0)}>${n.default?n.default({builder:a}):""}</div>`:`${T&&s?`<div${ye([X(a),X(d)],{})}${S("this",te,0)}>${n.default?n.default({builder:a}):""}</div>`:`${s?`<div${ye([X(a),X(d)],{})}${S("this",te,0)}>${n.default?n.default({builder:a}):""}</div>`:""}`}`}`}`}`}`}),jr=N((r,e,t,n)=>{let a,d=Pe(e,["asChild","id","el"]),s,u,{asChild:l=!1}=e,{id:v=void 0}=e,{el:k=void 0}=e;const{elements:{trigger:C},ids:E,getAttrs:_}=Ze();u=G(C,O=>s=O),bt();const T=_("trigger");return e.asChild===void 0&&t.asChild&&l!==void 0&&t.asChild(l),e.id===void 0&&t.id&&v!==void 0&&t.id(v),e.el===void 0&&t.el&&k!==void 0&&t.el(k),v&&E.trigger.set(v),a=s,Object.assign(a,T),u(),`${l?`${n.default?n.default({builder:a}):""}`:`<button${ye([X(a),{type:"button"},X(d)],{})}${S("this",k,0)}>${n.default?n.default({builder:a}):""}</button>`}`}),mt={error:"negative",success:"positive"},Ur=r=>Object.keys(mt).includes(r),Qr=r=>Ur(r)?(console.warn(`[Toast] The status "${r}" is deprecated. Please use "${mt[r]}" instead.`),mt[r]):r,Vr={negative:"border-negative/50 bg-negative/10 text-negative",positive:"border-positive/50 bg-positive/10 text-positive",info:"border-info/50 bg-info/10 text-info",warning:"border-warning/50 bg-warning/10 text-warning"},Gr=N((r,e,t,n)=>{let{id:a}=e,{status:d="info"}=e,{title:s}=e,{message:u}=e,{dismissable:l=!0}=e;return Vt(),e.id===void 0&&t.id&&a!==void 0&&t.id(a),e.status===void 0&&t.status&&d!==void 0&&t.status(d),e.title===void 0&&t.title&&s!==void 0&&t.title(s),e.message===void 0&&t.message&&u!==void 0&&t.message(u),e.dismissable===void 0&&t.dismissable&&l!==void 0&&t.dismissable(l),d=Qr(d),`<div role="none" class="${"print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+b(Vr[d],!0)}">${s?`<span class="cursor-pointer font-bold pr-8 flex items-center">${b(s)}</span>`:""} <span class="cursor-pointer">${b(u)}</span></div>`}),Xr=N((r,e,t,n)=>{let a,d;return d=G(Zt,s=>a=s),d(),`<div class="z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80">${ue(a,s=>`${g(Gr,"Toast").$$render(r,Object.assign({},s),{},{})}`)}</div>`}),Yr={default:{a:{role:"img",viewBox:"0 0 24 24",fill:"currentColor"},title:[{}],path:[{d:"M12 10.8c-1.087-2.114-4.046-6.053-6.798-7.995C2.566.944 1.561 1.266.902 1.565.139 1.908 0 3.08 0 3.768c0 .69.378 5.65.624 6.479.815 2.736 3.713 3.66 6.383 3.364.136-.02.275-.039.415-.056-.138.022-.276.04-.415.056-3.912.58-7.387 2.005-2.83 7.078 5.013 5.19 6.87-1.113 7.823-4.308.953 3.195 2.05 9.271 7.733 4.308 4.267-4.308 1.172-6.498-2.74-7.078a8.741 8.741 0 0 1-.415-.056c.14.017.279.036.415.056 2.67.297 5.568-.628 6.383-3.364.246-.828.624-5.79.624-6.478 0-.69-.139-1.861-.902-2.206-.659-.298-1.664-.62-4.3 1.24C16.046 4.748 13.087 8.687 12 10.8Z"}]}},qr={default:{a:{role:"img",viewBox:"0 0 24 24",fill:"currentColor"},title:[{}],path:[{d:"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"}]}},Zr={default:{a:{role:"img",viewBox:"0 0 24 24",fill:"currentColor"},title:[{}],path:[{d:"M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z"}]}},Jr={default:{a:{role:"img",viewBox:"0 0 24 24",fill:"currentColor"},title:[{}],path:[{d:"M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"}]}},$r="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png",en="/_app/immutable/assets/wordmark-black.rfl-FBgf.png",ht=N((r,e,t,n)=>{let{logo:a}=e,{lightLogo:d}=e,{darkLogo:s}=e,{title:u}=e;return e.logo===void 0&&t.logo&&a!==void 0&&t.logo(a),e.lightLogo===void 0&&t.lightLogo&&d!==void 0&&t.lightLogo(d),e.darkLogo===void 0&&t.darkLogo&&s!==void 0&&t.darkLogo(s),e.title===void 0&&t.title&&u!==void 0&&t.title(u),`${u?`${b(u)}`:`<img${S("src",a??d??en,0)} alt="Home" class="h-5 aspect-auto block dark:hidden"${S("href",z("/"),0)}> <img${S("src",a??s??$r,0)} alt="Home" class="h-5 aspect-auto hidden dark:block"${S("href",z("/"),0)}>`}`}),tn=N((r,e,t,n)=>{let{algolia:a}=e;const d=`
	**:[&.DocSearch-Button]:bg-base-100
	**:[&.DocSearch-Button]:border-base-300
	**:[&.DocSearch-Button]:hover:bg-base-200/40
	**:[&.DocSearch-Button]:transition-colors
	**:[&.DocSearch-Button]:duration-200
	**:[&.DocSearch-Button]:rounded-md
	**:[&.DocSearch-Button]:flex
	**:[&.DocSearch-Button]:gap-16
	**:[&.DocSearch-Button]:cursor-pointer
	**:[&.DocSearch-Button]:py-1
	**:[&.DocSearch-Button]:pl-2
	**:[&.DocSearch-Button]:sm:pr-1
	**:[&.DocSearch-Button]:pr-20
	**:[&.DocSearch-Button]:sm:text-xs
	**:[&.DocSearch-Button]:border
	**:[&.DocSearch-Button]:font-sans
	**:[&.DocSearch-Button]:font-medium
	**:[&.DocSearch-Button]:items-center;
	`,s=`
	**:[&DocSearch-Button-Placeholder]:text-base-content-muted
	`,u=`
	**:[&.DocSearch-Search-Icon]:hidden
	`,l=`
	**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:text-base-content-muted
	**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:*:text-base-content-muted
	`;return e.algolia===void 0&&t.algolia&&a!==void 0&&t.algolia(a),`<div id="docsearch" class="${b(d,!0)+" "+b(s,!0)+" "+b(u,!0)+" "+b(l,!0)}"></div>`}),ct=N((r,e,t,n)=>{let a=Pe(e,["class","inset"]),{class:d=void 0}=e,{inset:s=void 0}=e;return e.class===void 0&&t.class&&d!==void 0&&t.class(d),e.inset===void 0&&t.inset&&s!==void 0&&t.inset(s),`${g(Nr,"DropdownMenuPrimitive.Item").$$render(r,Object.assign({},{class:vt("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",s&&"pl-8",d)},a),{},{default:()=>`${n.default?n.default({}):""}`})}`}),rn=N((r,e,t,n)=>{let a=Pe(e,["class","sideOffset","transition","transitionConfig"]),{class:d=void 0}=e,{sideOffset:s=4}=e,{transition:u=er}=e,{transitionConfig:l=void 0}=e;return e.class===void 0&&t.class&&d!==void 0&&t.class(d),e.sideOffset===void 0&&t.sideOffset&&s!==void 0&&t.sideOffset(s),e.transition===void 0&&t.transition&&u!==void 0&&t.transition(u),e.transitionConfig===void 0&&t.transitionConfig&&l!==void 0&&t.transitionConfig(l),`${g(Lr,"DropdownMenuPrimitive.Content").$$render(r,Object.assign({},{transition:u},{transitionConfig:l},{sideOffset:s},{class:vt("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",d)},a),{},{default:()=>`${n.default?n.default({}):""}`})}`}),It=N((r,e,t,n)=>{let a=Pe(e,["class"]),{class:d=void 0}=e;return e.class===void 0&&t.class&&d!==void 0&&t.class(d),`<span${ye([{class:Dt(vt("ml-auto text-xs tracking-widest opacity-60",d))},X(a)],{})}>${n.default?n.default({}):""}</span>`}),nn=zr,an=jr,on=Rr,sn=N((r,e,t,n)=>{let a,d,s,u,l,v,k,C;C=G(Jt,f=>k=f),new Event("export-beforeprint"),new Event("export-afterprint");const{selectedAppearance:E,activeAppearance:_,cycleAppearance:T,themesConfig:O}=Wt();v=G(E,f=>l=f),u=G(_,f=>s=f);let{neverShowQueries:o}=e;return e.neverShowQueries===void 0&&t.neverShowQueries&&o!==void 0&&t.neverShowQueries(o),a=l==="system"?"System":l==="light"?"Light":"Dark",d=s==="light"?rr:nr,u(),v(),C(),`${g(nn,"DropdownMenu.Root").$$render(r,{},{},{default:()=>`${g(an,"DropdownMenu.Trigger").$$render(r,{asChild:!0},{},{default:({builder:f})=>`${g(br,"Button").$$render(r,{builders:[f],variant:"ghost",size:"sm",class:"px-1","aria-label":"Menu"},{},{default:()=>`${g(ve,"Icon").$$render(r,{src:tr,class:"h-6 w-6"},{},{})}`})}`})} ${g(rn,"DropdownMenu.Content").$$render(r,{class:"w-52 text-xs"},{},{default:()=>`${g(on,"DropdownMenu.Group").$$render(r,{},{},{default:()=>`${g(ct,"DropdownMenu.Item").$$render(r,{},{},{default:()=>`Print PDF
				${g(It,"DropdownMenu.Shortcut").$$render(r,{},{},{default:()=>"⌘P"})}`})} ${o?"":`${g(ct,"DropdownMenu.Item").$$render(r,{},{},{default:()=>`${b(k?"Hide ":"Show ")} Queries`})}`} ${O.appearance.switcher?`${g(ct,"DropdownMenu.Item").$$render(r,{},{},{default:()=>`Appearance
					${g(It,"DropdownMenu.Shortcut").$$render(r,{class:"tracking-normal flex flex-row items-center"},{},{default:()=>`<span class="text-xs leading-none">${b(a)}</span> ${g(ve,"Icon").$$render(r,{src:d,class:"h-4 w-4 ml-1"},{},{})}`})}`})}`:""}`})} `})}`})}`}),dn=N((r,e,t,n)=>{let{mobileSidebarOpen:a=void 0}=e,{title:d=void 0}=e,{logo:s=void 0}=e,{lightLogo:u=void 0}=e,{darkLogo:l=void 0}=e,{neverShowQueries:v=void 0}=e,{fullWidth:k=void 0}=e,{maxWidth:C=void 0}=e,{hideSidebar:E=void 0}=e,{sidebarFrontMatter:_=void 0}=e,{algolia:T=void 0}=e,{githubRepo:O=void 0}=e,{xProfile:o=void 0}=e,{blueskyProfile:f=void 0}=e,{slackCommunity:x=void 0}=e;return e.mobileSidebarOpen===void 0&&t.mobileSidebarOpen&&a!==void 0&&t.mobileSidebarOpen(a),e.title===void 0&&t.title&&d!==void 0&&t.title(d),e.logo===void 0&&t.logo&&s!==void 0&&t.logo(s),e.lightLogo===void 0&&t.lightLogo&&u!==void 0&&t.lightLogo(u),e.darkLogo===void 0&&t.darkLogo&&l!==void 0&&t.darkLogo(l),e.neverShowQueries===void 0&&t.neverShowQueries&&v!==void 0&&t.neverShowQueries(v),e.fullWidth===void 0&&t.fullWidth&&k!==void 0&&t.fullWidth(k),e.maxWidth===void 0&&t.maxWidth&&C!==void 0&&t.maxWidth(C),e.hideSidebar===void 0&&t.hideSidebar&&E!==void 0&&t.hideSidebar(E),e.sidebarFrontMatter===void 0&&t.sidebarFrontMatter&&_!==void 0&&t.sidebarFrontMatter(_),e.algolia===void 0&&t.algolia&&T!==void 0&&t.algolia(T),e.githubRepo===void 0&&t.githubRepo&&O!==void 0&&t.githubRepo(O),e.xProfile===void 0&&t.xProfile&&o!==void 0&&t.xProfile(o),e.blueskyProfile===void 0&&t.blueskyProfile&&f!==void 0&&t.blueskyProfile(f),e.slackCommunity===void 0&&t.slackCommunity&&x!==void 0&&t.slackCommunity(x),`<header class="fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden"><div${S("class",(k?"max-w-full ":C?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between",0)} style="${"max-width:"+b(C,!0)+"px;"}">${E||_==="never"?`<a${S("href",z("/"),0)} class="block text-sm font-bold text-base-content">${g(ht,"Logo").$$render(r,{logo:s,lightLogo:u,darkLogo:l,title:d},{},{})}</a>`:`<div class="flex gap-x-4 items-center"><button type="button" class="${"text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+b(_==="hide"?"block":"md:hidden",!0)}">${a?`<span class="sr-only" data-svelte-h="svelte-13q18xv">Close sidebar</span> ${g(ve,"Icon").$$render(r,{class:"w-5 h-5",src:Kt},{},{})}`:`<span class="sr-only" data-svelte-h="svelte-73kebv">Open sidebar</span> ${g(ve,"Icon").$$render(r,{class:"w-5 h-5",src:ar},{},{})}`}</button> <a${S("href",z("/"),0)} class="text-sm font-bold text-base-content hidden md:block">${g(ht,"Logo").$$render(r,{logo:s,lightLogo:u,darkLogo:l,title:d},{},{})}</a></div>`} <div class="flex gap-2 text-sm items-center">${T?`${g(tn,"AlgoliaDocSearch").$$render(r,{algolia:T},{},{})}`:""} <div class="flex gap-2 items-center">${O?`<a${S("href",z(O),0)} class="hover:bg-base-200 rounded-lg p-2 transition-all duration-200" target="_blank" rel="noreferrer">${g(ve,"Icon").$$render(r,{src:qr,class:"w-4 h-4 text-base-content"},{},{})}</a>`:""} ${o?`<a${S("href",z(o),0)} class="hover:bg-base-200 rounded-lg p-2 transition-all duration-200" target="_blank" rel="noreferrer">${g(ve,"Icon").$$render(r,{src:Jr,class:"w-4 h-4 text-base-content"},{},{})}</a>`:""} ${f?`<a${S("href",f,0)} class="hover:bg-gray-50 rounded-lg p-2 transition-all duration-200" target="_blank" rel="noreferrer">${g(ve,"Icon").$$render(r,{src:Yr,fill:"currentColor",class:"w-4 h-4 text-base-content "},{},{})}</a>`:""} ${x?`<a${S("href",z(x),0)} class="hover:bg-base-200 rounded-lg p-2 transition-all duration-200" target="_blank" rel="noreferrer">${g(ve,"Icon").$$render(r,{src:Zr,class:"w-4 h-4 text-base-content "},{},{})}</a>`:""}</div> <div class="relative">${g(sn,"KebabMenu").$$render(r,{neverShowQueries:v},{},{})}</div></div></div></header>`}),ln=N((r,e,t,n)=>'<div role="status" class="animate-pulse" data-svelte-h="svelte-1u7962h"><span class="sr-only">Loading...</span> <div class="h-8 rounded-full bg-base-200 w-48 mb-8"></div> <div class="flex gap-3"><div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[65%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[100%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[75%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div></div>'),de=N((r,e,t,n)=>`<span class="rounded-sm px-0.5 py-[1px] bg-positive/10 border border-positive/20 text-positive text-base sm:text-xs">${n.default?n.default({}):""}</span>`);function Rt(r){Object.keys(r.children).forEach(function(e){const t=r.children[e];Rt(t),(!t.label&&!t.href||t.children.length===0&&t.frontMatter?.sidebar_link===!1)&&delete r.children[e]})}function zt(r){return r.children=Object.values(r.children).sort((e,t)=>!isNaN(e.frontMatter?.sidebar_position)&&!isNaN(t.frontMatter?.sidebar_position)?e.frontMatter.sidebar_position-t.frontMatter.sidebar_position||e.label.localeCompare(t.label):isNaN(e.frontMatter?.sidebar_position)?isNaN(t.frontMatter?.sidebar_position)?e.label.localeCompare(t.label):1:-1),r.children.forEach(zt),r}const un=N((r,e,t,n)=>{let a,d;d=G(it,o=>a=o);let{fileTree:s=void 0}=e,{title:u=void 0}=e,{logo:l=void 0}=e,{homePageName:v=void 0}=e,{builtWithEvidence:k=void 0}=e,{hideHeader:C=!1}=e,{sidebarFrontMatter:E=void 0}=e,{sidebarDepth:_=3}=e;s=structuredClone(s),Rt(s),s=zt(s);let T=s?.children,{mobileSidebarOpen:O=!1}=e;return e.fileTree===void 0&&t.fileTree&&s!==void 0&&t.fileTree(s),e.title===void 0&&t.title&&u!==void 0&&t.title(u),e.logo===void 0&&t.logo&&l!==void 0&&t.logo(l),e.homePageName===void 0&&t.homePageName&&v!==void 0&&t.homePageName(v),e.builtWithEvidence===void 0&&t.builtWithEvidence&&k!==void 0&&t.builtWithEvidence(k),e.hideHeader===void 0&&t.hideHeader&&C!==void 0&&t.hideHeader(C),e.sidebarFrontMatter===void 0&&t.sidebarFrontMatter&&E!==void 0&&t.sidebarFrontMatter(E),e.sidebarDepth===void 0&&t.sidebarDepth&&_!==void 0&&t.sidebarDepth(_),e.mobileSidebarOpen===void 0&&t.mobileSidebarOpen&&O!==void 0&&t.mobileSidebarOpen(O),d(),`${O?`<div class="fixed inset-0 bg-base-100/80 z-50 backdrop-blur-sm" role="button" tabindex="-1"></div> <div class="bg-base-100 border-r border-base-200 shadow-lg fixed inset-0 z-50 flex sm:w-72 h-screen w-screen flex-col overflow-hidden select-none"><div class="flex flex-col h-full pb-4"><div class="py-3 px-8 mb-3 flex items-start justify-between"><a${S("href",z("/"),0)} class="block mt-1 text-sm font-bold">${g(ht,"Logo").$$render(r,{logo:l,title:u},{},{})}</a> <span role="button" tabindex="-1"><button type="button" class="hover:bg-base-200 rounded-lg p-1 transition-all duration-500"><span class="sr-only" data-svelte-h="svelte-13q18xv">Close sidebar</span> ${g(ve,"Icon").$$render(r,{src:Kt,class:"w-5 h-5"},{},{})}</button></span></div> <div class="flex-1 px-8 sm:pb-0 pb-4 overflow-auto text-base sm:text-sm pretty-scrollbar" id="mobileScrollable"><div class="flex flex-col pb-6"><a class="sticky top-0 bg-base-100 shadow shadow-base-100 text-base-heading font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100"${S("href",z("/"),0)}>${b(v)}</a> ${ue(T,o=>`${o.children.length===0&&o.href&&(o.frontMatter?.sidebar_link!==!1||o.frontMatter?.sidebar_link===void 0)?(()=>{let f=a.url.pathname.toUpperCase()===o.href.toUpperCase()+"/";return` <a class="${"group inline-block py-1 capitalize transition-colors duration-100 "+b(f?"text-primary":"text-base-content-muted hover:text-base-content",!0)}"${S("href",z(o.href),0)}>${b(o.frontMatter?.title??o.label)} ${o.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(o.frontMatter.sidebar_badge)} `})}`:""} </a>`})():""}`)}</div> ${ue(T,o=>`${o.children.length>0?`<div class="flex flex-col pb-6">${o.href&&(o.frontMatter?.sidebar_link!==!1||o.frontMatter?.sidebar_link===void 0)?`<a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"${S("href",z(o.href),0)}>${b(o.frontMatter?.title??o.label)} ${o.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(o.frontMatter.sidebar_badge)} `})}`:""} </a>`:`<span class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"${S("href",z(o.href),0)}>${b(o.frontMatter?.title??o.label)} ${o.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(o.frontMatter.sidebar_badge)} `})}`:""} </span>`} ${ue(o.children,f=>`${f.href&&(f.frontMatter?.sidebar_link!==!1||f.frontMatter?.sidebar_link===void 0)?(()=>{let x=a.url.pathname.toUpperCase()===f.href.toUpperCase()+"/";return` <a class="${"group inline-block py-1 capitalize transition-colors duration-100 "+b(x?"text-primary":"text-base-content-muted hover:text-base-content",!0)}"${S("href",z(f.href),0)}>${b(f.frontMatter?.title??f.label)} ${f.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(f.frontMatter.sidebar_badge)} `})}`:""} </a>`})():`<span class="group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted">${b(f.frontMatter?.title??f.label)} ${f.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(f.frontMatter.sidebar_badge)} `})}`:""} </span>`} ${f.children.length>0&&_>2?`${ue(f.children,x=>`${x.href&&(x.frontMatter?.sidebar_link!==!1||x.frontMatter?.sidebar_link===void 0)?(()=>{let L=a.url.pathname.toUpperCase()===x.href.toUpperCase()+"/";return` <a${S("href",z(x.href),0)} class="${"group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+b(L?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content",!0)}">${b(x.frontMatter?.title??x.label)} ${x.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(x.frontMatter.sidebar_badge)} `})}`:""} </a>`})():""}`)}`:""}`)} </div>`:""}`)}</div></div></div>`:""}  <aside class="${"w-48 flex-none "+b(E==="hide"?"hidden":"hidden md:flex",!0)}">${O?"":`<div class="${["hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar",C?"top-8":""].join(" ").trim()}"><div class="flex flex-col pb-6"><a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading"${S("href",z("/"),0)}>${b(v)}</a> ${ue(T,o=>`${o.children.length===0&&o.href&&(o.frontMatter?.sidebar_link!==!1||o.frontMatter?.sidebar_link===void 0)?(()=>{let f=a.url.pathname.toUpperCase()===o.href.toUpperCase()+"/";return` <a class="${"group inline-block py-1 capitalize transition-all duration-100 "+b(f?"text-primary":"text-base-content-muted hover:text-base-content",!0)}"${S("href",z(o.href),0)}>${b(o.frontMatter?.title??o.label)} ${o.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(o.frontMatter.sidebar_badge)} `})}`:""} </a>`})():""}`)}</div> ${ue(T,o=>`${o.children.length>0?`<div class="flex flex-col pb-6">${o.href&&(o.frontMatter?.sidebar_link!==!1||o.frontMatter?.sidebar_link===void 0)?`<a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group block capitalize hover:underline text-base-heading z-10"${S("href",z(o.href),0)}>${b(o.frontMatter?.title??o.label)} ${o.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(o.frontMatter.sidebar_badge)} `})}`:""} </a>`:`<span class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize text-base-heading z-10"${S("href",z(o.href),0)}>${b(o.frontMatter?.title??o.label)} ${o.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(o.frontMatter.sidebar_badge)} `})}`:""} </span>`} ${ue(o.children,f=>`${f.href&&(f.frontMatter?.sidebar_link!==!1||f.frontMatter?.sidebar_link===void 0)?(()=>{let x=a.url.pathname.toUpperCase()===f.href.toUpperCase()+"/";return` <a${S("href",z(f.href),0)} class="${"group inline-block py-1 capitalize transition-all duration-100 "+b(x?"text-primary":"text-base-content-muted hover:text-base-content",!0)}">${b(f.frontMatter?.title??f.label)} ${f.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(f.frontMatter.sidebar_badge)} `})}`:""} </a>`})():`<span class="group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted">${b(f.frontMatter?.title??f.label)} ${f.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(f.frontMatter.sidebar_badge)} `})}`:""} </span>`} ${f.children.length>0&&_>2?`${ue(f.children,x=>`${x.href&&(x.frontMatter?.sidebar_link!==!1||x.frontMatter?.sidebar_link===void 0)?(()=>{let L=a.url.pathname.toUpperCase()===x.href.toUpperCase()+"/";return` <div class="relative py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 border-l ml-[1px] transition-all duration-200 hover:border-base-content"><a${S("href",z(x.href),0)} class="${"group inline-block w-full capitalize transition-all duration-200 "+b(L?"text-primary":"text-base-content-muted hover:text-base-content",!0)}">${b(x.frontMatter?.title??x.label)} ${x.frontMatter?.sidebar_badge?`${g(de,"Badge").$$render(r,{},{},{default:()=>`${b(x.frontMatter.sidebar_badge)} `})}`:""}</a> ${L?'<div class="absolute top-0 -left-[1px] w-[1px] h-full bg-primary"></div>':""} </div>`})():""}`)}`:""}`)} </div>`:""}`)}</div>`} ${k?'<div class="fixed bottom-0 text-xs py-2" data-svelte-h="svelte-fworv4"><a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a></div>':""}</aside>`}),cn=N((r,e,t,n)=>{let a=[];Gt(()=>{});const d={h1:"mt-3 font-semibold block bg-base-100 shadow shadow-base-100",h2:"pl-0 text-base-content-muted",h3:"pl-4 text-base-content-muted"};return`${a&&a.length>1?`<span class="block text-xs sticky top-0 mb-2 bg-base-100 shadow-base-100 font-medium" data-svelte-h="svelte-14mun4z">On this page</span> ${ue(a,s=>`<a href="${"#"+b(s.id,!0)}" class="${b(d[s.nodeName.toLowerCase()],!0)+" block text-xs transition-all duration-200 py-1 hover:underline"}">${b(s.innerText)} </a>`)}`:""}`}),fn=N((r,e,t,n)=>{let a,d,s,u;d=G(Ht,v=>a=v),u=G(it,v=>s=v);let{hideHeader:l=!1}=e;return e.hideHeader===void 0&&t.hideHeader&&l!==void 0&&t.hideHeader(l),d(),u(),`<aside class="hidden lg:block w-48">${!a&&s.data.isUserPage?`<div class="${["fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar",l?"top-8":""].join(" ").trim()}">${g(cn,"ContentsList").$$render(r,{},{},{})}</div>`:""}</aside>`});function mn(r,e){if(r==="/")return e;const t=r.replace(Ft.deployment.basePath,"").split("/").slice(1);let n=e;for(let a of t)if(n.children[a]?n=n.children[a]:n=Object.values(n.children).find(d=>d.isTemplated),!n)return null;return n}function hn(r,e){const t=[{href:"/",title:"Home"}];r.forEach((n,a)=>{n!=""&&`/${n}`!==Ft.deployment.basePath&&t.push({href:"/"+r.slice(0,a+1).join("/"),title:decodeURIComponent(n.replace(/_/g," ").replace(/-/g," "))})}),t.length>3&&t.splice(1,t.length-3,{href:t.slice(-3)[0].href,title:"..."});for(const n of t)if(n.href==="/")n.href=z("/"),n.title="Home";else{const a=mn(n.href,e);!a||!a.isPage?n.href=null:n.title=a.title??n.title}return t}const vn=N((r,e,t,n)=>{let a,d,s;s=G(it,l=>d=l);let{fileTree:u}=e;return e.fileTree===void 0&&t.fileTree&&u!==void 0&&t.fileTree(u),a=hn(d.url.pathname.split("/").slice(1),u),s(),`<div class="flex items-start mt-0 whitespace-nowrap overflow-auto"><div class="inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4">${ue(a,(l,v)=>`${v>0?`${g(ve,"Icon").$$render(r,{src:ir,size:"12px",theme:"solid"},{},{})} ${l.href?`<a${S("href",z(l.href),0)} class="hover:underline">${b(l.title)}</a>`:`<span class="cursor-default">${b(l.title)}</span>`}`:`<a${S("href",z(l.href),0)} class="hover:underline">${b(l.title)} </a>`}`)}</div></div>`}),bn=N((r,e,t,n)=>""),gn=N((r,e,t,n)=>`${`${n.default?n.default({}):""}`}`),pn={code:"body{background-color:var(--color-base-100);color:var(--color-base-content)}",map:`{"version":3,"file":"EvidenceDefaultLayout.svelte","sources":["EvidenceDefaultLayout.svelte"],"sourcesContent":["<script>\\n\\timport { navigating, page } from '$app/stores';\\n\\timport { dev } from '$app/environment';\\n\\timport { LoadingSkeleton, QueryStatus } from './misc';\\n\\timport { Sidebar } from './sidebar';\\n\\timport { Header } from './header';\\n\\timport { ToastWrapper } from '../../molecules/toast';\\n\\timport BreadCrumbs from './BreadCrumbs.svelte';\\n\\timport TableOfContents from './tableofcontents/TableOfContents.svelte';\\n\\timport ErrorOverlay from './ErrorOverlay.svelte';\\n\\timport { browser } from '$app/environment';\\n\\timport DevTools from '../../devtools/DevTools.svelte';\\n\\timport { onMount } from 'svelte';\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\timport { addBasePath } from '@evidence-dev/sdk/utils/svelte';\\n\\n\\t// Remove splash screen from app.html\\n\\tif (browser) {\\n\\t\\tconst splash = document.getElementById('__evidence_project_splash');\\n\\t\\tsplash?.remove();\\n\\t}\\n\\n\\texport let data;\\n\\n\\t// Layout options\\n\\t/** @type {string} */\\n\\texport let title = undefined;\\n\\t/** @type {string | undefined} */\\n\\texport let logo = undefined;\\n\\t/** @type {string | undefined} */\\n\\texport let lightLogo = undefined;\\n\\t/** @type {string | undefined} */\\n\\texport let darkLogo = undefined;\\n\\t/** @type {boolean} */\\n\\texport let neverShowQueries = false;\\n\\t/** @type {boolean} */\\n\\texport let fullWidth = false;\\n\\t/** @type {boolean} */\\n\\texport let hideSidebar = false;\\n\\t/** @type {boolean} */\\n\\texport let builtWithEvidence = true;\\n\\t/** @type {{appId: string, apiKey: string, indexName: string}} */\\n\\texport let algolia = undefined;\\n\\t/** @type {string} */\\n\\texport let githubRepo = undefined;\\n\\t/** @type {string} */\\n\\texport let xProfile = undefined;\\n\\t/** @type {string} */\\n\\texport let blueskyProfile = undefined;\\n\\t/** @type {string} */\\n\\texport let slackCommunity = undefined;\\n\\t/** @type {string}*/\\n\\texport let maxWidth = undefined;\\n\\t/** @type {string}*/\\n\\texport let homePageName = 'Home';\\n\\t/** @type {boolean} */\\n\\texport let hideBreadcrumbs = false;\\n\\t/** @type {boolean} */\\n\\texport let hideHeader = false;\\n\\t/** @type {boolean} */\\n\\texport let hideTOC = false;\\n\\t/** @type {number} */\\n\\texport let sidebarDepth = 3;\\n\\n\\tconst prefetchStrategy = dev ? 'tap' : 'hover';\\n\\n\\tlet mobileSidebarOpen = false;\\n\\n\\t$: if ($navigating) {\\n\\t\\tmobileSidebarOpen = false;\\n\\t}\\n\\n\\tlet fileTree = data?.pagesManifest;\\n\\n\\tfunction convertFileTreeToFileMap(fileTree) {\\n\\t\\tconst map = new Map();\\n\\n\\t\\tfunction traverse(node, currentPath = '') {\\n\\t\\t\\t// Build the full path for the current node\\n\\t\\t\\tconst fullPath = node.href || currentPath;\\n\\n\\t\\t\\t// Add the current node to the map if it's a page\\n\\t\\t\\tif (node.isPage) {\\n\\t\\t\\t\\tmap.set(decodeURI(fullPath), node);\\n\\t\\t\\t}\\n\\n\\t\\t\\t// Traverse children\\n\\t\\t\\tif (node.children) {\\n\\t\\t\\t\\tObject.entries(node.children).forEach(([key, child]) => {\\n\\t\\t\\t\\t\\tconst childPath = \`\${fullPath}/\${key}\`;\\n\\t\\t\\t\\t\\ttraverse(child, childPath);\\n\\t\\t\\t\\t});\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\ttraverse(fileTree);\\n\\t\\treturn map;\\n\\t}\\n\\n\\t$: fileMap = convertFileTreeToFileMap(fileTree);\\n\\n\\t$: routeFrontMatter = fileMap.get($page.route.id)?.frontMatter;\\n\\n\\t$: sidebarFrontMatter = routeFrontMatter?.sidebar;\\n\\n\\t$: if (!['show', 'hide', 'never'].includes(sidebarFrontMatter)) {\\n\\t\\tsidebarFrontMatter = undefined;\\n\\t}\\n\\n\\t$: hideBreadcrumbsFrontmatter = routeFrontMatter?.hide_breadcrumbs;\\n\\t$: hideBreadcrumbsEffective = hideBreadcrumbsFrontmatter ?? hideBreadcrumbs;\\n\\n\\t$: fullWidthFrontmatter = routeFrontMatter?.full_width;\\n\\t$: fullWidthEffective = fullWidthFrontmatter ?? fullWidth;\\n\\n\\t$: maxWidthFrontmatter = routeFrontMatter?.max_width;\\n\\t$: maxWidthEffective = maxWidthFrontmatter ?? maxWidth;\\n\\n\\t$: hideHeaderFrontmatter = routeFrontMatter?.hide_header;\\n\\t$: hideHeaderEffective = hideHeaderFrontmatter ?? hideHeader;\\n\\n\\t$: hideTocFrontmatter = routeFrontMatter?.hide_toc;\\n\\t$: hideTocEffective = hideTocFrontmatter ?? hideTOC;\\n\\n\\tonMount(async () => {\\n\\t\\tif (!('serviceWorker' in navigator)) return;\\n\\n\\t\\tconst registration = await navigator.serviceWorker.register(\\n\\t\\t\\taddBasePath('/fix-tprotocol-service-worker.js'),\\n\\t\\t\\t{\\n\\t\\t\\t\\tscope: addBasePath('/'),\\n\\t\\t\\t\\ttype: dev ? 'module' : 'classic'\\n\\t\\t\\t}\\n\\t\\t);\\n\\t\\tconsole.debug('[fix-tprotocol-service-worker] Service Worker registered', { registration });\\n\\t});\\n\\n\\tconst {\\n\\t\\tsyncThemeAttribute,\\n\\t\\tcycleAppearance,\\n\\t\\tselectedAppearance,\\n\\t\\tsetAppearance,\\n\\t\\tactiveAppearance\\n\\t} = getThemeStores();\\n\\n\\tonMount(() => {\\n\\t\\t/** @param {KeyboardEvent} e */\\n\\t\\tconst onKeydown = (e) => {\\n\\t\\t\\tif (e.key.toLowerCase() === 'l' && e.shiftKey && (e.ctrlKey || e.metaKey)) {\\n\\t\\t\\t\\tcycleAppearance();\\n\\t\\t\\t}\\n\\t\\t};\\n\\t\\twindow.addEventListener('keydown', onKeydown);\\n\\t\\treturn () => window.removeEventListener('keydown', onKeydown);\\n\\t});\\n\\n\\tonMount(() => syncThemeAttribute(document.querySelector('html')));\\n\\n\\t//handles printing in dark mode\\n\\tonMount(() => {\\n\\t\\tlet currentTheme;\\n\\n\\t\\tconst beforePrintHandler = () => {\\n\\t\\t\\tcurrentTheme = $activeAppearance;\\n\\t\\t\\tif ($selectedAppearance === 'dark') {\\n\\t\\t\\t\\tsetAppearance('light');\\n\\t\\t\\t}\\n\\t\\t};\\n\\n\\t\\tconst afterPrintHandler = () => {\\n\\t\\t\\tif (currentTheme === 'dark') {\\n\\t\\t\\t\\tsetAppearance('dark');\\n\\t\\t\\t}\\n\\t\\t};\\n\\n\\t\\twindow.addEventListener('beforeprint', beforePrintHandler);\\n\\t\\twindow.addEventListener('afterprint', afterPrintHandler);\\n\\n\\t\\treturn () => {\\n\\t\\t\\twindow.removeEventListener('beforeprint', beforePrintHandler);\\n\\t\\t\\twindow.removeEventListener('afterprint', afterPrintHandler);\\n\\t\\t};\\n\\t});\\n<\/script>\\n\\n<slot />\\n\\n<ToastWrapper />\\n<DevTools>\\n\\t<div data-sveltekit-preload-data={prefetchStrategy} class=\\"antialiased\\">\\n\\t\\t<ErrorOverlay />\\n\\t\\t{#if !hideHeaderEffective}\\n\\t\\t\\t<Header\\n\\t\\t\\t\\tbind:mobileSidebarOpen\\n\\t\\t\\t\\t{title}\\n\\t\\t\\t\\t{logo}\\n\\t\\t\\t\\t{lightLogo}\\n\\t\\t\\t\\t{darkLogo}\\n\\t\\t\\t\\t{neverShowQueries}\\n\\t\\t\\t\\tfullWidth={fullWidthEffective}\\n\\t\\t\\t\\tmaxWidth={maxWidthEffective}\\n\\t\\t\\t\\t{hideSidebar}\\n\\t\\t\\t\\t{githubRepo}\\n\\t\\t\\t\\t{slackCommunity}\\n\\t\\t\\t\\t{xProfile}\\n\\t\\t\\t\\t{blueskyProfile}\\n\\t\\t\\t\\t{algolia}\\n\\t\\t\\t\\t{sidebarFrontMatter}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t\\t<div\\n\\t\\t\\tclass={(fullWidthEffective ? 'max-w-full ' : maxWidthEffective ? '' : ' max-w-7xl ') +\\n\\t\\t\\t\\t'print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start'}\\n\\t\\t\\tstyle=\\"max-width:{maxWidthEffective}px;\\"\\n\\t\\t>\\n\\t\\t\\t{#if !hideSidebar && sidebarFrontMatter !== 'never' && $page.route.id !== '/settings'}\\n\\t\\t\\t\\t<div class=\\"print:hidden\\">\\n\\t\\t\\t\\t\\t<Sidebar\\n\\t\\t\\t\\t\\t\\t{fileTree}\\n\\t\\t\\t\\t\\t\\tbind:mobileSidebarOpen\\n\\t\\t\\t\\t\\t\\t{title}\\n\\t\\t\\t\\t\\t\\t{logo}\\n\\t\\t\\t\\t\\t\\t{homePageName}\\n\\t\\t\\t\\t\\t\\t{builtWithEvidence}\\n\\t\\t\\t\\t\\t\\thideHeader={hideHeaderEffective}\\n\\t\\t\\t\\t\\t\\t{sidebarFrontMatter}\\n\\t\\t\\t\\t\\t\\t{sidebarDepth}\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t{/if}\\n\\t\\t\\t<main\\n\\t\\t\\t\\tclass={($page.route.id === '/settings'\\n\\t\\t\\t\\t\\t? 'w-full mt-16 sm:mt-20 '\\n\\t\\t\\t\\t\\t: (!hideSidebar && !['hide', 'never'].includes(sidebarFrontMatter) ? 'md:pl-8 ' : '') +\\n\\t\\t\\t\\t\\t\\t(!hideTocEffective ? 'md:pr-8 ' : '') +\\n\\t\\t\\t\\t\\t\\t(!hideHeaderEffective\\n\\t\\t\\t\\t\\t\\t\\t? !hideBreadcrumbsEffective\\n\\t\\t\\t\\t\\t\\t\\t\\t? ' mt-16 sm:mt-20 '\\n\\t\\t\\t\\t\\t\\t\\t\\t: ' mt-16 sm:mt-[74px] '\\n\\t\\t\\t\\t\\t\\t\\t: !hideBreadcrumbsEffective\\n\\t\\t\\t\\t\\t\\t\\t\\t? ' mt-4 sm:mt-8 '\\n\\t\\t\\t\\t\\t\\t\\t\\t: ' mt-4 sm:mt-[26px] ')) + 'flex-grow overflow-x-hidden print:px-0 print:mt-8'}\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#if !hideBreadcrumbsEffective && $page.route.id !== '/settings'}\\n\\t\\t\\t\\t\\t<div class=\\"print:hidden\\">\\n\\t\\t\\t\\t\\t\\t{#if $page.route.id !== '/settings'}\\n\\t\\t\\t\\t\\t\\t\\t<BreadCrumbs {fileTree} />\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{#if !$navigating}\\n\\t\\t\\t\\t\\t<article id=\\"evidence-main-article\\" class=\\"select-text markdown pb-10\\">\\n\\t\\t\\t\\t\\t\\t<slot name=\\"content\\" />\\n\\t\\t\\t\\t\\t</article>\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t<LoadingSkeleton />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</main>\\n\\t\\t\\t{#if !hideTocEffective && $page.route.id !== '/settings'}\\n\\t\\t\\t\\t<div class=\\"print:hidden\\">\\n\\t\\t\\t\\t\\t<TableOfContents hideHeader={hideHeaderEffective} />\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t</div>\\n\\t{#if !$navigating && dev && !$page.url.pathname.startsWith('/settings')}\\n\\t\\t<QueryStatus />\\n\\t{/if}\\n</DevTools>\\n\\n<style>\\n\\t:global(body) {\\n\\t\\tbackground-color: var(--color-base-100);\\n\\t\\tcolor: var(--color-base-content);\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA+QS,IAAM,CACb,gBAAgB,CAAE,IAAI,gBAAgB,CAAC,CACvC,KAAK,CAAE,IAAI,oBAAoB,CAChC"}`};function xn(r){const e=new Map;function t(n,a=""){const d=n.href||a;n.isPage&&e.set(decodeURI(d),n),n.children&&Object.entries(n.children).forEach(([s,u])=>{const l=`${d}/${s}`;t(u,l)})}return t(r),e}const yn=N((r,e,t,n)=>{let a,d,s,u,l,v,k,C,E,_,T,O,o,f,x,L,F,ie,J;F=G(it,oe=>L=oe),J=G(Ht,oe=>ie=oe);let{data:Q}=e,{title:$=void 0}=e,{logo:ce=void 0}=e,{lightLogo:fe=void 0}=e,{darkLogo:le=void 0}=e,{neverShowQueries:ee=!1}=e,{fullWidth:te=!1}=e,{hideSidebar:be=!1}=e,{builtWithEvidence:Ce=!0}=e,{algolia:Me=void 0}=e,{githubRepo:Ee=void 0}=e,{xProfile:Te=void 0}=e,{blueskyProfile:ge=void 0}=e,{slackCommunity:Fe=void 0}=e,{maxWidth:Ke=void 0}=e,{homePageName:Ne="Home"}=e,{hideBreadcrumbs:Re=!1}=e,{hideHeader:ze=!1}=e,{hideTOC:Le=!1}=e,{sidebarDepth:je=3}=e;const Je="hover";let me=!1,Oe=Q?.pagesManifest;const{syncThemeAttribute:ot,cycleAppearance:$e,selectedAppearance:st,setAppearance:pt,activeAppearance:Ue}=Wt();f=G(st,oe=>oe),x=G(Ue,oe=>oe),e.data===void 0&&t.data&&Q!==void 0&&t.data(Q),e.title===void 0&&t.title&&$!==void 0&&t.title($),e.logo===void 0&&t.logo&&ce!==void 0&&t.logo(ce),e.lightLogo===void 0&&t.lightLogo&&fe!==void 0&&t.lightLogo(fe),e.darkLogo===void 0&&t.darkLogo&&le!==void 0&&t.darkLogo(le),e.neverShowQueries===void 0&&t.neverShowQueries&&ee!==void 0&&t.neverShowQueries(ee),e.fullWidth===void 0&&t.fullWidth&&te!==void 0&&t.fullWidth(te),e.hideSidebar===void 0&&t.hideSidebar&&be!==void 0&&t.hideSidebar(be),e.builtWithEvidence===void 0&&t.builtWithEvidence&&Ce!==void 0&&t.builtWithEvidence(Ce),e.algolia===void 0&&t.algolia&&Me!==void 0&&t.algolia(Me),e.githubRepo===void 0&&t.githubRepo&&Ee!==void 0&&t.githubRepo(Ee),e.xProfile===void 0&&t.xProfile&&Te!==void 0&&t.xProfile(Te),e.blueskyProfile===void 0&&t.blueskyProfile&&ge!==void 0&&t.blueskyProfile(ge),e.slackCommunity===void 0&&t.slackCommunity&&Fe!==void 0&&t.slackCommunity(Fe),e.maxWidth===void 0&&t.maxWidth&&Ke!==void 0&&t.maxWidth(Ke),e.homePageName===void 0&&t.homePageName&&Ne!==void 0&&t.homePageName(Ne),e.hideBreadcrumbs===void 0&&t.hideBreadcrumbs&&Re!==void 0&&t.hideBreadcrumbs(Re),e.hideHeader===void 0&&t.hideHeader&&ze!==void 0&&t.hideHeader(ze),e.hideTOC===void 0&&t.hideTOC&&Le!==void 0&&t.hideTOC(Le),e.sidebarDepth===void 0&&t.sidebarDepth&&je!==void 0&&t.sidebarDepth(je),r.css.add(pn);let we,De,Qe=r.head;do we=!0,r.head=Qe,ie&&(me=!1),a=xn(Oe),d=a.get(L.route.id)?.frontMatter,s=d?.sidebar,["show","hide","never"].includes(s)||(s=void 0),u=d?.hide_breadcrumbs,l=u??Re,v=d?.full_width,k=v??te,C=d?.max_width,E=C??Ke,_=d?.hide_header,T=_??ze,O=d?.hide_toc,o=O??Le,De=`${n.default?n.default({}):""} ${g(Xr,"ToastWrapper").$$render(r,{},{},{})} ${g(gn,"DevTools").$$render(r,{},{},{default:()=>`<div${S("data-sveltekit-preload-data",Je,0)} class="antialiased">${g(bn,"ErrorOverlay").$$render(r,{},{},{})} ${T?"":`${g(dn,"Header").$$render(r,{title:$,logo:ce,lightLogo:fe,darkLogo:le,neverShowQueries:ee,fullWidth:k,maxWidth:E,hideSidebar:be,githubRepo:Ee,slackCommunity:Fe,xProfile:Te,blueskyProfile:ge,algolia:Me,sidebarFrontMatter:s,mobileSidebarOpen:me},{mobileSidebarOpen:oe=>{me=oe,we=!1}},{})}`} <div${S("class",(k?"max-w-full ":E?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start",0)} style="${"max-width:"+b(E,!0)+"px;"}">${!be&&s!=="never"&&L.route.id!=="/settings"?`<div class="print:hidden">${g(un,"Sidebar").$$render(r,{fileTree:Oe,title:$,logo:ce,homePageName:Ne,builtWithEvidence:Ce,hideHeader:T,sidebarFrontMatter:s,sidebarDepth:je,mobileSidebarOpen:me},{mobileSidebarOpen:oe=>{me=oe,we=!1}},{})}</div>`:""} <main${S("class",(L.route.id==="/settings"?"w-full mt-16 sm:mt-20 ":(!be&&!["hide","never"].includes(s)?"md:pl-8 ":"")+(o?"":"md:pr-8 ")+(T?l?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":l?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8",0)}>${!l&&L.route.id!=="/settings"?`<div class="print:hidden">${L.route.id!=="/settings"?`${g(vn,"BreadCrumbs").$$render(r,{fileTree:Oe},{},{})}`:""}</div>`:""} ${ie?`${g(ln,"LoadingSkeleton").$$render(r,{},{},{})}`:`<article id="evidence-main-article" class="select-text markdown pb-10">${n.content?n.content({}):""}</article>`}</main> ${!o&&L.route.id!=="/settings"?`<div class="print:hidden">${g(fn,"TableOfContents").$$render(r,{hideHeader:T},{},{})}</div>`:""}</div></div> `})}`;while(!we);return f(),x(),F(),J(),De}),Zn=N((r,e,t,n)=>{let{data:a}=e;return e.data===void 0&&t.data&&a!==void 0&&t.data(a),`${g(yn,"EvidenceDefaultLayout").$$render(r,{data:a},{},{content:()=>`${n.default?n.default({slot:"content"}):""}`})}`});export{Zn as default};
