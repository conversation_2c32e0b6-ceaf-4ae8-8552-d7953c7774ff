import{c as a}from"../../../chunks/ssr.js";import"dequal";import"../../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import"clsx";import"chroma-js";import"../../../chunks/Query.js";import"@uwdata/mosaic-sql";import"@evidence-dev/universal-sql/client-duckdb";import"ssf";import"@tidyjs/tidy";import"deep-object-diff";import"../../../chunks/index5.js";import"export-to-csv";import"echarts";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const C=a((s,o,r,l)=>{let{data:t}=o,{settings:i,customFormattingSettings:m,sources:p,plugins:e}=t;return o.data===void 0&&r.data&&t!==void 0&&r.data(t),{settings:i,customFormattingSettings:m,sources:p,plugins:e}=t,'<p data-svelte-h="svelte-591hpj">Settings are only available in development mode.</p>'});export{C as default};
