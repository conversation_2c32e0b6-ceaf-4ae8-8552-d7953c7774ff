import{b as h}from"../../chunks/environment.js";import{setParquetURLs as y,query as O,initDB as $,updateSearchPath as v}from"@evidence-dev/universal-sql/client-duckdb";import{p as o,s as E}from"../../chunks/profile.js";import"../../chunks/Query.js";import{a as i}from"../../chunks/index6.js";import b from"blueimp-md5";const R=!0,T=!0,A="always",F=async()=>{let e={};{const{readFile:n}=await import("fs/promises");({renderedFiles:e}=JSON.parse(await n("./static/data/manifest.json","utf-8").catch(()=>"{}")))}await o($),Object.keys(e??{}).length===0?console.warn('No sources found, execute "npm run sources" to generate'.trim()):(await o(y,e,{addBasePath:i}),await o(v,Object.keys(e)))},g=o(F),L=["/settings","/explore"],c=new Map,D=async({fetch:e,route:n,params:p,url:r})=>{const[{customFormattingSettings:j},l,w]=await Promise.all([e(i("/api/customFormattingSettings.json/GET.json")).then(t=>t.json()),e(i("/api/pagesManifest.json")).then(t=>t.json()),e(i(`/api/${n.id}/evidencemeta.json`)).then(t=>t.json()).catch(()=>({queries:[]}))]),_=b(n.id),M=b(Object.entries(p).sort().map(([t,s])=>`${t}${s}`).join("")),P=n.id&&L.every(t=>!n.id.startsWith(t));let S={};const{inputs:u=E({label:"",value:"(SELECT NULL WHERE 0 /* An Input has not been set */)"})}=c.get(r.pathname)??{},q=c.has(r.pathname);h&&!q&&(c.set(r.pathname,{inputs:u}),await e(r),c.delete(r.pathname)),await g;function f(t,{query_name:s,callback:d=m=>m}={}){return d(O(t,{route_hash:_,additional_hash:M,query_name:s,prerendering:h}))}let a=l;for(const t of(n.id??"").split("/").slice(1)){if(a=a.children[t],!a)break;if(a.frontMatter?.title)a.title=a.frontMatter.title;else if(a.frontMatter?.breadcrumb){let{breadcrumb:s}=a.frontMatter;for(const[d,m]of Object.entries(p))s=s.replaceAll(`\${params.${d}}`,m);a.title=(await f(s))[0]?.breadcrumb}}return{__db:{query:f,async load(){return g},async updateParquetURLs(t){const{renderedFiles:s}=JSON.parse(t);await o(y,s,{addBasePath:i})}},inputs:u,data:S,customFormattingSettings:j,isUserPage:P,evidencemeta:w,pagesManifest:l}};export{D as load,T as prerender,R as ssr,A as trailingSlash};
