import{d as u}from"../../../chunks/index4.js";import{f as t}from"../../../chunks/index.js";import{logQueryEvent as c}from"@evidence-dev/telemetry";import{DatasourceSpecFileSchema as f,loadSourcePlugins as l,Options as m,writeSourceConfig as p}from"@evidence-dev/sdk/plugins";import{l as g}from"../../../chunks/index5.js";const q=async()=>({}),v={updateSource:async n=>{const s=Object.fromEntries(await n.request.formData()),r=s.source?JSON.parse(s.source):null;if(!r)return t(400,{message:"Missing required field 'source'"});const e=f.safeParse(r);if(!e.success)return t(400,e.error.format());const i=await l(),[,d]=i.getBySource(e.data.type),o=m(d.options,e.data.options);delete r.environmentVariables,delete r.initialName;try{return{updatedSource:await p(o,r)}}catch(a){return g.debug(`error updating sources: ${a}`,{error:a}),t(500,a.message)}},testSource:async n=>{const s=Object.fromEntries(await n.request.formData());if(!s?.source)return t(400,{message:"Missing required field 'source'"});const r=s.source?JSON.parse(s.source):null;if(!r)return t(400,{message:"Missing required field 'source'"});const e=f.safeParse(r);if(!e.success)return t(400,e.error.format());const i=await l(),[d,o]=i.getBySource(e.data.type);if(!o)return c("db-plugin-unvailable",e.data.type,void 0,void 0,u),t(400,{message:`Plugin for datasource "${e.data.type}" not found.`});const a=await o.testConnection(e.data.options,r.dir);return a!==!0?(c("db-connection-error",e.data.type,e.data.name,void 0,u),t(200,{message:a.reason})):(c("db-connection-success",e.data.type,e.data.name,void 0,u),{success:!0})}};export{v as actions,q as load};
