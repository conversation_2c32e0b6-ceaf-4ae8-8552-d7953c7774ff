import{c as Ae,s as U,b as So,a as qa,n as De,d as ka}from"../../chunks/utils.js";import{c as P,a as ye,b as q,d as Q,s as Lt,g as Gt,o as yt,h as D,v as k,e as oa,i as Je,f as Wa}from"../../chunks/ssr.js";import"dequal";import{o as ja,w as Qa,f as Ha,m as kt,e as yo,t as aa,n as Qt,p as Ya,s as go,i as na,d as Va,r as Xa,b as ra,c as Ht,k as Yt,u as Za,a as Ja,v as $a,x as Rt,y as en,z as to,A as _a,V as Zt,B as tn,I as on,C as an,D as nn,E as rn,G as ln,H as sn,J as An,K as dn,L as cn,l as At,M as Ta,N as Ma,O as un,P as Cn}from"../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import{w as Jt,d as st}from"../../chunks/index2.js";import"clsx";import"chroma-js";import{r as fn,s as vn,l as la,Q as Dt,a as ia,p as mn}from"../../chunks/Query.js";import{h as _t,p as hn}from"../../chunks/profile.js";import{a as sa}from"../../chunks/index6.js";import{p as ko}from"../../chunks/stores.js";import{b as yn,f as je,g as Xt,a as Ue,D as Eo,C as gn,c as Aa,s as pn,d as Qe,e as Vt,E as Da,Q as bn,h as xn,i as En,j as wn,k as In,l as Bn}from"../../chunks/CodeBlock.js";import{t as j,c as ze,I as $t,f as On}from"../../chunks/index7.js";import{M as Sn,b as da,I as kn,a as ca,c as _n}from"../../chunks/Info.js";import{t as ua,o as Tn,g as Mn,d as Dn,u as zn,a as Ca,b as Ln,c as za,r as La,e as Ga,f as Gn,h as Un}from"../../chunks/helpers.js";import{r as Rn,h as fa,c as Nn,i as va,B as Pn}from"../../chunks/button.js";import"deep-object-diff";import"../../chunks/index5.js";import{c as Kn}from"../../chunks/checkRequiredProps.js";import"ssf";import{tidy as it,summarize as Fn,nDistinct as qn,groupBy as wo,mutateWithSummary as ma,sum as Io,mutate as po,rate as ha,rename as ya,complete as ga,summarizeAt as Wn}from"@tidyjs/tidy";import"@uwdata/mosaic-sql";import"export-to-csv";import{throttle as jn}from"echarts";import"@evidence-dev/universal-sql/client-duckdb";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import Qn from"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const Hn={positioning:{placement:"bottom"},arrowSize:8,defaultOpen:!1,disableFocusTrap:!1,closeOnEscape:!0,preventScroll:!1,onOpenChange:void 0,closeOnOutsideClick:!0,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},{name:Tt}=Va("popover"),Yn=["trigger","content"];function Vn(o){const e={...Hn,...o},t=ua(ja(e,"open","ids")),{positioning:n,arrowSize:a,disableFocusTrap:r,preventScroll:l,closeOnEscape:A,closeOnOutsideClick:v,portal:d,forceVisible:C,openFocus:y,closeFocus:f,onOutsideClick:s}=t,g=e.open??Jt(e.defaultOpen),c=Tn(g,e?.onOpenChange),u=Qa.writable(null),E=ua({...Mn(Yn),...e.ids});Ha(()=>{u.set(document.getElementById(E.trigger.get()))});function M(){c.set(!1);const i=document.getElementById(E.trigger.get());fa({prop:f.get(),defaultEl:i})}const w=Dn({open:c,activeTrigger:u,forceVisible:C}),S=kt(Tt("content"),{stores:[w,d,E.content],returned:([i,b,p])=>({hidden:i&&na?void 0:!0,tabindex:-1,style:go({display:i?void 0:"none"}),id:p,"data-state":i?"open":"closed","data-portal":Ya(b)}),action:i=>{let b=Qt;const p=yo([w,u,n,r,A,v,d],([x,h,N,K,X,R,de])=>{b(),!(!x||!h)&&aa().then(()=>{b(),b=zn(i,{anchorElement:h,open:c,options:{floating:N,focusTrap:K?null:{returnFocusOnDeactivate:!1,clickOutsideDeactivates:R,allowOutsideClick:!0,escapeDeactivates:X},modal:{shouldCloseOnInteractOutside:I,onClose:M,open:x,closeOnInteractOutside:R},escapeKeydown:X?{handler:()=>{M()}}:null,portal:Ca(i,de)}}).destroy})});return{destroy(){p(),b()}}}});function T(i){c.update(b=>!b),i&&i!==u.get()&&u.set(i)}function I(i){if(s.get()?.(i),i.defaultPrevented)return!1;const b=i.target,p=document.getElementById(E.trigger.get());return!(p&&Xa(b)&&(b===p||p.contains(b)))}const z=kt(Tt("trigger"),{stores:[w,E.content,E.trigger],returned:([i,b,p])=>({role:"button","aria-haspopup":"dialog","aria-expanded":i?"true":"false","data-state":pa(i),"aria-controls":b,id:p}),action:i=>({destroy:ra(Ht(i,"click",()=>{T(i)}),Ht(i,"keydown",p=>{p.key!==Yt.ENTER&&p.key!==Yt.SPACE||(p.preventDefault(),T(i))}))})}),O=kt(Tt("overlay"),{stores:[w],returned:([i])=>({hidden:i?void 0:!0,tabindex:-1,style:go({display:i?void 0:"none"}),"aria-hidden":"true","data-state":pa(i)}),action:i=>{let b=Qt,p=Qt,x=Qt;if(A.get()){const h=Za(i,{handler:()=>{M()}});h&&h.destroy&&(b=h.destroy)}return p=yo([d],([h])=>{if(x(),h===null)return;const N=Ca(i,h);N!==null&&(x=Ln(i,N).destroy)}),{destroy(){b(),p(),x()}}}}),B=kt(Tt("arrow"),{stores:a,returned:i=>({"data-arrow":!0,style:go({position:"absolute",width:`var(--arrow-size, ${i}px)`,height:`var(--arrow-size, ${i}px)`})})}),m=kt(Tt("close"),{returned:()=>({type:"button"}),action:i=>({destroy:ra(Ht(i,"click",p=>{p.defaultPrevented||M()}),Ht(i,"keydown",p=>{p.defaultPrevented||p.key!==Yt.ENTER&&p.key!==Yt.SPACE||(p.preventDefault(),T())}))})});return yo([c,u,l],([i,b,p])=>{if(!na)return;const x=[];if(i){b||aa().then(()=>{const N=document.getElementById(E.trigger.get());Ja(N)&&u.set(N)}),p&&x.push(Rn());const h=b??document.getElementById(E.trigger.get());fa({prop:y.get(),defaultEl:h})}return()=>{x.forEach(h=>h())}}),{ids:E,elements:{trigger:z,content:S,arrow:B,close:m,overlay:O},states:{open:c},options:t}}function pa(o){return o?"open":"closed"}function Xn(){return{NAME:"separator",PARTS:["root"]}}function Zn(o){const{NAME:e,PARTS:t}=Xn(),n=za(e,t),a={...Nn(La(o)),getAttrs:n};return{...a,updateOption:Ga(a.options)}}const Jn=P((o,e,t,n)=>{let a,r=Ae(e,["orientation","decorative","asChild","el"]),l,A,{orientation:v="horizontal"}=e,{decorative:d=!0}=e,{asChild:C=!1}=e,{el:y=void 0}=e;const{elements:{root:f},updateOption:s,getAttrs:g}=Zn({orientation:v,decorative:d});A=U(f,u=>l=u);const c=g("root");return e.orientation===void 0&&t.orientation&&v!==void 0&&t.orientation(v),e.decorative===void 0&&t.decorative&&d!==void 0&&t.decorative(d),e.asChild===void 0&&t.asChild&&C!==void 0&&t.asChild(C),e.el===void 0&&t.el&&y!==void 0&&t.el(y),s("orientation",v),s("decorative",d),a=l,Object.assign(a,c),A(),`${C?`${n.default?n.default({builder:a}):""}`:`<div${ye([Q(a),Q(r)],{})}${q("this",y,0)}></div>`}`});function Ua(){return{NAME:"popover",PARTS:["arrow","close","content","trigger"]}}function $n(o){const{NAME:e,PARTS:t}=Ua(),n=za(e,t),a={...Vn({positioning:{placement:"bottom",gutter:0},...La(o),forceVisible:!0}),getAttrs:n};return Lt(e,a),{...a,updateOption:Ga(a.options)}}function _o(){const{NAME:o}=Ua();return Gt(o)}function er(o){const t={...{side:"bottom",align:"center"},...o},{options:{positioning:n}}=_o();Gn(n)(t)}const tr=P((o,e,t,n)=>{let a,r,{disableFocusTrap:l=void 0}=e,{closeOnEscape:A=void 0}=e,{closeOnOutsideClick:v=void 0}=e,{preventScroll:d=void 0}=e,{portal:C=void 0}=e,{open:y=void 0}=e,{onOpenChange:f=void 0}=e,{openFocus:s=void 0}=e,{closeFocus:g=void 0}=e,{onOutsideClick:c=void 0}=e;const{updateOption:u,states:{open:E},ids:M}=$n({disableFocusTrap:l,closeOnEscape:A,closeOnOutsideClick:v,preventScroll:d,portal:C,defaultOpen:y,openFocus:s,closeFocus:g,onOutsideClick:c,onOpenChange:({next:S})=>(y!==S&&(f?.(S),y=S),S),positioning:{gutter:0,offset:{mainAxis:1}}}),w=st([M.content,M.trigger],([S,T])=>({content:S,trigger:T}));return r=U(w,S=>a=S),e.disableFocusTrap===void 0&&t.disableFocusTrap&&l!==void 0&&t.disableFocusTrap(l),e.closeOnEscape===void 0&&t.closeOnEscape&&A!==void 0&&t.closeOnEscape(A),e.closeOnOutsideClick===void 0&&t.closeOnOutsideClick&&v!==void 0&&t.closeOnOutsideClick(v),e.preventScroll===void 0&&t.preventScroll&&d!==void 0&&t.preventScroll(d),e.portal===void 0&&t.portal&&C!==void 0&&t.portal(C),e.open===void 0&&t.open&&y!==void 0&&t.open(y),e.onOpenChange===void 0&&t.onOpenChange&&f!==void 0&&t.onOpenChange(f),e.openFocus===void 0&&t.openFocus&&s!==void 0&&t.openFocus(s),e.closeFocus===void 0&&t.closeFocus&&g!==void 0&&t.closeFocus(g),e.onOutsideClick===void 0&&t.onOutsideClick&&c!==void 0&&t.onOutsideClick(c),y!==void 0&&E.set(y),u("disableFocusTrap",l),u("closeOnEscape",A),u("closeOnOutsideClick",v),u("preventScroll",d),u("portal",C),u("openFocus",s),u("closeFocus",g),u("onOutsideClick",c),r(),`${n.default?n.default({ids:a}):""}`}),or=P((o,e,t,n)=>{let a,r=Ae(e,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]),l,A,v,d,{transition:C=void 0}=e,{transitionConfig:y=void 0}=e,{inTransition:f=void 0}=e,{inTransitionConfig:s=void 0}=e,{outTransition:g=void 0}=e,{outTransitionConfig:c=void 0}=e,{asChild:u=!1}=e,{id:E=void 0}=e,{side:M="bottom"}=e,{align:w="center"}=e,{sideOffset:S=0}=e,{alignOffset:T=0}=e,{collisionPadding:I=8}=e,{avoidCollisions:z=!0}=e,{collisionBoundary:O=void 0}=e,{sameWidth:B=!1}=e,{fitViewport:m=!1}=e,{strategy:i="absolute"}=e,{overlap:b=!1}=e,{el:p=void 0}=e;const{elements:{content:x},states:{open:h},ids:N,getAttrs:K}=_o();d=U(x,R=>v=R),A=U(h,R=>l=R);const X=K("content");return e.transition===void 0&&t.transition&&C!==void 0&&t.transition(C),e.transitionConfig===void 0&&t.transitionConfig&&y!==void 0&&t.transitionConfig(y),e.inTransition===void 0&&t.inTransition&&f!==void 0&&t.inTransition(f),e.inTransitionConfig===void 0&&t.inTransitionConfig&&s!==void 0&&t.inTransitionConfig(s),e.outTransition===void 0&&t.outTransition&&g!==void 0&&t.outTransition(g),e.outTransitionConfig===void 0&&t.outTransitionConfig&&c!==void 0&&t.outTransitionConfig(c),e.asChild===void 0&&t.asChild&&u!==void 0&&t.asChild(u),e.id===void 0&&t.id&&E!==void 0&&t.id(E),e.side===void 0&&t.side&&M!==void 0&&t.side(M),e.align===void 0&&t.align&&w!==void 0&&t.align(w),e.sideOffset===void 0&&t.sideOffset&&S!==void 0&&t.sideOffset(S),e.alignOffset===void 0&&t.alignOffset&&T!==void 0&&t.alignOffset(T),e.collisionPadding===void 0&&t.collisionPadding&&I!==void 0&&t.collisionPadding(I),e.avoidCollisions===void 0&&t.avoidCollisions&&z!==void 0&&t.avoidCollisions(z),e.collisionBoundary===void 0&&t.collisionBoundary&&O!==void 0&&t.collisionBoundary(O),e.sameWidth===void 0&&t.sameWidth&&B!==void 0&&t.sameWidth(B),e.fitViewport===void 0&&t.fitViewport&&m!==void 0&&t.fitViewport(m),e.strategy===void 0&&t.strategy&&i!==void 0&&t.strategy(i),e.overlap===void 0&&t.overlap&&b!==void 0&&t.overlap(b),e.el===void 0&&t.el&&p!==void 0&&t.el(p),E&&N.content.set(E),a=v,Object.assign(a,X),l&&er({side:M,align:w,sideOffset:S,alignOffset:T,collisionPadding:I,avoidCollisions:z,collisionBoundary:O,sameWidth:B,fitViewport:m,strategy:i,overlap:b}),A(),d(),`${u&&l?`${n.default?n.default({builder:a}):""}`:`${C&&l?`<div${ye([Q(a),Q(r)],{})}${q("this",p,0)}>${n.default?n.default({builder:a}):""}</div>`:`${f&&g&&l?`<div${ye([Q(a),Q(r)],{})}${q("this",p,0)}>${n.default?n.default({builder:a}):""}</div>`:`${f&&l?`<div${ye([Q(a),Q(r)],{})}${q("this",p,0)}>${n.default?n.default({builder:a}):""}</div>`:`${g&&l?`<div${ye([Q(a),Q(r)],{})}${q("this",p,0)}>${n.default?n.default({builder:a}):""}</div>`:`${l?`<div${ye([Q(a),Q(r)],{})}${q("this",p,0)}>${n.default?n.default({builder:a}):""}</div>`:""}`}`}`}`}`}`}),ar=P((o,e,t,n)=>{let a,r,l=Ae(e,["asChild","id","el"]),A,v,d,C,{asChild:y=!1}=e,{id:f=void 0}=e,{el:s=void 0}=e;const{elements:{trigger:g},states:{open:c},ids:u,getAttrs:E}=_o();v=U(g,w=>A=w),C=U(c,w=>d=w),Un();const M=E("trigger");return e.asChild===void 0&&t.asChild&&y!==void 0&&t.asChild(y),e.id===void 0&&t.id&&f!==void 0&&t.id(f),e.el===void 0&&t.el&&s!==void 0&&t.el(s),f&&u.trigger.set(f),a={...M,"aria-controls":d?u.content:void 0},r=A,Object.assign(r,a),v(),C(),`${y?`${n.default?n.default({builder:r}):""}`:`<button${ye([Q(r),{type:"button"},Q(l)],{})}${q("this",s,0)}>${n.default?n.default({builder:r}):""}</button>`}`}),Bo=(o,e={serializeStrings:!0})=>o==null?"null":typeof o=="string"?e.serializeStrings!==!1?`'${o.replaceAll("'","''")}'`:o:typeof o=="number"||typeof o=="bigint"||typeof o=="boolean"?String(o):o instanceof Date?`'${o.toISOString()}'::TIMESTAMP_MS`:Array.isArray(o)?`[${o.map(t=>Bo(t,e)).join(", ")}]`:JSON.stringify(o),nr=P((o,e,t,n)=>{let{enabled:a=!0}=e;return e.enabled===void 0&&t.enabled&&a!==void 0&&t.enabled(a),a=j(a),`<div class="${["contents",a?"print:hidden":""].join(" ").trim()}">${n.default?n.default({}):""}</div>`}),Ra=Symbol("EVIDENCE_DROPDOWN_CTX");let rr=0;const ht=P((o,e,t,n)=>{let{value:a}=e,{valueLabel:r=a}=e,{idx:l=-1}=e,{__auto:A=!1}=e;A||(l=rr++);const v=Gt(Ra);return yt(v.registerOption({value:a,label:r,idx:l,__auto:A})),e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.valueLabel===void 0&&t.valueLabel&&r!==void 0&&t.valueLabel(r),e.idx===void 0&&t.idx&&l!==void 0&&t.idx(l),e.__auto===void 0&&t.__auto&&A!==void 0&&t.__auto(A),""});function lr(o){return Object.keys(o).reduce((e,t)=>o[t]===void 0?e:e+`${t}:${o[t]};`,"")}const ir={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function eo(o,e,t,n){const a=Array.isArray(e)?e:[e];return a.forEach(r=>o.addEventListener(r,t,n)),()=>{a.forEach(r=>o.removeEventListener(r,t,n))}}function Na(...o){return(...e)=>{for(const t of o)typeof t=="function"&&t(...e)}}const sr=P((o,e,t,n)=>{let a,r=Ae(e,["label","shouldFilter","filter","value","onValueChange","loop","onKeydown","state","ids","asChild"]),l,A,{label:v=void 0}=e,{shouldFilter:d=!0}=e,{filter:C=void 0}=e,{value:y=void 0}=e,{onValueChange:f=void 0}=e,{loop:s=void 0}=e,{onKeydown:g=void 0}=e,{state:c=void 0}=e,{ids:u=void 0}=e,{asChild:E=!1}=e;const{commandEl:M,handleRootKeydown:w,ids:S,state:T}=$a({label:v,shouldFilter:d,filter:C,value:y,onValueChange:b=>{b!==y&&(y=b,f?.(b))},loop:s,state:c,ids:u});A=U(T,b=>l=b);function I(b){b&&b!==l.value&&So(T,l.value=b,l)}function z(b){return M.set(b),{destroy:Na(eo(b,"keydown",m))}}const O={role:"application",id:S.root,"data-cmdk-root":""},B={"data-cmdk-label":"",for:S.input,id:S.label,style:lr(ir)};function m(b){g?.(b),!b.defaultPrevented&&w(b)}const i={action:z,attrs:O};return e.label===void 0&&t.label&&v!==void 0&&t.label(v),e.shouldFilter===void 0&&t.shouldFilter&&d!==void 0&&t.shouldFilter(d),e.filter===void 0&&t.filter&&C!==void 0&&t.filter(C),e.value===void 0&&t.value&&y!==void 0&&t.value(y),e.onValueChange===void 0&&t.onValueChange&&f!==void 0&&t.onValueChange(f),e.loop===void 0&&t.loop&&s!==void 0&&t.loop(s),e.onKeydown===void 0&&t.onKeydown&&g!==void 0&&t.onKeydown(g),e.state===void 0&&t.state&&c!==void 0&&t.state(c),e.ids===void 0&&t.ids&&u!==void 0&&t.ids(u),e.asChild===void 0&&t.asChild&&E!==void 0&&t.asChild(E),I(y),a={root:i,label:{attrs:B},stateStore:T,state:l},A(),`${E?`${n.default?n.default({...a}):""}`:`<div${ye([Q(O),Q(r)],{})}> <label${ye([Q(B)],{})}>${D(v??"")}</label> ${n.default?n.default({...a}):""}</div>`}`}),Ar=P((o,e,t,n)=>{Ae(e,["asChild"]);let a,r,{asChild:l=!1}=e;const A=Rt();return r=U(A,v=>a=v),e.asChild===void 0&&t.asChild&&l!==void 0&&t.asChild(l),a.filtered.count,r(),""}),dr=P((o,e,t,n)=>{let a,r,l,A,v=Ae(e,["heading","value","alwaysRender","asChild"]),d,C,{heading:y=void 0}=e,{value:f=""}=e,{alwaysRender:s=!1}=e,{asChild:g=!1}=e;const{id:c}=en(s),u=to(),E=Rt(),M=_a(),w=st(E,I=>s||u.filter()===!1||!I.search?!0:I.filtered.groups.has(c));C=U(w,I=>d=I);function S(I){if(f){u.value(c,f),I.setAttribute(Zt,f);return}y?f=y.trim().toLowerCase():I.textContent&&(f=I.textContent.trim().toLowerCase()),u.value(c,f),I.setAttribute(Zt,f)}const T={"data-cmdk-group-heading":"","aria-hidden":!0,id:M};return e.heading===void 0&&t.heading&&y!==void 0&&t.heading(y),e.value===void 0&&t.value&&f!==void 0&&t.value(f),e.alwaysRender===void 0&&t.alwaysRender&&s!==void 0&&t.alwaysRender(s),e.asChild===void 0&&t.asChild&&g!==void 0&&t.asChild(g),a={"data-cmdk-group":"",role:"presentation",hidden:d?void 0:!0,"data-value":f},r={"data-cmdk-group-items":"",role:"group","aria-labelledby":y?M:void 0},l={action:S,attrs:a},A={attrs:r},C(),`${g?`${n.default?n.default({container:l,group:A,heading:{attrs:T}}):""}`:`<div${ye([Q(a),Q(v)],{})}>${y?`<div${ye([Q(T)],{})}>${D(y)}</div>`:""} <div${ye([Q(r)],{})}>${n.default?n.default({container:l,group:A,heading:{attrs:T}}):""}</div></div>`}`});function cr(o){return new Promise(e=>setTimeout(e,o))}const ur=P((o,e,t,n)=>{let a=Ae(e,["autofocus","value","asChild","el"]),r,l,A,v;const{ids:d,commandEl:C}=to(),y=Rt(),f=st(y,I=>I.search);v=U(f,I=>A=I);const s=st(y,I=>I.value);let{autofocus:g=void 0}=e,{value:c=A}=e,{asChild:u=!1}=e,{el:E=void 0}=e;const M=st([s,C],([I,z])=>tn?z?.querySelector(`${on}[${Zt}="${I}"]`)?.getAttribute("id"):void 0);l=U(M,I=>r=I);function w(I){y.updateState("search",I)}function S(I){return g&&cr(10).then(()=>I.focus()),{destroy:eo(I,"change",O=>{an(O.target)&&y.updateState("search",O.target.value)})}}let T;return e.autofocus===void 0&&t.autofocus&&g!==void 0&&t.autofocus(g),e.value===void 0&&t.value&&c!==void 0&&t.value(c),e.asChild===void 0&&t.asChild&&u!==void 0&&t.asChild(u),e.el===void 0&&t.el&&E!==void 0&&t.el(E),w(c),T={type:"text","data-cmdk-input":"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":d.list,"aria-labelledby":d.label,"aria-activedescendant":r??void 0,id:d.input},l(),v(),`${u?`${n.default?n.default({action:S,attrs:T}):""}`:`<input${ye([Q(T),Q(a)],{})}${q("this",E,0)}${q("value",c,0)}>`}`}),Cr=P((o,e,t,n)=>{let a,r=Ae(e,["disabled","value","onSelect","alwaysRender","asChild","id"]),l,A,v,{disabled:d=!1}=e,{value:C=""}=e,{onSelect:y=void 0}=e,{alwaysRender:f=!1}=e,{asChild:s=!1}=e,{id:g=_a()}=e;const c=nn(),u=to(),E=Rt(),M=f??c?.alwaysRender,w=st(E,O=>{if(M||u.filter()===!1||!O.search)return!0;const B=O.filtered.items.get(g);return rn(B)?!1:B>0});v=U(w,O=>O);const S=st(E,O=>O.value===C);A=U(S,O=>l=O);function T(O){!C&&O.textContent&&(C=O.textContent.trim().toLowerCase()),u.value(g,C),O.setAttribute(Zt,C);const B=Na(eo(O,"pointermove",()=>{d||z()}),eo(O,"click",()=>{d||I()}));return{destroy(){B()}}}function I(){z(),y?.(C)}function z(){E.updateState("value",C,!0)}return e.disabled===void 0&&t.disabled&&d!==void 0&&t.disabled(d),e.value===void 0&&t.value&&C!==void 0&&t.value(C),e.onSelect===void 0&&t.onSelect&&y!==void 0&&t.onSelect(y),e.alwaysRender===void 0&&t.alwaysRender&&f!==void 0&&t.alwaysRender(f),e.asChild===void 0&&t.asChild&&s!==void 0&&t.asChild(s),e.id===void 0&&t.id&&g!==void 0&&t.id(g),a={"aria-disabled":d?!0:void 0,"aria-selected":l?!0:void 0,"data-disabled":d?!0:void 0,"data-selected":l?!0:void 0,"data-cmdk-item":"","data-value":C,role:"option",id:g},A(),v(),`${`${s?`${n.default?n.default({action:T,attrs:a}):""}`:`<div${ye([Q(a),Q(r)],{})}>${n.default?n.default({action:T,attrs:a}):""}</div>`}`}`}),fr=P((o,e,t,n)=>{let a=Ae(e,["el","asChild"]),r;const{ids:l}=to(),A=Rt();r=U(A,c=>c);let{el:v=void 0}=e,{asChild:d=!1}=e;function C(c){let u;const E=c.closest("[data-cmdk-list]");if(!ln(E))return;const M=new ResizeObserver(()=>{u=requestAnimationFrame(()=>{const w=c.offsetHeight;E.style.setProperty("--cmdk-list-height",w.toFixed(1)+"px")})});return M.observe(c),{destroy(){cancelAnimationFrame(u),M.unobserve(c)}}}const y={"data-cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:l.list,"aria-labelledby":l.input},f={"data-cmdk-list-sizer":""},s={attrs:y},g={attrs:f,action:C};return e.el===void 0&&t.el&&v!==void 0&&t.el(v),e.asChild===void 0&&t.asChild&&d!==void 0&&t.asChild(d),r(),`${d?`${n.default?n.default({list:s,sizer:g}):""}`:`<div${ye([Q(y),Q(a)],{})}${q("this",v,0)}><div${ye([Q(f)],{})}>${n.default?n.default({}):""}</div></div>`}`}),vr=P((o,e,t,n)=>{let a=Ae(e,["value","class"]),{value:r=void 0}=e,{class:l=void 0}=e;e.value===void 0&&t.value&&r!==void 0&&t.value(r),e.class===void 0&&t.class&&l!==void 0&&t.class(l);let A,v,d=o.head;do A=!0,o.head=d,v=`${k(sr,"CommandPrimitive.Root").$$render(o,Object.assign({},{class:ze("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",l)},a,{value:r}),{value:C=>{r=C,A=!1}},{default:()=>`${n.default?n.default({}):""}`})}`;while(!A);return v}),mr=P((o,e,t,n)=>{let a=Ae(e,["class"]),{class:r=void 0}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),`${k(Ar,"CommandPrimitive.Empty").$$render(o,Object.assign({},{class:ze("py-6 text-center text-sm",r)},a),{},{default:()=>`${n.default?n.default({}):""}`})}`}),hr=P((o,e,t,n)=>{let a=Ae(e,["class"]),{class:r=void 0}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),`${k(dr,"CommandPrimitive.Group").$$render(o,Object.assign({},{class:ze("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",r)},a),{},{default:()=>`${n.default?n.default({}):""}`})}`}),Oo=P((o,e,t,n)=>{let a=Ae(e,["class"]),{class:r=void 0}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),`${k(Cr,"CommandPrimitive.Item").$$render(o,Object.assign({},{class:ze("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r)},a),{},{default:()=>`${n.default?n.default({}):""}`})}`}),yr=P((o,e,t,n)=>{let a=Ae(e,["class","value"]),{class:r=void 0}=e,{value:l=""}=e;e.class===void 0&&t.class&&r!==void 0&&t.class(r),e.value===void 0&&t.value&&l!==void 0&&t.value(l);let A,v,d=o.head;do A=!0,o.head=d,v=`<div class="flex items-center border-b border-base-300 px-3" data-cmdk-input-wrapper="">${k($t,"Icon").$$render(o,{src:Sn,class:"mr-2 h-4 w-4 shrink-0 text-base-content-muted"},{},{})} ${k(ur,"CommandPrimitive.Input").$$render(o,Object.assign({},{class:ze("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",r)},a,{value:l}),{value:C=>{l=C,A=!1}},{})}</div>`;while(!A);return v}),gr=P((o,e,t,n)=>{let a=Ae(e,["class"]),{class:r=void 0}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),`${k(fr,"CommandPrimitive.List").$$render(o,Object.assign({},{class:ze("max-h-[300px] overflow-y-auto overflow-x-hidden",r)},a),{},{default:()=>`${n.default?n.default({}):""}`})}`}),ba=P((o,e,t,n)=>{let{value:a}=e,{valueLabel:r=a}=e,{active:l=!1}=e,{handleSelect:A}=e,{multiple:v}=e;return e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.valueLabel===void 0&&t.valueLabel&&r!==void 0&&t.valueLabel(r),e.active===void 0&&t.active&&l!==void 0&&t.active(l),e.handleSelect===void 0&&t.handleSelect&&A!==void 0&&t.handleSelect(A),e.multiple===void 0&&t.multiple&&v!==void 0&&t.multiple(v),`${k(Oo,"Command.Item").$$render(o,{value:String(r),onSelect:()=>A({value:a,label:r})},{},{default:()=>`${v?`<div${q("class",ze("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",l?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible"),0)}>${k($t,"Icon").$$render(o,{src:da,class:ze("h-4 w-4")},{},{})}</div>`:`<div class="mr-2 flex h-4 w-4 items-center justify-center">${k($t,"Icon").$$render(o,{src:da,class:ze("h-4 w-4",l?"":"text-transparent")},{},{})}</div>`} <span class="line-clamp-4">${D(r)}</span>`})}`}),pr=P((o,e,t,n)=>{let a=Ae(e,["class","transition","transitionConfig","align","sideOffset"]),{class:r=void 0}=e,{transition:l=On}=e,{transitionConfig:A=void 0}=e,{align:v="center"}=e,{sideOffset:d=4}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),e.transition===void 0&&t.transition&&l!==void 0&&t.transition(l),e.transitionConfig===void 0&&t.transitionConfig&&A!==void 0&&t.transitionConfig(A),e.align===void 0&&t.align&&v!==void 0&&t.align(v),e.sideOffset===void 0&&t.sideOffset&&d!==void 0&&t.sideOffset(d),`${k(or,"PopoverPrimitive.Content").$$render(o,Object.assign({},{transition:l},{transitionConfig:A},{align:v},{sideOffset:d},a,{class:ze("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",r)}),{},{default:()=>`${n.default?n.default({}):""}`})}`}),br=tr,xr=ar,xa=P((o,e,t,n)=>{let a=Ae(e,["class","orientation","decorative"]),{class:r=void 0}=e,{orientation:l="horizontal"}=e,{decorative:A=void 0}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),e.orientation===void 0&&t.orientation&&l!==void 0&&t.orientation(l),e.decorative===void 0&&t.decorative&&A!==void 0&&t.decorative(A),`${k(Jn,"SeparatorPrimitive.Root").$$render(o,Object.assign({},{class:ze("shrink-0 bg-base-300",l==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",r)},{orientation:l},{decorative:A},a),{},{})}`}),bo=P((o,e,t,n)=>{let a=Ae(e,["class","href","variant"]),{class:r=void 0}=e,{href:l=void 0}=e,{variant:A="default"}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),e.href===void 0&&t.href&&l!==void 0&&t.href(l),e.variant===void 0&&t.variant&&A!==void 0&&t.variant(A),`${(v=>v?`<${l?"a":"span"}${ye([{href:oa(l)},{class:oa(ze(sn({variant:A,className:r})))},Q(a)],{})}>${va(v)?"":`${n.default?n.default({}):""}`}${va(v)?"":`</${v}>`}`:"")(l?"a":"span")}`}),Er={code:".viewport.svelte-1youqmj{position:relative;overflow-y:auto;-webkit-overflow-scrolling:touch;display:block}.contents.svelte-1youqmj,.row.svelte-1youqmj{display:block}.row.svelte-1youqmj{overflow:hidden}",map:`{"version":3,"file":"Virtual.svelte","sources":["Virtual.svelte"],"sourcesContent":["<script>\\n\\timport { onMount, tick } from 'svelte';\\n\\n\\t// props\\n\\texport let items;\\n\\texport let height = '100%';\\n\\texport let itemHeight = undefined;\\n\\n\\t// read-only, but visible to consumers via bind:start\\n\\texport let start = 0;\\n\\texport let end = 0;\\n\\n\\t// local state\\n\\tlet height_map = [];\\n\\tlet rows;\\n\\tlet viewport;\\n\\tlet contents;\\n\\tlet viewport_height = 0;\\n\\tlet visible;\\n\\tlet mounted;\\n\\n\\tlet top = 0;\\n\\tlet bottom = 0;\\n\\tlet average_height;\\n\\n\\t$: visible = items.slice(start, end).map((data, i) => {\\n\\t\\treturn { index: i + start, data };\\n\\t});\\n\\n\\t// whenever \`items\` changes, invalidate the current heightmap\\n\\t$: if (mounted) refresh(items, viewport_height, itemHeight);\\n\\n\\tasync function refresh(items, viewport_height, itemHeight) {\\n\\t\\tconst { scrollTop } = viewport;\\n\\n\\t\\tawait tick(); // wait until the DOM is up to date\\n\\t\\tif (!mounted) return;\\n\\n\\t\\tlet content_height = top - scrollTop;\\n\\t\\tlet i = start;\\n\\n\\t\\twhile (content_height < viewport_height && i < items.length) {\\n\\t\\t\\tlet row = rows[i - start];\\n\\n\\t\\t\\tif (!row) {\\n\\t\\t\\t\\tend = i + 1;\\n\\t\\t\\t\\tawait tick(); // render the newly visible row\\n\\t\\t\\t\\tif (!mounted) return;\\n\\t\\t\\t\\trow = rows[i - start];\\n\\t\\t\\t}\\n\\n\\t\\t\\tconst row_height = (height_map[i] =\\n\\t\\t\\t\\titemHeight || row?.offsetHeight || Number.MAX_SAFE_INTEGER);\\n\\t\\t\\tcontent_height += row_height;\\n\\t\\t\\ti += 1;\\n\\t\\t}\\n\\n\\t\\tend = i;\\n\\n\\t\\tconst remaining = items.length - end;\\n\\t\\taverage_height = (top + content_height) / end;\\n\\n\\t\\tbottom = remaining * average_height;\\n\\t\\theight_map.length = items.length;\\n\\t}\\n\\n\\tasync function handle_scroll() {\\n\\t\\tconst { scrollTop } = viewport;\\n\\n\\t\\tconst old_start = start;\\n\\n\\t\\tfor (let v = 0; v < rows.length; v += 1) {\\n\\t\\t\\theight_map[start + v] = itemHeight || rows[v]?.offsetHeight || Number.MAX_SAFE_INTEGER;\\n\\t\\t}\\n\\n\\t\\tlet i = 0;\\n\\t\\tlet y = 0;\\n\\n\\t\\twhile (i < items.length) {\\n\\t\\t\\tconst row_height = height_map[i] || average_height;\\n\\t\\t\\tif (y + row_height > scrollTop) {\\n\\t\\t\\t\\tstart = i;\\n\\t\\t\\t\\ttop = y;\\n\\n\\t\\t\\t\\tbreak;\\n\\t\\t\\t}\\n\\n\\t\\t\\ty += row_height;\\n\\t\\t\\ti += 1;\\n\\t\\t}\\n\\n\\t\\twhile (i < items.length) {\\n\\t\\t\\ty += height_map[i] || average_height;\\n\\t\\t\\ti += 1;\\n\\n\\t\\t\\tif (y > scrollTop + viewport_height) break;\\n\\t\\t}\\n\\n\\t\\tend = i;\\n\\n\\t\\tconst remaining = items.length - end;\\n\\t\\taverage_height = y / end;\\n\\n\\t\\twhile (i < items.length) height_map[i++] = average_height;\\n\\t\\tbottom = remaining * average_height;\\n\\n\\t\\t// prevent jumping if we scrolled up into unknown territory\\n\\t\\tif (start < old_start) {\\n\\t\\t\\tawait tick();\\n\\n\\t\\t\\tlet expected_height = 0;\\n\\t\\t\\tlet actual_height = 0;\\n\\n\\t\\t\\tfor (let i = start; i < old_start; i += 1) {\\n\\t\\t\\t\\tif (rows[i - start]) {\\n\\t\\t\\t\\t\\texpected_height += height_map[i];\\n\\t\\t\\t\\t\\tactual_height += itemHeight || rows[i - start]?.offsetHeight || Number.MAX_SAFE_INTEGER;\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n\\n\\t\\t\\tconst d = actual_height - expected_height;\\n\\t\\t\\tviewport.scrollTo(0, scrollTop + d);\\n\\t\\t}\\n\\n\\t\\t// TODO if we overestimated the space these\\n\\t\\t// rows would occupy we may need to add some\\n\\t\\t// more. maybe we can just call handle_scroll again?\\n\\t}\\n\\n\\t// trigger initial refresh\\n\\tonMount(() => {\\n\\t\\trows = contents.getElementsByClassName('row');\\n\\t\\tmounted = true;\\n\\t\\treturn () => (mounted = false);\\n\\t});\\n<\/script>\\n\\n<div\\n\\tbind:this={viewport}\\n\\tbind:offsetHeight={viewport_height}\\n\\ton:scroll={handle_scroll}\\n\\tstyle=\\"height: {height};\\"\\n\\tclass=\\"viewport\\"\\n>\\n\\t<div\\n\\t\\tclass=\\"contents\\"\\n\\t\\tbind:this={contents}\\n\\t\\tstyle=\\"padding-top: {top}px; padding-bottom: {bottom}px;\\"\\n\\t>\\n\\t\\t{#each visible as row (row.index)}\\n\\t\\t\\t<div class=\\"row\\">\\n\\t\\t\\t\\t<slot item={row.data}>Missing template</slot>\\n\\t\\t\\t</div>\\n\\t\\t{/each}\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.viewport {\\n\\t\\tposition: relative;\\n\\t\\toverflow-y: auto;\\n\\t\\t-webkit-overflow-scrolling: touch;\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.contents,\\n\\t.row {\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.row {\\n\\t\\toverflow: hidden;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA8JC,wBAAU,CACT,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,CAChB,0BAA0B,CAAE,KAAK,CACjC,OAAO,CAAE,KACV,CAEA,wBAAS,CACT,mBAAK,CACJ,OAAO,CAAE,KACV,CAEA,mBAAK,CACJ,QAAQ,CAAE,MACX"}`},wr=P((o,e,t,n)=>{let{items:a}=e,{height:r="100%"}=e,{itemHeight:l=void 0}=e,{start:A=0}=e,{end:v=0}=e,d,C,y,f=0,s=0;return e.items===void 0&&t.items&&a!==void 0&&t.items(a),e.height===void 0&&t.height&&r!==void 0&&t.height(r),e.itemHeight===void 0&&t.itemHeight&&l!==void 0&&t.itemHeight(l),e.start===void 0&&t.start&&A!==void 0&&t.start(A),e.end===void 0&&t.end&&v!==void 0&&t.end(v),o.css.add(Er),y=a.slice(A,v).map((g,c)=>({index:c+A,data:g})),`<div style="${"height: "+D(r,!0)+";"}" class="viewport svelte-1youqmj"${q("this",d,0)}><div class="contents svelte-1youqmj" style="${"padding-top: "+D(f,!0)+"px; padding-bottom: "+D(s,!0)+"px;"}"${q("this",C,0)}>${Je(y,g=>`<div class="row svelte-1youqmj">${n.default?n.default({item:g.data}):"Missing template"} </div>`)}</div> </div>`}),Ea=5;function Ir(o){return"similarity"in o?o.similarity*-1:o.ordinal??0}const wa=P((o,e,t,n)=>{let a,r=qa(n),l,A=De,v=()=>(A(),A=U(a,_=>l=_),a),d,C=De,y=()=>(C(),C=U(me,_=>d=_),me),f,s,g,c,u,E,M,w,S,T;w=U(ko,_=>M=_);const I=An();c=U(I,_=>g=_);let{title:z=void 0}=e,{name:O}=e,{multiple:B=!1}=e,{hideDuringPrint:m=!0}=e,{disableSelectAll:i=!1}=e,{defaultValue:b=[]}=e,{noDefault:p=!1}=e,{selectAllByDefault:x=!1}=e,{description:h=void 0}=e,{value:N="value",data:K,label:X=N,order:R=void 0,where:de=void 0}=e;const{results:Ie,update:Re}=yn({value:N,data:K,label:X,order:R,where:de},`Dropdown-${O}`,M?.data?.data[`Dropdown-${O}_data`]);E=U(Ie,_=>u=_);let G=!!K;const _e=O in g&&"rawValues"in g[O]&&Array.isArray(g[O].rawValues)?g[O].rawValues:[],Te=dn({multiselect:B,defaultValues:Array.isArray(b)?b:[b],initialOptions:_e,noDefault:p,selectAllByDefault:j(x)}),{addOptions:ue,removeOptions:Ce,options:ce,selectedOptions:Oe,selectAll:ge,deselectAll:V,toggleSelected:te,pauseSorting:oe,resumeSorting:Z,forceSort:fe,destroy:ee}=Te;T=U(ce,_=>S=_),s=U(Oe,_=>f=_),yt(ee);const pe=_=>{JSON.stringify(_)!==JSON.stringify(g[O])&&So(I,g[O]=_,g)};let be=[],xe=f.length>0;yt(Oe.subscribe(_=>{if(xe||=_.length>0,_&&xe){const F=_;B?pe({label:F.map($=>$.label).join(", "),value:F.length?`(${F.map($=>Bo($.value))})`:"(select null where 0)",rawValues:F}):F.length?F.length&&pe({label:F[0].label,value:Bo(F[0].value,{serializeStrings:!1}),rawValues:F}):pe({label:"",value:null,rawValues:[]})}})),Lt(Ra,{registerOption:_=>(ue(_),()=>{Ce(_)})});let le,ae="",ve=0,me;const ie=cn(()=>{if(ve++,ae&&G){const _=ve,F=a.search(ae,"label");F.hash!==me?.hash&&fn(()=>{_===ve&&(y(me=F),fe())},F.fetch())}else y(me=a??K)});let H=[];N||(K?H.push('Missing required prop: "value".'):r.default||H.push('Dropdown requires either "value" and "data" props or <DropdownOption />.')),K&&typeof K!="object"&&(typeof K=="string"?H.push(`'${K}' is not a recognized query result. Data should be provided in the format: data = {'${K.replace("data.","")}'}`):H.push(`'${K}' is not a recognized query result. Data should be an object. e.g data = {QueryName}`));try{Kn({name:O})}catch(_){H.push(_.message)}let he=!1;e.title===void 0&&t.title&&z!==void 0&&t.title(z),e.name===void 0&&t.name&&O!==void 0&&t.name(O),e.multiple===void 0&&t.multiple&&B!==void 0&&t.multiple(B),e.hideDuringPrint===void 0&&t.hideDuringPrint&&m!==void 0&&t.hideDuringPrint(m),e.disableSelectAll===void 0&&t.disableSelectAll&&i!==void 0&&t.disableSelectAll(i),e.defaultValue===void 0&&t.defaultValue&&b!==void 0&&t.defaultValue(b),e.noDefault===void 0&&t.noDefault&&p!==void 0&&t.noDefault(p),e.selectAllByDefault===void 0&&t.selectAllByDefault&&x!==void 0&&t.selectAllByDefault(x),e.description===void 0&&t.description&&h!==void 0&&t.description(h),e.value===void 0&&t.value&&N!==void 0&&t.value(N),e.data===void 0&&t.data&&K!==void 0&&t.data(K),e.label===void 0&&t.label&&X!==void 0&&t.label(X),e.order===void 0&&t.order&&R!==void 0&&t.order(R),e.where===void 0&&t.where&&de!==void 0&&t.where(de);let se,ne,Be=o.head;do se=!0,o.head=Be,B=j(B),m=j(m),i=j(i),p=j(p),x=j(x),Re({value:N,data:K,label:X,order:R,where:de}),v({hasQuery:G,query:a}=u),a&&a.fetch(),ie(),le?oe():Z(),d?.dataLoaded&&(be=d),l?.error&&G&&!he&&(H=[...H,l.error],he=!0),ne=`${n.default?n.default({}):""} ${Je(be,_=>`${k(ht,"DropdownOption").$$render(o,{value:_[N]??_.value,valueLabel:_[X]??_.label,idx:Ir(_),__auto:!0},{},{})}`)} ${k(nr,"HiddenInPrint").$$render(o,{enabled:m},{},{default:()=>`<div class="mt-2 mb-4 ml-0 mr-2 inline-block">${H.length>0?`${k(kn,"InlineError").$$render(o,{inputType:"Dropdown",error:H,height:"32",width:"140"},{},{})} `:`${k(br,"Popover.Root").$$render(o,{open:le},{open:_=>{le=_,se=!1}},{default:()=>`${k(xr,"Popover.Trigger").$$render(o,{asChild:!0},{},{default:({builder:_})=>`${k(Pn,"Button").$$render(o,{builders:[_],variant:"outline",role:"combobox",size:"sm",class:"min-w-5 h-8 border border-base-300","aria-label":z??je(O)},{},{default:()=>`${z&&!B?`${D(z)} ${h?`${k(ca,"Info").$$render(o,{description:h,className:"pl-1"},{},{})}`:""} ${f.length>0?`${k(xa,"Separator").$$render(o,{orientation:"vertical",class:"mx-2 h-4"},{},{})} ${D(f[0].label)}`:""}`:`${f.length>0&&!B?`${D(f[0].label)}`:`${D(z??je(O))} ${h?`${k(ca,"Info").$$render(o,{description:h,className:"pl-1"},{},{})}`:""}`}`} ${k($t,"Icon").$$render(o,{src:_n,class:"ml-2 h-4 w-4"},{},{})} ${f.length>0&&B?`${k(xa,"Separator").$$render(o,{orientation:"vertical",class:"mx-2 h-4"},{},{})} ${k(bo,"Badge").$$render(o,{variant:"default",class:"rounded-xs px-1 font-normal sm:hidden"},{},{default:()=>`${D(f.length)}`})} <div class="hidden space-x-1 sm:flex">${f.length>3?`${k(bo,"Badge").$$render(o,{variant:"default",class:"rounded-xs px-1 font-normal"},{},{default:()=>`${D(f.length)} Selected`})}`:`${Je(f,F=>`${k(bo,"Badge").$$render(o,{variant:"default",class:"rounded-xs px-1 font-normal"},{},{default:()=>`${D(F.label)} `})}`)}`}</div>`:""}`})}`})} ${k(pr,"Popover.Content").$$render(o,{class:"w-[200px] p-0",align:"start",side:"bottom"},{},{default:()=>`${k(vr,"Command.Root").$$render(o,{shouldFilter:!1},{},{default:()=>`${k(yr,"Command.Input").$$render(o,{placeholder:z,value:ae},{value:_=>{ae=_,se=!1}},{})} ${k(gr,"Command.List").$$render(o,{},{},{default:()=>`${k(mr,"Command.Empty").$$render(o,{},{},{default:()=>"No results found."})} ${k(hr,"Command.Group").$$render(o,{},{},{default:()=>`${S.length<=Ea?`${Je(S,(_,F)=>`${k(ba,"DropdownOptionDisplay").$$render(o,{id:F,value:_.value,valueLabel:_.label,handleSelect:({value:$,label:Le})=>{te({value:$,label:Le}),B||(le=!1)},multiple:B,active:f.some($=>$.value===_.value&&$.label===_.label)},{},{})}`)}`:`${k(wr,"VirtualList").$$render(o,{height:`${Ea*32}px`,items:S},{},{default:({item:_})=>`${k(ba,"DropdownOptionDisplay").$$render(o,{value:_?.value,valueLabel:_?.label,handleSelect:({value:F,label:$})=>{te({value:F,label:$}),B||(le=!1)},multiple:B,active:f.some(F=>F.value===_.value&&F.label===_.label)},{},{})}`})}`}`})} ${B?`${i?"":`<div class="-mx-1 h-px bg-base-300"></div> ${k(Oo,"Command.Item").$$render(o,{class:"justify-center text-center",onSelect:ge},{},{default:()=>"Select all"})}`} <div class="-mx-1 h-px bg-base-300"></div> ${k(Oo,"Command.Item").$$render(o,{disabled:f.length===0,class:"justify-center text-center",onSelect:V},{},{default:()=>"Clear selection"})}`:""}`})}`})}`})}`})}`}</div>`})}`;while(!se);return A(),C(),s(),c(),E(),w(),T(),ne}),Br={code:"svg.svelte-lqleyo.svelte-lqleyo{display:inline-block;vertical-align:middle;transition:transform 0.15s ease-in}span.svelte-lqleyo.svelte-lqleyo{margin:auto 0 auto 0}[aria-expanded='true'].svelte-lqleyo svg.svelte-lqleyo{transform:rotate(0.25turn)}",map:`{"version":3,"file":"ChevronToggle.svelte","sources":["ChevronToggle.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\n\\tconst { resolveColor } = getThemeStores();\\n\\n\\texport let toggled = false;\\n\\n\\texport let color = 'base-content';\\n\\t$: colorStore = resolveColor(color);\\n\\n\\texport let size = 10;\\n<\/script>\\n\\n<span aria-expanded={toggled}>\\n\\t<svg viewBox=\\"0 0 16 16\\" width={size} height={size}\\n\\t\\t><path\\n\\t\\t\\tfill={$colorStore}\\n\\t\\t\\tfill-rule=\\"evenodd\\"\\n\\t\\t\\td=\\"M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z\\"\\n\\t\\t/></svg\\n\\t>\\n</span>\\n\\n<style>\\n\\tsvg {\\n\\t\\tdisplay: inline-block;\\n\\t\\tvertical-align: middle;\\n\\t\\ttransition: transform 0.15s ease-in;\\n\\t}\\n\\n\\tspan {\\n\\t\\tmargin: auto 0 auto 0;\\n\\t}\\n\\n\\t[aria-expanded='true'] svg {\\n\\t\\ttransform: rotate(0.25turn);\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4BC,+BAAI,CACH,OAAO,CAAE,YAAY,CACrB,cAAc,CAAE,MAAM,CACtB,UAAU,CAAE,SAAS,CAAC,KAAK,CAAC,OAC7B,CAEA,gCAAK,CACJ,MAAM,CAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACrB,CAEA,CAAC,aAAa,CAAC,MAAM,eAAC,CAAC,iBAAI,CAC1B,SAAS,CAAE,OAAO,QAAQ,CAC3B"}`},Ia=P((o,e,t,n)=>{let a,r,l=De,A=()=>(l(),l=U(a,f=>r=f),a);const{resolveColor:v}=At();let{toggled:d=!1}=e,{color:C="base-content"}=e,{size:y=10}=e;return e.toggled===void 0&&t.toggled&&d!==void 0&&t.toggled(d),e.color===void 0&&t.color&&C!==void 0&&t.color(C),e.size===void 0&&t.size&&y!==void 0&&t.size(y),o.css.add(Br),A(a=v(C)),l(),`<span${q("aria-expanded",d,0)} class="svelte-lqleyo"><svg viewBox="0 0 16 16"${q("width",y,0)}${q("height",y,0)} class="svelte-lqleyo"><path${q("fill",r,0)} fill-rule="evenodd" d="M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"></path></svg> </span>`}),Or={code:".marker.svelte-v9l93j{border-left:5px solid transparent;border-right:5px solid transparent;border-top:9px solid var(--base-content-muted);transform:rotate(-90deg);transition:transform 0.2s ease}.rotate-marker.svelte-v9l93j{transform:rotate(0deg)}button.svelte-v9l93j{display:flex;align-items:center;cursor:pointer}",map:`{"version":3,"file":"Details.svelte","sources":["Details.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide } from 'svelte/transition';\\n\\timport { toBoolean } from '../../utils.js';\\n\\n\\texport let title = 'Details';\\n\\texport let open = false;\\n\\t$: open = toBoolean(open);\\n\\n\\texport let printShowAll = true;\\n\\t$: printShowAll = toBoolean(printShowAll);\\n\\tlet printing = false;\\n<\/script>\\n\\n<svelte:window\\n\\ton:beforeprint={() => (printing = true)}\\n\\ton:afterprint={() => (printing = false)}\\n\\ton:export-beforeprint={() => (printing = true)}\\n\\ton:export-afterprint={() => (printing = false)}\\n/>\\n\\n{#if !printing || !printShowAll}\\n\\t<div class=\\"mb-4 mt-2\\">\\n\\t\\t<button\\n\\t\\t\\tclass=\\"text-sm text-base-content-muted cursor-pointer inline-flex gap-2\\"\\n\\t\\t\\ton:click={() => (open = !open)}\\n\\t\\t>\\n\\t\\t\\t<span class={open ? 'marker rotate-marker' : 'marker'} />\\n\\t\\t\\t<span> {title} </span>\\n\\t\\t</button>\\n\\n\\t\\t{#if open}\\n\\t\\t\\t<div class=\\"pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm\\" transition:slide|local>\\n\\t\\t\\t\\t<slot />\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\t</div>\\n{:else}\\n\\t<div class=\\"mb-4 mt-2 text-base-content-muted\\">\\n\\t\\t<span class=\\"text-sm font-semibold inline-flex\\"> {title} </span>\\n\\t\\t<div class=\\"pt-1 mb-6 text-sm\\">\\n\\t\\t\\t<slot />\\n\\t\\t</div>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\t.marker {\\n\\t\\tborder-left: 5px solid transparent;\\n\\t\\tborder-right: 5px solid transparent;\\n\\t\\tborder-top: 9px solid var(--base-content-muted);\\n\\t\\ttransform: rotate(-90deg);\\n\\t\\ttransition: transform 0.2s ease;\\n\\t}\\n\\n\\t.rotate-marker {\\n\\t\\ttransform: rotate(0deg);\\n\\t}\\n\\n\\tbutton {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tcursor: pointer;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAkDC,qBAAQ,CACP,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CAClC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CACnC,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC/C,SAAS,CAAE,OAAO,MAAM,CAAC,CACzB,UAAU,CAAE,SAAS,CAAC,IAAI,CAAC,IAC5B,CAEA,4BAAe,CACd,SAAS,CAAE,OAAO,IAAI,CACvB,CAEA,oBAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OACT"}`},Sr=P((o,e,t,n)=>{let{title:a="Details"}=e,{open:r=!1}=e,{printShowAll:l=!0}=e;return e.title===void 0&&t.title&&a!==void 0&&t.title(a),e.open===void 0&&t.open&&r!==void 0&&t.open(r),e.printShowAll===void 0&&t.printShowAll&&l!==void 0&&t.printShowAll(l),o.css.add(Or),r=j(r),l=j(l),` ${`<div class="mb-4 mt-2"><button class="text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"><span class="${D(ka(r?"marker rotate-marker":"marker"),!0)+" svelte-v9l93j"}"></span> <span>${D(a)}</span></button> ${r?`<div class="pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm">${n.default?n.default({}):""}</div>`:""}</div>`}`}),kr={code:"div.pagination.svelte-ghf30y.svelte-ghf30y{padding:0px 5px;align-content:center;border-bottom:1px solid var(--base-200);height:1.25em;background-color:var(--base-100);display:flex;flex-direction:row;justify-content:space-between;align-items:center}.slider.svelte-ghf30y.svelte-ghf30y{-webkit-appearance:none;width:75%;height:10px;margin:0 0;outline:none;border-radius:10px;display:inline-block;cursor:pointer}.slider.svelte-ghf30y.svelte-ghf30y::-webkit-slider-thumb{background-color:var(--color-info);-webkit-appearance:none;appearance:none;width:10px;height:10px;cursor:pointer;border-radius:10px}.slider.svelte-ghf30y.svelte-ghf30y::-moz-range-thumb{background-color:var(--color-info);width:10px;height:10px;cursor:pointer}.slider.svelte-ghf30y.svelte-ghf30y::-moz-range-thumb{background-color:var(--color-info);width:10px;height:10px;cursor:pointer}span.svelte-ghf30y.svelte-ghf30y{font-family:var(--ui-font-family-compact);-webkit-font-smoothing:antialiased;float:right}.scrollbox.svelte-ghf30y.svelte-ghf30y{width:100%;overflow-x:auto;border-bottom:1px solid var(--base-300);background-color:var(--base-100)}.results-pane.svelte-ghf30y .download-button{margin-top:10px}table.svelte-ghf30y.svelte-ghf30y{width:100%;border-collapse:collapse;font-family:var(--ui-font-family);font-variant-numeric:tabular-nums}td.svelte-ghf30y.svelte-ghf30y{padding:2px 8px;overflow:hidden;text-overflow:ellipsis}td.svelte-ghf30y div.svelte-ghf30y{width:100px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.other.svelte-ghf30y.svelte-ghf30y{text-align:left}.string.svelte-ghf30y.svelte-ghf30y{text-align:left}.date.svelte-ghf30y.svelte-ghf30y{text-align:left}.number.svelte-ghf30y.svelte-ghf30y{text-align:right}.boolean.svelte-ghf30y.svelte-ghf30y{text-align:left}.index.svelte-ghf30y.svelte-ghf30y{text-align:left;max-width:min-content}tr.type-indicator.svelte-ghf30y.svelte-ghf30y{border-bottom:1px solid var(--base-300)}.footer.svelte-ghf30y.svelte-ghf30y{display:flex;justify-content:flex-end;font-size:12px}",map:`{"version":3,"file":"QueryDataTable.svelte","sources":["QueryDataTable.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { blur, slide } from 'svelte/transition';\\n\\timport DownloadData from '../DownloadData.svelte';\\n\\timport getColumnSummary from '@evidence-dev/component-utilities/getColumnSummary';\\n\\timport { formatValue } from '@evidence-dev/component-utilities/formatting';\\n\\timport { throttle } from 'echarts';\\n\\n\\texport let queryID;\\n\\texport let data;\\n\\n\\t$: columnSummary = getColumnSummary(data, 'array');\\n\\t$: columnWidths = 90 / (columnSummary.length + 1);\\n\\n\\t// Slicer\\n\\tlet index = 0;\\n\\tlet size = 5;\\n\\t$: max = Math.max(data.length - size, 0);\\n\\t$: dataPage = data.slice(index, index + size);\\n\\tlet updatedSlice;\\n\\n\\tfunction slice() {\\n\\t\\tupdatedSlice = data.slice(index, index + size);\\n\\t\\tdataPage = updatedSlice;\\n\\t}\\n\\n\\tconst updateIndex = throttle((event) => {\\n\\t\\tindex = Math.min(Math.max(0, index + Math.floor(event.deltaY / Math.abs(event.deltaY))), max);\\n\\t\\tslice();\\n\\t}, 60);\\n\\n\\tfunction handleWheel(event) {\\n\\t\\t// abort if scroll is in x-direction\\n\\t\\tif (Math.abs(event.deltaX) >= Math.abs(event.deltaY)) {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tconst hasScrolledToTop = event.deltaY < 0 && index === 0;\\n\\t\\tconst hasScrolledToBottom = event.deltaY > 0 && index === max;\\n\\n\\t\\tif (hasScrolledToTop || hasScrolledToBottom) {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tevent.preventDefault();\\n\\t\\tupdateIndex(event);\\n\\t}\\n<\/script>\\n\\n<div class=\\"results-pane py-1\\" transition:slide|local>\\n\\t<div class=\\"scrollbox pretty-scrollbar\\">\\n\\t\\t<table class=\\"text-xs\\" in:blur>\\n\\t\\t\\t<thead>\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<th class=\\"py-0 px-2 font-medium index text-base-content-muted\\" style=\\"width:10%\\" />\\n\\t\\t\\t\\t\\t{#each columnSummary as column}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"py-0 px-2 font-medium {column.type}\\"\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\tevidenceType={column.evidenceColumnType?.evidenceType || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t\\tevidenceTypeFidelity={column.evidenceColumnType?.typeFidelity || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{column.id}\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr><tr />\\n\\t\\t\\t\\t<tr class=\\"type-indicator\\">\\n\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\tclass=\\"py-0 px-2 index type-indicator text-base-content-muted font-normal\\"\\n\\t\\t\\t\\t\\t\\tstyle=\\"width:10%\\"\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{#each columnSummary as column}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"{column.type} type-indicator text-base-content-muted font-normal py-0 px-2\\"\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\tevidenceType={column.evidenceColumnType?.evidenceType || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t\\tevidenceTypeFidelity={column.evidenceColumnType?.typeFidelity || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{column.type}\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr><tr />\\n\\t\\t\\t</thead>\\n\\t\\t\\t<tbody on:wheel={handleWheel}>\\n\\t\\t\\t\\t{#each dataPage as row, i}\\n\\t\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t\\t<td class=\\"index text-base-content-muted\\" style=\\"width:10%\\">\\n\\t\\t\\t\\t\\t\\t\\t{#if i === 0}\\n\\t\\t\\t\\t\\t\\t\\t\\t<!-- <input type=\\"number\\" bind:value={index} max={max} min=0 on:input={slice} class=\\"index-key\\" autofocus reversed> -->\\n\\t\\t\\t\\t\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t{#each columnSummary as column, j}\\n\\t\\t\\t\\t\\t\\t\\t{#if row[column.id] == null}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"text-base-content-muted {columnSummary[j].type}\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'number'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"number\\" style=\\"width:{columnWidths}%;\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'date'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"string\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ttitle={formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'string'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"string\\" style=\\"width:{columnWidths}%\\" title={row[column.id]}>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] || 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'boolean'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"boolean\\" style=\\"width:{columnWidths}%\\" title={row[column.id]}>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] ?? 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"other\\" style=\\"width:{columnWidths}%\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] || 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\t{/each}\\n\\t\\t\\t</tbody>\\n\\t\\t</table>\\n\\t</div>\\n\\n\\t{#if max > 0}\\n\\t\\t<div class=\\"pagination\\">\\n\\t\\t\\t<input\\n\\t\\t\\t\\ttype=\\"range\\"\\n\\t\\t\\t\\t{max}\\n\\t\\t\\t\\tstep=\\"1\\"\\n\\t\\t\\t\\tbind:value={index}\\n\\t\\t\\t\\ton:input={slice}\\n\\t\\t\\t\\tclass=\\"slider bg-info/30 hover:bg-info/40 transition-colors\\"\\n\\t\\t\\t/>\\n\\t\\t\\t<span class=\\"text-xs\\">\\n\\t\\t\\t\\t{(index + size).toLocaleString()} of {(max + size).toLocaleString()}\\n\\t\\t\\t</span>\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t<div class=\\"footer\\">\\n\\t\\t<DownloadData class=\\"download-button\\" {data} {queryID} display />\\n\\t</div>\\n</div>\\n\\n<style>\\n\\tdiv.pagination {\\n\\t\\tpadding: 0px 5px;\\n\\t\\talign-content: center;\\n\\t\\tborder-bottom: 1px solid var(--base-200);\\n\\t\\theight: 1.25em;\\n\\t\\tbackground-color: var(--base-100);\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: row;\\n\\t\\tjustify-content: space-between;\\n\\t\\talign-items: center;\\n\\t}\\n\\n\\t.slider {\\n\\t\\t-webkit-appearance: none;\\n\\t\\twidth: 75%;\\n\\t\\theight: 10px;\\n\\t\\tmargin: 0 0;\\n\\t\\toutline: none;\\n\\t\\tborder-radius: 10px;\\n\\t\\tdisplay: inline-block;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.slider::-webkit-slider-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\t-webkit-appearance: none;\\n\\t\\tappearance: none;\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t\\tborder-radius: 10px;\\n\\t}\\n\\n\\t.slider::-moz-range-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.slider::-moz-range-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\tspan {\\n\\t\\tfont-family: var(--ui-font-family-compact);\\n\\t\\t-webkit-font-smoothing: antialiased;\\n\\t\\tfloat: right;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\twidth: 100%;\\n\\t\\toverflow-x: auto;\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t\\tbackground-color: var(--base-100);\\n\\t}\\n\\n\\t.results-pane :global(.download-button) {\\n\\t\\tmargin-top: 10px;\\n\\t}\\n\\n\\ttable {\\n\\t\\twidth: 100%;\\n\\t\\tborder-collapse: collapse;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-variant-numeric: tabular-nums;\\n\\t}\\n\\n\\ttd {\\n\\t\\tpadding: 2px 8px;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\ttd div {\\n\\t\\twidth: 100px;\\n\\t\\twhite-space: nowrap;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\t.other {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.string {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.date {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.number {\\n\\t\\ttext-align: right;\\n\\t}\\n\\n\\t.boolean {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.index {\\n\\t\\ttext-align: left;\\n\\t\\tmax-width: min-content;\\n\\t}\\n\\n\\ttr.type-indicator {\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t}\\n\\n\\t.footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\tfont-size: 12px;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAiLC,GAAG,uCAAY,CACd,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,MAAM,CAAE,MAAM,CACd,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MACd,CAEA,mCAAQ,CACP,kBAAkB,CAAE,IAAI,CACxB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,CAAC,CAAC,CAAC,CACX,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,IAAI,CACnB,OAAO,CAAE,YAAY,CACrB,MAAM,CAAE,OACT,CAEA,mCAAO,sBAAuB,CAC7B,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAChB,CAEA,mCAAO,kBAAmB,CACzB,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OACT,CAEA,mCAAO,kBAAmB,CACzB,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OACT,CAEA,gCAAK,CACJ,WAAW,CAAE,IAAI,wBAAwB,CAAC,CAC1C,sBAAsB,CAAE,WAAW,CACnC,KAAK,CAAE,KACR,CAEA,sCAAW,CACV,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,gBAAgB,CAAE,IAAI,UAAU,CACjC,CAEA,2BAAa,CAAS,gBAAkB,CACvC,UAAU,CAAE,IACb,CAEA,iCAAM,CACL,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,oBAAoB,CAAE,YACvB,CAEA,8BAAG,CACF,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,gBAAE,CAAC,iBAAI,CACN,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,kCAAO,CACN,UAAU,CAAE,IACb,CAEA,mCAAQ,CACP,UAAU,CAAE,IACb,CAEA,iCAAM,CACL,UAAU,CAAE,IACb,CAEA,mCAAQ,CACP,UAAU,CAAE,KACb,CAEA,oCAAS,CACR,UAAU,CAAE,IACb,CAEA,kCAAO,CACN,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,WACZ,CAEA,EAAE,2CAAgB,CACjB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CACxC,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,SAAS,CAAE,IACZ"}`};let Mt=5;const _r=P((o,e,t,n)=>{let a,r,l,A,{queryID:v}=e,{data:d}=e,C=0,y;function f(){y=d.slice(C,C+Mt),A=y}return jn(s=>{C=Math.min(Math.max(0,C+Math.floor(s.deltaY/Math.abs(s.deltaY))),l),f()},60),e.queryID===void 0&&t.queryID&&v!==void 0&&t.queryID(v),e.data===void 0&&t.data&&d!==void 0&&t.data(d),o.css.add(kr),a=Xt(d,"array"),r=90/(a.length+1),l=Math.max(d.length-Mt,0),A=d.slice(C,C+Mt),`<div class="results-pane py-1 svelte-ghf30y"><div class="scrollbox pretty-scrollbar svelte-ghf30y"><table class="text-xs svelte-ghf30y"><thead><tr><th class="py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y" style="width:10%"></th> ${Je(a,s=>`<th class="${"py-0 px-2 font-medium "+D(s.type,!0)+" svelte-ghf30y"}" style="${"width:"+D(r,!0)+"%"}"${q("evidencetype",s.evidenceColumnType?.evidenceType||"unavailable",0)}${q("evidencetypefidelity",s.evidenceColumnType?.typeFidelity||"unavailable",0)}>${D(s.id)} </th>`)} </tr><tr></tr> <tr class="type-indicator svelte-ghf30y"><th class="py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y" style="width:10%"></th> ${Je(a,s=>`<th class="${D(s.type,!0)+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"}" style="${"width:"+D(r,!0)+"%"}"${q("evidencetype",s.evidenceColumnType?.evidenceType||"unavailable",0)}${q("evidencetypefidelity",s.evidenceColumnType?.typeFidelity||"unavailable",0)}>${D(s.type)} </th>`)} </tr><tr></tr></thead> <tbody>${Je(A,(s,g)=>`<tr><td class="index text-base-content-muted svelte-ghf30y" style="width:10%">${g===0?` ${D((C+g+1).toLocaleString())}`:`${D((C+g+1).toLocaleString())}`}</td> ${Je(a,(c,u)=>`${s[c.id]==null?`<td class="${"text-base-content-muted "+D(a[u].type,!0)+" svelte-ghf30y"}" style="${"width:"+D(r,!0)+"%"}">${D("Ø")} </td>`:`${a[u].type==="number"?`<td class="number svelte-ghf30y" style="${"width:"+D(r,!0)+"%;"}">${D(Ue(s[c.id],a[u].format,a[u].columnUnitSummary))} </td>`:`${a[u].type==="date"?`<td class="string svelte-ghf30y" style="${"width:"+D(r,!0)+"%"}"${q("title",Ue(s[c.id],a[u].format,a[u].columnUnitSummary),0)}><div class="svelte-ghf30y">${D(Ue(s[c.id],a[u].format,a[u].columnUnitSummary))}</div> </td>`:`${a[u].type==="string"?`<td class="string svelte-ghf30y" style="${"width:"+D(r,!0)+"%"}"${q("title",s[c.id],0)}><div class="svelte-ghf30y">${D(s[c.id]||"Ø")}</div> </td>`:`${a[u].type==="boolean"?`<td class="boolean svelte-ghf30y" style="${"width:"+D(r,!0)+"%"}"${q("title",s[c.id],0)}><div class="svelte-ghf30y">${D(s[c.id]??"Ø")}</div> </td>`:`<td class="other svelte-ghf30y" style="${"width:"+D(r,!0)+"%"}">${D(s[c.id]||"Ø")} </td>`}`}`}`}`}`)} </tr>`)}</tbody></table></div> ${l>0?`<div class="pagination svelte-ghf30y"><input type="range"${q("max",l,0)} step="1" class="slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"${q("value",C,0)}> <span class="text-xs svelte-ghf30y">${D((C+Mt).toLocaleString())} of ${D((l+Mt).toLocaleString())}</span></div>`:""} <div class="footer svelte-ghf30y">${k(Eo,"DownloadData").$$render(o,{class:"download-button",data:d,queryID:v,display:!0},{},{})}</div> </div>`}),Tr={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},Mr={code:"code.svelte-re3fhx{display:block}",map:`{"version":3,"file":"Prismjs.svelte","sources":["Prismjs.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport './prismtheme.css';\\n\\timport Prism from 'prismjs';\\n\\timport { prism_sql } from './prism-sql';\\n\\n\\texport let code = '';\\n<\/script>\\n\\n<pre class=\\"text-xs max-h-56 overflow-auto pretty-scrollbar\\">\\n  <code class=\\"language-sql\\">{@html Prism.highlight(code, prism_sql)}</code>\\n</pre>\\n\\n<style>\\n\\tcode {\\n\\t\\tdisplay: block; /* inline-block has odd behavior when it overflows on webkit mobile */\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAiBC,kBAAK,CACJ,OAAO,CAAE,KACV"}`},Ba=P((o,e,t,n)=>{let{code:a=""}=e;return e.code===void 0&&t.code&&a!==void 0&&t.code(a),o.css.add(Mr),`<pre class="text-xs max-h-56 overflow-auto pretty-scrollbar">  <code class="language-sql svelte-re3fhx"><!-- HTML_TAG_START -->${Qn.highlight(a,Tr)}<!-- HTML_TAG_END --></code>
</pre>`}),Dr={code:"div.toggle.svelte-ska6l4{background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);padding:6px 0 10px 12px;font-family:var(--ui-font-family);font-size:10px;user-select:none;-webkit-user-select:none;-moz-user-select:none}button.svelte-ska6l4{padding:2px 4px 2px 4px;border-radius:3px;cursor:pointer;background-color:transparent;font-size:1em;font-weight:600}button.off.svelte-ska6l4{border:1px solid var(--base-300);transition:all 400ms}button.off.svelte-ska6l4:hover{background-color:var(--base-300);transition:all 400ms}",map:`{"version":3,"file":"CompilerToggle.svelte","sources":["CompilerToggle.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide } from 'svelte/transition';\\n\\n\\texport let showCompiled;\\n\\n\\tconst toggleCompiled = function () {\\n\\t\\tshowCompiled = !showCompiled;\\n\\t};\\n<\/script>\\n\\n<div class=\\"toggle\\" transition:slide|local>\\n\\t{#if showCompiled}\\n\\t\\t<button class=\\"text-info bg-info/10 border border-info\\">Compiled</button>\\n\\t\\t<button on:click={toggleCompiled} class=\\"off\\">Written</button>\\n\\t{:else}\\n\\t\\t<button on:click={toggleCompiled} class=\\"off\\">Compiled</button>\\n\\t\\t<button class=\\"text-info bg-info/10 border border-info\\">Written</button>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\tdiv.toggle {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tpadding: 6px 0 10px 12px;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-size: 10px;\\n\\t\\tuser-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-moz-user-select: none;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tpadding: 2px 4px 2px 4px;\\n\\t\\tborder-radius: 3px;\\n\\t\\tcursor: pointer;\\n\\t\\tbackground-color: transparent;\\n\\t\\tfont-size: 1em;\\n\\t\\tfont-weight: 600;\\n\\t}\\n\\n\\tbutton.off {\\n\\t\\tborder: 1px solid var(--base-300);\\n\\t\\ttransition: all 400ms;\\n\\t}\\n\\n\\tbutton.off:hover {\\n\\t\\tbackground-color: var(--base-300);\\n\\t\\ttransition: all 400ms;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAyBC,GAAG,qBAAQ,CACV,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,OAAO,CAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CACxB,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,IAAI,CACjB,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IACnB,CAEA,oBAAO,CACN,OAAO,CAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CACxB,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,GAAG,CACd,WAAW,CAAE,GACd,CAEA,MAAM,kBAAK,CACV,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACjC,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,MAAM,kBAAI,MAAO,CAChB,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,UAAU,CAAE,GAAG,CAAC,KACjB"}`},zr=P((o,e,t,n)=>{let{showCompiled:a}=e;return e.showCompiled===void 0&&t.showCompiled&&a!==void 0&&t.showCompiled(a),o.css.add(Dr),`<div class="toggle svelte-ska6l4">${a?'<button class="text-info bg-info/10 border border-info svelte-ska6l4" data-svelte-h="svelte-wrfleh">Compiled</button> <button class="off svelte-ska6l4" data-svelte-h="svelte-v36xno">Written</button>':'<button class="off svelte-ska6l4" data-svelte-h="svelte-1vzm9jy">Compiled</button> <button class="text-info bg-info/10 border border-info svelte-ska6l4" data-svelte-h="svelte-qu81ez">Written</button>'} </div>`}),Lr={code:":root{--scrollbar-track-color:transparent;--scrollbar-color:rgba(0, 0, 0, 0.2);--scrollbar-active-color:rgba(0, 0, 0, 0.4);--scrollbar-size:0.75rem;--scrollbar-minlength:1.5rem}.code-container.svelte-1ursthx{background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);overflow-x:auto;overflow-y:hidden;padding-top:0;padding-right:12px;padding-bottom:6px;padding-left:15px;scrollbar-width:thin;scrollbar-color:var(--scrollbar-color) var(--scrollbar-track-color)}.code-container.svelte-1ursthx::-webkit-scrollbar{height:var(--scrollbar-size);width:var(--scrollbar-size)}.code-container.svelte-1ursthx::-webkit-scrollbar-track{background-color:var(--scrollbar-track-color)}.over-container.svelte-1ursthx{overflow-y:hidden;overflow-x:auto}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb{background-color:var(--scrollbar-color);border-radius:7px;background-clip:padding-box}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:hover{background-color:var(--scrollbar-active-color)}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:vertical{min-height:var(--scrollbar-minlength);border:3px solid transparent}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:horizontal{min-width:var(--scrollbar-minlength);border:3px solid transparent}.status-bar.svelte-1ursthx{margin-top:0px;margin-bottom:0px;background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);border-bottom:1px solid var(--base-300);overflow-x:auto;overflow-y:hidden;scrollbar-width:thin;scrollbar-color:var(--scrollbar-color) var(--scrollbar-track-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar{height:var(--scrollbar-size);width:var(--scrollbar-size)}.status-bar.svelte-1ursthx::-webkit-scrollbar-track{background-color:var(--scrollbar-track-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb{background-color:var(--scrollbar-color);border-radius:7px;background-clip:padding-box}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:hover{background-color:var(--scrollbar-active-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:vertical{min-height:var(--scrollbar-minlength);border:3px solid transparent}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:horizontal{min-width:var(--scrollbar-minlength);border:3px solid transparent}.closed.svelte-1ursthx{border-bottom-left-radius:6px;border-bottom-right-radius:6px}.open.svelte-1ursthx{border-bottom-left-radius:0px;border-bottom-right-radius:0px}.status-bar.success.svelte-1ursthx{color:var(--info);cursor:pointer}.status-bar.error.svelte-1ursthx{color:var(--negative);-webkit-user-select:all;-moz-user-select:all;user-select:all;cursor:auto}button.svelte-1ursthx{font-family:var(--ui-font-family-compact);-webkit-font-smoothing:antialiased;font-size:12px;-webkit-user-select:none;user-select:none;white-space:nowrap;text-align:left;width:100%;background-color:var(--base-200);border:none;border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);margin-bottom:0px;cursor:pointer;padding:5px}button.title.svelte-1ursthx{border-top:1px solid var(--base-300);border-top-left-radius:6px;border-top-right-radius:6px}.scrollbox.svelte-1ursthx{display:flex;flex-direction:column}.container-a.svelte-1ursthx{background-color:var(--base-200);border-top-left-radius:6px;border-top-right-radius:6px;box-sizing:border-box;display:flex;flex-direction:column}@media print{.scrollbox.svelte-1ursthx{break-inside:avoid}}",map:`{"version":3,"file":"QueryViewer.svelte","sources":["QueryViewer.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide, blur } from 'svelte/transition';\\n\\timport DataTable from './QueryViewerSupport/QueryDataTable.svelte';\\n\\timport ChevronToggle from './ChevronToggle.svelte';\\n\\timport Prism from './QueryViewerSupport/Prismjs.svelte';\\n\\timport { showQueries, localStorageStore } from '@evidence-dev/component-utilities/stores';\\n\\timport CompilerToggle from './QueryViewerSupport/CompilerToggle.svelte';\\n\\timport { page } from '$app/stores';\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\n\\texport let queryID;\\n\\t/** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n\\texport let queryResult;\\n\\n\\t$: pageQueries = $page.data.evidencemeta.queries;\\n\\n\\t// Title & Query Toggle\\n\\tlet showSQL = localStorageStore('showSQL_'.concat(queryID), false);\\n\\t// Query text & Compiler Toggle\\n\\tlet showResults = localStorageStore(\`showResults_\${queryID}\`);\\n\\n\\tconst toggleSQL = function () {\\n\\t\\t$showSQL = !$showSQL;\\n\\t};\\n\\n\\tconst toggleResults = function () {\\n\\t\\tif (!error && $queryResult.length > 0) {\\n\\t\\t\\t$showResults = !$showResults;\\n\\t\\t}\\n\\t};\\n\\n\\tlet inputQuery;\\n\\tlet showCompilerToggle;\\n\\tlet showCompiled = true;\\n\\t/** @type {undefined | Error } */\\n\\tlet error = undefined;\\n\\n\\t// Enter an error state if the queryResult isn't defined\\n\\t$: {\\n\\t\\tif (!$queryResult) error = new Error('queryResult is undefined');\\n\\t\\telse error = $queryResult.error;\\n\\t}\\n\\n\\t$: rowCount = $queryResult?.length ?? 0;\\n\\t$: colCount = $queryResult.columns.length ?? $queryResult?._evidenceColumnTypes.length ?? 0;\\n\\n\\t$: {\\n\\t\\tlet query = pageQueries?.find((d) => d.id === queryID);\\n\\n\\t\\tif (query) {\\n\\t\\t\\tinputQuery = query.inputQueryString;\\n\\t\\t\\tshowCompilerToggle = query.compiled && query.compileError === undefined;\\n\\t\\t}\\n\\t}\\n\\n\\tconst { theme } = getThemeStores();\\n<\/script>\\n\\n<div class=\\"over-container\\" in:blur|local>\\n\\t{#if $showQueries}\\n\\t\\t<!-- Title -->\\n\\t\\t<div class=\\"scrollbox my-3\\" transition:slide|local>\\n\\t\\t\\t<div class=\\"container-a\\">\\n\\t\\t\\t\\t<button type=\\"button\\" aria-label=\\"show-sql\\" on:click={toggleSQL} class=\\"title\\">\\n\\t\\t\\t\\t\\t<ChevronToggle toggled={$showSQL} />\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t<!-- Compile Toggle  -->\\n\\t\\t\\t\\t{#if $showSQL && showCompilerToggle}\\n\\t\\t\\t\\t\\t<CompilerToggle bind:showCompiled />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t<!-- Query Display -->\\n\\t\\t\\t\\t{#if $showSQL}\\n\\t\\t\\t\\t\\t<div class=\\"code-container\\" transition:slide|local>\\n\\t\\t\\t\\t\\t\\t{#if showCompiled}\\n\\t\\t\\t\\t\\t\\t\\t<Prism code={queryResult.originalText} />\\n\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t<Prism code={inputQuery} />\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t\\t<!-- Status -->\\n\\t\\t\\t<button\\n\\t\\t\\t\\ttype=\\"button\\"\\n\\t\\t\\t\\taria-label=\\"view-query\\"\\n\\t\\t\\t\\tclass={'status-bar'}\\n\\t\\t\\t\\tclass:error\\n\\t\\t\\t\\tclass:success={!error}\\n\\t\\t\\t\\tclass:open={$showResults}\\n\\t\\t\\t\\tclass:closed={!$showResults}\\n\\t\\t\\t\\ton:click={toggleResults}\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#if error}\\n\\t\\t\\t\\t\\t{error.message}\\n\\t\\t\\t\\t{:else if rowCount}\\n\\t\\t\\t\\t\\t<ChevronToggle toggled={$showResults} color={$theme.colors['info']} />\\n\\t\\t\\t\\t\\t{rowCount.toLocaleString()}\\n\\t\\t\\t\\t\\t{rowCount > 1 ? 'records' : 'record'} with {colCount.toLocaleString()}\\n\\t\\t\\t\\t\\t{colCount > 1 ? 'properties' : 'property'}\\n\\t\\t\\t\\t{:else if $queryResult.loading}\\n\\t\\t\\t\\t\\tloading...\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\tran successfully but no data was returned\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t<!-- Results -->\\n\\t\\t\\t</button>\\n\\t\\t\\t{#if rowCount > 0 && !error && $showResults}\\n\\t\\t\\t\\t<DataTable data={queryResult} {queryID} />\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\t:root {\\n\\t\\t--scrollbar-track-color: transparent;\\n\\t\\t--scrollbar-color: rgba(0, 0, 0, 0.2);\\n\\t\\t--scrollbar-active-color: rgba(0, 0, 0, 0.4);\\n\\t\\t--scrollbar-size: 0.75rem;\\n\\t\\t--scrollbar-minlength: 1.5rem; /* Minimum length of scrollbar thumb (width of horizontal, height of vertical) */\\n\\t}\\n\\n\\t.code-container {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\toverflow-x: auto;\\n\\t\\toverflow-y: hidden;\\n\\t\\tpadding-top: 0;\\n\\t\\tpadding-right: 12px;\\n\\t\\tpadding-bottom: 6px;\\n\\t\\tpadding-left: 15px;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: var(--scrollbar-color) var(--scrollbar-track-color);\\n\\t}\\n\\t.code-container::-webkit-scrollbar {\\n\\t\\theight: var(--scrollbar-size);\\n\\t\\twidth: var(--scrollbar-size);\\n\\t}\\n\\t.code-container::-webkit-scrollbar-track {\\n\\t\\tbackground-color: var(--scrollbar-track-color);\\n\\t}\\n\\n\\t.over-container {\\n\\t\\toverflow-y: hidden;\\n\\t\\toverflow-x: auto;\\n\\t}\\n\\n\\t.code-container::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: var(--scrollbar-color);\\n\\t\\tborder-radius: 7px;\\n\\t\\tbackground-clip: padding-box;\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:hover {\\n\\t\\tbackground-color: var(--scrollbar-active-color);\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:vertical {\\n\\t\\tmin-height: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:horizontal {\\n\\t\\tmin-width: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\n\\t.status-bar {\\n\\t\\tmargin-top: 0px;\\n\\t\\tmargin-bottom: 0px;\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t\\toverflow-x: auto;\\n\\t\\toverflow-y: hidden;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: var(--scrollbar-color) var(--scrollbar-track-color);\\n\\t}\\n\\n\\t.status-bar::-webkit-scrollbar {\\n\\t\\theight: var(--scrollbar-size);\\n\\t\\twidth: var(--scrollbar-size);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-track {\\n\\t\\tbackground-color: var(--scrollbar-track-color);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: var(--scrollbar-color);\\n\\t\\tborder-radius: 7px;\\n\\t\\tbackground-clip: padding-box;\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:hover {\\n\\t\\tbackground-color: var(--scrollbar-active-color);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:vertical {\\n\\t\\tmin-height: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:horizontal {\\n\\t\\tmin-width: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\n\\t.closed {\\n\\t\\tborder-bottom-left-radius: 6px;\\n\\t\\tborder-bottom-right-radius: 6px;\\n\\t}\\n\\n\\t.open {\\n\\t\\tborder-bottom-left-radius: 0px;\\n\\t\\tborder-bottom-right-radius: 0px;\\n\\t}\\n\\n\\t.status-bar.success {\\n\\t\\tcolor: var(--info);\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.status-bar.error {\\n\\t\\tcolor: var(--negative);\\n\\t\\t-webkit-user-select: all;\\n\\t\\t-moz-user-select: all;\\n\\t\\tuser-select: all;\\n\\t\\tcursor: auto;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tfont-family: var(--ui-font-family-compact);\\n\\t\\t-webkit-font-smoothing: antialiased;\\n\\t\\tfont-size: 12px;\\n\\t\\t-webkit-user-select: none;\\n\\t\\tuser-select: none;\\n\\t\\twhite-space: nowrap;\\n\\t\\ttext-align: left;\\n\\t\\twidth: 100%;\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder: none;\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tmargin-bottom: 0px;\\n\\t\\tcursor: pointer;\\n\\t\\tpadding: 5px;\\n\\t}\\n\\n\\tbutton.title {\\n\\t\\tborder-top: 1px solid var(--base-300);\\n\\t\\tborder-top-left-radius: 6px;\\n\\t\\tborder-top-right-radius: 6px;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t}\\n\\n\\t.container-a {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-top-left-radius: 6px;\\n\\t\\tborder-top-right-radius: 6px;\\n\\t\\tbox-sizing: border-box;\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t}\\n\\t/* container-a avoids whitespace appearing in the slide transition */\\n\\n\\t@media print {\\n\\t\\t.scrollbox {\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAuHC,KAAM,CACL,uBAAuB,CAAE,WAAW,CACpC,iBAAiB,CAAE,kBAAkB,CACrC,wBAAwB,CAAE,kBAAkB,CAC5C,gBAAgB,CAAE,OAAO,CACzB,qBAAqB,CAAE,MACxB,CAEA,8BAAgB,CACf,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,MAAM,CAClB,WAAW,CAAE,CAAC,CACd,aAAa,CAAE,IAAI,CACnB,cAAc,CAAE,GAAG,CACnB,YAAY,CAAE,IAAI,CAClB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,IAAI,iBAAiB,CAAC,CAAC,IAAI,uBAAuB,CACpE,CACA,8BAAe,mBAAoB,CAClC,MAAM,CAAE,IAAI,gBAAgB,CAAC,CAC7B,KAAK,CAAE,IAAI,gBAAgB,CAC5B,CACA,8BAAe,yBAA0B,CACxC,gBAAgB,CAAE,IAAI,uBAAuB,CAC9C,CAEA,8BAAgB,CACf,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,IACb,CAEA,8BAAe,yBAA0B,CACxC,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,WAClB,CACA,8BAAe,yBAAyB,MAAO,CAC9C,gBAAgB,CAAE,IAAI,wBAAwB,CAC/C,CACA,8BAAe,yBAAyB,SAAU,CACjD,UAAU,CAAE,IAAI,qBAAqB,CAAC,CACtC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CACA,8BAAe,yBAAyB,WAAY,CACnD,SAAS,CAAE,IAAI,qBAAqB,CAAC,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,0BAAY,CACX,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,IAAI,iBAAiB,CAAC,CAAC,IAAI,uBAAuB,CACpE,CAEA,0BAAW,mBAAoB,CAC9B,MAAM,CAAE,IAAI,gBAAgB,CAAC,CAC7B,KAAK,CAAE,IAAI,gBAAgB,CAC5B,CACA,0BAAW,yBAA0B,CACpC,gBAAgB,CAAE,IAAI,uBAAuB,CAC9C,CACA,0BAAW,yBAA0B,CACpC,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,WAClB,CACA,0BAAW,yBAAyB,MAAO,CAC1C,gBAAgB,CAAE,IAAI,wBAAwB,CAC/C,CACA,0BAAW,yBAAyB,SAAU,CAC7C,UAAU,CAAE,IAAI,qBAAqB,CAAC,CACtC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CACA,0BAAW,yBAAyB,WAAY,CAC/C,SAAS,CAAE,IAAI,qBAAqB,CAAC,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,sBAAQ,CACP,yBAAyB,CAAE,GAAG,CAC9B,0BAA0B,CAAE,GAC7B,CAEA,oBAAM,CACL,yBAAyB,CAAE,GAAG,CAC9B,0BAA0B,CAAE,GAC7B,CAEA,WAAW,uBAAS,CACnB,KAAK,CAAE,IAAI,MAAM,CAAC,CAClB,MAAM,CAAE,OACT,CAEA,WAAW,qBAAO,CACjB,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,mBAAmB,CAAE,GAAG,CACxB,gBAAgB,CAAE,GAAG,CACrB,WAAW,CAAE,GAAG,CAChB,MAAM,CAAE,IACT,CAEA,qBAAO,CACN,WAAW,CAAE,IAAI,wBAAwB,CAAC,CAC1C,sBAAsB,CAAE,WAAW,CACnC,SAAS,CAAE,IAAI,CACf,mBAAmB,CAAE,IAAI,CACzB,WAAW,CAAE,IAAI,CACjB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,GACV,CAEA,MAAM,qBAAO,CACZ,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACrC,sBAAsB,CAAE,GAAG,CAC3B,uBAAuB,CAAE,GAC1B,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAEA,2BAAa,CACZ,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,sBAAsB,CAAE,GAAG,CAC3B,uBAAuB,CAAE,GAAG,CAC5B,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAGA,OAAO,KAAM,CACZ,yBAAW,CACV,YAAY,CAAE,KACf,CACD"}`},Oa=P((o,e,t,n)=>{let a,r,l,A,v,d,C,y,f,s,g,c,u,E,M;g=U(ko,h=>s=h),u=U(vn,h=>c=h);let{queryID:w}=e,{queryResult:S}=e;v=U(S,h=>A=h);let T=la("showSQL_".concat(w),!1);f=U(T,h=>y=h);let I=la();C=U(I,h=>d=h);let z,O,B=!0,m;const{theme:i}=At();M=U(i,h=>E=h),e.queryID===void 0&&t.queryID&&w!==void 0&&t.queryID(w),e.queryResult===void 0&&t.queryResult&&S!==void 0&&t.queryResult(S),o.css.add(Lr);let b,p,x=o.head;do{b=!0,o.head=x,a=s.data.evidencemeta.queries,A?m=A.error:m=new Error("queryResult is undefined"),r=A?.length??0,l=A.columns.length??A?._evidenceColumnTypes.length??0;{let h=a?.find(N=>N.id===w);h&&(z=h.inputQueryString,O=h.compiled&&h.compileError===void 0)}p=`<div class="over-container svelte-1ursthx">${c?` <div class="scrollbox my-3 svelte-1ursthx"><div class="container-a svelte-1ursthx"><button type="button" aria-label="show-sql" class="title svelte-1ursthx">${k(Ia,"ChevronToggle").$$render(o,{toggled:y},{},{})} ${D(w)}</button>  ${y&&O?`${k(zr,"CompilerToggle").$$render(o,{showCompiled:B},{showCompiled:h=>{B=h,b=!1}},{})}`:""}  ${y?`<div class="code-container svelte-1ursthx">${B?`${k(Ba,"Prism").$$render(o,{code:S.originalText},{},{})}`:`${k(Ba,"Prism").$$render(o,{code:z},{},{})}`}</div>`:""}</div>  <button type="button" aria-label="view-query" class="${[D(ka("status-bar"),!0)+" svelte-1ursthx",(m?"error":"")+" "+(m?"":"success")+" "+(d?"open":"")+" "+(d?"":"closed")].join(" ").trim()}">${m?`${D(m.message)}`:`${r?`${k(Ia,"ChevronToggle").$$render(o,{toggled:d,color:E.colors.info},{},{})} ${D(r.toLocaleString())} ${D(r>1?"records":"record")} with ${D(l.toLocaleString())} ${D(l>1?"properties":"property")}`:`${A.loading?"loading...":"ran successfully but no data was returned"}`}`} </button> ${r>0&&!m&&d?`${k(_r,"DataTable").$$render(o,{data:S,queryID:w},{},{})}`:""}</div>`:""} </div>`}while(!b);return v(),C(),f(),g(),u(),M(),p}),Gr=P((o,e,t,n)=>{let a,r,l,A,v,d=De,C=()=>(d(),d=U(a,m=>m),a);const{resolveColorsObject:y}=At();let{config:f=void 0}=e,{height:s="291px"}=e,{width:g="100%"}=e,{copying:c=!1}=e,{printing:u=!1}=e,{echartsOptions:E=void 0}=e,{seriesOptions:M=void 0}=e,{seriesColors:w=void 0}=e,{isMap:S=!1}=e,{extraHeight:T=void 0}=e,I=!1,z,O;const B=Gt("gridConfig");return B&&(I=!0,{cols:z,gapWidth:O}=B),e.config===void 0&&t.config&&f!==void 0&&t.config(f),e.height===void 0&&t.height&&s!==void 0&&t.height(s),e.width===void 0&&t.width&&g!==void 0&&t.width(g),e.copying===void 0&&t.copying&&c!==void 0&&t.copying(c),e.printing===void 0&&t.printing&&u!==void 0&&t.printing(u),e.echartsOptions===void 0&&t.echartsOptions&&E!==void 0&&t.echartsOptions(E),e.seriesOptions===void 0&&t.seriesOptions&&M!==void 0&&t.seriesOptions(M),e.seriesColors===void 0&&t.seriesColors&&w!==void 0&&t.seriesColors(w),e.isMap===void 0&&t.isMap&&S!==void 0&&t.isMap(S),e.extraHeight===void 0&&t.extraHeight&&T!==void 0&&t.extraHeight(T),C(a=y(w)),r=Math.min(Number(z),2),l=(650-Number(O)*(r-1))/r,A=Math.min(Number(z),3),v=(841-Number(O)*(A-1))/A,d(),`${c?`<div class="chart" style="${"height: "+D(s,!0)+"; width: "+D(g,!0)+"; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`:`${u?`${I?`<div class="chart md:hidden" style="${"height: "+D(s,!0)+"; width: "+D(l,!0)+"px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div> <div class="chart hidden md:block" style="${"height: "+D(s,!0)+"; width: "+D(v,!0)+"px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`:`<div class="chart md:hidden" style="${"height: "+D(s,!0)+"; width: 650px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div> <div class="chart hidden md:block" style="${"height: "+D(s,!0)+"; width: 841px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`}`:""}`}`}),Ur=P((o,e,t,n)=>{let{height:a="231px"}=e;return e.height===void 0&&t.height&&a!==void 0&&t.height(a),`<div role="status" class="animate-pulse"><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="bg-base-100 rounded-md max-w-[100%]" style="${"height:"+D(a,!0)+"; margin-top: 15px; margin-bottom: 31px;"}"></div></div>`}),Rr={code:"@media print{.chart.svelte-db4qxn{-moz-column-break-inside:avoid;break-inside:avoid}.chart-container.svelte-db4qxn{padding:0}}.chart.svelte-db4qxn{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none}.chart-footer.svelte-db4qxn{display:flex;justify-content:flex-end;align-items:center;margin:3px 12px;font-size:12px;height:9px}",map:`{"version":3,"file":"ECharts.svelte","sources":["ECharts.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { browser } from '$app/environment';\\n\\timport echarts from '@evidence-dev/component-utilities/echarts';\\n\\timport echartsCanvasDownload from '@evidence-dev/component-utilities/echartsCanvasDownload';\\n\\timport EChartsCopyTarget from './EChartsCopyTarget.svelte';\\n\\timport DownloadData from '../../ui/DownloadData.svelte';\\n\\timport CodeBlock from '../../ui/CodeBlock.svelte';\\n\\timport ChartLoading from '../../ui/ChartLoading.svelte';\\n\\timport { flush } from 'svelte/internal';\\n\\timport { createEventDispatcher } from 'svelte';\\n\\timport { getThemeStores } from '../../../themes/themes.js';\\n\\n\\tconst { activeAppearance, theme, resolveColorsObject } = getThemeStores();\\n\\n\\texport let config = undefined;\\n\\n\\texport let queryID = undefined;\\n\\texport let evidenceChartTitle = undefined;\\n\\n\\texport let height = '291px';\\n\\texport let width = '100%';\\n\\n\\texport let data;\\n\\n\\texport let renderer = undefined;\\n\\texport let downloadableData = undefined;\\n\\texport let downloadableImage = undefined;\\n\\texport let echartsOptions = undefined;\\n\\texport let seriesOptions = undefined;\\n\\texport let printEchartsConfig; // helper for custom chart development\\n\\n\\texport let seriesColors = undefined;\\n\\t$: seriesColorsStore = resolveColorsObject(seriesColors);\\n\\n\\texport let connectGroup = undefined;\\n\\n\\texport let xAxisLabelOverflow = undefined;\\n\\n\\tconst dispatch = createEventDispatcher();\\n\\n\\tlet downloadChart = false;\\n\\tlet copying = false;\\n\\tlet printing = false;\\n\\tlet hovering = false;\\n<\/script>\\n\\n<svelte:window\\n\\ton:copy={() => {\\n\\t\\tcopying = true;\\n\\t\\tflush();\\n\\t\\tsetTimeout(() => {\\n\\t\\t\\tcopying = false;\\n\\t\\t}, 0);\\n\\t}}\\n\\ton:beforeprint={() => (printing = true)}\\n\\ton:afterprint={() => (printing = false)}\\n\\ton:export-beforeprint={() => (printing = true)}\\n\\ton:export-afterprint={() => (printing = false)}\\n/>\\n\\n<div\\n\\trole=\\"none\\"\\n\\tclass=\\"chart-container mt-2 mb-3\\"\\n\\ton:mouseenter={() => (hovering = true)}\\n\\ton:mouseleave={() => (hovering = false)}\\n>\\n\\t{#if !printing}\\n\\t\\t{#if !browser}\\n\\t\\t\\t<ChartLoading {height} />\\n\\t\\t{:else}\\n\\t\\t\\t<div\\n\\t\\t\\t\\tclass=\\"chart\\"\\n\\t\\t\\t\\tstyle=\\"\\n\\t\\t\\t\\theight: {height};\\n\\t\\t\\t\\twidth: {width};\\n\\t\\t\\t\\toverflow: visible;\\n\\t\\t\\t\\tdisplay: {copying ? 'none' : 'inherit'}\\n\\t\\t\\t\\"\\n\\t\\t\\t\\tuse:echarts={{\\n\\t\\t\\t\\t\\tconfig,\\n\\t\\t\\t\\t\\t...$$restProps,\\n\\t\\t\\t\\t\\techartsOptions,\\n\\t\\t\\t\\t\\tseriesOptions,\\n\\t\\t\\t\\t\\tdispatch,\\n\\t\\t\\t\\t\\trenderer,\\n\\t\\t\\t\\t\\tconnectGroup,\\n\\t\\t\\t\\t\\txAxisLabelOverflow,\\n\\t\\t\\t\\t\\tseriesColors: $seriesColorsStore,\\n\\t\\t\\t\\t\\ttheme: $activeAppearance\\n\\t\\t\\t\\t}}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t{/if}\\n\\n\\t<EChartsCopyTarget\\n\\t\\t{config}\\n\\t\\t{height}\\n\\t\\t{width}\\n\\t\\t{copying}\\n\\t\\t{printing}\\n\\t\\t{echartsOptions}\\n\\t\\t{seriesOptions}\\n\\t\\tseriesColors={seriesColorsStore}\\n\\t/>\\n\\n\\t{#if downloadableData || downloadableImage}\\n\\t\\t<div class=\\"chart-footer\\">\\n\\t\\t\\t{#if downloadableImage}\\n\\t\\t\\t\\t<DownloadData\\n\\t\\t\\t\\t\\ttext=\\"Save Image\\"\\n\\t\\t\\t\\t\\tclass=\\"download-button\\"\\n\\t\\t\\t\\t\\tdownloadData={() => {\\n\\t\\t\\t\\t\\t\\tdownloadChart = true;\\n\\t\\t\\t\\t\\t\\tsetTimeout(() => {\\n\\t\\t\\t\\t\\t\\t\\tdownloadChart = false;\\n\\t\\t\\t\\t\\t\\t}, 0);\\n\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\tdisplay={hovering}\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<svg\\n\\t\\t\\t\\t\\t\\txmlns=\\"http://www.w3.org/2000/svg\\"\\n\\t\\t\\t\\t\\t\\twidth=\\"12\\"\\n\\t\\t\\t\\t\\t\\theight=\\"12\\"\\n\\t\\t\\t\\t\\t\\tviewBox=\\"0 0 24 24\\"\\n\\t\\t\\t\\t\\t\\tfill=\\"none\\"\\n\\t\\t\\t\\t\\t\\tstroke=\\"#000\\"\\n\\t\\t\\t\\t\\t\\tstroke-width=\\"2\\"\\n\\t\\t\\t\\t\\t\\tstroke-linecap=\\"round\\"\\n\\t\\t\\t\\t\\t\\tstroke-linejoin=\\"round\\"\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<rect x=\\"3\\" y=\\"3\\" width=\\"18\\" height=\\"18\\" rx=\\"2\\" />\\n\\t\\t\\t\\t\\t\\t<circle cx=\\"8.5\\" cy=\\"8.5\\" r=\\"1.5\\" />\\n\\t\\t\\t\\t\\t\\t<path d=\\"M20.4 14.5L16 10 4 20\\" />\\n\\t\\t\\t\\t\\t</svg>\\n\\t\\t\\t\\t</DownloadData>\\n\\t\\t\\t{/if}\\n\\t\\t\\t{#if data && downloadableData}\\n\\t\\t\\t\\t<DownloadData\\n\\t\\t\\t\\t\\ttext=\\"Download Data\\"\\n\\t\\t\\t\\t\\t{data}\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t\\tclass=\\"download-button\\"\\n\\t\\t\\t\\t\\tdisplay={hovering}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t{#if printEchartsConfig && !printing}\\n\\t\\t<CodeBlock source={JSON.stringify(config, undefined, 3)} copyToClipboard={true}>\\n\\t\\t\\t{JSON.stringify(config, undefined, 3)}\\n\\t\\t</CodeBlock>\\n\\t{/if}\\n</div>\\n\\n{#if downloadChart}\\n\\t<div\\n\\t\\tclass=\\"chart\\"\\n\\t\\tstyle=\\"\\n        display: none;\\n        visibility: visible;\\n        height: {height};\\n        width: 666px;\\n        margin-left: 0;\\n        margin-top: 15px;\\n        margin-bottom: 15px;\\n        overflow: visible;\\n    \\"\\n\\t\\tuse:echartsCanvasDownload={{\\n\\t\\t\\tconfig,\\n\\t\\t\\t...$$restProps,\\n\\t\\t\\techartsOptions,\\n\\t\\t\\tseriesOptions,\\n\\t\\t\\tseriesColors: $seriesColorsStore,\\n\\t\\t\\tqueryID,\\n\\t\\t\\tevidenceChartTitle,\\n\\t\\t\\ttheme: $activeAppearance,\\n\\t\\t\\tbackgroundColor: $theme.colors['base-100']\\n\\t\\t}}\\n\\t/>\\n{/if}\\n\\n<style>\\n\\t@media print {\\n\\t\\t.chart {\\n\\t\\t\\t-moz-column-break-inside: avoid;\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\n\\t\\t.chart-container {\\n\\t\\t\\tpadding: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.chart {\\n\\t\\t-moz-user-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-ms-user-select: none;\\n\\t\\t-o-user-select: none;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\t.chart-footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\talign-items: center;\\n\\t\\tmargin: 3px 12px;\\n\\t\\tfont-size: 12px;\\n\\t\\theight: 9px;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4LC,OAAO,KAAM,CACZ,oBAAO,CACN,wBAAwB,CAAE,KAAK,CAC/B,YAAY,CAAE,KACf,CAEA,8BAAiB,CAChB,OAAO,CAAE,CACV,CACD,CAEA,oBAAO,CACN,gBAAgB,CAAE,IAAI,CACtB,mBAAmB,CAAE,IAAI,CACzB,eAAe,CAAE,IAAI,CACrB,cAAc,CAAE,IAAI,CACpB,WAAW,CAAE,IACd,CAEA,2BAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,IAAI,CAChB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,GACT"}`},Nr=P((o,e,t,n)=>{let a;Ae(e,["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"]);let r=De,l=()=>(r(),r=U(a,h=>h),a),A,v;const{activeAppearance:d,theme:C,resolveColorsObject:y}=At();A=U(d,h=>h),v=U(C,h=>h);let{config:f=void 0}=e,{queryID:s=void 0}=e,{evidenceChartTitle:g=void 0}=e,{height:c="291px"}=e,{width:u="100%"}=e,{data:E}=e,{renderer:M=void 0}=e,{downloadableData:w=void 0}=e,{downloadableImage:S=void 0}=e,{echartsOptions:T=void 0}=e,{seriesOptions:I=void 0}=e,{printEchartsConfig:z}=e,{seriesColors:O=void 0}=e,{connectGroup:B=void 0}=e,{xAxisLabelOverflow:m=void 0}=e;Wa();let i=!1,b=!1,p=!1,x=!1;return e.config===void 0&&t.config&&f!==void 0&&t.config(f),e.queryID===void 0&&t.queryID&&s!==void 0&&t.queryID(s),e.evidenceChartTitle===void 0&&t.evidenceChartTitle&&g!==void 0&&t.evidenceChartTitle(g),e.height===void 0&&t.height&&c!==void 0&&t.height(c),e.width===void 0&&t.width&&u!==void 0&&t.width(u),e.data===void 0&&t.data&&E!==void 0&&t.data(E),e.renderer===void 0&&t.renderer&&M!==void 0&&t.renderer(M),e.downloadableData===void 0&&t.downloadableData&&w!==void 0&&t.downloadableData(w),e.downloadableImage===void 0&&t.downloadableImage&&S!==void 0&&t.downloadableImage(S),e.echartsOptions===void 0&&t.echartsOptions&&T!==void 0&&t.echartsOptions(T),e.seriesOptions===void 0&&t.seriesOptions&&I!==void 0&&t.seriesOptions(I),e.printEchartsConfig===void 0&&t.printEchartsConfig&&z!==void 0&&t.printEchartsConfig(z),e.seriesColors===void 0&&t.seriesColors&&O!==void 0&&t.seriesColors(O),e.connectGroup===void 0&&t.connectGroup&&B!==void 0&&t.connectGroup(B),e.xAxisLabelOverflow===void 0&&t.xAxisLabelOverflow&&m!==void 0&&t.xAxisLabelOverflow(m),o.css.add(Rr),l(a=y(O)),r(),A(),v(),` <div role="none" class="chart-container mt-2 mb-3 svelte-db4qxn">${`${`${k(Ur,"ChartLoading").$$render(o,{height:c},{},{})}`}`} ${k(Gr,"EChartsCopyTarget").$$render(o,{config:f,height:c,width:u,copying:b,printing:p,echartsOptions:T,seriesOptions:I,seriesColors:a},{},{})} ${w||S?`<div class="chart-footer svelte-db4qxn">${S?`${k(Eo,"DownloadData").$$render(o,{text:"Save Image",class:"download-button",downloadData:()=>{i=!0,setTimeout(()=>{i=!1},0)},display:x,queryID:s},{},{default:()=>'<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><path d="M20.4 14.5L16 10 4 20"></path></svg>'})}`:""} ${E&&w?`${k(Eo,"DownloadData").$$render(o,{text:"Download Data",data:E,queryID:s,class:"download-button",display:x},{},{})}`:""}</div>`:""} ${z?`${k(gn,"CodeBlock").$$render(o,{source:JSON.stringify(f,void 0,3),copyToClipboard:!0},{},{default:()=>`${D(JSON.stringify(f,void 0,3))}`})}`:""}</div> ${i?`<div class="chart svelte-db4qxn" style="${"display: none; visibility: visible; height: "+D(c,!0)+"; width: 666px; margin-left: 0; margin-top: 15px; margin-bottom: 15px; overflow: visible;"}"></div>`:""}`});function Ut(o,e){const t=new Set(o.map(n=>n[e]));return Array.from(t)}function Pr(o,e){return it(o,Fn({count:qn(e)}))[0].count}function Kr(o,e,t){let n;if(typeof t!="object")n=it(o,wo(e,ma({xTotal:Io(t)})),po({percentOfX:ha(t,"xTotal")}),ya({percentOfX:t+"_pct"}));else{n=it(o,po({valueSum:0}));for(let a=0;a<n.length;a++){n[a].valueSum=0;for(let r=0;r<t.length;r++)n[a].valueSum=n[a].valueSum+n[a][t[r]]}n=it(n,wo(e,ma({xTotal:Io("valueSum")})));for(let a=0;a<t.length;a++)n=it(n,po({percentOfX:ha(t[a],"xTotal")}),ya({percentOfX:t[a]+"_pct"}))}return n}function zt(o,e,t){return[...o].sort((n,a)=>(n[e]<a[e]?-1:1)*(t?1:-1))}function Pa(o,e,t){const n=e+t;return o%n<e?0:1}const Fr=P((o,e,t,n)=>{let a,r,l,A,v,d=De,C=()=>(d(),d=U(l,we=>v=we),l),y,f,s,g=De,c=()=>(g(),g=U(r,we=>s=we),r),u,E=De,M=()=>(E(),E=U(a,we=>u=we),a),w,S,T=Jt({}),I=Jt({});S=U(I,we=>w=we);const{theme:z,resolveColor:O,resolveColorsObject:B,resolveColorPalette:m}=At();f=U(z,we=>y=we);let{data:i=void 0}=e,{queryID:b=void 0}=e,{x:p=void 0}=e,{y:x=void 0}=e,{y2:h=void 0}=e,{series:N=void 0}=e,{size:K=void 0}=e,{tooltipTitle:X=void 0}=e,{showAllXAxisLabels:R=void 0}=e,{printEchartsConfig:de=!1}=e,Ie=!!x,Re=!!p,{swapXY:G=!1}=e,{title:_e=void 0}=e,{subtitle:Te=void 0}=e,{chartType:ue="Chart"}=e,{bubble:Ce=!1}=e,{hist:ce=!1}=e,{boxplot:Oe=!1}=e,ge,{xType:V=void 0}=e,{xAxisTitle:te="false"}=e,{xBaseline:oe=!0}=e,{xTickMarks:Z=!1}=e,{xGridlines:fe=!1}=e,{xAxisLabels:ee=!0}=e,{sort:pe=!0}=e,{xFmt:be=void 0}=e,{xMin:xe=void 0}=e,{xMax:le=void 0}=e,{yLog:ae=!1}=e,{yType:ve=ae===!0?"log":"value"}=e,{yLogBase:me=10}=e,{yAxisTitle:ie="false"}=e,{yBaseline:H=!1}=e,{yTickMarks:he=!1}=e,{yGridlines:se=!0}=e,{yAxisLabels:ne=!0}=e,{yMin:Be=void 0}=e,{yMax:_=void 0}=e,{yScale:F=!1}=e,{yFmt:$=void 0}=e,{yAxisColor:Le="true"}=e,{y2AxisTitle:Ee="false"}=e,{y2Baseline:W=!1}=e,{y2TickMarks:re=!1}=e,{y2Gridlines:Me=!0}=e,{y2AxisLabels:Ge=!0}=e,{y2Min:He=void 0}=e,{y2Max:$e=void 0}=e,{y2Scale:Pe=!1}=e,{y2Fmt:Ke=void 0}=e,{y2AxisColor:et="true"}=e,{sizeFmt:Ye=void 0}=e,{colorPalette:tt="default"}=e,{legend:Se=void 0}=e,{echartsOptions:ot=void 0}=e,{seriesOptions:at=void 0}=e,{seriesColors:nt=void 0}=e,{stackType:rt=void 0}=e,{stacked100:Fe=!1}=e,{chartAreaHeight:Ne}=e,{renderer:oo=void 0}=e,{downloadableData:gt=!0}=e,{downloadableImage:pt=!0}=e,{connectGroup:ao=void 0}=e,{leftPadding:no=void 0}=e,{rightPadding:ro=void 0}=e,{xLabelWrap:bt=!1}=e;const Fa=bt?"break":"truncate";let Y,lo,io=[],xt=[],To,Nt,qe,Mo,We,ke,Ve,so,Do,Pt,Ao,zo,co,Et,Lo,Go,Kt,wt,Uo,Ro,No,Po,Ko,Fo,qo,Wo,jo,Qo,Ho,It,Ft,qt,uo,Co,Yo,Vo,Bt,Xo,Zo,fo,Jo,vo,dt=[],Ot=!0,Xe=[],Wt=[],Ze,ct,$o,ut;e.data===void 0&&t.data&&i!==void 0&&t.data(i),e.queryID===void 0&&t.queryID&&b!==void 0&&t.queryID(b),e.x===void 0&&t.x&&p!==void 0&&t.x(p),e.y===void 0&&t.y&&x!==void 0&&t.y(x),e.y2===void 0&&t.y2&&h!==void 0&&t.y2(h),e.series===void 0&&t.series&&N!==void 0&&t.series(N),e.size===void 0&&t.size&&K!==void 0&&t.size(K),e.tooltipTitle===void 0&&t.tooltipTitle&&X!==void 0&&t.tooltipTitle(X),e.showAllXAxisLabels===void 0&&t.showAllXAxisLabels&&R!==void 0&&t.showAllXAxisLabels(R),e.printEchartsConfig===void 0&&t.printEchartsConfig&&de!==void 0&&t.printEchartsConfig(de),e.swapXY===void 0&&t.swapXY&&G!==void 0&&t.swapXY(G),e.title===void 0&&t.title&&_e!==void 0&&t.title(_e),e.subtitle===void 0&&t.subtitle&&Te!==void 0&&t.subtitle(Te),e.chartType===void 0&&t.chartType&&ue!==void 0&&t.chartType(ue),e.bubble===void 0&&t.bubble&&Ce!==void 0&&t.bubble(Ce),e.hist===void 0&&t.hist&&ce!==void 0&&t.hist(ce),e.boxplot===void 0&&t.boxplot&&Oe!==void 0&&t.boxplot(Oe),e.xType===void 0&&t.xType&&V!==void 0&&t.xType(V),e.xAxisTitle===void 0&&t.xAxisTitle&&te!==void 0&&t.xAxisTitle(te),e.xBaseline===void 0&&t.xBaseline&&oe!==void 0&&t.xBaseline(oe),e.xTickMarks===void 0&&t.xTickMarks&&Z!==void 0&&t.xTickMarks(Z),e.xGridlines===void 0&&t.xGridlines&&fe!==void 0&&t.xGridlines(fe),e.xAxisLabels===void 0&&t.xAxisLabels&&ee!==void 0&&t.xAxisLabels(ee),e.sort===void 0&&t.sort&&pe!==void 0&&t.sort(pe),e.xFmt===void 0&&t.xFmt&&be!==void 0&&t.xFmt(be),e.xMin===void 0&&t.xMin&&xe!==void 0&&t.xMin(xe),e.xMax===void 0&&t.xMax&&le!==void 0&&t.xMax(le),e.yLog===void 0&&t.yLog&&ae!==void 0&&t.yLog(ae),e.yType===void 0&&t.yType&&ve!==void 0&&t.yType(ve),e.yLogBase===void 0&&t.yLogBase&&me!==void 0&&t.yLogBase(me),e.yAxisTitle===void 0&&t.yAxisTitle&&ie!==void 0&&t.yAxisTitle(ie),e.yBaseline===void 0&&t.yBaseline&&H!==void 0&&t.yBaseline(H),e.yTickMarks===void 0&&t.yTickMarks&&he!==void 0&&t.yTickMarks(he),e.yGridlines===void 0&&t.yGridlines&&se!==void 0&&t.yGridlines(se),e.yAxisLabels===void 0&&t.yAxisLabels&&ne!==void 0&&t.yAxisLabels(ne),e.yMin===void 0&&t.yMin&&Be!==void 0&&t.yMin(Be),e.yMax===void 0&&t.yMax&&_!==void 0&&t.yMax(_),e.yScale===void 0&&t.yScale&&F!==void 0&&t.yScale(F),e.yFmt===void 0&&t.yFmt&&$!==void 0&&t.yFmt($),e.yAxisColor===void 0&&t.yAxisColor&&Le!==void 0&&t.yAxisColor(Le),e.y2AxisTitle===void 0&&t.y2AxisTitle&&Ee!==void 0&&t.y2AxisTitle(Ee),e.y2Baseline===void 0&&t.y2Baseline&&W!==void 0&&t.y2Baseline(W),e.y2TickMarks===void 0&&t.y2TickMarks&&re!==void 0&&t.y2TickMarks(re),e.y2Gridlines===void 0&&t.y2Gridlines&&Me!==void 0&&t.y2Gridlines(Me),e.y2AxisLabels===void 0&&t.y2AxisLabels&&Ge!==void 0&&t.y2AxisLabels(Ge),e.y2Min===void 0&&t.y2Min&&He!==void 0&&t.y2Min(He),e.y2Max===void 0&&t.y2Max&&$e!==void 0&&t.y2Max($e),e.y2Scale===void 0&&t.y2Scale&&Pe!==void 0&&t.y2Scale(Pe),e.y2Fmt===void 0&&t.y2Fmt&&Ke!==void 0&&t.y2Fmt(Ke),e.y2AxisColor===void 0&&t.y2AxisColor&&et!==void 0&&t.y2AxisColor(et),e.sizeFmt===void 0&&t.sizeFmt&&Ye!==void 0&&t.sizeFmt(Ye),e.colorPalette===void 0&&t.colorPalette&&tt!==void 0&&t.colorPalette(tt),e.legend===void 0&&t.legend&&Se!==void 0&&t.legend(Se),e.echartsOptions===void 0&&t.echartsOptions&&ot!==void 0&&t.echartsOptions(ot),e.seriesOptions===void 0&&t.seriesOptions&&at!==void 0&&t.seriesOptions(at),e.seriesColors===void 0&&t.seriesColors&&nt!==void 0&&t.seriesColors(nt),e.stackType===void 0&&t.stackType&&rt!==void 0&&t.stackType(rt),e.stacked100===void 0&&t.stacked100&&Fe!==void 0&&t.stacked100(Fe),e.chartAreaHeight===void 0&&t.chartAreaHeight&&Ne!==void 0&&t.chartAreaHeight(Ne),e.renderer===void 0&&t.renderer&&oo!==void 0&&t.renderer(oo),e.downloadableData===void 0&&t.downloadableData&&gt!==void 0&&t.downloadableData(gt),e.downloadableImage===void 0&&t.downloadableImage&&pt!==void 0&&t.downloadableImage(pt),e.connectGroup===void 0&&t.connectGroup&&ao!==void 0&&t.connectGroup(ao),e.leftPadding===void 0&&t.leftPadding&&no!==void 0&&t.leftPadding(no),e.rightPadding===void 0&&t.rightPadding&&ro!==void 0&&t.rightPadding(ro),e.xLabelWrap===void 0&&t.xLabelWrap&&bt!==void 0&&t.xLabelWrap(bt),Lt(Ta,T),Lt(Ma,I),de=j(de),G=j(G),oe=j(oe),Z=j(Z),fe=j(fe),ee=j(ee),pe=j(pe),ae=j(ae),H=j(H),he=j(he),se=j(se),ne=j(ne),F=j(F),M(a=O(Le)),W=j(W),re=j(re),Me=j(Me),Ge=j(Ge),Pe=j(Pe),c(r=O(et)),C(l=m(tt)),A=B(nt),gt=j(gt),pt=j(pt),bt=j(bt);try{if(ct=void 0,dt=[],xt=[],Xe=[],Wt=[],Nt=[],Ie=!!x,Re=!!p,Aa(i),Y=Xt(i),lo=Object.keys(Y),Re||(p=lo[0]),!Ie){io=lo.filter(function(L){return![p,N,K].includes(L)});for(let L=0;L<io.length;L++)Nt=io[L],To=Y[Nt].type,To==="number"&&xt.push(Nt);x=xt.length>1?xt:xt[0]}Ce?ge={x:p,y:x,size:K}:ce?ge={x:p}:Oe?ge={}:ge={x:p,y:x};for(let L in ge)ge[L]==null&&dt.push(L);if(dt.length===1)throw Error(new Intl.ListFormat().format(dt)+" is required");if(dt.length>1)throw Error(new Intl.ListFormat().format(dt)+" are required");if(Fe===!0&&x.includes("_pct")&&Ot===!1)if(typeof x=="object"){for(let L=0;L<x.length;L++)x[L]=x[L].replace("_pct","");Ot=!1}else x=x.replace("_pct",""),Ot=!1;if(p&&Xe.push(p),x)if(typeof x=="object")for(Ze=0;Ze<x.length;Ze++)Xe.push(x[Ze]);else Xe.push(x);if(h)if(typeof h=="object")for(Ze=0;Ze<h.length;Ze++)Xe.push(h[Ze]);else Xe.push(h);if(K&&Xe.push(K),N&&Wt.push(N),X&&Wt.push(X),Aa(i,Xe,Wt),Fe===!0){if(i=Kr(i,p,x),typeof x=="object"){for(let L=0;L<x.length;L++)x[L]=x[L]+"_pct";Ot=!1}else x=x+"_pct",Ot=!1;Y=Xt(i)}switch(qe=Y[p].type,qe){case"number":qe="value";break;case"string":qe="category";break;case"date":qe="time";break;default:break}if(V=V==="category"?"category":qe,R?R=R==="true"||R===!0:R=V==="category",G&&V!=="category")throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(G&&h)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(G&&(V="category"),Mo=qe==="value"&&V==="category",i=pe?qe==="category"?zt(i,x,!1):zt(i,p,!0):i,qe==="time"&&(i=zt(i,p,!0)),$o=Xt(i,"array"),ut=$o.filter(L=>L.type==="date"),ut=ut.map(L=>L.id),ut.length>0)for(let L=0;L<ut.length;L++)i=pn(i,ut[L]);be?We=Qe(be,Y[p].format?.valueType):We=Y[p].format,x?$?typeof x=="object"?ke=Qe($,Y[x[0]].format?.valueType):ke=Qe($,Y[x].format?.valueType):typeof x=="object"?ke=Y[x[0]].format:ke=Y[x].format:ke="str",h&&(Ke?typeof h=="object"?Ve=Qe(Ke,Y[h[0]].format?.valueType):Ve=Qe(Ke,Y[h].format?.valueType):typeof h=="object"?Ve=Y[h[0]].format:Ve=Y[h].format),K&&(Ye?so=Qe(Ye,Y[K].format?.valueType):so=Y[K].format),Do=Y[p].columnUnitSummary,x&&(typeof x=="object"?Pt=Y[x[0]].columnUnitSummary:Pt=Y[x].columnUnitSummary),h&&(typeof h=="object"?Ao=Y[h[0]].columnUnitSummary:Ao=Y[h].columnUnitSummary),te=te==="true"?je(p,We):te==="false"?"":te,ie=ie==="true"?typeof x=="object"?"":je(x,ke):ie==="false"?"":ie,Ee=Ee==="true"?typeof h=="object"?"":je(h,Ve):Ee==="false"?"":Ee;let we=typeof x=="object"?x.length:1,ea=N?Pr(i,N):1,St=we*ea,mo=typeof h=="object"?h.length:h?1:0,ho=St+mo;if(Se!==void 0&&(Se=Se==="true"||Se===!0),Se=Se??ho>1,Fe===!0&&ae===!0)throw Error("Log axis cannot be used in a 100% stacked chart");if(rt==="stacked"&&ho>1&&ae===!0)throw Error("Log axis cannot be used in a stacked chart");let Ct;if(typeof x=="object"){Ct=Y[x[0]].columnUnitSummary.min;for(let L=0;L<x.length;L++)Y[x[L]].columnUnitSummary.min<Ct&&(Ct=Y[x[L]].columnUnitSummary.min)}else x&&(Ct=Y[x].columnUnitSummary.min);if(ae===!0&&Ct<=0&&Ct!==null)throw Error("Log axis cannot display values less than or equal to zero");T.update(L=>({...L,data:i,x:p,y:x,y2:h,series:N,swapXY:G,sort:pe,xType:V,xFormat:We,yFormat:ke,y2Format:Ve,sizeFormat:so,xMismatch:Mo,size:K,yMin:Be,y2Min:He,columnSummary:Y,xAxisTitle:te,yAxisTitle:ie,y2AxisTitle:Ee,tooltipTitle:X,chartAreaHeight:Ne,chartType:ue,yCount:we,y2Count:mo})),zo=Ut(i,p);let ta;if(G?co={type:ve,logBase:me,position:"top",axisLabel:{show:ne,hideOverlap:!0,showMaxLabel:!0,formatter(L){return Vt(L,ke,Pt)},margin:4},min:Be,max:_,scale:F,splitLine:{show:se},axisLine:{show:H,onZero:!1},axisTick:{show:he},boundaryGap:!1,z:2}:co={type:V,min:xe,max:le,tooltip:{show:!0,position:"inside",formatter(L){if(L.isTruncated())return L.name}},splitLine:{show:fe},axisLine:{show:oe},axisTick:{show:Z},axisLabel:{show:ee,hideOverlap:!0,showMaxLabel:V==="category"||V==="value",formatter:V==="time"||V==="category"?!1:function(L){return Vt(L,We,Do)},margin:6},scale:!0,z:2},G?Et={type:V,inverse:"true",splitLine:{show:fe},axisLine:{show:oe},axisTick:{show:Z},axisLabel:{show:ee,hideOverlap:!0},scale:!0,min:xe,max:le,z:2}:(Et={type:ve,logBase:me,splitLine:{show:se},axisLine:{show:H,onZero:!1},axisTick:{show:he},axisLabel:{show:ne,hideOverlap:!0,margin:4,formatter(L){return Vt(L,ke,Pt)},color:h?u==="true"?v[0]:u!=="false"?u:void 0:void 0},name:ie,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:h?u==="true"?v[0]:u!=="false"?u:void 0:void 0},nameGap:6,min:Be,max:_,scale:F,boundaryGap:["0%","1%"],z:2},ta={type:"value",show:!1,alignTicks:!0,splitLine:{show:Me},axisLine:{show:W,onZero:!1},axisTick:{show:re},axisLabel:{show:Ge,hideOverlap:!0,margin:4,formatter(L){return Vt(L,Ve,Ao)},color:s==="true"?v[St]:s!=="false"?s:void 0},name:Ee,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:s==="true"?v[St]:s!=="false"?s:void 0},nameGap:6,min:He,max:$e,scale:Pe,boundaryGap:["0%","1%"],z:2},Et=[Et,ta]),Ne){if(Ne=Number(Ne),isNaN(Ne))throw Error("chartAreaHeight must be a number");if(Ne<=0)throw Error("chartAreaHeight must be a positive number")}else Ne=180;Kt=!!_e,wt=!!Te,Uo=Se*(N!==null||typeof x=="object"&&x.length>1),Ro=ie!==""&&G,No=te!==""&&!G,Po=15,Ko=13,Fo=6*wt,qo=Kt*Po+wt*Ko+Fo*Math.max(Kt,wt),Wo=10,jo=10,Qo=14,Ho=14,It=15,It=It*Uo,Ft=7,Ft=Ft*Math.max(Kt,wt),qt=qo+Ft,uo=qt+It+Ho*Ro+Wo,Co=No*Qo+jo,Xo=8,fo=1,G&&(Zo=zo.length,fo=Math.max(1,Zo/Xo)),Yo=Ne*fo+uo+Co,Vo=qt+It+7,Jo=Yo+"px",vo="100%",Bt=G?ie:te,Bt!==""&&(Bt=Bt+" →"),Lo={id:"horiz-axis-title",type:"text",style:{text:Bt,textAlign:"right",fill:y.colors["base-content-muted"]},cursor:"auto",right:G?"2%":"3%",top:G?Vo:null,bottom:G?null:"2%"},Go={title:{text:_e,subtext:Te,subtextStyle:{width:vo}},tooltip:{trigger:"axis",show:!0,formatter(L){let ft,vt,mt,jt;if(ho>1){vt=L[0].value[G?1:0],ft=`<span id="tooltip" style='font-weight: 600;'>${Ue(vt,We)}</span>`;for(let lt=L.length-1;lt>=0;lt--)L[lt].seriesName!=="stackTotal"&&(mt=L[lt].value[G?0:1],ft=ft+`<br> <span style='font-size: 11px;'>${L[lt].marker} ${L[lt].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${Ue(mt,Pa(L[lt].componentIndex,we,mo)===0?ke:Ve)}</span>`)}else V==="value"?(vt=L[0].value[G?1:0],mt=L[0].value[G?0:1],jt=L[0].seriesName,ft=`<span id="tooltip" style='font-weight: 600;'>${je(p,We)}: </span><span style='float:right; margin-left: 10px;'>${Ue(vt,We)}</span><br/><span style='font-weight: 600;'>${je(jt,ke)}: </span><span style='float:right; margin-left: 10px;'>${Ue(mt,ke)}</span>`):(vt=L[0].value[G?1:0],mt=L[0].value[G?0:1],jt=L[0].seriesName,ft=`<span id="tooltip" style='font-weight: 600;'>${Ue(vt,We)}</span><br/><span>${je(jt,ke)}: </span><span style='float:right; margin-left: 10px;'>${Ue(mt,ke)}</span>`);return ft},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:Se,type:"scroll",top:qt,padding:[0,0,0,0],data:[]},grid:{left:no??(G?"1%":"0.8%"),right:ro??(G?"4%":"3%"),bottom:Co,top:uo,containLabel:!0},xAxis:co,yAxis:Et,series:[],animation:!0,graphic:Lo,color:v},I.update(()=>Go)}catch(we){if(ct=we.message,console.error("\x1B[31m%s\x1B[0m",`Error in ${ue}: ${we.message}`),un)throw ct;T.update(St=>({...St,error:ct}))}return d(),f(),g(),E(),S(),`${ct?`${k(Da,"ErrorChart").$$render(o,{error:ct,title:ue},{},{})}`:`${n.default?n.default({}):""} ${k(Nr,"ECharts").$$render(o,{config:w,height:Jo,width:vo,data:i,queryID:b,evidenceChartTitle:_e,showAllXAxisLabels:R,swapXY:G,echartsOptions:ot,seriesOptions:at,printEchartsConfig:de,renderer:oo,downloadableData:gt,downloadableImage:pt,connectGroup:ao,xAxisLabelOverflow:Fa,seriesColors:A},{},{})}`}`}),{Object:qr}=xn,Wr=P((o,e,t,n)=>{let a,{data:r}=e;const l=Dt.isQuery(r)?r.hash:void 0;let A=r?.hash===l,{emptySet:v=void 0}=e,{emptyMessage:d=void 0}=e,{height:C=200}=e,y=r?.id;return e.data===void 0&&t.data&&r!==void 0&&t.data(r),e.emptySet===void 0&&t.emptySet&&v!==void 0&&t.emptySet(v),e.emptyMessage===void 0&&t.emptyMessage&&d!==void 0&&t.emptyMessage(d),e.height===void 0&&t.height&&C!==void 0&&t.height(C),A=r?.hash===l,a={...Object.fromEntries(Object.entries(e).filter(([,f])=>f!==void 0))},` ${k(bn,"QueryLoad").$$render(o,{data:r,height:C},{},{error:({loaded:f})=>`${k(Da,"ErrorChart").$$render(o,{slot:"error",title:a.chartType,error:f.error.message},{},{})}`,empty:()=>`${k(En,"EmptyChart").$$render(o,{slot:"empty",emptyMessage:d,emptySet:v,chartType:a.chartType,isInitial:A},{},{})}`,default:({loaded:f})=>`${k(Fr,"Chart").$$render(o,qr.assign({},a,{data:Dt.isQuery(f)?Array.from(f):f},{queryID:y}),{},{default:()=>`${n.default?n.default({}):""}`})}`})}`});function jr(o,e,t,n,a,r,l,A,v,d,C=void 0,y=void 0,f=void 0,s=void 0){function g(m,i,b,p){let x={name:i,data:m,yAxisIndex:b};return x={...p,...x},x}let c,u,E,M=[],w,S,T,I,z;function O(m,i){const b=[];function p(h){return typeof h>"u"}function x(h,N){p(h)||(Array.isArray(h)?h.forEach(K=>b.push([K,N])):b.push([h,N]))}return x(m,0),x(i,1),b}let B=O(t,f);if(n!=null&&B.length===1)for(I=Ut(o,n),c=0;c<I.length;c++){if(S=o.filter(m=>m[n]===I[c]),a?w=S.map(m=>[m[B[0][0]],A?m[e].toString():m[e]]):w=S.map(m=>[A?m[e].toString():m[e],m[B[0][0]]]),C){let m=S.map(i=>i[C]);w.forEach((i,b)=>i.push(m[b]))}if(y){let m=S.map(i=>i[y]);w.forEach((i,b)=>i.push(m[b]))}T=I[c]??"null",z=B[0][1],E=g(w,T,z,r),M.push(E)}if(n!=null&&B.length>1)for(I=Ut(o,n),c=0;c<I.length;c++)for(S=o.filter(m=>m[n]===I[c]),u=0;u<B.length;u++){if(a?w=S.map(m=>[m[B[u][0]],A?m[e].toString():m[e]]):w=S.map(m=>[A?m[e].toString():m[e],m[B[u][0]]]),C){let m=S.map(i=>i[C]);w.forEach((i,b)=>i.push(m[b]))}if(y){let m=S.map(i=>i[y]);w.forEach((i,b)=>i.push(m[b]))}T=(I[c]??"null")+" - "+v[B[u][0]].title,z=B[u][1],E=g(w,T,z,r),M.push(E)}if(n==null&&B.length>1)for(c=0;c<B.length;c++){if(a?w=o.map(m=>[m[B[c][0]],A?m[e].toString():m[e]]):w=o.map(m=>[A?m[e].toString():m[e],m[B[c][0]]]),C){let m=o.map(i=>i[C]);w.forEach((i,b)=>i.push(m[b]))}if(y){let m=o.map(i=>i[y]);w.forEach((i,b)=>i.push(m[b]))}T=v[B[c][0]].title,z=B[c][1],E=g(w,T,z,r),M.push(E)}if(n==null&&B.length===1){if(a?w=o.map(m=>[m[B[0][0]],A?m[e].toString():m[e]]):w=o.map(m=>[A?m[e].toString():m[e],m[B[0][0]]]),C){let m=o.map(i=>i[C]);w.forEach((i,b)=>i.push(m[b]))}if(y){let m=o.map(i=>i[y]);w.forEach((i,b)=>i.push(m[b]))}T=v[B[0][0]].title,z=B[0][1],E=g(w,T,z,r),M.push(E)}return d&&M.sort((m,i)=>d.indexOf(m.name)-d.indexOf(i.name)),s&&M.forEach(m=>{m.name=wn(m.name,s)}),M}function Qr(o){let e=[];for(let t=1;t<o.length;t++)e.push(o[t]-o[t-1]);return e}function Ka(o,e){return(typeof o!="number"||isNaN(o))&&(o=0),(typeof e!="number"||isNaN(e))&&(e=0),o=Math.abs(o),e=Math.abs(e),e<=.01?o:Ka(e,o%e)}function Hr(o,e){if(!Array.isArray(o))throw new TypeError("Cannot calculate extent of non-array value.");let t,n;for(const a of o)typeof a=="number"&&(t===void 0?a>=a&&(t=n=a):(t>a&&(t=a),n<a&&(n=a)));return[t,n]}function Yr(o,e){let[t,n]=Hr(o);const a=[];let r=t;for(;r<=n;)a.push(Math.round((r+Number.EPSILON)*1e8)/1e8),r+=e;return a}function Vr(o){if(o.length<=1)return;o.sort(function(t,n){return t-n}),o=o.map(function(t){return t*1e8}),o=Qr(o);let e=o.reduce((t,n)=>Ka(t,n))/1e8;return e=Math.round((e+Number.EPSILON)*1e8)/1e8,e}function xo(o,e,t,n,a=!1,r=!1){let l=!1;const A=o.map(s=>Object.assign({},s,{[e]:s[e]instanceof Date?(l=!0,s[e].toISOString()):s[e]})).filter(s=>s[e]!==void 0&&s[e]!==null),v=Array.from(A).reduce((s,g)=>(g[e]instanceof Date&&(g[e]=g[e].toISOString(),l=!0),n?(s[g[n]??"null"]||(s[g[n]??"null"]=[]),s[g[n]??"null"].push(g)):(s.default||(s.default=[]),s.default.push(g)),s),{}),d={};let C;const y=A.find(s=>s&&s[e]!==null&&s[e]!==void 0)?.[e]??null;switch(typeof y){case"object":throw y===null?new Error(`Column '${e}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(C=Ut(A,e),r){const s=Vr(C);d[e]=Yr(C,s)}break;case"string":C=Ut(A,e),d[e]=C;break}const f=[];for(const s of Object.values(v)){const g=n?{[n]:null}:{};if(a)if(t instanceof Array)for(let u=0;u<t.length;u++)g[t[u]]=0;else g[t]=0;else if(t instanceof Array)for(let u=0;u<t.length;u++)g[t[u]]=null;else g[t]=null;n&&(d[n]=n);const c=[];Object.keys(d).length===0?c.push(ga([e],g)):c.push(ga(d,g)),f.push(it(s,...c))}return l?f.flat().map(s=>({...s,[e]:new Date(s[e])})):f.flat()}function Sa(o,e,t){let n=it(o,wo(e,[Wn(t,Io)]));if(typeof t=="object")for(let a=0;a<n.length;a++){n[a].stackTotal=0;for(let r=0;r<t.length;r++)n[a].stackTotal=n[a].stackTotal+n[a][t[r]]}return n}let Xr=60;const Zr=P((o,e,t,n)=>{let a,r,l,A,v,d,C,y,f,s,g,c,u,E,M,w,S,T,I,z=De,O=()=>(z(),z=U(A,W=>I=W),A),B,m=De,i=()=>(m(),m=U(l,W=>B=W),l),b,p=De,x=()=>(p(),p=U(v,W=>b=W),v),h,N=De,K=()=>(N(),N=U(a,W=>h=W),a);const{resolveColor:X}=At();let{y:R=void 0}=e;const de=!!R;let{y2:Ie=void 0}=e;const Re=!!Ie;let{series:G=void 0}=e;const _e=!!G;let{options:Te=void 0}=e,{name:ue=void 0}=e,{type:Ce="stacked"}=e,{stackName:ce=void 0}=e,{fillColor:Oe=void 0}=e,{fillOpacity:ge=void 0}=e,{outlineColor:V=void 0}=e,{outlineWidth:te=void 0}=e,{labels:oe=!1}=e,{seriesLabels:Z=!0}=e,{labelSize:fe=11}=e,{labelPosition:ee=void 0}=e,{labelColor:pe=void 0}=e,{labelFmt:be=void 0}=e,xe;be&&(xe=Qe(be));let{yLabelFmt:le=void 0}=e,ae;le&&(ae=Qe(le));let{y2LabelFmt:ve=void 0}=e,me;ve&&(me=Qe(ve));let{y2SeriesType:ie="bar"}=e,{stackTotalLabel:H=!0}=e,{showAllLabels:he=!1}=e,{seriesOrder:se=void 0}=e,ne,Be,_,F;const $={outside:"top",inside:"inside"},Le={outside:"right",inside:"inside"};let{seriesLabelFmt:Ee=void 0}=e;return e.y===void 0&&t.y&&R!==void 0&&t.y(R),e.y2===void 0&&t.y2&&Ie!==void 0&&t.y2(Ie),e.series===void 0&&t.series&&G!==void 0&&t.series(G),e.options===void 0&&t.options&&Te!==void 0&&t.options(Te),e.name===void 0&&t.name&&ue!==void 0&&t.name(ue),e.type===void 0&&t.type&&Ce!==void 0&&t.type(Ce),e.stackName===void 0&&t.stackName&&ce!==void 0&&t.stackName(ce),e.fillColor===void 0&&t.fillColor&&Oe!==void 0&&t.fillColor(Oe),e.fillOpacity===void 0&&t.fillOpacity&&ge!==void 0&&t.fillOpacity(ge),e.outlineColor===void 0&&t.outlineColor&&V!==void 0&&t.outlineColor(V),e.outlineWidth===void 0&&t.outlineWidth&&te!==void 0&&t.outlineWidth(te),e.labels===void 0&&t.labels&&oe!==void 0&&t.labels(oe),e.seriesLabels===void 0&&t.seriesLabels&&Z!==void 0&&t.seriesLabels(Z),e.labelSize===void 0&&t.labelSize&&fe!==void 0&&t.labelSize(fe),e.labelPosition===void 0&&t.labelPosition&&ee!==void 0&&t.labelPosition(ee),e.labelColor===void 0&&t.labelColor&&pe!==void 0&&t.labelColor(pe),e.labelFmt===void 0&&t.labelFmt&&be!==void 0&&t.labelFmt(be),e.yLabelFmt===void 0&&t.yLabelFmt&&le!==void 0&&t.yLabelFmt(le),e.y2LabelFmt===void 0&&t.y2LabelFmt&&ve!==void 0&&t.y2LabelFmt(ve),e.y2SeriesType===void 0&&t.y2SeriesType&&ie!==void 0&&t.y2SeriesType(ie),e.stackTotalLabel===void 0&&t.stackTotalLabel&&H!==void 0&&t.stackTotalLabel(H),e.showAllLabels===void 0&&t.showAllLabels&&he!==void 0&&t.showAllLabels(he),e.seriesOrder===void 0&&t.seriesOrder&&se!==void 0&&t.seriesOrder(se),e.seriesLabelFmt===void 0&&t.seriesLabelFmt&&Ee!==void 0&&t.seriesLabelFmt(Ee),K(a=Gt(Ta)),r=Gt(Ma),i(l=X(Oe)),O(A=X(V)),oe=oe==="true"||oe===!0,Z=Z==="true"||Z===!0,x(v=X(pe)),H=H==="true"||H===!0,d=h.data,C=h.x,R=de?R:h.y,Ie=Re?Ie:h.y2,y=h.yFormat,f=h.y2Format,s=h.yCount,g=h.y2Count,c=h.swapXY,u=h.xType,E=h.xMismatch,M=h.columnSummary,w=h.sort,G=_e?G:h.series,!G&&typeof R!="object"?(ue=ue??je(R,M[R].title),c&&u!=="category"&&(d=xo(d,C,R,G,!0,u!=="time"),u="category"),ce="stack1",_=c?"right":"top"):(w===!0&&u==="category"&&(ne=Sa(d,C,R),typeof R=="object"?ne=zt(ne,"stackTotal",!1):ne=zt(ne,R,!1),Be=ne.map(W=>W[C]),d=[...d].sort(function(W,re){return Be.indexOf(W[C])-Be.indexOf(re[C])})),c||(u==="value"||u==="category")&&Ce.includes("stacked")?(d=xo(d,C,R,G,!0,u==="value"),u="category"):u==="time"&&Ce.includes("stacked")&&(d=xo(d,C,R,G,!0,!0)),Ce.includes("stacked")?(ce=ce??"stack1",_="inside"):(ce=void 0,_=c?"right":"top")),Ce==="stacked"&&(F=Sa(d,C,R)),ee=(c?Le[ee]:$[ee])??_,S={type:"bar",stack:ce,label:{show:oe&&Z,formatter(W){return W.value[c?0:1]===0?"":Ue(W.value[c?0:1],[ae??xe??y,me??xe??f][Pa(W.componentIndex,s,g)])},position:ee,fontSize:fe,color:b},labelLayout:{hideOverlap:!he},emphasis:{focus:"series"},barMaxWidth:Xr,itemStyle:{color:B,opacity:ge,borderColor:I,borderWidth:te}},T=jr(d,C,R,G,c,S,ue,E,M,se,void 0,void 0,Ie,Ee),r.update(W=>(W.series.push(...T),W.legend.data.push(...T.map(re=>re.name.toString())),oe===!0&&Ce==="stacked"&&typeof R=="object"|G!==void 0&&H===!0&&G!==C&&(W.series.push({type:"bar",stack:ce,name:"stackTotal",color:"none",data:F.map(re=>[c?0:E?re[C].toString():re[C],c?E?re[C].toString():re[C]:0]),label:{show:!0,position:c?"right":"top",formatter(re){let Me=0;return T.forEach(Ge=>{Me+=Ge.data[re.dataIndex][c?0:1]}),Me===0?"":Ue(Me,xe??y)},fontWeight:"bold",fontSize:fe,padding:c?[0,0,0,5]:void 0}}),W.legend.selectedMode=!1),W)),z(),m(),p(),N(),""}),Jr=P((o,e,t,n)=>{let a,r,l,A,v,d,C;const{resolveColor:y,resolveColorsObject:f,resolveColorPalette:s}=At();let{data:g=void 0}=e,{x:c=void 0}=e,{y:u=void 0}=e,{y2:E=void 0}=e,{series:M=void 0}=e,{xType:w=void 0}=e,{yLog:S=void 0}=e,{yLogBase:T=void 0}=e,{y2SeriesType:I=void 0}=e,{yFmt:z=void 0}=e,{y2Fmt:O=void 0}=e,{xFmt:B=void 0}=e,{title:m=void 0}=e,{subtitle:i=void 0}=e,{legend:b=void 0}=e,{xAxisTitle:p=void 0}=e,{yAxisTitle:x=E?"true":void 0}=e,{y2AxisTitle:h=E?"true":void 0}=e,{xGridlines:N=void 0}=e,{yGridlines:K=void 0}=e,{y2Gridlines:X=void 0}=e,{xAxisLabels:R=void 0}=e,{yAxisLabels:de=void 0}=e,{y2AxisLabels:Ie=void 0}=e,{xBaseline:Re=void 0}=e,{yBaseline:G=void 0}=e,{y2Baseline:_e=void 0}=e,{xTickMarks:Te=void 0}=e,{yTickMarks:ue=void 0}=e,{y2TickMarks:Ce=void 0}=e,{yMin:ce=void 0}=e,{yMax:Oe=void 0}=e,{yScale:ge=void 0}=e,{y2Min:V=void 0}=e,{y2Max:te=void 0}=e,{y2Scale:oe=void 0}=e,{swapXY:Z=!1}=e,{showAllXAxisLabels:fe}=e,{type:ee="stacked"}=e,pe=ee==="stacked100",{fillColor:be=void 0}=e,{fillOpacity:xe=void 0}=e,{outlineColor:le=void 0}=e,{outlineWidth:ae=void 0}=e,{chartAreaHeight:ve=void 0}=e,{sort:me=void 0}=e,{colorPalette:ie="default"}=e,{labels:H=void 0}=e,{labelSize:he=void 0}=e,{labelPosition:se=void 0}=e,{labelColor:ne=void 0}=e,{labelFmt:Be=void 0}=e,{yLabelFmt:_=void 0}=e,{y2LabelFmt:F=void 0}=e,{stackTotalLabel:$=void 0}=e,{seriesLabels:Le=void 0}=e,{showAllLabels:Ee=void 0}=e,{yAxisColor:W=void 0}=e,{y2AxisColor:re=void 0}=e,{echartsOptions:Me=void 0}=e,{seriesOptions:Ge=void 0}=e,{printEchartsConfig:He=!1}=e,{emptySet:$e=void 0}=e,{emptyMessage:Pe=void 0}=e,{renderer:Ke=void 0}=e,{downloadableData:et=void 0}=e,{downloadableImage:Ye=void 0}=e,{seriesColors:tt=void 0}=e,{seriesOrder:Se=void 0}=e,{connectGroup:ot=void 0}=e,{seriesLabelFmt:at=void 0}=e,{leftPadding:nt=void 0}=e,{rightPadding:rt=void 0}=e,{xLabelWrap:Fe=void 0}=e;return e.data===void 0&&t.data&&g!==void 0&&t.data(g),e.x===void 0&&t.x&&c!==void 0&&t.x(c),e.y===void 0&&t.y&&u!==void 0&&t.y(u),e.y2===void 0&&t.y2&&E!==void 0&&t.y2(E),e.series===void 0&&t.series&&M!==void 0&&t.series(M),e.xType===void 0&&t.xType&&w!==void 0&&t.xType(w),e.yLog===void 0&&t.yLog&&S!==void 0&&t.yLog(S),e.yLogBase===void 0&&t.yLogBase&&T!==void 0&&t.yLogBase(T),e.y2SeriesType===void 0&&t.y2SeriesType&&I!==void 0&&t.y2SeriesType(I),e.yFmt===void 0&&t.yFmt&&z!==void 0&&t.yFmt(z),e.y2Fmt===void 0&&t.y2Fmt&&O!==void 0&&t.y2Fmt(O),e.xFmt===void 0&&t.xFmt&&B!==void 0&&t.xFmt(B),e.title===void 0&&t.title&&m!==void 0&&t.title(m),e.subtitle===void 0&&t.subtitle&&i!==void 0&&t.subtitle(i),e.legend===void 0&&t.legend&&b!==void 0&&t.legend(b),e.xAxisTitle===void 0&&t.xAxisTitle&&p!==void 0&&t.xAxisTitle(p),e.yAxisTitle===void 0&&t.yAxisTitle&&x!==void 0&&t.yAxisTitle(x),e.y2AxisTitle===void 0&&t.y2AxisTitle&&h!==void 0&&t.y2AxisTitle(h),e.xGridlines===void 0&&t.xGridlines&&N!==void 0&&t.xGridlines(N),e.yGridlines===void 0&&t.yGridlines&&K!==void 0&&t.yGridlines(K),e.y2Gridlines===void 0&&t.y2Gridlines&&X!==void 0&&t.y2Gridlines(X),e.xAxisLabels===void 0&&t.xAxisLabels&&R!==void 0&&t.xAxisLabels(R),e.yAxisLabels===void 0&&t.yAxisLabels&&de!==void 0&&t.yAxisLabels(de),e.y2AxisLabels===void 0&&t.y2AxisLabels&&Ie!==void 0&&t.y2AxisLabels(Ie),e.xBaseline===void 0&&t.xBaseline&&Re!==void 0&&t.xBaseline(Re),e.yBaseline===void 0&&t.yBaseline&&G!==void 0&&t.yBaseline(G),e.y2Baseline===void 0&&t.y2Baseline&&_e!==void 0&&t.y2Baseline(_e),e.xTickMarks===void 0&&t.xTickMarks&&Te!==void 0&&t.xTickMarks(Te),e.yTickMarks===void 0&&t.yTickMarks&&ue!==void 0&&t.yTickMarks(ue),e.y2TickMarks===void 0&&t.y2TickMarks&&Ce!==void 0&&t.y2TickMarks(Ce),e.yMin===void 0&&t.yMin&&ce!==void 0&&t.yMin(ce),e.yMax===void 0&&t.yMax&&Oe!==void 0&&t.yMax(Oe),e.yScale===void 0&&t.yScale&&ge!==void 0&&t.yScale(ge),e.y2Min===void 0&&t.y2Min&&V!==void 0&&t.y2Min(V),e.y2Max===void 0&&t.y2Max&&te!==void 0&&t.y2Max(te),e.y2Scale===void 0&&t.y2Scale&&oe!==void 0&&t.y2Scale(oe),e.swapXY===void 0&&t.swapXY&&Z!==void 0&&t.swapXY(Z),e.showAllXAxisLabels===void 0&&t.showAllXAxisLabels&&fe!==void 0&&t.showAllXAxisLabels(fe),e.type===void 0&&t.type&&ee!==void 0&&t.type(ee),e.fillColor===void 0&&t.fillColor&&be!==void 0&&t.fillColor(be),e.fillOpacity===void 0&&t.fillOpacity&&xe!==void 0&&t.fillOpacity(xe),e.outlineColor===void 0&&t.outlineColor&&le!==void 0&&t.outlineColor(le),e.outlineWidth===void 0&&t.outlineWidth&&ae!==void 0&&t.outlineWidth(ae),e.chartAreaHeight===void 0&&t.chartAreaHeight&&ve!==void 0&&t.chartAreaHeight(ve),e.sort===void 0&&t.sort&&me!==void 0&&t.sort(me),e.colorPalette===void 0&&t.colorPalette&&ie!==void 0&&t.colorPalette(ie),e.labels===void 0&&t.labels&&H!==void 0&&t.labels(H),e.labelSize===void 0&&t.labelSize&&he!==void 0&&t.labelSize(he),e.labelPosition===void 0&&t.labelPosition&&se!==void 0&&t.labelPosition(se),e.labelColor===void 0&&t.labelColor&&ne!==void 0&&t.labelColor(ne),e.labelFmt===void 0&&t.labelFmt&&Be!==void 0&&t.labelFmt(Be),e.yLabelFmt===void 0&&t.yLabelFmt&&_!==void 0&&t.yLabelFmt(_),e.y2LabelFmt===void 0&&t.y2LabelFmt&&F!==void 0&&t.y2LabelFmt(F),e.stackTotalLabel===void 0&&t.stackTotalLabel&&$!==void 0&&t.stackTotalLabel($),e.seriesLabels===void 0&&t.seriesLabels&&Le!==void 0&&t.seriesLabels(Le),e.showAllLabels===void 0&&t.showAllLabels&&Ee!==void 0&&t.showAllLabels(Ee),e.yAxisColor===void 0&&t.yAxisColor&&W!==void 0&&t.yAxisColor(W),e.y2AxisColor===void 0&&t.y2AxisColor&&re!==void 0&&t.y2AxisColor(re),e.echartsOptions===void 0&&t.echartsOptions&&Me!==void 0&&t.echartsOptions(Me),e.seriesOptions===void 0&&t.seriesOptions&&Ge!==void 0&&t.seriesOptions(Ge),e.printEchartsConfig===void 0&&t.printEchartsConfig&&He!==void 0&&t.printEchartsConfig(He),e.emptySet===void 0&&t.emptySet&&$e!==void 0&&t.emptySet($e),e.emptyMessage===void 0&&t.emptyMessage&&Pe!==void 0&&t.emptyMessage(Pe),e.renderer===void 0&&t.renderer&&Ke!==void 0&&t.renderer(Ke),e.downloadableData===void 0&&t.downloadableData&&et!==void 0&&t.downloadableData(et),e.downloadableImage===void 0&&t.downloadableImage&&Ye!==void 0&&t.downloadableImage(Ye),e.seriesColors===void 0&&t.seriesColors&&tt!==void 0&&t.seriesColors(tt),e.seriesOrder===void 0&&t.seriesOrder&&Se!==void 0&&t.seriesOrder(Se),e.connectGroup===void 0&&t.connectGroup&&ot!==void 0&&t.connectGroup(ot),e.seriesLabelFmt===void 0&&t.seriesLabelFmt&&at!==void 0&&t.seriesLabelFmt(at),e.leftPadding===void 0&&t.leftPadding&&nt!==void 0&&t.leftPadding(nt),e.rightPadding===void 0&&t.rightPadding&&rt!==void 0&&t.rightPadding(rt),e.xLabelWrap===void 0&&t.xLabelWrap&&Fe!==void 0&&t.xLabelWrap(Fe),Z==="true"||Z===!0?Z=!0:Z=!1,a=y(be),r=y(le),l=s(ie),A=y(ne),v=y(W),d=y(re),C=f(tt),`${k(Wr,"Chart").$$render(o,{data:g,x:c,y:u,y2:E,xFmt:B,yFmt:z,y2Fmt:O,series:M,xType:w,yLog:S,yLogBase:T,legend:b,xAxisTitle:p,yAxisTitle:x,y2AxisTitle:h,xGridlines:N,yGridlines:K,y2Gridlines:X,xAxisLabels:R,yAxisLabels:de,y2AxisLabels:Ie,xBaseline:Re,yBaseline:G,y2Baseline:_e,xTickMarks:Te,yTickMarks:ue,y2TickMarks:Ce,yAxisColor:v,y2AxisColor:d,yMin:ce,yMax:Oe,yScale:ge,y2Min:V,y2Max:te,y2Scale:oe,swapXY:Z,title:m,subtitle:i,chartType:"Bar Chart",stackType:ee,sort:me,stacked100:pe,chartAreaHeight:ve,showAllXAxisLabels:fe,colorPalette:l,echartsOptions:Me,seriesOptions:Ge,printEchartsConfig:He,emptySet:$e,emptyMessage:Pe,renderer:Ke,downloadableData:et,downloadableImage:Ye,connectGroup:ot,xLabelWrap:Fe,seriesColors:C,leftPadding:nt,rightPadding:rt},{},{default:()=>`${k(Zr,"Bar").$$render(o,{type:ee,fillColor:a,fillOpacity:xe,outlineColor:r,outlineWidth:ae,labels:H,labelSize:he,labelPosition:se,labelColor:A,labelFmt:Be,yLabelFmt:_,y2LabelFmt:F,stackTotalLabel:$,seriesLabels:Le,showAllLabels:Ee,y2SeriesType:I,seriesOrder:Se,seriesLabelFmt:at},{},{})} ${n.default?n.default({}):""}`})}`}),$r={code:".domo-banner.svelte-16omzez.svelte-16omzez{margin:20px 0;padding:20px;border:2px solid #00d4aa;border-radius:12px;background:linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);color:#065f46;text-align:center}.domo-banner.svelte-16omzez h3.svelte-16omzez{margin:0 0 10px 0;color:#047857}.dev-banner.svelte-16omzez.svelte-16omzez{margin:20px 0;padding:20px;border:2px solid #f59e0b;border-radius:12px;background:linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);color:#92400e;text-align:center}.dev-banner.svelte-16omzez h3.svelte-16omzez{margin:0 0 10px 0;color:#d97706}.btn-primary.svelte-16omzez.svelte-16omzez,.btn-secondary.svelte-16omzez.svelte-16omzez{display:inline-block;padding:12px 24px;border:none;border-radius:8px;text-decoration:none;font-weight:600;font-size:14px;cursor:pointer;transition:all 0.2s}.btn-primary.svelte-16omzez.svelte-16omzez{background-color:#2563eb;color:white}.btn-primary.svelte-16omzez.svelte-16omzez:hover{background-color:#1d4ed8;text-decoration:none;color:white}.btn-secondary.svelte-16omzez.svelte-16omzez{background-color:#f3f4f6;color:#374151;border:1px solid #d1d5db}.btn-secondary.svelte-16omzez.svelte-16omzez:hover{background-color:#e5e7eb;text-decoration:none;color:#374151}.next-steps.svelte-16omzez.svelte-16omzez{display:grid;grid-template-columns:repeat(auto-fit, minmax(250px, 1fr));gap:20px;margin:30px 0}.step.svelte-16omzez.svelte-16omzez{padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#fafafa}.step.svelte-16omzez h4.svelte-16omzez{margin:0 0 10px 0;color:#1f2937;font-size:16px}.step.svelte-16omzez p.svelte-16omzez{margin:0;color:#6b7280;line-height:1.5}.workflow-toggle.svelte-16omzez.svelte-16omzez{margin:30px 0;text-align:center}.workflow-toggle.svelte-16omzez button.svelte-16omzez{margin:0 10px}.workflow-picker-section.svelte-16omzez.svelte-16omzez{margin:30px 0;padding:30px;border:2px solid #e5e7eb;border-radius:12px;background-color:#f9fafb}.workflow-picker-section.svelte-16omzez h3.svelte-16omzez{margin:0 0 10px 0;color:#1f2937}.workflow-picker-section.svelte-16omzez p.svelte-16omzez{margin:0 0 20px 0;color:#6b7280}.workflow-picker.svelte-16omzez.svelte-16omzez{max-width:800px;margin:0 auto;font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif}.workflow-step.svelte-16omzez.svelte-16omzez{margin:25px 0;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:white}.workflow-step.svelte-16omzez label.svelte-16omzez{display:block;margin-bottom:8px;font-weight:600;color:#374151}.dataset-dropdown.svelte-16omzez.svelte-16omzez,.workflow-step.svelte-16omzez input.svelte-16omzez,.workflow-step.svelte-16omzez select.svelte-16omzez{width:100%;padding:10px 12px;border:1px solid #d1d5db;border-radius:6px;font-size:14px;background-color:white}.dataset-dropdown.svelte-16omzez.svelte-16omzez:focus,.workflow-step.svelte-16omzez input.svelte-16omzez:focus,.workflow-step.svelte-16omzez select.svelte-16omzez:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px rgba(59, 130, 246, 0.1)}.dataset-preview.svelte-16omzez.svelte-16omzez{margin-top:20px;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:white}.dataset-info.svelte-16omzez.svelte-16omzez{display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:15px;margin-bottom:20px}.data-preview.svelte-16omzez.svelte-16omzez{margin-top:15px;padding:15px;border:1px solid #e5e7eb;border-radius:6px;background-color:#f9fafb}.config-grid.svelte-16omzez.svelte-16omzez{display:grid;grid-template-columns:repeat(auto-fit, minmax(250px, 1fr));gap:20px}.workflow-actions.svelte-16omzez.svelte-16omzez{text-align:center;margin:30px 0}.btn.svelte-16omzez.svelte-16omzez{padding:12px 24px;border:none;border-radius:6px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.2s;margin:0 5px}.btn.svelte-16omzez.svelte-16omzez:disabled{opacity:0.6;cursor:not-allowed}.btn-primary.svelte-16omzez.svelte-16omzez{background-color:#3b82f6;color:white}.btn-primary.svelte-16omzez.svelte-16omzez:hover:not(:disabled){background-color:#2563eb}.btn-secondary.svelte-16omzez.svelte-16omzez{background-color:#f3f4f6;color:#374151;border:1px solid #d1d5db}.btn-secondary.svelte-16omzez.svelte-16omzez:hover:not(:disabled){background-color:#e5e7eb}.loading-status.svelte-16omzez.svelte-16omzez{margin:20px 0;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#f9fafb;text-align:center}.loading-spinner.svelte-16omzez.svelte-16omzez{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #3b82f6;border-radius:50%;animation:svelte-16omzez-spin 1s linear infinite;margin-bottom:16px}@keyframes svelte-16omzez-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.preview-actions.svelte-16omzez.svelte-16omzez{margin:15px 0}.data-preview.svelte-16omzez.svelte-16omzez{margin-top:20px;max-height:300px;overflow:auto}#analysis-section.svelte-16omzez.svelte-16omzez{margin:40px 0;padding:30px;border:2px solid #10b981;border-radius:12px;background-color:#f0fdf4}#analysis-section.svelte-16omzez h2.svelte-16omzez{margin:0 0 20px 0;color:#047857}.loaded-datasets-summary.svelte-16omzez.svelte-16omzez{margin:15px 0}.loaded-datasets-summary.svelte-16omzez ul.svelte-16omzez{margin:10px 0;padding-left:20px}.loaded-datasets-summary.svelte-16omzez li.svelte-16omzez{margin:5px 0}.analysis-examples.svelte-16omzez.svelte-16omzez{margin:30px 0}.analysis-examples.svelte-16omzez h3.svelte-16omzez{color:#047857;margin-bottom:15px}.query-example.svelte-16omzez.svelte-16omzez{margin:20px 0;padding:20px;border:1px solid #d1fae5;border-radius:8px;background-color:white}.query-example.svelte-16omzez h4.svelte-16omzez{margin:0 0 10px 0;color:#065f46}.query-example.svelte-16omzez pre.svelte-16omzez{margin:10px 0 0 0;padding:15px;background-color:#f3f4f6;border-radius:6px;overflow-x:auto}.query-example.svelte-16omzez code.svelte-16omzez{font-family:'Monaco', 'Menlo', 'Ubuntu Mono', monospace;font-size:13px;line-height:1.4}",map:`{"version":3,"file":"+page.md","sources":["+page.md"],"sourcesContent":["\\n<!-- \\n    MDSvex comes in handy here because it takes frontmatter and shoves it into the metadata object.\\n    This means that all we need to do is build out the expected page metadata\\n-->\\n<!-- Show title as h1 if defined, and not hidden -->\\n{#if typeof metadata !== \\"undefined\\" && (metadata.title || metadata.og?.title) && metadata.hide_title !== true}\\n<h1 class=\\"title\\">{metadata.title ?? metadata.og?.title}</h1>\\n{/if}\\n<svelte:head>\\n<!-- Title has a default case; so we need to handle it in a special way -->\\n{#if typeof metadata !== \\"undefined\\" && (metadata.title || metadata.og?.title)}\\n<title>{metadata.title ?? metadata.og?.title}</title>\\n<meta property=\\"og:title\\" content={metadata.og?.title ?? metadata.title} />\\n<meta name=\\"twitter:title\\" content={metadata.og?.title ?? metadata.title} />\\n{:else}\\n<!-- EITHER there is no metadata, or there is no specified style -->\\n<title>Evidence</title>\\n{/if}\\n\\n<!-- default twitter cardtags -->\\n<meta name=\\"twitter:card\\" content=\\"summary_large_image\\" />\\n<meta name=\\"twitter:site\\" content=\\"@evidence_dev\\" />\\n\\n{#if typeof metadata === \\"object\\"}\\n{#if metadata.description || metadata.og?.description}\\n  <meta\\n    name=\\"description\\"\\n    content={metadata.description ?? metadata.og?.description}\\n  />\\n  <meta\\n    property=\\"og:description\\"\\n    content={metadata.og?.description ?? metadata.description}\\n  />\\n  <meta\\n    name=\\"twitter:description\\"\\n    content={metadata.og?.description ?? metadata.description}\\n  />\\n{/if}\\n{#if metadata.og?.image}\\n  <meta property=\\"og:image\\" content={addBasePath(metadata.og?.image)} />\\n  <meta name=\\"twitter:image\\" content={addBasePath(metadata.og?.image)} />\\n{/if}\\n{/if}\\n</svelte:head>\\n<script context=\\"module\\">\\n\\tconst metadata = {\\"title\\":\\"Evidence Dashboard for Domo\\"};\\n\\tconst { title } = metadata; <\/script>\\n<script>\\nimport { Dropdown } from '@evidence-dev/core-components'\\nimport { DropdownOption } from '@evidence-dev/core-components'\\nimport { Details } from '@evidence-dev/core-components'\\nimport { QueryViewer } from '@evidence-dev/core-components'\\nimport { BarChart } from '@evidence-dev/core-components'\\nimport { addBasePath } from \\"@evidence-dev/sdk/utils/svelte\\";\\n\\t\\timport { pageHasQueries, routeHash, toasts } from '@evidence-dev/component-utilities/stores';\\nimport { fmt } from '@evidence-dev/component-utilities/formatting';\\nimport { CUSTOM_FORMATTING_SETTINGS_CONTEXT_KEY } from '@evidence-dev/component-utilities/globalContexts';\\nimport { ensureInputContext } from '@evidence-dev/sdk/utils/svelte';\\nimport { profile } from '@evidence-dev/component-utilities/profile';\\nimport { Query, hasUnsetValues } from '@evidence-dev/sdk/usql';\\nimport { setQueryFunction } from '@evidence-dev/component-utilities/buildQuery';\\n\\t\\t\\n        import { page } from '$app/stores';\\n        import { setContext, getContext, beforeUpdate, onDestroy, onMount } from 'svelte';\\n\\t\\timport { writable, get } from 'svelte/store';\\n        \\n        // Functions\\n\\n        \\n        let props;\\n        export { props as data }; // little hack to make the data name not overlap\\n        let { data = {}, customFormattingSettings, __db, inputs } = props;\\n        $: ({ data = {}, customFormattingSettings, __db } = props);\\n\\n        $routeHash = '6666cd76f96956469e7be39d750cc7d9';\\n\\n\\t\\t\\n\\t\\tlet inputs_store = ensureInputContext(writable(inputs));\\n\\t\\tonDestroy(inputs_store.subscribe((value) => inputs = value));\\n\\n        $: pageHasQueries.set(Object.keys(data).length > 0);\\n\\n        setContext(CUSTOM_FORMATTING_SETTINGS_CONTEXT_KEY, {\\n            getCustomFormats: () => {\\n                return customFormattingSettings.customFormats || [];\\n            }\\n        });\\n\\n\\t\\timport { browser, dev } from \\"$app/environment\\";\\n\\n\\t\\tif (!browser) {\\n\\t\\t\\tonDestroy(() => Query.emptyCache());\\n\\t\\t}\\n\\n\\t\\tconst queryFunc = (query, query_name) => profile(__db.query, query, { query_name });\\n\\t\\tsetQueryFunction(queryFunc);\\n\\n\\t\\tconst scoreNotifier = !dev? () => {} : (info) => {\\n\\t\\t\\ttoasts.add({\\n\\t\\t\\t\\tid: Math.random(),\\n\\t\\t\\t\\ttitle: info.id,\\n\\t\\t\\t\\tmessage: \`Results estimated to use \${\\n\\t\\t\\t\\t\\tIntl.NumberFormat().format(info.score / (1024 * 1024))\\n\\t\\t\\t\\t}mb of memory, performance may be impacted\`,\\n\\t\\t\\t\\tstatus: 'warning'\\n\\t\\t\\t}, 5000);\\n\\t\\t};\\n\\n\\t\\tif (import.meta?.hot) {\\n            if (typeof import.meta.hot.data.hmrHasRun === 'undefined') import.meta.hot.data.hmrHasRun = false\\n\\n\\t\\t\\timport.meta.hot.on(\\"evidence:reset-queries\\", async (payload) => {\\n\\t\\t\\t\\tawait $page.data.__db.updateParquetURLs(JSON.stringify(payload.latestManifest), true);\\n\\t\\t\\t\\tQuery.emptyCache()\\n\\t\\t\\t\\t__categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved });\\n__orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved });\\n\\t\\t\\t})\\n\\t    }\\n\\t\\t\\n\\t\\tlet params = $page.params;\\n\\t\\t$: params = $page.params;\\n\\t\\t\\n\\t\\tlet _mounted = false;\\n\\t\\tonMount(() => (_mounted = true));\\n\\n        \\n\\t\\t\\n\\t\\t\\n                // Update external queries\\n                if (import.meta?.hot) {\\n\\t\\t\\t\\t\\timport.meta.hot.on(\\"vite:beforeUpdate\\", () => {\\n\\t\\t\\t\\t\\t\\t// remove all prerendered queries\\n\\t\\t\\t\\t\\t\\tprops.data = {}\\n\\t\\t\\t\\t\\t});\\n\\n                    import.meta.hot.on(\\"evidence:queryChange\\", ({queryId, content}) => {\\n                        let errors = []\\n                        if (!queryId) errors.push(\\"Malformed event: Missing queryId\\")\\n                        if (!content) errors.push(\\"Malformed event: Missing content\\")\\n                        if (errors.length) {\\n                            console.warn(\\"Failed to update query on serverside change!\\", errors.join(\\"\\\\n\\"))\\n                            return\\n                        }\\n\\n                        if (queryId === \\"categories\\") {\\n                            __categoriesText = content\\n                        }\\n                        \\n                    })\\n                }\\n\\n                let categoriesInitialStates = { initialData: undefined, initialError: undefined }\\n                \\n                // Give initial states for these variables\\n                /** @type {boolean} */\\n                let __categoriesHasUnresolved = hasUnsetValues\`select\\n      category\\n  from needful_things.orders\\n  group by category\`;\\n                /** @type {string} */\\n                let __categoriesText = \`select\\n      category\\n  from needful_things.orders\\n  group by category\`\\n\\n\\n                if (browser) {\\n                    // Data came from SSR\\n                    if (data.categories_data) {\\n                        // vvv is this still used/possible?\\n                        if (data.categories_data instanceof Error) {\\n                            categoriesInitialStates.initialError = data.categories_data\\n                        } else {\\n                            categoriesInitialStates.initialData = data.categories_data\\n                        }\\n                        if (data.categories_columns) {\\n                            categoriesInitialStates.knownColumns = data.categories_columns\\n                        }\\n                    }\\n                }\\n\\n                /** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n                let categories;\\n\\n                $: __categoriesHasUnresolved = hasUnsetValues\`select\\n      category\\n  from needful_things.orders\\n  group by category\`;\\n                $: __categoriesText = \`select\\n      category\\n  from needful_things.orders\\n  group by category\`\\n\\n                // keep initial state around until after the query has resolved once\\n                let __categoriesInitialFactory = false;\\n                $: if (__categoriesHasUnresolved || !__categoriesInitialFactory) {    \\n                    if (!__categoriesHasUnresolved) {\\n                        __categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved, ...categoriesInitialStates });\\n                        __categoriesInitialFactory = true;\\n                    }\\n                } else {\\n                    __categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved });\\n                }\\n\\n                const __categoriesFactory = Query.createReactive(\\n                    { callback: v => {\\n                        categories = v\\n                    }, execFn: queryFunc },\\n                    { id: 'categories', ...categoriesInitialStates }\\n                )\\n\\n                // Assign a value for the initial run-through\\n                // This is split because chicken / egg\\n                __categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved, ...categoriesInitialStates })\\n\\n                // Add queries to global scope inside symbols to ease debugging\\n                globalThis[Symbol.for(\\"categories\\")] = { get value() { return categories } }\\n                \\n                \\n            \\n\\n                // Update external queries\\n                if (import.meta?.hot) {\\n\\t\\t\\t\\t\\timport.meta.hot.on(\\"vite:beforeUpdate\\", () => {\\n\\t\\t\\t\\t\\t\\t// remove all prerendered queries\\n\\t\\t\\t\\t\\t\\tprops.data = {}\\n\\t\\t\\t\\t\\t});\\n\\n                    import.meta.hot.on(\\"evidence:queryChange\\", ({queryId, content}) => {\\n                        let errors = []\\n                        if (!queryId) errors.push(\\"Malformed event: Missing queryId\\")\\n                        if (!content) errors.push(\\"Malformed event: Missing content\\")\\n                        if (errors.length) {\\n                            console.warn(\\"Failed to update query on serverside change!\\", errors.join(\\"\\\\n\\"))\\n                            return\\n                        }\\n\\n                        if (queryId === \\"orders_by_category\\") {\\n                            __orders_by_categoryText = content\\n                        }\\n                        \\n                    })\\n                }\\n\\n                let orders_by_categoryInitialStates = { initialData: undefined, initialError: undefined }\\n                \\n                // Give initial states for these variables\\n                /** @type {boolean} */\\n                let __orders_by_categoryHasUnresolved = hasUnsetValues\`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n                /** @type {string} */\\n                let __orders_by_categoryText = \`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`\\n\\n\\n                if (browser) {\\n                    // Data came from SSR\\n                    if (data.orders_by_category_data) {\\n                        // vvv is this still used/possible?\\n                        if (data.orders_by_category_data instanceof Error) {\\n                            orders_by_categoryInitialStates.initialError = data.orders_by_category_data\\n                        } else {\\n                            orders_by_categoryInitialStates.initialData = data.orders_by_category_data\\n                        }\\n                        if (data.orders_by_category_columns) {\\n                            orders_by_categoryInitialStates.knownColumns = data.orders_by_category_columns\\n                        }\\n                    }\\n                }\\n\\n                /** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n                let orders_by_category;\\n\\n                $: __orders_by_categoryHasUnresolved = hasUnsetValues\`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n                $: __orders_by_categoryText = \`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`\\n\\n                // keep initial state around until after the query has resolved once\\n                let __orders_by_categoryInitialFactory = false;\\n                $: if (__orders_by_categoryHasUnresolved || !__orders_by_categoryInitialFactory) {    \\n                    if (!__orders_by_categoryHasUnresolved) {\\n                        __orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved, ...orders_by_categoryInitialStates });\\n                        __orders_by_categoryInitialFactory = true;\\n                    }\\n                } else {\\n                    __orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved });\\n                }\\n\\n                const __orders_by_categoryFactory = Query.createReactive(\\n                    { callback: v => {\\n                        orders_by_category = v\\n                    }, execFn: queryFunc },\\n                    { id: 'orders_by_category', ...orders_by_categoryInitialStates }\\n                )\\n\\n                // Assign a value for the initial run-through\\n                // This is split because chicken / egg\\n                __orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved, ...orders_by_categoryInitialStates })\\n\\n                // Add queries to global scope inside symbols to ease debugging\\n                globalThis[Symbol.for(\\"orders_by_category\\")] = { get value() { return orders_by_category } }\\n                \\n                \\n            \\n\\t\\t\\n\\t\\tif (!browser) {\\n\\t\\t\\tonDestroy(inputs_store.subscribe((inputs) => {\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t__orders_by_categoryHasUnresolved = hasUnsetValues\`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n\\t\\t\\t\\t\\t\\t__orders_by_categoryText = \`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n\\t\\t\\t\\t\\t\\t__orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved });\\n\\t\\t\\t\\t\\n\\t\\t\\t}));\\n\\t\\t}\\n\\t\\t\\n\\t\\t\\n\\t\\t\\n    \\n\\t\\n  let isDomoEnvironment = false;\\n  let loadedDatasets = [];\\n  let selectedDatasetId = '';\\n  let selectedDataset = null;\\n  let showWorkflowPicker = false;\\n  let showAnalysis = false;\\n\\n  // Check Domo environment\\n  if (typeof window !== 'undefined') {\\n    isDomoEnvironment = typeof window.domo !== 'undefined';\\n  }\\n\\n  function toggleWorkflowPicker() {\\n    showWorkflowPicker = !showWorkflowPicker;\\n    if (showWorkflowPicker) {\\n      // Initialize workflow picker when shown\\n      setTimeout(() => {\\n        initializeWorkflowPicker();\\n      }, 100);\\n    }\\n  }\\n\\n  function showAnalysisSection() {\\n    showAnalysis = true;\\n    // Scroll to analysis section\\n    setTimeout(() => {\\n      document.getElementById('analysis-section')?.scrollIntoView({ behavior: 'smooth' });\\n    }, 100);\\n  }\\n\\n  function initializeWorkflowPicker() {\\n    // Load the Domo integration script if not already loaded\\n    if (!window.domoDuckDBIntegration) {\\n      const script = document.createElement('script');\\n      script.src = '/static/domo-duckdb-integration.js';\\n      script.onload = function() {\\n        console.log('Domo integration script loaded');\\n      };\\n      document.head.appendChild(script);\\n    }\\n  }\\n<\/script>\\n<style>\\n  .domo-banner {\\n    margin: 20px 0;\\n    padding: 20px;\\n    border: 2px solid #00d4aa;\\n    border-radius: 12px;\\n    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);\\n    color: #065f46;\\n    text-align: center;\\n  }\\n\\n  .domo-banner h3 {\\n    margin: 0 0 10px 0;\\n    color: #047857;\\n  }\\n\\n  .dev-banner {\\n    margin: 20px 0;\\n    padding: 20px;\\n    border: 2px solid #f59e0b;\\n    border-radius: 12px;\\n    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);\\n    color: #92400e;\\n    text-align: center;\\n  }\\n\\n  .dev-banner h3 {\\n    margin: 0 0 10px 0;\\n    color: #d97706;\\n  }\\n\\n  .quick-start-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    margin: 30px 0;\\n  }\\n\\n  .quick-start-card {\\n    padding: 25px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 12px;\\n    background-color: white;\\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\\n    transition: transform 0.2s, box-shadow 0.2s;\\n  }\\n\\n  .quick-start-card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .quick-start-card h4 {\\n    margin: 0 0 15px 0;\\n    color: #1f2937;\\n    font-size: 18px;\\n  }\\n\\n  .quick-start-card p {\\n    margin: 0 0 20px 0;\\n    color: #6b7280;\\n    line-height: 1.5;\\n  }\\n\\n  .btn-primary, .btn-secondary {\\n    display: inline-block;\\n    padding: 12px 24px;\\n    border: none;\\n    border-radius: 8px;\\n    text-decoration: none;\\n    font-weight: 600;\\n    font-size: 14px;\\n    cursor: pointer;\\n    transition: all 0.2s;\\n  }\\n\\n  .btn-primary {\\n    background-color: #2563eb;\\n    color: white;\\n  }\\n\\n  .btn-primary:hover {\\n    background-color: #1d4ed8;\\n    text-decoration: none;\\n    color: white;\\n  }\\n\\n  .btn-secondary {\\n    background-color: #f3f4f6;\\n    color: #374151;\\n    border: 1px solid #d1d5db;\\n  }\\n\\n  .btn-secondary:hover {\\n    background-color: #e5e7eb;\\n    text-decoration: none;\\n    color: #374151;\\n  }\\n\\n\\n\\n  .next-steps {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 20px;\\n    margin: 30px 0;\\n  }\\n\\n  .step {\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: #fafafa;\\n  }\\n\\n  .step h4 {\\n    margin: 0 0 10px 0;\\n    color: #1f2937;\\n    font-size: 16px;\\n  }\\n\\n  .step p {\\n    margin: 0;\\n    color: #6b7280;\\n    line-height: 1.5;\\n  }\\n\\n  .step a {\\n    color: #2563eb;\\n    text-decoration: none;\\n  }\\n\\n  .step a:hover {\\n    text-decoration: underline;\\n  }\\n\\n  /* Workflow Toggle */\\n  .workflow-toggle {\\n    margin: 30px 0;\\n    text-align: center;\\n  }\\n\\n  .workflow-toggle button {\\n    margin: 0 10px;\\n  }\\n\\n  /* Workflow Picker Section */\\n  .workflow-picker-section {\\n    margin: 30px 0;\\n    padding: 30px;\\n    border: 2px solid #e5e7eb;\\n    border-radius: 12px;\\n    background-color: #f9fafb;\\n  }\\n\\n  .workflow-picker-section h3 {\\n    margin: 0 0 10px 0;\\n    color: #1f2937;\\n  }\\n\\n  .workflow-picker-section p {\\n    margin: 0 0 20px 0;\\n    color: #6b7280;\\n  }\\n\\n  /* Workflow Picker Styles */\\n  .workflow-picker {\\n    max-width: 800px;\\n    margin: 0 auto;\\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n  }\\n\\n  .workflow-step {\\n    margin: 25px 0;\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: white;\\n  }\\n\\n  .workflow-step label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: #374151;\\n  }\\n\\n  .dataset-dropdown,\\n  .workflow-step input,\\n  .workflow-step select {\\n    width: 100%;\\n    padding: 10px 12px;\\n    border: 1px solid #d1d5db;\\n    border-radius: 6px;\\n    font-size: 14px;\\n    background-color: white;\\n  }\\n\\n  .dataset-dropdown:focus,\\n  .workflow-step input:focus,\\n  .workflow-step select:focus {\\n    outline: none;\\n    border-color: #3b82f6;\\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n  }\\n\\n  .dataset-preview {\\n    margin-top: 20px;\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: white;\\n  }\\n\\n  .dataset-info {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 15px;\\n    margin-bottom: 20px;\\n  }\\n\\n  .info-item {\\n    padding: 10px;\\n    background-color: #f3f4f6;\\n    border-radius: 4px;\\n  }\\n\\n  .schema-table table,\\n  .data-preview table,\\n  .data-preview-table {\\n    width: 100%;\\n    border-collapse: collapse;\\n    margin-top: 10px;\\n  }\\n\\n  .schema-table th,\\n  .schema-table td,\\n  .data-preview th,\\n  .data-preview td,\\n  .data-preview-table th,\\n  .data-preview-table td {\\n    padding: 8px 12px;\\n    text-align: left;\\n    border: 1px solid #e5e7eb;\\n  }\\n\\n  .schema-table th,\\n  .data-preview th,\\n  .data-preview-table th {\\n    background-color: #f3f4f6;\\n    font-weight: 600;\\n  }\\n\\n  .data-preview {\\n    margin-top: 15px;\\n    padding: 15px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 6px;\\n    background-color: #f9fafb;\\n  }\\n\\n  .config-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 20px;\\n  }\\n\\n  .workflow-actions {\\n    text-align: center;\\n    margin: 30px 0;\\n  }\\n\\n  .btn {\\n    padding: 12px 24px;\\n    border: none;\\n    border-radius: 6px;\\n    font-size: 14px;\\n    font-weight: 600;\\n    cursor: pointer;\\n    transition: all 0.2s;\\n    margin: 0 5px;\\n  }\\n\\n  .btn:disabled {\\n    opacity: 0.6;\\n    cursor: not-allowed;\\n  }\\n\\n  .btn-primary {\\n    background-color: #3b82f6;\\n    color: white;\\n  }\\n\\n  .btn-primary:hover:not(:disabled) {\\n    background-color: #2563eb;\\n  }\\n\\n  .btn-secondary {\\n    background-color: #f3f4f6;\\n    color: #374151;\\n    border: 1px solid #d1d5db;\\n  }\\n\\n  .btn-secondary:hover:not(:disabled) {\\n    background-color: #e5e7eb;\\n  }\\n\\n  .loading-status {\\n    margin: 20px 0;\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: #f9fafb;\\n    text-align: center;\\n  }\\n\\n  .loading-overlay {\\n    position: fixed;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background-color: rgba(0, 0, 0, 0.5);\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n    justify-content: center;\\n    z-index: 1000;\\n  }\\n\\n  .loading-spinner {\\n    width: 40px;\\n    height: 40px;\\n    border: 4px solid #f3f3f3;\\n    border-top: 4px solid #3b82f6;\\n    border-radius: 50%;\\n    animation: spin 1s linear infinite;\\n    margin-bottom: 16px;\\n  }\\n\\n  .loading-overlay p {\\n    color: white;\\n    font-size: 16px;\\n    margin: 0;\\n  }\\n\\n  @keyframes spin {\\n    0% { transform: rotate(0deg); }\\n    100% { transform: rotate(360deg); }\\n  }\\n\\n  .preview-actions {\\n    margin: 15px 0;\\n  }\\n\\n  .data-preview {\\n    margin-top: 20px;\\n    max-height: 300px;\\n    overflow: auto;\\n  }\\n\\n  /* Analysis Section */\\n  #analysis-section {\\n    margin: 40px 0;\\n    padding: 30px;\\n    border: 2px solid #10b981;\\n    border-radius: 12px;\\n    background-color: #f0fdf4;\\n  }\\n\\n  #analysis-section h2 {\\n    margin: 0 0 20px 0;\\n    color: #047857;\\n  }\\n\\n  .loaded-datasets-summary {\\n    margin: 15px 0;\\n  }\\n\\n  .loaded-datasets-summary ul {\\n    margin: 10px 0;\\n    padding-left: 20px;\\n  }\\n\\n  .loaded-datasets-summary li {\\n    margin: 5px 0;\\n  }\\n\\n  .analysis-examples {\\n    margin: 30px 0;\\n  }\\n\\n  .analysis-examples h3 {\\n    color: #047857;\\n    margin-bottom: 15px;\\n  }\\n\\n  .query-example {\\n    margin: 20px 0;\\n    padding: 20px;\\n    border: 1px solid #d1fae5;\\n    border-radius: 8px;\\n    background-color: white;\\n  }\\n\\n  .query-example h4 {\\n    margin: 0 0 10px 0;\\n    color: #065f46;\\n  }\\n\\n  .query-example pre {\\n    margin: 10px 0 0 0;\\n    padding: 15px;\\n    background-color: #f3f4f6;\\n    border-radius: 6px;\\n    overflow-x: auto;\\n  }\\n\\n  .query-example code {\\n    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\\n    font-size: 13px;\\n    line-height: 1.4;\\n  }\\n</style>\\n\\n\\n<h1 class=\\"markdown\\" id=\\"evidence-dashboard-for-domo-ddx\\"><a href=\\"#evidence-dashboard-for-domo-ddx\\">Evidence Dashboard for Domo DDX</a></h1>\\n{#if isDomoEnvironment}\\n  <div class=\\"domo-banner\\">\\n    <h3>🚀 Running in Domo DDX Environment</h3>\\n    <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p>\\n  </div>\\n{:else}\\n  <div class=\\"dev-banner\\">\\n    <h3>🛠️ Development Mode</h3>\\n    <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p>\\n  </div>\\n{/if}\\n<p\\n  class=\\"markdown\\"\\n>Welcome to your Evidence dashboard! This application combines the power of Evidence's analytics framework with Domo's data platform, allowing you to create interactive dashboards and reports using your Domo datasets.</p>\\n<h2 class=\\"markdown\\" id=\\"domo-dataset-workflow--analysis\\"><a href=\\"#domo-dataset-workflow--analysis\\">Domo Dataset Workflow & Analysis</a></h2>\\n<div class=\\"workflow-toggle\\">\\n  <button on:click={toggleWorkflowPicker} class=\\"btn-primary\\">\\n    {showWorkflowPicker ? '📊 Hide' : '📊 Load'} Domo Dataset\\n  </button>\\n  {#if loadedDatasets.length > 0}\\n    <button on:click={showAnalysisSection} class=\\"btn-secondary\\">\\n      📈 View Analysis\\n    </button>\\n  {/if}\\n</div>\\n{#if showWorkflowPicker}\\n  <!-- Domo Workflow Picker -->\\n  <div class=\\"workflow-picker-section\\">\\n    <h3>📊 Load Domo Dataset</h3>\\n    <p>Select and load Domo datasets into DuckDB for analysis</p>\\n    <div id=\\"domo-workflow-picker\\" class=\\"workflow-picker\\">\\n      <div class=\\"workflow-step\\">\\n        <label for=\\"dataset-selector\\">Select Dataset:</label>\\n        <select id=\\"dataset-selector\\" class=\\"dataset-dropdown\\">\\n          <option value=\\"\\">Choose a dataset...</option>\\n        </select>\\n      </div>\\n      <div id=\\"dataset-preview\\" class=\\"dataset-preview\\" style=\\"display: none;\\">\\n        <h4>Dataset Information</h4>\\n        <div id=\\"preview-content\\" class=\\"preview-content\\">\\n          <div id=\\"dataset-info\\" class=\\"dataset-info\\"></div>\\n          <h5>Schema</h5>\\n          <div id=\\"schema-table\\" class=\\"schema-table\\"></div>\\n          <div class=\\"preview-actions\\">\\n            <button id=\\"preview-btn\\" class=\\"btn btn-secondary\\">Preview Data</button>\\n          </div>\\n          <div id=\\"data-preview\\" class=\\"data-preview\\" style=\\"display: none;\\"></div>\\n        </div>\\n      </div>\\n      <div id=\\"loading-config\\" class=\\"workflow-step\\" style=\\"display: none;\\">\\n        <h4>Loading Configuration</h4>\\n        <div class=\\"config-grid\\">\\n          <div class=\\"config-item\\">\\n            <label for=\\"table-name\\">Table Name in DuckDB:</label>\\n            <input id=\\"table-name\\" type=\\"text\\" placeholder=\\"Enter table name\\" />\\n          </div>\\n          <div class=\\"config-item\\">\\n            <label for=\\"refresh-mode\\">Refresh Mode:</label>\\n            <select id=\\"refresh-mode\\">\\n              <option value=\\"replace\\">Replace existing data</option>\\n              <option value=\\"append\\">Append to existing data</option>\\n            </select>\\n          </div>\\n        </div>\\n      </div>\\n      <div id=\\"workflow-actions\\" class=\\"workflow-actions\\" style=\\"display: none;\\">\\n        <button id=\\"load-dataset-btn\\" class=\\"btn btn-primary\\">📊 Load Dataset into DuckDB</button>\\n      </div>\\n      <div id=\\"loading-status\\" class=\\"loading-status\\" style=\\"display: none;\\">\\n        <div class=\\"loading-spinner\\"></div>\\n        <p>Loading...</p>\\n      </div>\\n    </div>\\n  </div>\\n{/if}\\n<h2 class=\\"markdown\\" id=\\"sample-analysis\\"><a href=\\"#sample-analysis\\">Sample Analysis</a></h2>\\n<div id=\\"sample-analysis\\"></div>\\n<Details title='How Evidence Works with Your Data'>\\n  This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.\\n</Details>\\n\\n        {#if categories }\\n            <QueryViewer\\n                queryID = \\"categories\\"\\n                queryResult = {categories}\\n            /> \\n        {/if}\\n<Dropdown data={categories} name=category value=category>\\n    <DropdownOption value=\\"%\\" valueLabel=\\"All Categories\\"/>\\n</Dropdown>\\n<Dropdown name=year>\\n    <DropdownOption value=% valueLabel=\\"All Years\\"/>\\n    <DropdownOption value=2019/>\\n    <DropdownOption value=2020/>\\n    <DropdownOption value=2021/>\\n</Dropdown>\\n\\n        {#if orders_by_category }\\n            <QueryViewer\\n                queryID = \\"orders_by_category\\"\\n                queryResult = {orders_by_category}\\n            /> \\n        {/if}\\n<BarChart\\n    data={orders_by_category}\\n    title=\\"Sales by Month, {inputs.category.label}\\"\\n    x=month\\n    y=sales_usd\\n    series=category\\n/>\\n{#if showAnalysis}\\n  <div id=\\"analysis-section\\">\\n    <h2>📈 Data Analysis</h2>\\n    <Details title='Loaded Datasets'>\\n      <div class=\\"loaded-datasets-summary\\">\\n        {#if loadedDatasets.length > 0}\\n          <p>You have loaded {loadedDatasets.length} dataset(s) for analysis:</p>\\n          <ul>\\n            {#each loadedDatasets as dataset}\\n              <li><strong>{dataset.tableName}</strong> - {dataset.datasetName} ({dataset.rowCount.toLocaleString()} rows)</li>\\n            {/each}\\n          </ul>\\n        {:else}\\n          <p>No datasets loaded yet. Use the workflow picker above to load data.</p>\\n        {/if}\\n      </div>\\n    </Details>\\n    <div class=\\"analysis-examples\\">\\n      <h3>Example Queries</h3>\\n      <p>Here are some example queries you can run on your loaded data. Replace <code>your_table_name</code> with the actual table name from your loaded datasets.</p>\\n      <div class=\\"query-example\\">\\n        <h4>Basic Data Exploration</h4>\\n        <pre><code>-- Get row count and basic stats\\nSELECT\\n  COUNT(*) as total_rows,\\n  COUNT(DISTINCT column_name) as unique_values\\nFROM your_table_name;</code></pre>\\n      </div>\\n      <div class=\\"query-example\\">\\n        <h4>Time Series Analysis</h4>\\n        <pre><code>-- Aggregate by date (if you have date columns)\\nSELECT\\n  DATE_TRUNC('month', date_column) as month,\\n  SUM(numeric_column) as total\\nFROM your_table_name\\nGROUP BY month\\nORDER BY month;</code></pre>\\n      </div>\\n      <div class=\\"query-example\\">\\n        <h4>Category Analysis</h4>\\n        <pre><code>-- Group by categorical columns\\nSELECT\\n  category_column,\\n  COUNT(*) as count,\\n  AVG(numeric_column) as average\\nFROM your_table_name\\nGROUP BY category_column\\nORDER BY count DESC;</code></pre>\\n      </div>\\n    </div>\\n  </div>\\n{/if}\\n<h2 class=\\"markdown\\" id=\\"whats-next\\"><a href=\\"#whats-next\\">What's Next?</a></h2>\\n<div class=\\"next-steps\\">\\n  <div class=\\"step\\">\\n    <h4>1. Load Your Data</h4>\\n    <p>Use the workflow picker above to select and load Domo datasets into DuckDB</p>\\n  </div>\\n  <div class=\\"step\\">\\n    <h4>2. Create Queries</h4>\\n    <p>Write SQL queries against your loaded data using Evidence's query blocks</p>\\n  </div>\\n  <div class=\\"step\\">\\n    <h4>3. Build Visualizations</h4>\\n    <p>Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p>\\n  </div>\\n  <div class=\\"step\\">\\n    <h4>4. Deploy to Domo</h4>\\n    <p>Package your Evidence app and deploy it to Domo DDX for your team to use</p>\\n  </div>\\n</div>\\n\\n"],"names":[],"mappings":"AAwZE,0CAAa,CACX,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,gBAAgB,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAC7D,KAAK,CAAE,OAAO,CACd,UAAU,CAAE,MACd,CAEA,2BAAY,CAAC,iBAAG,CACd,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,yCAAY,CACV,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,gBAAgB,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAC7D,KAAK,CAAE,OAAO,CACd,UAAU,CAAE,MACd,CAEA,0BAAW,CAAC,iBAAG,CACb,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAmCA,0CAAY,CAAE,4CAAe,CAC3B,OAAO,CAAE,YAAY,CACrB,OAAO,CAAE,IAAI,CAAC,IAAI,CAClB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,IAAI,CACrB,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,IAClB,CAEA,0CAAa,CACX,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,KACT,CAEA,0CAAY,MAAO,CACjB,gBAAgB,CAAE,OAAO,CACzB,eAAe,CAAE,IAAI,CACrB,KAAK,CAAE,KACT,CAEA,4CAAe,CACb,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,OAAO,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OACpB,CAEA,4CAAc,MAAO,CACnB,gBAAgB,CAAE,OAAO,CACzB,eAAe,CAAE,IAAI,CACrB,KAAK,CAAE,OACT,CAIA,yCAAY,CACV,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,CACT,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,mCAAM,CACJ,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OACpB,CAEA,oBAAK,CAAC,iBAAG,CACP,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,IACb,CAEA,oBAAK,CAAC,gBAAE,CACN,MAAM,CAAE,CAAC,CACT,KAAK,CAAE,OAAO,CACd,WAAW,CAAE,GACf,CAYA,8CAAiB,CACf,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,UAAU,CAAE,MACd,CAEA,+BAAgB,CAAC,qBAAO,CACtB,MAAM,CAAE,CAAC,CAAC,IACZ,CAGA,sDAAyB,CACvB,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,gBAAgB,CAAE,OACpB,CAEA,uCAAwB,CAAC,iBAAG,CAC1B,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,uCAAwB,CAAC,gBAAE,CACzB,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAGA,8CAAiB,CACf,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,WAAW,CAAE,aAAa,CAAC,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,UACtE,CAEA,4CAAe,CACb,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KACpB,CAEA,6BAAc,CAAC,oBAAM,CACnB,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,GAAG,CAClB,WAAW,CAAE,GAAG,CAChB,KAAK,CAAE,OACT,CAEA,+CAAiB,CACjB,6BAAc,CAAC,oBAAK,CACpB,6BAAc,CAAC,qBAAO,CACpB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,CAAC,IAAI,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,IAAI,CACf,gBAAgB,CAAE,KACpB,CAEA,+CAAiB,MAAM,CACvB,6BAAc,CAAC,oBAAK,MAAM,CAC1B,6BAAc,CAAC,qBAAM,MAAO,CAC1B,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,OAAO,CACrB,UAAU,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAC9C,CAEA,8CAAiB,CACf,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KACpB,CAEA,2CAAc,CACZ,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,CACT,aAAa,CAAE,IACjB,CAkCA,2CAAc,CACZ,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OACpB,CAEA,0CAAa,CACX,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IACP,CAEA,+CAAkB,CAChB,UAAU,CAAE,MAAM,CAClB,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,kCAAK,CACH,OAAO,CAAE,IAAI,CAAC,IAAI,CAClB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,GAAG,CAChB,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,IAAI,CACpB,MAAM,CAAE,CAAC,CAAC,GACZ,CAEA,kCAAI,SAAU,CACZ,OAAO,CAAE,GAAG,CACZ,MAAM,CAAE,WACV,CAEA,0CAAa,CACX,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,KACT,CAEA,0CAAY,MAAM,KAAK,SAAS,CAAE,CAChC,gBAAgB,CAAE,OACpB,CAEA,4CAAe,CACb,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,OAAO,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OACpB,CAEA,4CAAc,MAAM,KAAK,SAAS,CAAE,CAClC,gBAAgB,CAAE,OACpB,CAEA,6CAAgB,CACd,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OAAO,CACzB,UAAU,CAAE,MACd,CAgBA,8CAAiB,CACf,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CAC7B,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,mBAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAClC,aAAa,CAAE,IACjB,CAQA,WAAW,mBAAK,CACd,EAAG,CAAE,SAAS,CAAE,OAAO,IAAI,CAAG,CAC9B,IAAK,CAAE,SAAS,CAAE,OAAO,MAAM,CAAG,CACpC,CAEA,8CAAiB,CACf,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,2CAAc,CACZ,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,IACZ,CAGA,+CAAkB,CAChB,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,gBAAgB,CAAE,OACpB,CAEA,gCAAiB,CAAC,iBAAG,CACnB,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,sDAAyB,CACvB,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,uCAAwB,CAAC,iBAAG,CAC1B,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,YAAY,CAAE,IAChB,CAEA,uCAAwB,CAAC,iBAAG,CAC1B,MAAM,CAAE,GAAG,CAAC,CACd,CAEA,gDAAmB,CACjB,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,iCAAkB,CAAC,iBAAG,CACpB,KAAK,CAAE,OAAO,CACd,aAAa,CAAE,IACjB,CAEA,4CAAe,CACb,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KACpB,CAEA,6BAAc,CAAC,iBAAG,CAChB,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,6BAAc,CAAC,kBAAI,CACjB,MAAM,CAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAClB,OAAO,CAAE,IAAI,CACb,gBAAgB,CAAE,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,UAAU,CAAE,IACd,CAEA,6BAAc,CAAC,mBAAK,CAClB,WAAW,CAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CACxD,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,GACf"}`},J={title:"Evidence Dashboard for Domo"},Dl=P((o,e,t,n)=>{let a,r,l,A;r=U(ko,p=>a=p),A=U(ia,p=>l=p);let{data:v}=e,{data:d={},customFormattingSettings:C,__db:y,inputs:f}=v;So(ia,l="6666cd76f96956469e7be39d750cc7d9",l);let s=Cn(Jt(f));yt(s.subscribe(p=>f=p)),Lt(In,{getCustomFormats:()=>C.customFormats||[]}),yt(()=>Dt.emptyCache());const g=(p,x)=>hn(y.query,p,{query_name:x});Bn(g),a.params;let c={initialData:void 0,initialError:void 0},u=_t`select
      category
  from needful_things.orders
  group by category`,E=`select
      category
  from needful_things.orders
  group by category`,M,w=!1;const S=Dt.createReactive({callback:p=>{M=p},execFn:g},{id:"categories",...c});S(E,{noResolve:u,...c}),globalThis[Symbol.for("categories")]={get value(){return M}};let T={initialData:void 0,initialError:void 0},I=_t`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${f.category.value}'
  and date_part('year', order_datetime) like '${f.year.value}'
  group by all
  order by sales_usd desc`,z=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${f.category.value}'
  and date_part('year', order_datetime) like '${f.year.value}'
  group by all
  order by sales_usd desc`,O,B=!1;const m=Dt.createReactive({callback:p=>{O=p},execFn:g},{id:"orders_by_category",...T});m(z,{noResolve:I,...T}),globalThis[Symbol.for("orders_by_category")]={get value(){return O}},yt(s.subscribe(p=>{I=_t`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${p.category.value}'
  and date_part('year', order_datetime) like '${p.year.value}'
  group by all
  order by sales_usd desc`,z=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${p.category.value}'
  and date_part('year', order_datetime) like '${p.year.value}'
  group by all
  order by sales_usd desc`,m(z,{noResolve:I})}));let i=!1,b=[];return typeof window<"u"&&(i=typeof window.domo<"u"),e.data===void 0&&t.data&&v!==void 0&&t.data(v),o.css.add($r),{data:d={},customFormattingSettings:C,__db:y}=v,mn.set(Object.keys(d).length>0),a.params,u=_t`select
      category
  from needful_things.orders
  group by category`,E=`select
      category
  from needful_things.orders
  group by category`,u||!w?u||(S(E,{noResolve:u,...c}),w=!0):S(E,{noResolve:u}),I=_t`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${f.category.value}'
  and date_part('year', order_datetime) like '${f.year.value}'
  group by all
  order by sales_usd desc`,z=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${f.category.value}'
  and date_part('year', order_datetime) like '${f.year.value}'
  group by all
  order by sales_usd desc`,I||!B?I||(m(z,{noResolve:I,...T}),B=!0):m(z,{noResolve:I}),r(),A(),`  ${typeof J<"u"&&J.title&&J.hide_title!==!0?`<h1 class="title">${D(J.title)}</h1>`:""} ${o.head+=`<!-- HEAD_svelte-2igo1p_START -->${typeof J<"u"&&J.title?`${o.title=`<title>${D(J.title)}</title>`,""} <meta property="og:title"${q("content",J.og?.title??J.title,0)}> <meta name="twitter:title"${q("content",J.og?.title??J.title,0)}>`:` ${o.title="<title>Evidence</title>",""}`}<meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev">${typeof J=="object"?`${J.description||J.og?.description?`<meta name="description"${q("content",J.description??J.og?.description,0)}> <meta property="og:description"${q("content",J.og?.description??J.description,0)}> <meta name="twitter:description"${q("content",J.og?.description??J.description,0)}>`:""} ${J.og?.image?`<meta property="og:image"${q("content",sa(J.og?.image),0)}> <meta name="twitter:image"${q("content",sa(J.og?.image),0)}>`:""}`:""}<!-- HEAD_svelte-2igo1p_END -->`,""}    <h1 class="markdown" id="evidence-dashboard-for-domo-ddx" data-svelte-h="svelte-dwxo2v"><a href="#evidence-dashboard-for-domo-ddx">Evidence Dashboard for Domo DDX</a></h1> ${i?'<div class="domo-banner svelte-16omzez" data-svelte-h="svelte-1uzwljv"><h3 class="svelte-16omzez">🚀 Running in Domo DDX Environment</h3> <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p></div>':'<div class="dev-banner svelte-16omzez" data-svelte-h="svelte-1b532x0"><h3 class="svelte-16omzez">🛠️ Development Mode</h3> <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p></div>'} <p class="markdown" data-svelte-h="svelte-17kjwqn">Welcome to your Evidence dashboard! This application combines the power of Evidence&#39;s analytics framework with Domo&#39;s data platform, allowing you to create interactive dashboards and reports using your Domo datasets.</p> <h2 class="markdown" id="domo-dataset-workflow--analysis" data-svelte-h="svelte-d64fj6"><a href="#domo-dataset-workflow--analysis">Domo Dataset Workflow &amp; Analysis</a></h2> <div class="workflow-toggle svelte-16omzez"><button class="btn-primary svelte-16omzez">${D("📊 Load")} Domo Dataset</button> ${b.length>0?'<button class="btn-secondary svelte-16omzez" data-svelte-h="svelte-1myai4g">📈 View Analysis</button>':""}</div>  <h2 class="markdown" id="sample-analysis" data-svelte-h="svelte-1941s2m"><a href="#sample-analysis">Sample Analysis</a></h2> <div id="sample-analysis"></div> ${k(Sr,"Details").$$render(o,{title:"How Evidence Works with Your Data"},{},{default:()=>"This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data."})} ${M?`${k(Oa,"QueryViewer").$$render(o,{queryID:"categories",queryResult:M},{},{})}`:""} ${k(wa,"Dropdown").$$render(o,{data:M,name:"category",value:"category"},{},{default:()=>`${k(ht,"DropdownOption").$$render(o,{value:"%",valueLabel:"All Categories"},{},{})}`})} ${k(wa,"Dropdown").$$render(o,{name:"year"},{},{default:()=>`${k(ht,"DropdownOption").$$render(o,{value:"%",valueLabel:"All Years"},{},{})} ${k(ht,"DropdownOption").$$render(o,{value:"2019"},{},{})} ${k(ht,"DropdownOption").$$render(o,{value:"2020"},{},{})} ${k(ht,"DropdownOption").$$render(o,{value:"2021"},{},{})}`})} ${O?`${k(Oa,"QueryViewer").$$render(o,{queryID:"orders_by_category",queryResult:O},{},{})}`:""} ${k(Jr,"BarChart").$$render(o,{data:O,title:"Sales by Month, "+f.category.label,x:"month",y:"sales_usd",series:"category"},{},{})}  <h2 class="markdown" id="whats-next" data-svelte-h="svelte-fy128a"><a href="#whats-next">What&#39;s Next?</a></h2> <div class="next-steps svelte-16omzez" data-svelte-h="svelte-pm10gv"><div class="step svelte-16omzez"><h4 class="svelte-16omzez">1. Load Your Data</h4> <p class="svelte-16omzez">Use the workflow picker above to select and load Domo datasets into DuckDB</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">2. Create Queries</h4> <p class="svelte-16omzez">Write SQL queries against your loaded data using Evidence&#39;s query blocks</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">3. Build Visualizations</h4> <p class="svelte-16omzez">Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">4. Deploy to Domo</h4> <p class="svelte-16omzez">Package your Evidence app and deploy it to Domo DDX for your team to use</p></div></div>`});export{Dl as default};
