import{c as F,b as O,v as f,h as C,i as st,j as ft,o as Qe,f as nn,g as on,s as an}from"../../../../chunks/ssr.js";import"dequal";import{O as ue,l as ee,M as qe,L as ln}from"../../../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import"clsx";import de from"chroma-js";import{Q as fe,t as rn}from"../../../../chunks/Query.js";import"@uwdata/mosaic-sql";import{query as sn}from"@evidence-dev/universal-sql/client-duckdb";import"ssf";import"@tidyjs/tidy";import"deep-object-diff";import"../../../../chunks/index5.js";import{c as dn,s as z,n as $,d as cn,a as un}from"../../../../chunks/utils.js";import{tv as Ge}from"tailwind-variants";import{I as Ct,d as An,C as Ye,e as mn,g as fn,t as K,h as Cn,i as vn,j as hn,E as pn,k as gn,P as wn}from"../../../../chunks/index7.js";import{c as Ee,g as Be,d as q,a as dt,V as He,Q as Te,h as Xe,i as Ze,m as bn,S as yn,D as Ne,C as xn,E as Je,n as Fe}from"../../../../chunks/CodeBlock.js";import{w as Sn}from"../../../../chunks/index2.js";import{b as kn}from"../../../../chunks/environment.js";import{a as ke}from"../../../../chunks/index6.js";import{init as En}from"echarts";import{b as Bn}from"../../../../chunks/index4.js";import{a as Tn,E as In}from"../../../../chunks/Info.js";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import"export-to-csv";import"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const Re=F((o,t,e,g)=>{let{icon:a=void 0}=t,{iconPosition:r="right"}=t,{size:i="default"}=t,{variant:c="default"}=t,{disabled:s=!1}=t,{formaction:m=void 0}=t,{class:A=void 0}=t,{type:h="button"}=t;const d=Ge({base:"inline-flex items-center justify-center rounded-md text-xs font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-base-300 disabled:pointer-events-none disabled:opacity-50",variants:{variant:{default:"bg-base-100 border shadow-sm hover:bg-base-200 active:bg-base-300",primary:"bg-primary text-primary-content shadow-sm hover:bg-primary/90  active:bg-primary/80",destructive:"bg-negative text-negative-content shadow-sm hover:bg-negative/90 active:bg-negative/80",muted:"bg-base-200 text-base-content hover:bg-base-300 active:bg-base-300/80",ghost:"hover:bg-base-200 hover:text-base-content",link:"text-base-content underline-offset-4 hover:underline"},size:{default:"h-8 px-3",lg:"h-8 px-10",xl:"h-10 px-10 text-sm"}},defaultVariants:{variant:"default",size:"default"}}),l=Ge({variants:{variant:{default:"stroke-base-content",primary:"stroke-primary-content",destructive:"stroke-negative-content",muted:"stroke-base-content",ghost:"stroke-base-content",link:"stroke-base-content"},size:{default:"h-4 w-4",lg:"h-4 w-4",xl:"h-5 w-5"},iconPosition:{left:"mr-2",right:"ml-2"}},defaultVariants:{variant:"default",size:"default",iconPosition:"left"}});return t.icon===void 0&&e.icon&&a!==void 0&&e.icon(a),t.iconPosition===void 0&&e.iconPosition&&r!==void 0&&e.iconPosition(r),t.size===void 0&&e.size&&i!==void 0&&e.size(i),t.variant===void 0&&e.variant&&c!==void 0&&e.variant(c),t.disabled===void 0&&e.disabled&&s!==void 0&&e.disabled(s),t.formaction===void 0&&e.formaction&&m!==void 0&&e.formaction(m),t.class===void 0&&e.class&&A!==void 0&&e.class(A),t.type===void 0&&e.type&&h!==void 0&&e.type(h),m&&(h="submit"),`<button${O("type",h,0)} ${s?"disabled":""}${O("formaction",m,0)}${O("class",d({variant:c,size:i,className:A}),0)}>${r==="left"&&a?`${f(Ct,"Icon").$$render(o,{src:a,class:l({variant:c,size:i,iconPosition:r})},{},{})}`:""} ${g.default?g.default({}):""} ${r==="right"&&a?`${f(Ct,"Icon").$$render(o,{src:a,class:l({variant:c,size:i,iconPosition:r})},{},{})}`:""}</button>`}),jn=F((o,t,e,g)=>{let{title:a}=t,{subtitle:r}=t;return t.title===void 0&&e.title&&a!==void 0&&e.title(a),t.subtitle===void 0&&e.subtitle&&r!==void 0&&e.subtitle(r),`<div class="mt-0 mb-0">${a?`<h4 class="${"text-[14px] text-base-heading font-bold mt-0 mb-"+C(r?"1":"2",!0)+" leading-none"}">${C(a)}</h4>`:""} ${r?`<p class="text-[13px] text-base-content-muted font-normal mt-0 mb-2">${C(r)}</p>`:""}</div>`}),Mn={code:".search-container.svelte-1eiyhgi{width:30%;display:block;align-items:center;position:relative;box-sizing:content-box}.search-icon.svelte-1eiyhgi{height:16px;width:16px;padding-left:3px;margin:0;position:absolute;top:50%;transform:translateY(-50%);-ms-transform:translateY(-50%);box-sizing:content-box}.search-bar.svelte-1eiyhgi{margin:0;position:absolute;top:50%;transform:translateY(-50%);-ms-transform:translateY(-50%);border:none;padding-left:23px;font-size:9pt;width:calc(100% - 10px);font-family:Arial;line-height:normal}.svelte-1eiyhgi:focus{outline:none}@media(max-width: 600px){.search-container.svelte-1eiyhgi{width:98%;height:28px}.search-bar.svelte-1eiyhgi{font-size:16px;width:calc(100% - 20px)}}@media print{.search-container.svelte-1eiyhgi{display:none}}",map:`{"version":3,"file":"SearchBar.svelte","sources":["SearchBar.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { Icon } from '@steeze-ui/svelte-icon';\\n\\timport { Search } from '@steeze-ui/tabler-icons';\\n\\n\\texport let placeholder = 'Search';\\n\\texport let value;\\n\\texport let searchFunction;\\n<\/script>\\n\\n<div class=\\"search-container bg-base-100 border border-base-300 shadow-sm h-7 rounded-md mb-1 mr-1\\">\\n\\t<input\\n\\t\\tclass=\\"search-bar bg-base-100 placeholder:text-base-content-muted/80\\"\\n\\t\\ttype=\\"text\\"\\n\\t\\t{placeholder}\\n\\t\\tbind:value\\n\\t\\ton:keyup={() => searchFunction(value)}\\n\\t/>\\n\\t<div class=\\"search-icon text-base-content-muted/80\\">\\n\\t\\t<Icon src={Search} class=\\"pl-0.5\\" />\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.search-container {\\n\\t\\twidth: 30%;\\n\\t\\tdisplay: block;\\n\\t\\talign-items: center;\\n\\t\\tposition: relative;\\n\\t\\tbox-sizing: content-box;\\n\\t}\\n\\n\\t.search-icon {\\n\\t\\theight: 16px;\\n\\t\\twidth: 16px;\\n\\t\\tpadding-left: 3px;\\n\\t\\tmargin: 0;\\n\\t\\tposition: absolute;\\n\\t\\ttop: 50%;\\n\\t\\ttransform: translateY(-50%);\\n\\t\\t-ms-transform: translateY(-50%);\\n\\t\\tbox-sizing: content-box;\\n\\t}\\n\\n\\t.search-bar {\\n\\t\\tmargin: 0;\\n\\t\\tposition: absolute;\\n\\t\\ttop: 50%;\\n\\t\\ttransform: translateY(-50%);\\n\\t\\t-ms-transform: translateY(-50%);\\n\\n\\t\\tborder: none;\\n\\t\\tpadding-left: 23px;\\n\\n\\t\\tfont-size: 9pt;\\n\\n\\t\\twidth: calc(100% - 10px);\\n\\n\\t\\tfont-family: Arial;\\n\\t\\tline-height: normal;\\n\\t}\\n\\n\\t*:focus {\\n\\t\\toutline: none;\\n\\t}\\n\\n\\t@media (max-width: 600px) {\\n\\t\\t.search-container {\\n\\t\\t\\twidth: 98%;\\n\\t\\t\\theight: 28px;\\n\\t\\t}\\n\\n\\t\\t.search-bar {\\n\\t\\t\\tfont-size: 16px;\\n\\t\\t\\twidth: calc(100% - 20px);\\n\\t\\t}\\n\\t}\\n\\n\\t@media print {\\n\\t\\t.search-container {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA2BC,gCAAkB,CACjB,KAAK,CAAE,GAAG,CACV,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,WACb,CAEA,2BAAa,CACZ,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,YAAY,CAAE,GAAG,CACjB,MAAM,CAAE,CAAC,CACT,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,aAAa,CAAE,WAAW,IAAI,CAAC,CAC/B,UAAU,CAAE,WACb,CAEA,0BAAY,CACX,MAAM,CAAE,CAAC,CACT,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,aAAa,CAAE,WAAW,IAAI,CAAC,CAE/B,MAAM,CAAE,IAAI,CACZ,YAAY,CAAE,IAAI,CAElB,SAAS,CAAE,GAAG,CAEd,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAExB,WAAW,CAAE,KAAK,CAClB,WAAW,CAAE,MACd,CAEA,eAAC,MAAO,CACP,OAAO,CAAE,IACV,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,gCAAkB,CACjB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,IACT,CAEA,0BAAY,CACX,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CACxB,CACD,CAEA,OAAO,KAAM,CACZ,gCAAkB,CACjB,OAAO,CAAE,IACV,CACD"}`},On=F((o,t,e,g)=>{let{placeholder:a="Search"}=t,{value:r}=t,{searchFunction:i}=t;return t.placeholder===void 0&&e.placeholder&&a!==void 0&&e.placeholder(a),t.value===void 0&&e.value&&r!==void 0&&e.value(r),t.searchFunction===void 0&&e.searchFunction&&i!==void 0&&e.searchFunction(i),o.css.add(Mn),`<div class="search-container bg-base-100 border border-base-300 shadow-sm h-7 rounded-md mb-1 mr-1 svelte-1eiyhgi"><input class="search-bar bg-base-100 placeholder:text-base-content-muted/80 svelte-1eiyhgi" type="text"${O("placeholder",a,0)}${O("value",r,0)}> <div class="search-icon text-base-content-muted/80 svelte-1eiyhgi">${f(Ct,"Icon").$$render(o,{src:An,class:"pl-0.5"},{},{})}</div> </div>`}),Le=F((o,t,e,g)=>{let{data:a}=t,{link:r}=t;return t.data===void 0&&e.data&&a!==void 0&&e.data(a),t.link===void 0&&e.link&&r!==void 0&&e.link(r),` ${kn?`${st(Array.from(new Set(a.map(i=>i[r]))),i=>`<a${O("href",ke(i),0)} class="hidden" aria-hidden="true">${C("")}</a>`)}`:""}`});function It(o,t){const e=t.filter(g=>g.id===o.id);if(e===void 0||e.length!==1){const g=o.id===void 0?new Error('please add an "id" property to all the <Column ... />'):new Error(`column with id: "${o.id}" not found`);if(ue)throw g;return console.warn(g.message),""}return e[0]}function Pe(o,t,e){if(!e||!o.length)return null;let g=0,a=0;return o.forEach(r=>{const i=Number(r[t]??0),c=Number(r[e]??0);g+=i*c,a+=c}),a>0?g/a:0}function ce(o,t,e,g,a=null){if(!e&&g==="number"&&(e="sum"),!o||!o.length)return null;if(g!=="number"&&["sum","min","max","mean","weightedMean","median",void 0].includes(e))return"-";const r=o.map(i=>i[t]).filter(i=>i!==void 0);switch(e){case"sum":return r.reduce((i,c)=>i+Number(c),0);case"min":return Math.min(...r);case"max":return Math.max(...r);case"mean":return r.length?r.reduce((i,c)=>i+Number(c),0)/r.length:"-";case"count":return o.length;case"countDistinct":return new Set(r).size;case"weightedMean":{if(!a)return"Weight column name required for weightedMean";let i=0,c=o.reduce((s,m)=>{const A=m[a]||0;return i+=A,s+(Number(m[t])||0)*A},0);return i>0?c/i:null}case"median":{const i=r.sort((s,m)=>s-m),c=Math.floor(i.length/2);return i.length%2!==0?i[c]:(i[c-1]+i[c])/2}default:return`${e}`}}function Dn(o,t){const e=o.filter(g=>!t.includes(g));return[...t,...e]}const _n=F((o,t,e,g)=>{let{data:a=void 0}=t,{row:r=0}=t,{column:i=void 0}=t,{value:c=void 0}=t,{text:s=void 0}=t,{chip:m=!1}=t,{downIsGood:A=!1}=t,{fmt:h=void 0}=t,{format_object:d=void 0}=t,{columnUnitSummary:l=void 0}=t,{showValue:v=!0}=t,{showSymbol:p=!0}=t,{symbolPosition:B="right"}=t,{align:b="right"}=t,{fontClass:x=m?"text-sm":"text-base"}=t,{neutralMin:k=0}=t,{neutralMax:D=0}=t;const w={positive:A?"text-negative":"text-positive",negative:A?"text-positive":"text-negative",neutral:"text-base-content-muted"},R={positive:A?"bg-negative/10 border border-negative/20":"bg-positive/10 border border-positive/20",negative:A?"bg-positive/10 border border-positive/20":"bg-negative/10 border border-negative/20",neutral:"bg-base-200 border border-base-300"};let n,L,T,W,y;t.data===void 0&&e.data&&a!==void 0&&e.data(a),t.row===void 0&&e.row&&r!==void 0&&e.row(r),t.column===void 0&&e.column&&i!==void 0&&e.column(i),t.value===void 0&&e.value&&c!==void 0&&e.value(c),t.text===void 0&&e.text&&s!==void 0&&e.text(s),t.chip===void 0&&e.chip&&m!==void 0&&e.chip(m),t.downIsGood===void 0&&e.downIsGood&&A!==void 0&&e.downIsGood(A),t.fmt===void 0&&e.fmt&&h!==void 0&&e.fmt(h),t.format_object===void 0&&e.format_object&&d!==void 0&&e.format_object(d),t.columnUnitSummary===void 0&&e.columnUnitSummary&&l!==void 0&&e.columnUnitSummary(l),t.showValue===void 0&&e.showValue&&v!==void 0&&e.showValue(v),t.showSymbol===void 0&&e.showSymbol&&p!==void 0&&e.showSymbol(p),t.symbolPosition===void 0&&e.symbolPosition&&B!==void 0&&e.symbolPosition(B),t.align===void 0&&e.align&&b!==void 0&&e.align(b),t.fontClass===void 0&&e.fontClass&&x!==void 0&&e.fontClass(x),t.neutralMin===void 0&&e.neutralMin&&k!==void 0&&e.neutralMin(k),t.neutralMax===void 0&&e.neutralMax&&D!==void 0&&e.neutralMax(D),m=m==="true"||m===!0,A=A==="true"||A===!0,v=v==="true"||v===!0,p=p==="true"||p===!0;try{if(n=void 0,a){if(typeof a=="string")throw Error(`Received: data=${a}, expected: data={${a}}`);if(Array.isArray(a)||(a=[a]),isNaN(r))throw Error("row must be a number (row="+r+")");try{Object.keys(a[r])[0]}catch{throw Error("Row "+r+" does not exist in the dataset")}i=i??Object.keys(a[r])[0],Ee(a,[i]),T=Be(a,"array"),L=a[r][i],T=T.filter(I=>I.id===i),h?W=q(h,T[0].format?.valueType):W=T[0].format}else if(c!==void 0){if(isNaN(c))throw Error("value must be a number (value="+c+")");L=c,W=h?q(h,"number"):d??void 0}else throw Error("No data or value provided. If you referenced a query result, check that the name is correct.");y=L>D?"positive":L<k?"negative":"neutral"}catch(I){if(n=I.message,console.error("\x1B[31m%s\x1B[0m",`Error in Value: ${n}`),ue)throw n}return`${n?`${f(He,"ValueError").$$render(o,{error:n},{},{})}`:`<span class="${["m-0 "+C(x,!0)+" font-ui inline-block rounded-md "+C(w[y],!0)+" "+C(m?R[y]:"",!0),m?"px-1":""].join(" ").trim()}"><span${ft({"text-align":b??"right"})}>${B==="right"?`${v?`${L===null?'<span class="font-[system-ui]" data-svelte-h="svelte-45ueay">–</span>':`<span>${C(dt(L,W,l))}</span>`}`:""} ${p?`<span class="font-[system-ui]"><!-- HTML_TAG_START -->${y==="positive"?"&#9650;":y==="negative"?"&#9660;":"–&thinsp;"}<!-- HTML_TAG_END --></span>`:""}`:`${p?`<span class="font-[system-ui]"><!-- HTML_TAG_START -->${y==="positive"?"&#9650;":y==="negative"?"&#9660;":"—"}<!-- HTML_TAG_END --></span>`:""} ${v?`${L===null?'<span class="font-[system-ui]" data-svelte-h="svelte-45ueay">–</span>':`<span>${C(dt(L,W,l))}</span>`}`:""}`} ${s?`<span>${C(s)}</span>`:""}</span></span>`}`}),{Object:Gn}=Xe;let Nn="Delta";const Ce=F((o,t,e,g)=>{let a,{data:r=void 0}=t;const i=typeof r=="object"&&"__isQueryStore"in r?r.hash:void 0;let c=r?.hash===i,{emptySet:s=void 0}=t,{emptyMessage:m=void 0}=t;return t.data===void 0&&e.data&&r!==void 0&&e.data(r),t.emptySet===void 0&&e.emptySet&&s!==void 0&&e.emptySet(s),t.emptyMessage===void 0&&e.emptyMessage&&m!==void 0&&e.emptyMessage(m),c=r?.hash===i,a=Object.fromEntries(Object.entries(t).filter(([,A])=>A!==void 0)),` ${f(Te,"QueryLoad").$$render(o,{data:r},{},{skeleton:()=>'<p slot="skeleton" class="text-base-content-muted" data-svelte-h="svelte-1vn8ohu">Loading...</p>',empty:()=>`<span slot="empty">${a.placeholder?"":`${f(Ze,"EmptyChart").$$render(o,{emptyMessage:m,emptySet:s,chartType:Nn,isInitial:c},{},{})}`}</span>`,default:({loaded:A})=>`${f(_n,"Delta").$$render(o,Gn.assign({},a,{data:A?.__isQueryStore?Array.from(A):A}),{},{})}`})}`}),Fn={code:".string.svelte-3nfh93{text-align:left}.date.svelte-3nfh93{text-align:left}.number.svelte-3nfh93{text-align:right}.boolean.svelte-3nfh93{text-align:left}.index.svelte-3nfh93{color:var(--base-content-muted);text-align:left;max-width:-moz-min-content;max-width:min-content}.svelte-3nfh93:focus{outline:none}",map:`{"version":3,"file":"TableCell.svelte","sources":["TableCell.svelte"],"sourcesContent":["<script>\\n\\texport let dataType = undefined;\\n\\texport let align = undefined;\\n\\texport let height = undefined;\\n\\texport let width = undefined;\\n\\texport let wrap = undefined;\\n\\n\\texport let verticalAlign = 'middle';\\n\\texport let rowSpan = 1;\\n\\texport let colSpan = 1;\\n\\texport let show = true;\\n\\texport let cellColor = undefined;\\n\\texport let fontColor = undefined;\\n\\n\\texport let topBorder = undefined;\\n\\texport let paddingLeft = undefined;\\n\\texport let borderBottom = undefined;\\n\\n\\texport let compact = false;\\n<\/script>\\n\\n<td\\n\\trole=\\"cell\\"\\n\\tclass=\\"{$$restProps.class ||\\n\\t\\t''} {dataType} {topBorder} whitespace-nowrap overflow-hidden first:pl-[3px]\\n\\t{compact ? 'text-xs py-[1px] pr-[16.5px]' : 'py-[2px] pl-[6px] pr-[13px]'}\\n\\t\\"\\n\\tstyle:text-align={align}\\n\\tstyle:height\\n\\tstyle:width\\n\\tstyle:white-space={wrap ? 'normal' : 'nowrap'}\\n\\tstyle:vertical-align={verticalAlign}\\n\\tstyle:display={show ? undefined : 'none'}\\n\\trowspan={rowSpan}\\n\\tcolspan={colSpan}\\n\\tstyle:background-color={cellColor}\\n\\tstyle:color={fontColor}\\n\\tstyle:padding-left={paddingLeft}\\n\\tstyle:border-bottom={borderBottom}\\n>\\n\\t<slot />\\n</td>\\n\\n<style>\\n\\t.string {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.date {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.number {\\n\\t\\ttext-align: right;\\n\\t}\\n\\n\\t.boolean {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.index {\\n\\t\\tcolor: var(--base-content-muted);\\n\\t\\ttext-align: left;\\n\\t\\tmax-width: -moz-min-content;\\n\\t\\tmax-width: min-content;\\n\\t}\\n\\n\\t*:focus {\\n\\t\\toutline: none;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4CC,qBAAQ,CACP,UAAU,CAAE,IACb,CAEA,mBAAM,CACL,UAAU,CAAE,IACb,CAEA,qBAAQ,CACP,UAAU,CAAE,KACb,CAEA,sBAAS,CACR,UAAU,CAAE,IACb,CAEA,oBAAO,CACN,KAAK,CAAE,IAAI,oBAAoB,CAAC,CAChC,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,gBAAgB,CAC3B,SAAS,CAAE,WACZ,CAEA,cAAC,MAAO,CACP,OAAO,CAAE,IACV"}`},jt=F((o,t,e,g)=>{let a=dn(t,["dataType","align","height","width","wrap","verticalAlign","rowSpan","colSpan","show","cellColor","fontColor","topBorder","paddingLeft","borderBottom","compact"]),{dataType:r=void 0}=t,{align:i=void 0}=t,{height:c=void 0}=t,{width:s=void 0}=t,{wrap:m=void 0}=t,{verticalAlign:A="middle"}=t,{rowSpan:h=1}=t,{colSpan:d=1}=t,{show:l=!0}=t,{cellColor:v=void 0}=t,{fontColor:p=void 0}=t,{topBorder:B=void 0}=t,{paddingLeft:b=void 0}=t,{borderBottom:x=void 0}=t,{compact:k=!1}=t;return t.dataType===void 0&&e.dataType&&r!==void 0&&e.dataType(r),t.align===void 0&&e.align&&i!==void 0&&e.align(i),t.height===void 0&&e.height&&c!==void 0&&e.height(c),t.width===void 0&&e.width&&s!==void 0&&e.width(s),t.wrap===void 0&&e.wrap&&m!==void 0&&e.wrap(m),t.verticalAlign===void 0&&e.verticalAlign&&A!==void 0&&e.verticalAlign(A),t.rowSpan===void 0&&e.rowSpan&&h!==void 0&&e.rowSpan(h),t.colSpan===void 0&&e.colSpan&&d!==void 0&&e.colSpan(d),t.show===void 0&&e.show&&l!==void 0&&e.show(l),t.cellColor===void 0&&e.cellColor&&v!==void 0&&e.cellColor(v),t.fontColor===void 0&&e.fontColor&&p!==void 0&&e.fontColor(p),t.topBorder===void 0&&e.topBorder&&B!==void 0&&e.topBorder(B),t.paddingLeft===void 0&&e.paddingLeft&&b!==void 0&&e.paddingLeft(b),t.borderBottom===void 0&&e.borderBottom&&x!==void 0&&e.borderBottom(x),t.compact===void 0&&e.compact&&k!==void 0&&e.compact(k),o.css.add(Fn),`<td role="cell" class="${C(a.class||"",!0)+" "+C(r,!0)+" "+C(B,!0)+" whitespace-nowrap overflow-hidden first:pl-[3px] "+C(k?"text-xs py-[1px] pr-[16.5px]":"py-[2px] pl-[6px] pr-[13px]",!0)+" svelte-3nfh93"}"${O("rowspan",h,0)}${O("colspan",d,0)}${ft({"text-align":i,height:c,width:s,"white-space":m?"normal":"nowrap","vertical-align":A,display:l?void 0:"none","background-color":v,color:p,"padding-left":b,"border-bottom":x})}>${g.default?g.default({}):""} </td>`});function Rn(o,t){if(o){if(o=Number(o),isNaN(o))throw Error("height must be a number");if(o<=0)throw Error("height must be a positive number")}else o=15;if(t){if(t=Number(t),isNaN(t))throw Error("width must be a number");if(t<=0)throw Error("width must be a positive number")}else t=50;return{height:o,width:t}}function Ln(o,t,e,g,a){const r=Be(o);if(r[e].type!=="date")throw Error("dateCol must be of type date");const i=r[t].format,c=r[e].format,s=g?q(g):i,m=a?q(a):c;return{value_format_object:s,date_format_object:m}}function Pn(o,t,e,g,a,r,i,c,s){return{title:{subtextStyle:{width:"100%"}},tooltip:{trigger:"axis",position:function(m,A,h,d,l){return[l.viewSize[0]/2-l.contentSize[0]/2,-11]},formatter:function(m){const A=m[0],h=`<div style="text-align: center; background-color: ${s.colors["base-100"]}; border-radius: 1px; padding: 0px 2px;">${dt(A.value[1],r)}</div>`,d=`<div style="background-color: transparent; height: ${c-1.5}px;"></div>`,l=`<div style="text-align: center; height: 1em; background-color: transparent; border-radius: 1px; padding: 0px 2px;">${dt(A.axisValueLabel,i)}</div>`;return h+d+l},backgroundColor:"transparent",borderWidth:0,borderColor:"transparent",extraCssText:"box-shadow: none; padding-bottom: 0;",padding:0,textStyle:{fontSize:9}},legend:{show:!1},grid:{left:0,right:0,bottom:0,top:0,containLabel:!0},xAxis:{type:"time",splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:s.colors["base-300"],width:.75}},axisLabel:{show:!1,hideOverlap:!0,showMaxLabel:!1,formatter:!1,margin:6},scale:!0,z:2,boundaryGap:"2%",axisPointer:{show:!0,snap:!0,type:"line",z:0,lineStyle:{width:.5},handle:{show:!1},label:{show:!1}}},yAxis:[{type:"value",logBase:10,splitLine:{show:!1},axisLine:{show:!1,onZero:!1},axisTick:{show:!1},axisLabel:{show:!1,hideOverlap:!0,margin:4},name:"",nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0]},nameGap:6,scale:a,boundaryGap:["1%","1%"],z:2}],series:[{type:e,triggerLineEvent:!0,label:{show:!1,position:"top",padding:0,fontSize:9},labelLayout:{hideOverlap:!0},connectNulls:!1,emphasis:{disabled:!0},lineStyle:{width:1,type:"solid",color:g??s.colors["base-content-muted"]},areaStyle:{color:t==="area"?g?de(g).brighten(1.5).hex():s.colors["base-300"]:"transparent"},itemStyle:{color:g??s.colors["base-content-muted"]},showSymbol:!1,symbol:"circle",symbolSize:0,step:!1,name:"sparkline",data:o,yAxisIndex:0}],animation:!1}}const xe=F((o,t,e,g)=>{let a,r,i,c,s=$,m=()=>(s(),s=z(a,P=>c=P),a);const{theme:A,resolveColor:h}=ee();i=z(A,P=>r=P);let{config:d={}}=t,{width:l=50}=t,{height:v=15}=t,{interactive:p=!0}=t,B,b=null,{data:x=void 0}=t,{dateCol:k=void 0}=t,{valueCol:D=void 0}=t,{valueFmt:w=void 0}=t,R,{dateFmt:n=void 0}=t,L,{type:T="line"}=t,{color:W=void 0}=t,{yScale:y=!1}=t,I=T==="area"?"line":T,{connectGroup:Y=void 0}=t,ct,H;function yt(){}Qe(()=>{}),t.config===void 0&&e.config&&d!==void 0&&e.config(d),t.width===void 0&&e.width&&l!==void 0&&e.width(l),t.height===void 0&&e.height&&v!==void 0&&e.height(v),t.interactive===void 0&&e.interactive&&p!==void 0&&e.interactive(p),t.data===void 0&&e.data&&x!==void 0&&e.data(x),t.dateCol===void 0&&e.dateCol&&k!==void 0&&e.dateCol(k),t.valueCol===void 0&&e.valueCol&&D!==void 0&&e.valueCol(D),t.valueFmt===void 0&&e.valueFmt&&w!==void 0&&e.valueFmt(w),t.dateFmt===void 0&&e.dateFmt&&n!==void 0&&e.dateFmt(n),t.type===void 0&&e.type&&T!==void 0&&e.type(T),t.color===void 0&&e.color&&W!==void 0&&e.color(W),t.yScale===void 0&&e.yScale&&y!==void 0&&e.yScale(y),t.connectGroup===void 0&&e.connectGroup&&Y!==void 0&&e.connectGroup(Y),p=p==="true"||p===!0,m(a=h(W)),y=y==="true"||y===!0;try{if(!["line","area","bar"].includes(T))throw Error("type must be line, area, or bar");({height:v,width:l}=Rn(v,l)),Ee(x,[D,k]),{value_format_object:R,date_format_object:L}=Ln(x,D,k,w,n);const P=x.map(G=>[G[k],G[D]]);if(P.sort((G,nt)=>G[0]-nt[0]),d=Pn(P,T,I,c,y,R,L,v,r),!Bn){const G=En(null,"evidence-light",{ssr:!0,renderer:"svg",height:v,width:l});G.setOption(d),ct=G.renderToSVGString(),G.dispose()}}catch(P){if(H=P,console.error("\x1B[31m%s\x1B[0m",`Error in Sparkline: ${H.message}`),ue)throw H}return i(),s(),`${H?`${f(He,"ValueError").$$render(o,{error:H},{},{})}`:`${`<div class="inline-block align-baseline" style="${"width: "+C(l,!0)+"px; height: "+C(v,!0)+"px;"}"><!-- HTML_TAG_START -->${ct}<!-- HTML_TAG_END --></div>`}`}`}),Un={code:".svelte-77gpa3:focus{outline:none}.row-link.svelte-77gpa3{cursor:pointer}",map:`{"version":3,"file":"TableRow.svelte","sources":["TableRow.svelte"],"sourcesContent":["<script>\\n\\timport { goto, preloadData } from '$app/navigation';\\n\\timport { safeExtractColumn } from './datatable.js';\\n\\timport Delta from '../core/Delta.svelte';\\n\\timport {\\n\\t\\tformatValue,\\n\\t\\tgetFormatObjectFromString\\n\\t} from '@evidence-dev/component-utilities/formatting';\\n\\timport TableCell from './TableCell.svelte';\\n\\timport chroma from 'chroma-js';\\n\\timport { Icon } from '@steeze-ui/svelte-icon';\\n\\timport { ChevronRight } from '@steeze-ui/tabler-icons';\\n\\timport Sparkline from '../core/_Sparkline.svelte';\\n\\timport { addBasePath } from '@evidence-dev/sdk/utils/svelte';\\n\\timport { getThemeStores } from '../../../themes/themes.js';\\n\\n\\texport let displayedData = undefined;\\n\\texport let rowShading = undefined;\\n\\texport let link = undefined;\\n\\texport let rowNumbers = undefined;\\n\\texport let rowLines = undefined;\\n\\texport let index = undefined;\\n\\texport let columnSummary = undefined;\\n\\texport let grouped = false; // if part of a group - styling will be adjusted\\n\\texport let groupType = undefined;\\n\\texport let groupColumn = undefined;\\n\\texport let rowSpan = undefined;\\n\\texport let groupNamePosition = 'middle'; // middle (default) | top | bottom\\n\\texport let orderedColumns = undefined;\\n\\texport let compact = undefined;\\n\\n\\tconst isUrlExternal = (url) =>\\n\\t\\tnew URL(url, window.location.origin).origin !== window.location.origin;\\n\\n\\tconst preloadLink = async (row) => {\\n\\t\\tif (!link || !row[link]) return;\\n\\t\\tconst url = row[link];\\n\\n\\t\\tif (isUrlExternal(url)) return;\\n\\n\\t\\tawait preloadData(url);\\n\\t};\\n\\n\\tconst navigateToLink = async (row, event) => {\\n\\t\\tif (!link || !row[link]) return;\\n\\t\\tconst url = row[link];\\n\\n\\t\\t// Don't handle the click if it's on a link element that should open in new tab\\n\\t\\tconst target = event?.target?.closest('a');\\n\\t\\tif (target?.getAttribute('target') === '_blank') {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tif (isUrlExternal(url)) {\\n\\t\\t\\twindow.location = addBasePath(url);\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tawait goto(addBasePath(row[link]));\\n\\t};\\n\\n\\tconst { theme } = getThemeStores();\\n<\/script>\\n\\n{#each displayedData as row, i}\\n\\t<tr\\n\\t\\tclass:bg-base-200={rowShading && i % 2 === 1}\\n\\t\\tclass:row-link={link && row[link]}\\n\\t\\tclass:hover:bg-base-200={link && row[link]}\\n\\t\\ton:mouseover={() => preloadLink(row)}\\n\\t\\ton:focus={() => preloadLink(row)}\\n\\t\\ton:click={(event) => navigateToLink(row, event)}\\n\\t\\tclass={rowLines ? 'border-b border-base-content-muted/20' : ''}\\n\\t>\\n\\t\\t{#if rowNumbers && groupType !== 'section'}\\n\\t\\t\\t<TableCell class={'index w-[2%]'} {compact}>\\n\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t</TableCell>\\n\\t\\t{/if}\\n\\n\\t\\t{#each orderedColumns as column, k}\\n\\t\\t\\t{@const useCol = safeExtractColumn(column, columnSummary)}\\n\\t\\t\\t{@const scaleCol = column.scaleColumn\\n\\t\\t\\t\\t? columnSummary.find((d) => d.id === column.scaleColumn)\\n\\t\\t\\t\\t: useCol}\\n\\t\\t\\t{@const column_min = column.colorMin ?? scaleCol.columnUnitSummary?.min}\\n\\t\\t\\t{@const column_max = column.colorMax ?? scaleCol.columnUnitSummary?.max}\\n\\t\\t\\t{@const is_nonzero =\\n\\t\\t\\t\\tcolumn_max - column_min !== 0 && !isNaN(column_max) && !isNaN(column_min)}\\n\\t\\t\\t{@const column_format = column.fmt\\n\\t\\t\\t\\t? getFormatObjectFromString(column.fmt, useCol.format?.valueType)\\n\\t\\t\\t\\t: column.fmtColumn\\n\\t\\t\\t\\t\\t? getFormatObjectFromString(row[column.fmtColumn], useCol.format?.valuetype)\\n\\t\\t\\t\\t\\t: useCol.format}\\n\\t\\t\\t{@const color_domain =\\n\\t\\t\\t\\tcolumn.colorBreakpoints ??\\n\\t\\t\\t\\t(column.colorMid ? [column_min, column.colorMid, column_max] : [column_min, column_max])}\\n\\t\\t\\t{@const color_scale = column.colorScale\\n\\t\\t\\t\\t? chroma.scale(column.colorScale).domain(color_domain).nodata($theme.colors['base-100'])\\n\\t\\t\\t\\t: ''}\\n\\t\\t\\t{@const cell_color =\\n\\t\\t\\t\\tcolumn.contentType === 'colorscale' && is_nonzero && column.colorScale\\n\\t\\t\\t\\t\\t? column.scaleColumn\\n\\t\\t\\t\\t\\t\\t? color_scale(row[column.scaleColumn]).hex()\\n\\t\\t\\t\\t\\t\\t: color_scale(row[column.id]).hex()\\n\\t\\t\\t\\t\\t: ''}\\n\\t\\t\\t{@const font_color = column.redNegatives\\n\\t\\t\\t\\t? row[column.id] < 0\\n\\t\\t\\t\\t\\t? $theme.colors.negative\\n\\t\\t\\t\\t\\t: ''\\n\\t\\t\\t\\t: column.contentType === 'colorscale' && is_nonzero && column.colorScale\\n\\t\\t\\t\\t\\t? chroma.contrast(cell_color, $theme.colors['base-content']) <\\n\\t\\t\\t\\t\\t\\tchroma.contrast(cell_color, $theme.colors['base-100']) + 0.5\\n\\t\\t\\t\\t\\t\\t? $theme.colors['base-100']\\n\\t\\t\\t\\t\\t\\t: $theme.colors['base-content']\\n\\t\\t\\t\\t\\t: ''}\\n\\t\\t\\t{@const bottom_border =\\n\\t\\t\\t\\ti !== displayedData.length - 1 &&\\n\\t\\t\\t\\trowLines &&\\n\\t\\t\\t\\tcolumn.contentType === 'colorscale' &&\\n\\t\\t\\t\\tis_nonzero &&\\n\\t\\t\\t\\tcolumn.colorScale\\n\\t\\t\\t\\t\\t? \`1px solid \${chroma(cell_color).darken(0.5)}\`\\n\\t\\t\\t\\t\\t: ''}\\n\\t\\t\\t<TableCell\\n\\t\\t\\t\\tclass={useCol?.type}\\n\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\tverticalAlign={groupType === 'section' ? groupNamePosition : undefined}\\n\\t\\t\\t\\trowSpan={groupType === 'section' && groupColumn === useCol.id && i === 0 ? rowSpan : 1}\\n\\t\\t\\t\\tshow={!(groupType === 'section' && groupColumn === useCol.id && i !== 0)}\\n\\t\\t\\t\\talign={column.align}\\n\\t\\t\\t\\tpaddingLeft={k === 0 && grouped && groupType === 'accordion' && !rowNumbers\\n\\t\\t\\t\\t\\t? '28px'\\n\\t\\t\\t\\t\\t: undefined}\\n\\t\\t\\t\\theight={column.height}\\n\\t\\t\\t\\twidth={column.width}\\n\\t\\t\\t\\twrap={column.wrap}\\n\\t\\t\\t\\tcellColor={cell_color}\\n\\t\\t\\t\\tfontColor={font_color}\\n\\t\\t\\t\\tborderBottom={bottom_border}\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#if column.contentType === 'image' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t<img\\n\\t\\t\\t\\t\\t\\tsrc={row[column.id]}\\n\\t\\t\\t\\t\\t\\talt={column.alt\\n\\t\\t\\t\\t\\t\\t\\t? row[column.alt]\\n\\t\\t\\t\\t\\t\\t\\t: row[column.id].replace(/^(.*[/])/g, '').replace(/[.][^.]+$/g, '')}\\n\\t\\t\\t\\t\\t\\tclass=\\"mx-auto my-2 max-w-[unset] rounded-[unset]\\"\\n\\t\\t\\t\\t\\t\\tstyle:height={column.height}\\n\\t\\t\\t\\t\\t\\tstyle:width={column.width}\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t{:else if column.contentType === 'link' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t<!-- if \`column.linkLabel\` is a column in \`row\`, but undefined, display - -->\\n\\t\\t\\t\\t\\t{#if column.linkLabel != undefined && row[column.linkLabel] == undefined && column.linkLabel in row}\\n\\t\\t\\t\\t\\t\\t-\\n\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t<a\\n\\t\\t\\t\\t\\t\\t\\thref={addBasePath(row[column.id])}\\n\\t\\t\\t\\t\\t\\t\\ttarget={column.openInNewTab ? '_blank' : ''}\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"text-primary hover:brightness-110 transition-colors duration-200\\"\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{#if column.linkLabel != undefined}\\n\\t\\t\\t\\t\\t\\t\\t\\t<!-- if the linklabel is a column name, display that column -->\\n\\t\\t\\t\\t\\t\\t\\t\\t{#if row[column.linkLabel] != undefined}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{@const labelSummary = safeExtractColumn({ id: column.linkLabel }, columnSummary)}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.linkLabel],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumn.fmt\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t? getFormatObjectFromString(column.fmt, labelSummary.format?.valueType)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t: labelSummary.format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tlabelSummary.columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<!-- otherwise, consider it a label (like Details ->) and display it -->\\n\\t\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{column.linkLabel}\\n\\t\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t<!-- if no linkLabel is specified, display the link itself -->\\n\\t\\t\\t\\t\\t\\t\\t\\t{@const columnSummary = useCol}\\n\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumn.fmt\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t? getFormatObjectFromString(column.fmt, columnSummary.format?.valueType)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t: columnSummary.format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary.columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t</a>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{:else if column.contentType === 'delta' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t<Delta\\n\\t\\t\\t\\t\\t\\tvalue={row[column.id]}\\n\\t\\t\\t\\t\\t\\tdownIsGood={column.downIsGood}\\n\\t\\t\\t\\t\\t\\tformat_object={column_format}\\n\\t\\t\\t\\t\\t\\tcolumnUnitSummary={useCol.columnUnitSummary}\\n\\t\\t\\t\\t\\t\\tshowValue={column.showValue}\\n\\t\\t\\t\\t\\t\\tshowSymbol={column.deltaSymbol}\\n\\t\\t\\t\\t\\t\\talign={column.align}\\n\\t\\t\\t\\t\\t\\tfontClass=\\"text-[9.25pt]\\"\\n\\t\\t\\t\\t\\t\\tneutralMin={column.neutralMin}\\n\\t\\t\\t\\t\\t\\tneutralMax={column.neutralMax}\\n\\t\\t\\t\\t\\t\\tchip={column.chip}\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t{:else if column.contentType === 'bar' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t{@const total_width =\\n\\t\\t\\t\\t\\t\\tcolumn_min >= 0\\n\\t\\t\\t\\t\\t\\t\\t? column_max\\n\\t\\t\\t\\t\\t\\t\\t: column_max < 0\\n\\t\\t\\t\\t\\t\\t\\t\\t? column_min\\n\\t\\t\\t\\t\\t\\t\\t\\t: Math.abs(column_min) + column_max}\\n\\t\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\t\\tstyle=\\"width: 100%; background-color: {column.backgroundColor}; position: relative; height: 100%; display: flex; align-items: center; overflow: hidden;\\"\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<!-- Conditionally render axis line and negative space if any row has negative values -->\\n\\t\\t\\t\\t\\t\\t{#if column_min < 0}\\n\\t\\t\\t\\t\\t\\t\\t<!-- Axis line in the center -->\\n\\t\\t\\t\\t\\t\\t\\t<!-- <div style=\\"width: 0.5px;background-color: #000; position: absolute; left: {column_min < 0 ? (Math.abs(column_min) / (Math.abs(column_min) + column_max)) * 100 : 0}%; top: 0; height: 100%; z-index: 1;\\"></div> -->\\n\\n\\t\\t\\t\\t\\t\\t\\t<!-- Negative bar (if value is negative) -->\\n\\t\\t\\t\\t\\t\\t\\t{#if row[column.id] < 0}\\n\\t\\t\\t\\t\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width: {Math.min(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tMath.abs((row[column.id] / total_width) * 100),\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t100\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}%; background-color: {column.negativeBarColor}; height: 100%; position: absolute; right: {(1 -\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tMath.abs(column_min) / (Math.abs(column_min) + column_max)) *\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t100}%;\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t></div>\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\n\\t\\t\\t\\t\\t\\t<!-- Positive bar (if value is positive or zero) -->\\n\\t\\t\\t\\t\\t\\t{#if row[column.id] >= 0}\\n\\t\\t\\t\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width: {Math.min(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(row[column.id] / total_width) * 100,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t100\\n\\t\\t\\t\\t\\t\\t\\t\\t)}%; background-color: {column.barColor}; height: 100%; position: absolute; left: {column_min <\\n\\t\\t\\t\\t\\t\\t\\t\\t0\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t? (Math.abs(column_min) / (Math.abs(column_min) + column_max)) * 100 + '%'\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t: '0'};\\"\\n\\t\\t\\t\\t\\t\\t\\t></div>\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\n\\t\\t\\t\\t\\t\\t<!-- Display label -->\\n\\t\\t\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\t\\t\\tclass:invisible={column.hideLabels}\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"\\n\\t\\t\\t\\t\\t\\t\\t\\tposition: relative; \\n\\t\\t\\t\\t\\t\\t\\t\\tz-index: 2; \\n\\t\\t\\t\\t\\t\\t\\t\\tpadding-left: 4px; \\n\\t\\t\\t\\t\\t\\t\\t\\tpadding-right: 4px; \\n\\t\\t\\t\\t\\t\\t\\t\\ttext-align: {column.align ?? 'left'};\\n\\t\\t\\t\\t\\t\\t\\t\\twidth: {(Math.abs(column_max) /\\n\\t\\t\\t\\t\\t\\t\\t\\t((column_min < 0 ? Math.abs(column_min) : 0) + Math.abs(column_max))) *\\n\\t\\t\\t\\t\\t\\t\\t\\t100}%;\\n\\t\\t\\t\\t\\t\\t\\t\\tleft: {column_min < 0 && column_max >= 0\\n\\t\\t\\t\\t\\t\\t\\t\\t? (Math.abs(column_min) / (Math.abs(column_min) + column_max)) * 100 + '%'\\n\\t\\t\\t\\t\\t\\t\\t\\t: '0'};\\"\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{formatValue(row[column.id], column_format, useCol.columnUnitSummary)}\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t</div>\\n\\n\\t\\t\\t\\t\\t<!-- <div style=\\"background-color: red; max-width: '{row[column.id] / column_max * 100}%'\\">{row[column.id]}</div> -->\\n\\t\\t\\t\\t{:else if column.contentType === 'sparkline' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t{@const alignment = column.align ?? 'center'}\\n\\t\\t\\t\\t\\t<div class=\\"items-{alignment} justify-{alignment} flex\\">\\n\\t\\t\\t\\t\\t\\t<Sparkline\\n\\t\\t\\t\\t\\t\\t\\ttype=\\"line\\"\\n\\t\\t\\t\\t\\t\\t\\tdata={[...row[column.id]]}\\n\\t\\t\\t\\t\\t\\t\\tdateCol={column.sparkX}\\n\\t\\t\\t\\t\\t\\t\\tvalueCol={column.sparkY}\\n\\t\\t\\t\\t\\t\\t\\tinteractive=\\"false\\"\\n\\t\\t\\t\\t\\t\\t\\tcolor={column.sparkColor}\\n\\t\\t\\t\\t\\t\\t\\tyScale={column.sparkYScale}\\n\\t\\t\\t\\t\\t\\t\\theight={column.sparkHeight ?? 19}\\n\\t\\t\\t\\t\\t\\t\\twidth={column.sparkWidth ?? 90}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{:else if column.contentType === 'sparkbar' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t<div class=\\"items-center justify-center flex\\">\\n\\t\\t\\t\\t\\t\\t<Sparkline\\n\\t\\t\\t\\t\\t\\t\\ttype=\\"bar\\"\\n\\t\\t\\t\\t\\t\\t\\tdata={[...row[column.id]]}\\n\\t\\t\\t\\t\\t\\t\\tdateCol={column.sparkX}\\n\\t\\t\\t\\t\\t\\t\\tvalueCol={column.sparkY}\\n\\t\\t\\t\\t\\t\\t\\tinteractive=\\"false\\"\\n\\t\\t\\t\\t\\t\\t\\tcolor={column.sparkColor}\\n\\t\\t\\t\\t\\t\\t\\tyScale={column.sparkYScale}\\n\\t\\t\\t\\t\\t\\t\\theight={column.sparkHeight ?? 19}\\n\\t\\t\\t\\t\\t\\t\\twidth={column.sparkWidth ?? 90}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{:else if column.contentType === 'sparkarea' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t{@const alignment =\\n\\t\\t\\t\\t\\t\\tcolumn.align === 'right' ? 'end' : column.align === 'left' ? 'start' : 'center'}\\n\\t\\t\\t\\t\\t<div class=\\"items-center justify-{alignment} flex\\">\\n\\t\\t\\t\\t\\t\\t<Sparkline\\n\\t\\t\\t\\t\\t\\t\\ttype=\\"area\\"\\n\\t\\t\\t\\t\\t\\t\\tdata={[...row[column.id]]}\\n\\t\\t\\t\\t\\t\\t\\tdateCol={column.sparkX}\\n\\t\\t\\t\\t\\t\\t\\tvalueCol={column.sparkY}\\n\\t\\t\\t\\t\\t\\t\\tinteractive=\\"false\\"\\n\\t\\t\\t\\t\\t\\t\\tcolor={column.sparkColor}\\n\\t\\t\\t\\t\\t\\t\\tyScale={column.sparkYScale}\\n\\t\\t\\t\\t\\t\\t\\theight={column.sparkHeight ?? 20}\\n\\t\\t\\t\\t\\t\\t\\twidth={column.sparkWidth ?? 80}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{:else if column.contentType === 'html' && row[column.id] !== undefined}\\n\\t\\t\\t\\t\\t{@html row[column.id]}\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t{formatValue(row[column.id], column_format, useCol.columnUnitSummary)}\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</TableCell>\\n\\t\\t{/each}\\n\\n\\t\\t{#if link && row[link]}\\n\\t\\t\\t<TableCell {compact} width=\\"16px\\">\\n\\t\\t\\t\\t<Icon src={ChevronRight} class=\\"w-4 h-4\\" />\\n\\t\\t\\t\\t<a href={addBasePath(row[link])} class=\\"sr-only\\">See more</a>\\n\\t\\t\\t</TableCell>\\n\\t\\t{/if}\\n\\t</tr>\\n{/each}\\n\\n<style>\\n\\t*:focus {\\n\\t\\toutline: none;\\n\\t}\\n\\n\\t.row-link {\\n\\t\\tcursor: pointer;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAwUC,cAAC,MAAO,CACP,OAAO,CAAE,IACV,CAEA,uBAAU,CACT,MAAM,CAAE,OACT"}`},Se=F((o,t,e,g)=>{let a,r,{displayedData:i=void 0}=t,{rowShading:c=void 0}=t,{link:s=void 0}=t,{rowNumbers:m=void 0}=t,{rowLines:A=void 0}=t,{index:h=void 0}=t,{columnSummary:d=void 0}=t,{grouped:l=!1}=t,{groupType:v=void 0}=t,{groupColumn:p=void 0}=t,{rowSpan:B=void 0}=t,{groupNamePosition:b="middle"}=t,{orderedColumns:x=void 0}=t,{compact:k=void 0}=t;const{theme:D}=ee();return r=z(D,w=>a=w),t.displayedData===void 0&&e.displayedData&&i!==void 0&&e.displayedData(i),t.rowShading===void 0&&e.rowShading&&c!==void 0&&e.rowShading(c),t.link===void 0&&e.link&&s!==void 0&&e.link(s),t.rowNumbers===void 0&&e.rowNumbers&&m!==void 0&&e.rowNumbers(m),t.rowLines===void 0&&e.rowLines&&A!==void 0&&e.rowLines(A),t.index===void 0&&e.index&&h!==void 0&&e.index(h),t.columnSummary===void 0&&e.columnSummary&&d!==void 0&&e.columnSummary(d),t.grouped===void 0&&e.grouped&&l!==void 0&&e.grouped(l),t.groupType===void 0&&e.groupType&&v!==void 0&&e.groupType(v),t.groupColumn===void 0&&e.groupColumn&&p!==void 0&&e.groupColumn(p),t.rowSpan===void 0&&e.rowSpan&&B!==void 0&&e.rowSpan(B),t.groupNamePosition===void 0&&e.groupNamePosition&&b!==void 0&&e.groupNamePosition(b),t.orderedColumns===void 0&&e.orderedColumns&&x!==void 0&&e.orderedColumns(x),t.compact===void 0&&e.compact&&k!==void 0&&e.compact(k),o.css.add(Un),r(),`${st(i,(w,R)=>`<tr class="${[C(cn(A?"border-b border-base-content-muted/20":""),!0)+" svelte-77gpa3",(c&&R%2===1?"bg-base-200":"")+" "+(s&&w[s]?"row-link":"")+" "+(s&&w[s]?"hover:bg-base-200":"")].join(" ").trim()}">${m&&v!=="section"?`${f(jt,"TableCell").$$render(o,{class:"index w-[2%]",compact:k},{},{default:()=>`${C((h+R+1).toLocaleString())} `})}`:""} ${st(x,(n,L)=>{let T=It(n,d),W=n.scaleColumn?d.find(j=>j.id===n.scaleColumn):T,y=n.colorMin??W.columnUnitSummary?.min,I=n.colorMax??W.columnUnitSummary?.max,Y=I-y!==0&&!isNaN(I)&&!isNaN(y),ct=n.fmt?q(n.fmt,T.format?.valueType):n.fmtColumn?q(w[n.fmtColumn],T.format?.valuetype):T.format,H=n.colorBreakpoints??(n.colorMid?[y,n.colorMid,I]:[y,I]),yt=n.colorScale?de.scale(n.colorScale).domain(H).nodata(a.colors["base-100"]):"",P=n.contentType==="colorscale"&&Y&&n.colorScale?n.scaleColumn?yt(w[n.scaleColumn]).hex():yt(w[n.id]).hex():"",G=n.redNegatives?w[n.id]<0?a.colors.negative:"":n.contentType==="colorscale"&&Y&&n.colorScale?de.contrast(P,a.colors["base-content"])<de.contrast(P,a.colors["base-100"])+.5?a.colors["base-100"]:a.colors["base-content"]:"",nt=R!==i.length-1&&A&&n.contentType==="colorscale"&&Y&&n.colorScale?`1px solid ${de(P).darken(.5)}`:"";return`           ${f(jt,"TableCell").$$render(o,{class:T?.type,compact:k,verticalAlign:v==="section"?b:void 0,rowSpan:v==="section"&&p===T.id&&R===0?B:1,show:!(v==="section"&&p===T.id&&R!==0),align:n.align,paddingLeft:L===0&&l&&v==="accordion"&&!m?"28px":void 0,height:n.height,width:n.width,wrap:n.wrap,cellColor:P,fontColor:G,borderBottom:nt},{},{default:()=>`${n.contentType==="image"&&w[n.id]!==void 0?`<img${O("src",w[n.id],0)}${O("alt",n.alt?w[n.alt]:w[n.id].replace(/^(.*[/])/g,"").replace(/[.][^.]+$/g,""),0)} class="mx-auto my-2 max-w-[unset] rounded-[unset] svelte-77gpa3"${ft({height:n.height,width:n.width})}>`:`${n.contentType==="link"&&w[n.id]!==void 0?` ${n.linkLabel!=null&&w[n.linkLabel]==null&&n.linkLabel in w?"-":`<a${O("href",ke(w[n.id]),0)}${O("target",n.openInNewTab?"_blank":"",0)} class="text-primary hover:brightness-110 transition-colors duration-200 svelte-77gpa3">${n.linkLabel!=null?` ${w[n.linkLabel]!=null?(()=>{let j=It({id:n.linkLabel},d);return` ${C(dt(w[n.linkLabel],n.fmt?q(n.fmt,j.format?.valueType):j.format,j.columnUnitSummary))} `})():`${C(n.linkLabel)}`}`:(()=>{let j=T;return`  ${C(dt(w[n.id],n.fmt?q(n.fmt,j.format?.valueType):j.format,j.columnUnitSummary))}`})()} </a>`}`:`${n.contentType==="delta"&&w[n.id]!==void 0?`${f(Ce,"Delta").$$render(o,{value:w[n.id],downIsGood:n.downIsGood,format_object:ct,columnUnitSummary:T.columnUnitSummary,showValue:n.showValue,showSymbol:n.deltaSymbol,align:n.align,fontClass:"text-[9.25pt]",neutralMin:n.neutralMin,neutralMax:n.neutralMax,chip:n.chip},{},{})}`:`${n.contentType==="bar"&&w[n.id]!==void 0?(()=>{let j=y>=0?I:I<0?y:Math.abs(y)+I;return` <div style="${"width: 100%; background-color: "+C(n.backgroundColor,!0)+"; position: relative; height: 100%; display: flex; align-items: center; overflow: hidden;"}" class="svelte-77gpa3"> ${y<0?`   ${w[n.id]<0?`<div style="${"width: "+C(Math.min(Math.abs(w[n.id]/j*100),100),!0)+"%; background-color: "+C(n.negativeBarColor,!0)+"; height: 100%; position: absolute; right: "+C((1-Math.abs(y)/(Math.abs(y)+I))*100,!0)+"%;"}" class="svelte-77gpa3"></div>`:""}`:""}  ${w[n.id]>=0?`<div style="${"width: "+C(Math.min(w[n.id]/j*100,100),!0)+"%; background-color: "+C(n.barColor,!0)+"; height: 100%; position: absolute; left: "+C(y<0?Math.abs(y)/(Math.abs(y)+I)*100+"%":"0",!0)+";"}" class="svelte-77gpa3"></div>`:""}  <div style="${"position: relative; z-index: 2; padding-left: 4px; padding-right: 4px; text-align: "+C(n.align??"left",!0)+"; width: "+C(Math.abs(I)/((y<0?Math.abs(y):0)+Math.abs(I))*100,!0)+"%; left: "+C(y<0&&I>=0?Math.abs(y)/(Math.abs(y)+I)*100+"%":"0",!0)+";"}" class="${["svelte-77gpa3",n.hideLabels?"invisible":""].join(" ").trim()}">${C(dt(w[n.id],ct,T.columnUnitSummary))} </div></div> `})():`${n.contentType==="sparkline"&&w[n.id]!==void 0?(()=>{let j=n.align??"center";return` <div class="${"items-"+C(j,!0)+" justify-"+C(j,!0)+" flex svelte-77gpa3"}">${f(xe,"Sparkline").$$render(o,{type:"line",data:[...w[n.id]],dateCol:n.sparkX,valueCol:n.sparkY,interactive:"false",color:n.sparkColor,yScale:n.sparkYScale,height:n.sparkHeight??19,width:n.sparkWidth??90},{},{})} </div>`})():`${n.contentType==="sparkbar"&&w[n.id]!==void 0?`<div class="items-center justify-center flex svelte-77gpa3">${f(xe,"Sparkline").$$render(o,{type:"bar",data:[...w[n.id]],dateCol:n.sparkX,valueCol:n.sparkY,interactive:"false",color:n.sparkColor,yScale:n.sparkYScale,height:n.sparkHeight??19,width:n.sparkWidth??90},{},{})} </div>`:`${n.contentType==="sparkarea"&&w[n.id]!==void 0?(()=>{let j=n.align==="right"?"end":n.align==="left"?"start":"center";return` <div class="${"items-center justify-"+C(j,!0)+" flex svelte-77gpa3"}">${f(xe,"Sparkline").$$render(o,{type:"area",data:[...w[n.id]],dateCol:n.sparkX,valueCol:n.sparkY,interactive:"false",color:n.sparkColor,yScale:n.sparkYScale,height:n.sparkHeight??20,width:n.sparkWidth??80},{},{})} </div>`})():`${n.contentType==="html"&&w[n.id]!==void 0?`<!-- HTML_TAG_START -->${w[n.id]}<!-- HTML_TAG_END -->`:`${C(dt(w[n.id],ct,T.columnUnitSummary))}`}`}`}`}`}`}`}`} `})}`})} ${s&&w[s]?`${f(jt,"TableCell").$$render(o,{compact:k,width:"16px"},{},{default:()=>`${f(Ct,"Icon").$$render(o,{src:Ye,class:"w-4 h-4"},{},{})} <a${O("href",ke(w[s]),0)} class="sr-only svelte-77gpa3">See more</a> `})}`:""} </tr>`)}`}),Kn=F((o,t,e,g)=>{let{data:a=void 0}=t,{rowNumbers:r=void 0}=t,{columnSummary:i=void 0}=t,{rowColor:c=void 0}=t,{fontColor:s=void 0}=t,{groupType:m=void 0}=t,{orderedColumns:A=void 0}=t,{compact:h=void 0}=t;return t.data===void 0&&e.data&&a!==void 0&&e.data(a),t.rowNumbers===void 0&&e.rowNumbers&&r!==void 0&&e.rowNumbers(r),t.columnSummary===void 0&&e.columnSummary&&i!==void 0&&e.columnSummary(i),t.rowColor===void 0&&e.rowColor&&c!==void 0&&e.rowColor(c),t.fontColor===void 0&&e.fontColor&&s!==void 0&&e.fontColor(s),t.groupType===void 0&&e.groupType&&m!==void 0&&e.groupType(m),t.orderedColumns===void 0&&e.orderedColumns&&A!==void 0&&e.orderedColumns(A),t.compact===void 0&&e.compact&&h!==void 0&&e.compact(h),`<tr class="font-semibold"${ft({"background-color":c,color:s})}>${r&&m!=="section"?`${f(jt,"TableCell").$$render(o,{class:"index w-[2%]",compact:h,topBorder:"border-t border-base-300"},{},{})}`:""} ${st(A,d=>{let l=It(d,i),v=d.totalFmt?q(d.totalFmt):d.fmt?q(d.fmt):l.format,p=d.totalAgg??"sum";return`   ${f(jt,"TableCell").$$render(o,{compact:h,dataType:l.type,align:d.align,height:d.height,width:d.width,wrap:d.wrap,topBorder:"border-t border-base-content-muted"},{},{default:()=>`${["sum","mean","weightedMean","median","min","max","count","countDistinct"].includes(p)?`${d.contentType==="delta"?`${f(Ce,"Delta").$$render(o,{value:p==="weightedMean"?Pe(a,d.id,d.weightCol):l.columnUnitSummary[p],downIsGood:d.downIsGood,format_object:v,columnUnitSummary:l.columnUnitSummary,showValue:d.showValue,showSymbol:d.deltaSymbol,align:d.align,fontClass:"font-semibold text-[9.25pt]",neutralMin:d.neutralMin,neutralMax:d.neutralMax,chip:d.chip},{},{})}`:`${C(dt(p==="weightedMean"?Pe(a,d.id,d.weightCol):l.columnUnitSummary[p],v,l.columnUnitSummary))}`}`:` ${C(d.totalFmt?dt(p,v,l.columnUnitSummary):p)}`} `})}`})}</tr>`}),zn=F((o,t,e,g)=>{let{groupName:a=void 0}=t,{currentGroupData:r=void 0}=t,{columnSummary:i=void 0}=t,{rowColor:c=void 0}=t,{groupBy:s=void 0}=t,{groupType:m=void 0}=t,{fontColor:A=void 0}=t,{orderedColumns:h=void 0}=t,{compact:d=void 0}=t;return t.groupName===void 0&&e.groupName&&a!==void 0&&e.groupName(a),t.currentGroupData===void 0&&e.currentGroupData&&r!==void 0&&e.currentGroupData(r),t.columnSummary===void 0&&e.columnSummary&&i!==void 0&&e.columnSummary(i),t.rowColor===void 0&&e.rowColor&&c!==void 0&&e.rowColor(c),t.groupBy===void 0&&e.groupBy&&s!==void 0&&e.groupBy(s),t.groupType===void 0&&e.groupType&&m!==void 0&&e.groupType(m),t.fontColor===void 0&&e.fontColor&&A!==void 0&&e.fontColor(A),t.orderedColumns===void 0&&e.orderedColumns&&h!==void 0&&e.orderedColumns(h),t.compact===void 0&&e.compact&&d!==void 0&&e.compact(d),`<tr class="w-full border-b border-base-content-muted bg-base-200"${ft({"background-color":c,color:A})}>${st(h,l=>{let v=It(l,i),p=l.fmt?q(l.fmt,v.format?.valueType):v.format,B=l.subtotalFmt?q(l.subtotalFmt):l.totalFmt?q(l.totalFmt):p,b=B?.valueType==="date"?"":B;return`    ${f(jt,"TableCell").$$render(o,{class:v.type+" font-medium",compact:d,align:l.align},{},{default:()=>`${l.id!==s?`${l.contentType==="delta"?`${f(Ce,"Delta").$$render(o,{value:ce(r,l.id,l.totalAgg,v.type,l.weightCol),downIsGood:l.downIsGood,format_object:p,columnUnitSummary:v.columnUnitSummary,showValue:l.showValue,showSymbol:l.deltaSymbol,align:l.align,fontClass:"font-medium text-[9.25pt]",neutralMin:l.neutralMin,neutralMax:l.neutralMax,chip:l.chip},{},{})}`:`${C(dt(ce(r,l.id,l.totalAgg,v.type,l.weightCol),b,v.columnUnitSummary))}`}`:`${m==="section"?`${C(a)}`:""}`} `})}`})}</tr>`}),Ue=F((o,t,e,g)=>{let{ascending:a}=t;return t.ascending===void 0&&e.ascending&&a!==void 0&&e.ascending(a),`${a?`${f(Ct,"Icon").$$render(o,{src:mn,class:"w-[10px] h-[10px] mb-0.5 inline"},{},{})}`:`${f(Ct,"Icon").$$render(o,{src:fn,class:"w-[10px] h-[10px] mb-0.5 inline"},{},{})}`}`}),Wn={code:"th.svelte-1cvajqj{white-space:nowrap;overflow:hidden}th.svelte-1cvajqj:first-child{padding-left:3px}.index.svelte-1cvajqj{color:var(--base-content-muted);text-align:left;max-width:-moz-min-content;max-width:min-content}.string.svelte-1cvajqj{text-align:left}.date.svelte-1cvajqj{text-align:left}.number.svelte-1cvajqj{text-align:right}.boolean.svelte-1cvajqj{text-align:left}",map:`{"version":3,"file":"TableHeader.svelte","sources":["TableHeader.svelte"],"sourcesContent":["<script>\\n\\timport SortIcon from '../../ui/SortIcon.svelte';\\n\\timport Info from '../../ui/Info.svelte';\\n\\timport { safeExtractColumn } from './datatable.js';\\n\\n\\texport let rowNumbers = undefined;\\n\\texport let headerColor = undefined;\\n\\texport let headerFontColor = undefined;\\n\\texport let orderedColumns = undefined;\\n\\texport let columnSummary = undefined;\\n\\texport let sortable = undefined;\\n\\texport let sortClick = undefined;\\n\\texport let formatColumnTitles = undefined;\\n\\texport let sortObj = undefined;\\n\\texport let wrapTitles = undefined;\\n\\texport let compact = undefined;\\n\\n\\t/** @type {string | undefined} */\\n\\texport let link = undefined;\\n\\n\\tfunction getWrapTitleAlignment(column, columnSummary) {\\n\\t\\tif (column.align) {\\n\\t\\t\\tif (column.align === 'right') {\\n\\t\\t\\t\\treturn 'justify-end';\\n\\t\\t\\t} else if (column.align === 'center') {\\n\\t\\t\\t\\treturn 'justify-center';\\n\\t\\t\\t} else {\\n\\t\\t\\t\\treturn 'justify-start';\\n\\t\\t\\t}\\n\\t\\t} else if (safeExtractColumn(column, columnSummary).type === 'number') {\\n\\t\\t\\treturn 'justify-end';\\n\\t\\t}\\n\\t}\\n<\/script>\\n\\n<thead>\\n\\t{#if orderedColumns.length > 0}\\n\\t\\t{@const columnsWithGroupSpan = orderedColumns.map((column, index, array) => {\\n\\t\\t\\t// Determine if this column starts a new group or continues an existing one\\n\\t\\t\\tlet isNewGroup = index === 0 || column.colGroup !== array[index - 1].colGroup;\\n\\t\\t\\tlet span = 1;\\n\\t\\t\\tif (column.colGroup) {\\n\\t\\t\\t\\t// Count how many contiguous columns have the same group\\n\\t\\t\\t\\tfor (let i = index + 1; i < array.length && array[i].colGroup === column.colGroup; i++) {\\n\\t\\t\\t\\t\\tspan++;\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n\\t\\t\\treturn { ...column, isNewGroup, span: isNewGroup ? span : 0 }; // Only assign span to the first column in a group\\n\\t\\t})}\\n\\t\\t{#if columnsWithGroupSpan.length > 0}\\n\\t\\t\\t<tr class=\\"border-0\\" style:background-color={headerColor}>\\n\\t\\t\\t\\t{#if rowNumbers}\\n\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\tclass=\\"index w-[2%] {compact ? 'text-xs py-[1px] px-[4px]' : 'py-[2px]'}\\"\\n\\t\\t\\t\\t\\t\\tstyle:background-color={headerColor}\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{#each columnsWithGroupSpan as column}\\n\\t\\t\\t\\t\\t{#if column.colGroup && column.isNewGroup}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\trole=\\"columnheader\\"\\n\\t\\t\\t\\t\\t\\t\\tcolspan={column.span}\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"pt-1 align-bottom {compact ? 'px-[1px]' : 'px-[2px]'}\\"\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t<!-- Group header with dynamic colspan -->\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\" border-b-[1px] border-base-content-muted whitespace-normal pb-[2px]\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t{column.colGroup}\\n\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{:else if column.colGroup}\\n\\t\\t\\t\\t\\t\\t<!-- Not new group, th covered by previous column span-->\\n\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t<!-- Not part of a group - empty header cell -->\\n\\t\\t\\t\\t\\t\\t<th></th>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{/each}\\n\\t\\t\\t</tr>\\n\\t\\t{/if}\\n\\t{/if}\\n\\n\\t<tr class=\\"border-b border-base-content-muted/60\\">\\n\\t\\t{#if rowNumbers}\\n\\t\\t\\t<th\\n\\t\\t\\t\\trole=\\"columnheader\\"\\n\\t\\t\\t\\tclass=\\"index w-[2%] {compact ? 'text-xs py-[1px] px-[4px]' : 'py-[2px] px-[8px]'}\\"\\n\\t\\t\\t\\tstyle:background-color={headerColor}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t\\t{#each orderedColumns as column}\\n\\t\\t\\t<th\\n\\t\\t\\t\\trole=\\"columnheader\\"\\n\\t\\t\\t\\tclass=\\"{safeExtractColumn(column, columnSummary).type} {compact\\n\\t\\t\\t\\t\\t? 'text-xs py-[1px] pl-[1px]'\\n\\t\\t\\t\\t\\t: 'py-[2px] pl-[6px]'}\\"\\n\\t\\t\\t\\tstyle:text-align={column.align ??\\n\\t\\t\\t\\t\\t(['sparkline', 'sparkbar', 'sparkarea', 'bar'].includes(column.contentType)\\n\\t\\t\\t\\t\\t\\t? 'center'\\n\\t\\t\\t\\t\\t\\t: undefined)}\\n\\t\\t\\t\\tstyle:color={headerFontColor}\\n\\t\\t\\t\\tstyle:background={headerColor}\\n\\t\\t\\t\\tstyle:cursor={sortable ? 'pointer' : 'auto'}\\n\\t\\t\\t\\ton:click={sortable ? sortClick(column.id) : ''}\\n\\t\\t\\t\\tstyle:vertical-align=\\"bottom\\"\\n\\t\\t\\t\\tstyle:border-radius={sortObj.col === column.id ? '2px' : ''}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\tclass=\\"{wrapTitles || column.wrapTitle\\n\\t\\t\\t\\t\\t\\t? \`flex items-end \${getWrapTitleAlignment(column, columnSummary)}\`\\n\\t\\t\\t\\t\\t\\t: ''} tracking-[-1.5px]\\"\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<span class=\\"tracking-normal {wrapTitles || column.wrapTitle ? 'whitespace-normal' : ''}\\">\\n\\t\\t\\t\\t\\t\\t{column.title\\n\\t\\t\\t\\t\\t\\t\\t? column.title\\n\\t\\t\\t\\t\\t\\t\\t: formatColumnTitles\\n\\t\\t\\t\\t\\t\\t\\t\\t? safeExtractColumn(column, columnSummary).title\\n\\t\\t\\t\\t\\t\\t\\t\\t: safeExtractColumn(column, columnSummary).id}\\n\\t\\t\\t\\t\\t\\t{#if column.description}\\n\\t\\t\\t\\t\\t\\t\\t<Info\\n\\t\\t\\t\\t\\t\\t\\t\\tdescription={column.description}\\n\\t\\t\\t\\t\\t\\t\\t\\tsize=\\"4\\"\\n\\t\\t\\t\\t\\t\\t\\t\\tclassName=\\"max-w-3 whitespace-normal\\"\\n\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t<span\\n\\t\\t\\t\\t\\t\\tclass=\\"tracking-normal {wrapTitles || column.wrapTitle ? 'ml-0.5' : ''} {compact\\n\\t\\t\\t\\t\\t\\t\\t? 'mr-1'\\n\\t\\t\\t\\t\\t\\t\\t: ''}\\"\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t{#if sortObj.col === column.id}\\n\\t\\t\\t\\t\\t\\t\\t<SortIcon ascending={sortObj.ascending} />\\n\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t<span class=\\"invisible\\"><SortIcon /></span>\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t</th>\\n\\t\\t{/each}\\n\\n\\t\\t<!-- Extra column for Chevron icons -->\\n\\t\\t{#if link}\\n\\t\\t\\t<th role=\\"columnheader\\">\\n\\t\\t\\t\\t<span class=\\"sr-only\\">Links</span>\\n\\t\\t\\t</th>\\n\\t\\t{/if}\\n\\t</tr>\\n</thead>\\n\\n<style>\\n\\tth {\\n\\t\\twhite-space: nowrap;\\n\\t\\toverflow: hidden;\\n\\t}\\n\\n\\tth:first-child {\\n\\t\\tpadding-left: 3px;\\n\\t}\\n\\n\\t.index {\\n\\t\\tcolor: var(--base-content-muted);\\n\\t\\ttext-align: left;\\n\\t\\tmax-width: -moz-min-content;\\n\\t\\tmax-width: min-content;\\n\\t}\\n\\n\\t.string {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.date {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.number {\\n\\t\\ttext-align: right;\\n\\t}\\n\\n\\t.boolean {\\n\\t\\ttext-align: left;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAqJC,iBAAG,CACF,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MACX,CAEA,iBAAE,YAAa,CACd,YAAY,CAAE,GACf,CAEA,qBAAO,CACN,KAAK,CAAE,IAAI,oBAAoB,CAAC,CAChC,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,gBAAgB,CAC3B,SAAS,CAAE,WACZ,CAEA,sBAAQ,CACP,UAAU,CAAE,IACb,CAEA,oBAAM,CACL,UAAU,CAAE,IACb,CAEA,sBAAQ,CACP,UAAU,CAAE,KACb,CAEA,uBAAS,CACR,UAAU,CAAE,IACb"}`},Vn=F((o,t,e,g)=>{let{rowNumbers:a=void 0}=t,{headerColor:r=void 0}=t,{headerFontColor:i=void 0}=t,{orderedColumns:c=void 0}=t,{columnSummary:s=void 0}=t,{sortable:m=void 0}=t,{sortClick:A=void 0}=t,{formatColumnTitles:h=void 0}=t,{sortObj:d=void 0}=t,{wrapTitles:l=void 0}=t,{compact:v=void 0}=t,{link:p=void 0}=t;function B(b,x){if(b.align)return b.align==="right"?"justify-end":b.align==="center"?"justify-center":"justify-start";if(It(b,x).type==="number")return"justify-end"}return t.rowNumbers===void 0&&e.rowNumbers&&a!==void 0&&e.rowNumbers(a),t.headerColor===void 0&&e.headerColor&&r!==void 0&&e.headerColor(r),t.headerFontColor===void 0&&e.headerFontColor&&i!==void 0&&e.headerFontColor(i),t.orderedColumns===void 0&&e.orderedColumns&&c!==void 0&&e.orderedColumns(c),t.columnSummary===void 0&&e.columnSummary&&s!==void 0&&e.columnSummary(s),t.sortable===void 0&&e.sortable&&m!==void 0&&e.sortable(m),t.sortClick===void 0&&e.sortClick&&A!==void 0&&e.sortClick(A),t.formatColumnTitles===void 0&&e.formatColumnTitles&&h!==void 0&&e.formatColumnTitles(h),t.sortObj===void 0&&e.sortObj&&d!==void 0&&e.sortObj(d),t.wrapTitles===void 0&&e.wrapTitles&&l!==void 0&&e.wrapTitles(l),t.compact===void 0&&e.compact&&v!==void 0&&e.compact(v),t.link===void 0&&e.link&&p!==void 0&&e.link(p),o.css.add(Wn),`<thead>${c.length>0?(()=>{let b=c.map((x,k,D)=>{let w=k===0||x.colGroup!==D[k-1].colGroup,R=1;if(x.colGroup)for(let n=k+1;n<D.length&&D[n].colGroup===x.colGroup;n++)R++;return{...x,isNewGroup:w,span:w?R:0}});return` ${b.length>0?`<tr class="border-0"${ft({"background-color":r})}>${a?`<th class="${"index w-[2%] "+C(v?"text-xs py-[1px] px-[4px]":"py-[2px]",!0)+" svelte-1cvajqj"}"${ft({"background-color":r})}></th>`:""} ${st(b,x=>`${x.colGroup&&x.isNewGroup?`<th role="columnheader"${O("colspan",x.span,0)} class="${"pt-1 align-bottom "+C(v?"px-[1px]":"px-[2px]",!0)+" svelte-1cvajqj"}"> <div class="border-b-[1px] border-base-content-muted whitespace-normal pb-[2px]">${C(x.colGroup)}</div> </th>`:`${x.colGroup?"":' <th class="svelte-1cvajqj"></th>'}`}`)}</tr>`:""}`})():""} <tr class="border-b border-base-content-muted/60">${a?`<th role="columnheader" class="${"index w-[2%] "+C(v?"text-xs py-[1px] px-[4px]":"py-[2px] px-[8px]",!0)+" svelte-1cvajqj"}"${ft({"background-color":r})}></th>`:""} ${st(c,b=>`<th role="columnheader" class="${C(It(b,s).type,!0)+" "+C(v?"text-xs py-[1px] pl-[1px]":"py-[2px] pl-[6px]",!0)+" svelte-1cvajqj"}"${ft({"text-align":b.align??(["sparkline","sparkbar","sparkarea","bar"].includes(b.contentType)?"center":void 0),color:i,background:r,cursor:m?"pointer":"auto","vertical-align":"bottom","border-radius":d.col===b.id?"2px":""})}><div class="${C(l||b.wrapTitle?`flex items-end ${B(b,s)}`:"",!0)+" tracking-[-1.5px] svelte-1cvajqj"}"><span class="${"tracking-normal "+C(l||b.wrapTitle?"whitespace-normal":"",!0)}">${C(b.title?b.title:h?It(b,s).title:It(b,s).id)} ${b.description?`${f(Tn,"Info").$$render(o,{description:b.description,size:"4",className:"max-w-3 whitespace-normal"},{},{})}`:""}</span> <span class="${"tracking-normal "+C(l||b.wrapTitle?"ml-0.5":"",!0)+" "+C(v?"mr-1":"",!0)}">${d.col===b.id?`${f(Ue,"SortIcon").$$render(o,{ascending:d.ascending},{},{})}`:`<span class="invisible">${f(Ue,"SortIcon").$$render(o,{},{},{})}</span>`} </span></div> </th>`)}  ${p?'<th role="columnheader" class="svelte-1cvajqj" data-svelte-h="svelte-17az424"><span class="sr-only">Links</span></th>':""}</tr> </thead>`}),Qn={code:"svg.svelte-lqleyo.svelte-lqleyo{display:inline-block;vertical-align:middle;transition:transform 0.15s ease-in}span.svelte-lqleyo.svelte-lqleyo{margin:auto 0 auto 0}[aria-expanded='true'].svelte-lqleyo svg.svelte-lqleyo{transform:rotate(0.25turn)}",map:`{"version":3,"file":"TableGroupIcon.svelte","sources":["TableGroupIcon.svelte"],"sourcesContent":["<script>\\n\\timport { getThemeStores } from '../../../themes/themes.js';\\n\\n\\texport let toggled = false;\\n\\texport let color = undefined;\\n\\texport let size = 10;\\n\\n\\tconst { theme } = getThemeStores();\\n<\/script>\\n\\n<span aria-expanded={toggled} class=\\"inline-flex\\">\\n\\t<svg viewBox=\\"0 0 16 16\\" width={size} height={size}\\n\\t\\t><path\\n\\t\\t\\tfill={color ?? $theme.colors['base-content']}\\n\\t\\t\\tfill-rule=\\"evenodd\\"\\n\\t\\t\\td=\\"M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z\\"\\n\\t\\t/></svg\\n\\t>\\n</span>\\n\\n<style>\\n\\tsvg {\\n\\t\\tdisplay: inline-block;\\n\\t\\tvertical-align: middle;\\n\\t\\ttransition: transform 0.15s ease-in;\\n\\t}\\n\\n\\tspan {\\n\\t\\tmargin: auto 0 auto 0;\\n\\t}\\n\\n\\t[aria-expanded='true'] svg {\\n\\t\\ttransform: rotate(0.25turn);\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAqBC,+BAAI,CACH,OAAO,CAAE,YAAY,CACrB,cAAc,CAAE,MAAM,CACtB,UAAU,CAAE,SAAS,CAAC,KAAK,CAAC,OAC7B,CAEA,gCAAK,CACJ,MAAM,CAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACrB,CAEA,CAAC,aAAa,CAAC,MAAM,eAAC,CAAC,iBAAI,CAC1B,SAAS,CAAE,OAAO,QAAQ,CAC3B"}`},qn=F((o,t,e,g)=>{let a,r,{toggled:i=!1}=t,{color:c=void 0}=t,{size:s=10}=t;const{theme:m}=ee();return r=z(m,A=>a=A),t.toggled===void 0&&e.toggled&&i!==void 0&&e.toggled(i),t.color===void 0&&e.color&&c!==void 0&&e.color(c),t.size===void 0&&e.size&&s!==void 0&&e.size(s),o.css.add(Qn),r(),`<span${O("aria-expanded",i,0)} class="inline-flex svelte-lqleyo"><svg viewBox="0 0 16 16"${O("width",s,0)}${O("height",s,0)} class="svelte-lqleyo"><path${O("fill",c??a.colors["base-content"],0)} fill-rule="evenodd" d="M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"></path></svg> </span>`}),Yn={code:"@media print{.chevron.svelte-l8wqr7{display:none}}",map:`{"version":3,"file":"GroupRow.svelte","sources":["GroupRow.svelte"],"sourcesContent":["<script>\\n\\timport { createEventDispatcher } from 'svelte';\\n\\timport TableGroupIcon from './TableGroupIcon.svelte';\\n\\timport Delta from '../core/Delta.svelte';\\n\\timport { safeExtractColumn, aggregateColumn } from './datatable.js';\\n\\timport {\\n\\t\\tformatValue,\\n\\t\\tgetFormatObjectFromString\\n\\t} from '@evidence-dev/component-utilities/formatting';\\n\\timport TableCell from './TableCell.svelte';\\n\\timport { toBoolean } from '../../../utils.js';\\n\\n\\texport let groupName;\\n\\texport let currentGroupData;\\n\\texport let toggled;\\n\\texport let columnSummary;\\n\\texport let rowNumbers;\\n\\texport let rowColor = undefined;\\n\\texport let subtotals = true;\\n\\t$: subtotals = toBoolean(subtotals);\\n\\texport let orderedColumns = undefined;\\n\\texport let compact = undefined;\\n\\n\\tconst dispatch = createEventDispatcher();\\n\\n\\tfunction toggleGroup() {\\n\\t\\tdispatch('toggle', { groupName });\\n\\t}\\n<\/script>\\n\\n<tr\\n\\ton:click={toggleGroup}\\n\\tclass=\\"cursor-pointer hover:bg-base-200 w-full border-t-[1px] border-base-200\\"\\n\\trole=\\"button\\"\\n\\ttabindex=\\"0\\"\\n\\ton:keypress={(e) => e.key === 'Enter' && toggleGroup()}\\n\\tstyle:background-color={rowColor}\\n>\\n\\t{#each orderedColumns as column, j}\\n\\t\\t{@const useCol = safeExtractColumn(column, columnSummary)}\\n\\t\\t{@const column_format = column.fmt\\n\\t\\t\\t? getFormatObjectFromString(column.fmt, useCol.format?.valueType)\\n\\t\\t\\t: useCol.format}\\n\\t\\t{@const format = column.subtotalFmt\\n\\t\\t\\t? getFormatObjectFromString(column.subtotalFmt)\\n\\t\\t\\t: column.totalFmt\\n\\t\\t\\t\\t? getFormatObjectFromString(column.totalFmt)\\n\\t\\t\\t\\t: column_format}\\n\\t\\t{@const useFormat = format?.valueType === 'date' ? '' : format}\\n\\t\\t{#if j === 0}\\n\\t\\t\\t<TableCell\\n\\t\\t\\t\\tclass=\\"font-medium py-[3px]\\"\\n\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\tcolSpan={rowNumbers ? 2 : 1}\\n\\t\\t\\t\\tpaddingLeft=\\"1px\\"\\n\\t\\t\\t>\\n\\t\\t\\t\\t<div class=\\"items-center gap-2 align-top\\">\\n\\t\\t\\t\\t\\t<span class=\\"inline-flex print-hidden chevron\\"><TableGroupIcon {toggled} /></span>\\n\\t\\t\\t\\t\\t{groupName}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t</TableCell>\\n\\t\\t{:else if subtotals}\\n\\t\\t\\t<TableCell class=\\"{useCol.type} font-medium\\" {compact} align={column.align}>\\n\\t\\t\\t\\t{#if [undefined, 'sum', 'mean', 'median', 'min', 'max', 'weightedMean', 'count', 'countDistinct'].includes(column.totalAgg) || column.subtotalFmt}\\n\\t\\t\\t\\t\\t{#if column.contentType === 'delta'}\\n\\t\\t\\t\\t\\t\\t<Delta\\n\\t\\t\\t\\t\\t\\t\\tvalue={aggregateColumn(\\n\\t\\t\\t\\t\\t\\t\\t\\tcurrentGroupData,\\n\\t\\t\\t\\t\\t\\t\\t\\tcolumn.id,\\n\\t\\t\\t\\t\\t\\t\\t\\tcolumn.totalAgg,\\n\\t\\t\\t\\t\\t\\t\\t\\tuseCol.type,\\n\\t\\t\\t\\t\\t\\t\\t\\tcolumn.weightCol\\n\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\tdownIsGood={column.downIsGood}\\n\\t\\t\\t\\t\\t\\t\\tformat_object={useFormat}\\n\\t\\t\\t\\t\\t\\t\\tcolumnUnitSummary={useCol.columnUnitSummary}\\n\\t\\t\\t\\t\\t\\t\\tshowValue={column.showValue}\\n\\t\\t\\t\\t\\t\\t\\tshowSymbol={column.deltaSymbol}\\n\\t\\t\\t\\t\\t\\t\\talign={column.align}\\n\\t\\t\\t\\t\\t\\t\\tfontClass=\\"font-medium text-[9.25pt]\\"\\n\\t\\t\\t\\t\\t\\t\\tneutralMin={column.neutralMin}\\n\\t\\t\\t\\t\\t\\t\\tneutralMax={column.neutralMax}\\n\\t\\t\\t\\t\\t\\t\\tchip={column.chip}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\taggregateColumn(\\n\\t\\t\\t\\t\\t\\t\\t\\tcurrentGroupData,\\n\\t\\t\\t\\t\\t\\t\\t\\tcolumn.id,\\n\\t\\t\\t\\t\\t\\t\\t\\tcolumn.totalAgg,\\n\\t\\t\\t\\t\\t\\t\\t\\tuseCol.type,\\n\\t\\t\\t\\t\\t\\t\\t\\tcolumn.weightCol\\n\\t\\t\\t\\t\\t\\t\\t),\\n\\t\\t\\t\\t\\t\\t\\tuseFormat,\\n\\t\\t\\t\\t\\t\\t\\tuseCol.columnUnitSummary\\n\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t{column.totalAgg}\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</TableCell>\\n\\t\\t{:else}\\n\\t\\t\\t<TableCell />\\n\\t\\t{/if}\\n\\t{/each}\\n</tr>\\n\\n<style>\\n\\t@media print {\\n\\t\\t.chevron {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4GC,OAAO,KAAM,CACZ,sBAAS,CACR,OAAO,CAAE,IACV,CACD"}`},Hn=F((o,t,e,g)=>{let{groupName:a}=t,{currentGroupData:r}=t,{toggled:i}=t,{columnSummary:c}=t,{rowNumbers:s}=t,{rowColor:m=void 0}=t,{subtotals:A=!0}=t,{orderedColumns:h=void 0}=t,{compact:d=void 0}=t;return nn(),t.groupName===void 0&&e.groupName&&a!==void 0&&e.groupName(a),t.currentGroupData===void 0&&e.currentGroupData&&r!==void 0&&e.currentGroupData(r),t.toggled===void 0&&e.toggled&&i!==void 0&&e.toggled(i),t.columnSummary===void 0&&e.columnSummary&&c!==void 0&&e.columnSummary(c),t.rowNumbers===void 0&&e.rowNumbers&&s!==void 0&&e.rowNumbers(s),t.rowColor===void 0&&e.rowColor&&m!==void 0&&e.rowColor(m),t.subtotals===void 0&&e.subtotals&&A!==void 0&&e.subtotals(A),t.orderedColumns===void 0&&e.orderedColumns&&h!==void 0&&e.orderedColumns(h),t.compact===void 0&&e.compact&&d!==void 0&&e.compact(d),o.css.add(Yn),A=K(A),`<tr class="cursor-pointer hover:bg-base-200 w-full border-t-[1px] border-base-200" role="button" tabindex="0"${ft({"background-color":m})}>${st(h,(l,v)=>{let p=It(l,c),B=l.fmt?q(l.fmt,p.format?.valueType):p.format,b=l.subtotalFmt?q(l.subtotalFmt):l.totalFmt?q(l.totalFmt):B,x=b?.valueType==="date"?"":b;return`    ${v===0?`${f(jt,"TableCell").$$render(o,{class:"font-medium py-[3px]",compact:d,colSpan:s?2:1,paddingLeft:"1px"},{},{default:()=>`<div class="items-center gap-2 align-top"><span class="inline-flex print-hidden chevron svelte-l8wqr7">${f(qn,"TableGroupIcon").$$render(o,{toggled:i},{},{})}</span> ${C(a)}</div> `})}`:`${A?`${f(jt,"TableCell").$$render(o,{class:p.type+" font-medium",compact:d,align:l.align},{},{default:()=>`${[void 0,"sum","mean","median","min","max","weightedMean","count","countDistinct"].includes(l.totalAgg)||l.subtotalFmt?`${l.contentType==="delta"?`${f(Ce,"Delta").$$render(o,{value:ce(r,l.id,l.totalAgg,p.type,l.weightCol),downIsGood:l.downIsGood,format_object:x,columnUnitSummary:p.columnUnitSummary,showValue:l.showValue,showSymbol:l.deltaSymbol,align:l.align,fontClass:"font-medium text-[9.25pt]",neutralMin:l.neutralMin,neutralMax:l.neutralMax,chip:l.chip},{},{})}`:`${C(dt(ce(r,l.id,l.totalAgg,p.type,l.weightCol),x,p.columnUnitSummary))}`}`:`${C(l.totalAgg)}`} `})}`:`${f(jt,"TableCell").$$render(o,{},{},{})}`}`}`})} </tr>`}),Xn={code:"button.svelte-1uc1g2y svg{stroke:var(--base-content);margin-top:auto;margin-bottom:auto;transition:stroke 200ms}button.svelte-1uc1g2y{display:flex;cursor:pointer;font-family:var(--ui-font-family);font-size:1em;color:var(--base-content);opacity:0.5;justify-items:flex-end;align-items:baseline;background-color:transparent;border:none;padding:0;margin:0 5px;gap:3px;transition:all 200ms;-moz-user-select:none;-webkit-user-select:none;-o-user-select:none;user-select:none}button.svelte-1uc1g2y:hover{opacity:1;color:var(--primary);transition:all 200ms}button.svelte-1uc1g2y:hover svg{stroke:var(--primary);transition:all 200ms}@media(max-width: 600px){button.svelte-1uc1g2y{display:none}}@media print{button.svelte-1uc1g2y{display:none}}",map:`{"version":3,"file":"EnterFullScreen.svelte","sources":["EnterFullScreen.svelte"],"sourcesContent":["<script>\\n\\timport { fade } from 'svelte/transition';\\n\\timport { Icon } from '@steeze-ui/svelte-icon';\\n\\timport { EnterFullScreen } from '@steeze-ui/radix-icons';\\n\\n\\texport let display;\\n<\/script>\\n\\n{#if display}\\n\\t<div transition:fade|local={{ duration: 200 }}>\\n\\t\\t<button type=\\"button\\" aria-label=\\"Enter Fullscreen\\" on:click>\\n\\t\\t\\t<span>Fullscreen</span>\\n\\t\\t\\t<Icon class=\\"w-3 h-3\\" src={EnterFullScreen} />\\n\\t\\t</button>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\tbutton :global(svg) {\\n\\t\\tstroke: var(--base-content);\\n\\t\\tmargin-top: auto;\\n\\t\\tmargin-bottom: auto;\\n\\t\\ttransition: stroke 200ms;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tdisplay: flex;\\n\\t\\tcursor: pointer;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-size: 1em;\\n\\t\\tcolor: var(--base-content);\\n\\t\\topacity: 0.5;\\n\\t\\tjustify-items: flex-end;\\n\\t\\talign-items: baseline;\\n\\t\\tbackground-color: transparent;\\n\\t\\tborder: none;\\n\\t\\tpadding: 0;\\n\\t\\tmargin: 0 5px;\\n\\t\\tgap: 3px;\\n\\t\\ttransition: all 200ms;\\n\\t\\t-moz-user-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-o-user-select: none;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\tbutton:hover {\\n\\t\\topacity: 1;\\n\\t\\tcolor: var(--primary);\\n\\t\\ttransition: all 200ms;\\n\\t}\\n\\n\\tbutton:hover :global(svg) {\\n\\t\\tstroke: var(--primary);\\n\\t\\ttransition: all 200ms;\\n\\t}\\n\\n\\t@media (max-width: 600px) {\\n\\t\\tbutton {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n\\n\\t@media print {\\n\\t\\tbutton {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAkBC,qBAAM,CAAS,GAAK,CACnB,MAAM,CAAE,IAAI,cAAc,CAAC,CAC3B,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,MAAM,CAAC,KACpB,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,OAAO,CACf,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,SAAS,CAAE,GAAG,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,QAAQ,CACrB,gBAAgB,CAAE,WAAW,CAC7B,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CAAC,CAAC,GAAG,CACb,GAAG,CAAE,GAAG,CACR,UAAU,CAAE,GAAG,CAAC,KAAK,CACrB,gBAAgB,CAAE,IAAI,CACtB,mBAAmB,CAAE,IAAI,CACzB,cAAc,CAAE,IAAI,CACpB,WAAW,CAAE,IACd,CAEA,qBAAM,MAAO,CACZ,OAAO,CAAE,CAAC,CACV,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,qBAAM,MAAM,CAAS,GAAK,CACzB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qBAAO,CACN,OAAO,CAAE,IACV,CACD,CAEA,OAAO,KAAM,CACZ,qBAAO,CACN,OAAO,CAAE,IACV,CACD"}`},Ke=F((o,t,e,g)=>{let{display:a}=t;return t.display===void 0&&e.display&&a!==void 0&&e.display(a),o.css.add(Xn),`${a?`<div><button type="button" aria-label="Enter Fullscreen" class="svelte-1uc1g2y"><span data-svelte-h="svelte-qzhydn">Fullscreen</span> ${f(Ct,"Icon").$$render(o,{class:"w-3 h-3",src:In},{},{})}</button></div>`:""}`}),Zn=o=>Object.keys($e).includes(o),$e={grey:"base-content-muted",blue:"info",green:"positive",yellow:"warning",red:"negative",bluegreen:"positive"},Jn=(o,t,e)=>Zn(e)?(console.warn(`[${o}] ${t}=${e} is deprecated. Please use a color from the theme, or a valid color string.`),$e[e]):e,$n=F((o,t,e,g)=>{let a,r,i,c,s,m,A,h=$,d=()=>(h(),h=z(s,M=>A=M),s),l,v=$,p=()=>(v(),v=z(c,M=>l=M),c),B,b=$,x=()=>(b(),b=z(i,M=>B=M),i),k,D=$,w=()=>(D(),D=z(r,M=>k=M),r),R,n;const{resolveColor:L,resolveColorScale:T}=ee();let W=on(qe);n=z(W,M=>R=M);let y;const I=Symbol();let{id:Y}=t;function ct(){try{if(!Object.keys(R.data[0]).includes(Y))throw y="Error in table: "+Y+" does not exist in the dataset",new Error(y)}catch(M){if(y=M.message,ue)throw y}}let{description:H=void 0}=t,{contentType:yt=void 0}=t,{title:P=void 0}=t,{align:G=void 0}=t;G==="centre"&&(G="center");let{wrap:nt=!1}=t,{wrapTitle:j=!1}=t,{height:Lt=void 0}=t,{width:ne=void 0}=t,{alt:Xt=void 0}=t,{openInNewTab:vt=!1}=t,{linkLabel:oe=void 0}=t,{fmt:Zt=void 0}=t,{totalAgg:Pt=void 0}=t,{totalFmt:ae=void 0}=t,{weightCol:ot=void 0}=t,{subtotalFmt:Mt=void 0}=t,{colorMax:E=void 0}=t,{colorMin:ht=void 0}=t,{colorMid:Q=void 0}=t,{colorBreakpoints:Ot=void 0}=t,{scaleColor:xt=void 0}=t,{colorScale:X="default"}=t,{scaleColumn:St=void 0}=t,{downIsGood:at=!1}=t,{showValue:Dt=!0}=t,{deltaSymbol:pt=!0}=t,{neutralMin:_=0}=t,{neutralMax:V=0}=t,{chip:ut=!1}=t,{sparkWidth:tt=void 0}=t,{sparkHeight:Ut=void 0}=t,{sparkColor:Kt=void 0}=t,{sparkX:gt=void 0}=t,{sparkY:zt=void 0}=t,{sparkYScale:kt=!1}=t,{barColor:_t="#a5cdee"}=t,{negativeBarColor:Gt="#fca5a5"}=t,{backgroundColor:Nt="transparent"}=t,{hideLabels:lt=!1}=t,{colGroup:At=void 0}=t,{fmtColumn:Et=void 0}=t,{redNegatives:rt=!1}=t;const Wt=()=>{ct(),W.update(M=>{const Bt=M.columns.findIndex(Vt=>Vt.identifier===I);return Bt===-1?M.columns.push(m):M.columns=[...M.columns.slice(0,Bt),m,...M.columns.slice(Bt+1)],M})};return Qe(()=>{W.update(M=>(M.columns=M.columns.filter(Bt=>Bt.identifier!==I),M))}),t.id===void 0&&e.id&&Y!==void 0&&e.id(Y),t.description===void 0&&e.description&&H!==void 0&&e.description(H),t.contentType===void 0&&e.contentType&&yt!==void 0&&e.contentType(yt),t.title===void 0&&e.title&&P!==void 0&&e.title(P),t.align===void 0&&e.align&&G!==void 0&&e.align(G),t.wrap===void 0&&e.wrap&&nt!==void 0&&e.wrap(nt),t.wrapTitle===void 0&&e.wrapTitle&&j!==void 0&&e.wrapTitle(j),t.height===void 0&&e.height&&Lt!==void 0&&e.height(Lt),t.width===void 0&&e.width&&ne!==void 0&&e.width(ne),t.alt===void 0&&e.alt&&Xt!==void 0&&e.alt(Xt),t.openInNewTab===void 0&&e.openInNewTab&&vt!==void 0&&e.openInNewTab(vt),t.linkLabel===void 0&&e.linkLabel&&oe!==void 0&&e.linkLabel(oe),t.fmt===void 0&&e.fmt&&Zt!==void 0&&e.fmt(Zt),t.totalAgg===void 0&&e.totalAgg&&Pt!==void 0&&e.totalAgg(Pt),t.totalFmt===void 0&&e.totalFmt&&ae!==void 0&&e.totalFmt(ae),t.weightCol===void 0&&e.weightCol&&ot!==void 0&&e.weightCol(ot),t.subtotalFmt===void 0&&e.subtotalFmt&&Mt!==void 0&&e.subtotalFmt(Mt),t.colorMax===void 0&&e.colorMax&&E!==void 0&&e.colorMax(E),t.colorMin===void 0&&e.colorMin&&ht!==void 0&&e.colorMin(ht),t.colorMid===void 0&&e.colorMid&&Q!==void 0&&e.colorMid(Q),t.colorBreakpoints===void 0&&e.colorBreakpoints&&Ot!==void 0&&e.colorBreakpoints(Ot),t.scaleColor===void 0&&e.scaleColor&&xt!==void 0&&e.scaleColor(xt),t.colorScale===void 0&&e.colorScale&&X!==void 0&&e.colorScale(X),t.scaleColumn===void 0&&e.scaleColumn&&St!==void 0&&e.scaleColumn(St),t.downIsGood===void 0&&e.downIsGood&&at!==void 0&&e.downIsGood(at),t.showValue===void 0&&e.showValue&&Dt!==void 0&&e.showValue(Dt),t.deltaSymbol===void 0&&e.deltaSymbol&&pt!==void 0&&e.deltaSymbol(pt),t.neutralMin===void 0&&e.neutralMin&&_!==void 0&&e.neutralMin(_),t.neutralMax===void 0&&e.neutralMax&&V!==void 0&&e.neutralMax(V),t.chip===void 0&&e.chip&&ut!==void 0&&e.chip(ut),t.sparkWidth===void 0&&e.sparkWidth&&tt!==void 0&&e.sparkWidth(tt),t.sparkHeight===void 0&&e.sparkHeight&&Ut!==void 0&&e.sparkHeight(Ut),t.sparkColor===void 0&&e.sparkColor&&Kt!==void 0&&e.sparkColor(Kt),t.sparkX===void 0&&e.sparkX&&gt!==void 0&&e.sparkX(gt),t.sparkY===void 0&&e.sparkY&&zt!==void 0&&e.sparkY(zt),t.sparkYScale===void 0&&e.sparkYScale&&kt!==void 0&&e.sparkYScale(kt),t.barColor===void 0&&e.barColor&&_t!==void 0&&e.barColor(_t),t.negativeBarColor===void 0&&e.negativeBarColor&&Gt!==void 0&&e.negativeBarColor(Gt),t.backgroundColor===void 0&&e.backgroundColor&&Nt!==void 0&&e.backgroundColor(Nt),t.hideLabels===void 0&&e.hideLabels&&lt!==void 0&&e.hideLabels(lt),t.colGroup===void 0&&e.colGroup&&At!==void 0&&e.colGroup(At),t.fmtColumn===void 0&&e.fmtColumn&&Et!==void 0&&e.fmtColumn(Et),t.redNegatives===void 0&&e.redNegatives&&rt!==void 0&&e.redNegatives(rt),nt=nt==="true"||nt===!0,j=j==="true"||j===!0,vt=vt==="true"||vt===!0,xt&&console.warn("[Column] scaleColor is deprecated. Use colorScale instead."),a=Jn("Column","colorScale",xt??X),w(r=T(a)),at=K(at),Dt=K(Dt),pt=K(pt),ut=K(ut),kt=K(kt),x(i=L(_t)),p(c=L(Gt)),d(s=L(Nt)),lt=K(lt),rt=K(rt),m={identifier:I,id:Y,title:P,align:G,wrap:nt,wrapTitle:j,contentType:yt,height:Lt,width:ne,alt:Xt,openInNewTab:vt,linkLabel:oe,fmt:Zt,fmtColumn:Et,totalAgg:Pt,totalFmt:ae,subtotalFmt:Mt,weightCol:ot,downIsGood:at,deltaSymbol:pt,chip:ut,neutralMin:_,neutralMax:V,showValue:Dt,colorMax:E,colorMin:ht,colorScale:k,scaleColumn:St,colGroup:At,colorMid:Q,colorBreakpoints:Ot,description:H,redNegatives:rt,sparkWidth:tt,sparkHeight:Ut,sparkColor:Kt,sparkX:gt,sparkY:zt,sparkYScale:kt,barColor:B,negativeBarColor:l,backgroundColor:A,hideLabels:lt},Wt(),h(),v(),b(),D(),n(),""}),to={code:".table-container.svelte-xwrh5j.svelte-xwrh5j{font-size:9.5pt}.scrollbox.svelte-xwrh5j.svelte-xwrh5j{width:100%;overflow-x:auto;scrollbar-width:thin}table.svelte-xwrh5j.svelte-xwrh5j{display:table;width:100%;border-collapse:collapse;font-variant-numeric:tabular-nums}.page-changer.svelte-xwrh5j.svelte-xwrh5j{padding:0;height:1.1em;width:1.1em}.pagination.svelte-xwrh5j.svelte-xwrh5j{font-size:12px;display:flex;align-items:center;justify-content:flex-end;height:2em;font-family:var(--ui-font-family);-webkit-user-select:none;-moz-user-select:none;user-select:none;text-align:right;margin-top:0.5em;margin-bottom:0;font-variant-numeric:tabular-nums}.page-labels.svelte-xwrh5j.svelte-xwrh5j{display:flex;justify-content:flex-start;align-items:center;gap:3px}.page-changer.svelte-xwrh5j.svelte-xwrh5j{font-size:20px;background:none;border:none;cursor:pointer;transition:color 200ms}.page-changer.hovering.svelte-xwrh5j.svelte-xwrh5j{color:var(--color-primary);transition:color 200ms}.page-changer.svelte-xwrh5j.svelte-xwrh5j:disabled{cursor:auto;-webkit-user-select:none;-moz-user-select:none;user-select:none;transition:color 200ms}.page-icon.svelte-xwrh5j.svelte-xwrh5j{height:1em;width:1em}.page-input.svelte-xwrh5j.svelte-xwrh5j{box-sizing:content-box;text-align:center;padding:0.25em 0.5em;margin:0;border:1px solid transparent;border-radius:4px;font-size:12px}.table-footer.svelte-xwrh5j.svelte-xwrh5j{display:flex;justify-content:flex-end;align-items:center;font-size:12px;height:9px}.page-input.svelte-xwrh5j.svelte-xwrh5j::-webkit-outer-spin-button,.page-input.svelte-xwrh5j.svelte-xwrh5j::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.page-input[type='number'].svelte-xwrh5j.svelte-xwrh5j{-moz-appearance:textfield;-webkit-appearance:textfield;appearance:textfield}.page-input.hovering.svelte-xwrh5j.svelte-xwrh5j{border:1px solid var(--base-300)}.page-input.error.svelte-xwrh5j.svelte-xwrh5j{border:1px solid var(--negative)}.page-input.svelte-xwrh5j.svelte-xwrh5j::-moz-placeholder{color:var(--base-content-muted)}.page-input.svelte-xwrh5j.svelte-xwrh5j::placeholder{color:var(--base-content-muted)}button.svelte-xwrh5j:enabled>.page-icon.svelte-xwrh5j:hover{filter:brightness(0.8)}.svelte-xwrh5j.svelte-xwrh5j:focus{outline:none}.svelte-xwrh5j.svelte-xwrh5j::-moz-placeholder{color:var(--base-content-muted);opacity:1}.svelte-xwrh5j.svelte-xwrh5j::placeholder{color:var(--base-content-muted);opacity:1}.svelte-xwrh5j.svelte-xwrh5j:-ms-input-placeholder{color:var(--base-content-muted)}.svelte-xwrh5j.svelte-xwrh5j::-ms-input-placeholder{color:var(--base-content-muted)}.noresults.svelte-xwrh5j.svelte-xwrh5j{display:none;color:var(--base-content-muted);text-align:center;margin-top:5px}.shownoresults.svelte-xwrh5j.svelte-xwrh5j{display:block}.print-page-count.svelte-xwrh5j.svelte-xwrh5j{display:none}@media(max-width: 600px){.page-changer.svelte-xwrh5j.svelte-xwrh5j{height:1.2em;width:1.2em}.page-icon.svelte-xwrh5j.svelte-xwrh5j{height:1.2em;width:1.2em}.page-count.svelte-xwrh5j.svelte-xwrh5j{font-size:1.1em}.page-input.svelte-xwrh5j.svelte-xwrh5j{font-size:1.1em}}@media print{.pagination.svelte-xwrh5j.svelte-xwrh5j{-moz-column-break-inside:avoid;break-inside:avoid}.page-changer.svelte-xwrh5j.svelte-xwrh5j{display:none}.page-count.svelte-xwrh5j.svelte-xwrh5j{display:none}.print-page-count.svelte-xwrh5j.svelte-xwrh5j{display:inline}}",map:`{"version":3,"file":"_DataTable.svelte","sources":["_DataTable.svelte"],"sourcesContent":["<script>\\n\\timport { writable } from 'svelte/store';\\n\\timport { setContext } from 'svelte';\\n\\timport { slide } from 'svelte/transition';\\n\\timport { propKey, strictBuild } from '@evidence-dev/component-utilities/chartContext';\\n\\timport getColumnSummary from '@evidence-dev/component-utilities/getColumnSummary';\\n\\timport { convertColumnToDate } from '@evidence-dev/component-utilities/dateParsing';\\n\\timport ErrorChart from '../core/ErrorChart.svelte';\\n\\timport ComponentTitle from '../core/ComponentTitle.svelte';\\n\\timport SearchBar from '../core/SearchBar.svelte';\\n\\timport checkInputs from '@evidence-dev/component-utilities/checkInputs';\\n\\timport DownloadData from '../../ui/DownloadData.svelte';\\n\\timport InvisibleLinks from '../../../atoms/InvisibleLinks.svelte';\\n\\n\\timport { Icon } from '@steeze-ui/svelte-icon';\\n\\timport CodeBlock from '../../ui/CodeBlock.svelte';\\n\\timport { aggregateColumn, getFinalColumnOrder } from './datatable.js';\\n\\timport TableRow from './TableRow.svelte';\\n\\timport TotalRow from './TotalRow.svelte';\\n\\timport SubtotalRow from './SubtotalRow.svelte';\\n\\timport TableHeader from './TableHeader.svelte';\\n\\timport GroupRow from './GroupRow.svelte';\\n\\timport { ChevronsLeft, ChevronsRight, ChevronLeft, ChevronRight } from '@steeze-ui/tabler-icons';\\n\\timport EnterFullScreen from './EnterFullScreen.svelte';\\n\\timport Fullscreen from '../../../atoms/fullscreen/Fullscreen.svelte';\\n\\timport Column from './Column.svelte';\\n\\timport { Query } from '@evidence-dev/sdk/usql';\\n\\timport QueryLoad from '../../../atoms/query-load/QueryLoad.svelte';\\n\\timport { toasts } from '@evidence-dev/component-utilities/stores';\\n\\timport { query } from '@evidence-dev/universal-sql/client-duckdb';\\n\\timport Skeleton from '../../../atoms/skeletons/Skeleton.svelte';\\n\\timport { browserDebounce } from '@evidence-dev/sdk/utils';\\n\\timport { getThemeStores } from '../../../themes/themes.js';\\n\\timport { toBoolean } from '../../../utils.js';\\n\\n\\tconst { resolveColor } = getThemeStores();\\n\\n\\t// Set up props store\\n\\tlet props = writable({});\\n\\tsetContext(propKey, props);\\n\\n\\t// Data, pagination, and row index numbers\\n\\texport let data;\\n\\texport let queryID = undefined;\\n\\texport let rows = 10; // number of rows to show\\n\\t$: rows = Number.parseInt(rows);\\n\\n\\t/** @type {string | undefined}*/\\n\\texport let title = undefined;\\n\\n\\t/** @type {string | undefined}*/\\n\\texport let subtitle = undefined;\\n\\n\\texport let rowNumbers = false;\\n\\t$: rowNumbers = toBoolean(rowNumbers);\\n\\n\\t// Sort props\\n\\texport let sort = undefined;\\n\\tlet sortBy = undefined;\\n\\tlet sortAsc = undefined;\\n\\tlet sortDirection = undefined;\\n\\tlet sortObj = {};\\n\\t$: if (sort) {\\n\\t\\tconst [column, direction] = sort.split(' ');\\n\\t\\tsortBy = column;\\n\\t\\tsortDirection = direction;\\n\\t\\tsortAsc = direction === 'desc' ? false : true; // Default to ascending if no direction is provided\\n\\t\\tsortObj = sortBy ? { col: sortBy, ascending: sortAsc } : { col: null, ascending: null };\\n\\t}\\n\\n\\texport let groupBy = undefined;\\n\\texport let groupsOpen = true; // starting toggle for groups - open or closed\\n\\t$: groupsOpen = toBoolean(groupsOpen);\\n\\texport let groupType = 'accordion'; // accordion | section\\n\\n\\texport let accordionRowColor = undefined;\\n\\t$: accordionRowColorStore = resolveColor(accordionRowColor);\\n\\n\\texport let groupNamePosition = 'middle'; // middle (default) | top | bottom\\n\\n\\tif (groupType === 'section') {\\n\\t\\trowNumbers = false; // turn off row numbers\\n\\t}\\n\\n\\texport let subtotals = false;\\n\\t$: subtotals = toBoolean(subtotals);\\n\\n\\texport let subtotalRowColor = undefined;\\n\\t$: subtotalRowColorStore = resolveColor(subtotalRowColor);\\n\\n\\texport let subtotalFontColor = undefined;\\n\\t$: subtotalFontColorStore = resolveColor(subtotalFontColor);\\n\\n\\tlet groupToggleStates = {};\\n\\n\\tfunction handleToggle({ detail }) {\\n\\t\\tconst { groupName } = detail;\\n\\t\\tgroupToggleStates[groupName] = !groupToggleStates[groupName];\\n\\t}\\n\\n\\tlet paginated;\\n\\t$: paginated = data.length > rows && !groupBy;\\n\\n\\tlet hovering = false;\\n\\n\\texport let generateMarkdown = false;\\n\\t$: generateMarkdown = generateMarkdown === 'true' || generateMarkdown === true;\\n\\n\\t// Table features\\n\\texport let search = false;\\n\\t$: search = toBoolean(search);\\n\\n\\texport let sortable = true;\\n\\t$: sortable = toBoolean(sortable);\\n\\n\\texport let downloadable = true;\\n\\t$: downloadable = toBoolean(downloadable);\\n\\n\\texport let totalRow = false;\\n\\t$: totalRow = toBoolean(totalRow);\\n\\n\\texport let totalRowColor = undefined;\\n\\t$: totalRowColorStore = resolveColor(totalRowColor);\\n\\n\\texport let totalFontColor = undefined;\\n\\t$: totalFontColorStore = resolveColor(totalFontColor);\\n\\n\\texport let isFullPage = false;\\n\\n\\t// Row Links:\\n\\texport let link = undefined;\\n\\n\\texport let showLinkCol = false; // hides link column when columns have not been explicitly selected\\n\\t$: showLinkCol = toBoolean(showLinkCol);\\n\\n\\tlet error = undefined;\\n\\tlet groupDataPopulated = false;\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// Add props to store to let child components access them\\n\\t// ---------------------------------------------------------------------------------------\\n\\tprops.update((d) => {\\n\\t\\treturn { ...d, data, columns: [] };\\n\\t});\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// STYLING\\n\\t// ---------------------------------------------------------------------------------------\\n\\texport let rowShading = false;\\n\\t$: rowShading = toBoolean(rowShading);\\n\\n\\texport let rowLines = true;\\n\\t$: rowLines = toBoolean(rowLines);\\n\\n\\texport let wrapTitles = false;\\n\\t$: wrapTitles = toBoolean(wrapTitles);\\n\\n\\texport let headerColor = undefined;\\n\\t$: headerColorStore = resolveColor(headerColor);\\n\\n\\texport let headerFontColor = undefined;\\n\\t$: headerFontColorStore = resolveColor(headerFontColor);\\n\\n\\texport let formatColumnTitles = true;\\n\\t$: formatColumnTitles = toBoolean(formatColumnTitles);\\n\\n\\texport let backgroundColor = undefined;\\n\\t$: backgroundColorStore = resolveColor(backgroundColor);\\n\\n\\texport let compact = undefined;\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// DATA SETUP\\n\\t// ---------------------------------------------------------------------------------------\\n\\n\\tlet columnSummary;\\n\\n\\tlet priorityColumns = [groupBy];\\n\\n\\tprops.update((d) => {\\n\\t\\treturn { ...d, priorityColumns };\\n\\t});\\n\\n\\t$: finalColumnOrder = getFinalColumnOrder(\\n\\t\\t$props.columns.map((d) => d.id),\\n\\t\\t$props.priorityColumns\\n\\t);\\n\\t$: orderedColumns = [...$props.columns].sort(\\n\\t\\t(a, b) => finalColumnOrder.indexOf(a.id) - finalColumnOrder.indexOf(b.id)\\n\\t);\\n\\n\\t$: try {\\n\\t\\terror = undefined;\\n\\n\\t\\t// CHECK INPUTS\\n\\t\\tcheckInputs(data);\\n\\n\\t\\t// GET COLUMN SUMMARY\\n\\t\\tcolumnSummary = getColumnSummary(data, 'array');\\n\\n\\t\\t// Check if sort column is in table\\n\\t\\tif (sortBy) {\\n\\t\\t\\tif (!columnSummary.map((d) => d.id).includes(sortBy)) {\\n\\t\\t\\t\\tthrow Error(\\n\\t\\t\\t\\t\\t\`\${sortBy} is not a column in the dataset. sort should contain one column name and optionally a direction (asc or desc). E.g., sort=my_column or sort=\\"my_column desc\\"\`\\n\\t\\t\\t\\t);\\n\\t\\t\\t}\\n\\t\\t\\tif (sortDirection && !['asc', 'desc'].includes(sortDirection)) {\\n\\t\\t\\t\\tthrow Error(\`\${sortDirection} is not a valid sort direction. Please use asc or desc\`);\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t// PROCESS DATES\\n\\t\\t// Filter for columns with type of \\"date\\"\\n\\t\\tconst dateCols = columnSummary\\n\\t\\t\\t.filter((d) => d.type === 'date' && !(data[0]?.[d.id] instanceof Date))\\n\\t\\t\\t.map((d) => d.id);\\n\\n\\t\\tfor (let i = 0; i < dateCols.length; i++) {\\n\\t\\t\\tdata = convertColumnToDate(data, dateCols[i]);\\n\\t\\t}\\n\\n\\t\\t// Hide link column if columns have not been explicitly selected:\\n\\t\\tif (link) {\\n\\t\\t\\tconst linkColIndex = columnSummary.findIndex((d) => d.id === link);\\n\\t\\t\\tif (linkColIndex !== -1 && !showLinkCol) {\\n\\t\\t\\t\\tcolumnSummary.splice(linkColIndex, 1);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t} catch (e) {\\n\\t\\terror = e.message;\\n\\t\\tif (strictBuild) {\\n\\t\\t\\tthrow error;\\n\\t\\t}\\n\\t}\\n\\n\\tlet index = 0;\\n\\n\\tlet inputPage = null;\\n\\t$: inputPageElWidth = \`\${(inputPage ?? 1).toString().length}ch\`;\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// SEARCH\\n\\t// ---------------------------------------------------------------------------------------\\n\\tlet searchValue = '';\\n\\t/** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n\\tlet filteredData;\\n\\t$: filteredData = data;\\n\\tlet showNoResults = false;\\n\\n\\t/** @type {ReturnValue<typeof Query[\\"createReactive\\"]>}*/\\n\\tlet searchFactory;\\n\\t$: if (Query.isQuery(data) && search) {\\n\\t\\tsearchFactory = browserDebounce(\\n\\t\\t\\tQuery.createReactive(\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tloadGracePeriod: 1000,\\n\\t\\t\\t\\t\\tcallback: (v) => {\\n\\t\\t\\t\\t\\t\\tfilteredData = v;\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\texecFn: query\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\tdata.opts,\\n\\t\\t\\t\\tdata\\n\\t\\t\\t),\\n\\t\\t\\t200\\n\\t\\t);\\n\\t}\\n\\n\\t$: if (searchFactory) {\\n\\t\\tif (searchValue) {\\n\\t\\t\\tsearchFactory(\\n\\t\\t\\t\\tdata.search(\\n\\t\\t\\t\\t\\tsearchValue,\\n\\t\\t\\t\\t\\t$props.columns.map((c) => c.id),\\n\\t\\t\\t\\t\\tsearchValue.length === 1 ? 0.5 : searchValue.length >= 6 ? 0.9 : 0.8\\n\\t\\t\\t\\t),\\n\\t\\t\\t\\tdata.opts\\n\\t\\t\\t);\\n\\t\\t} else {\\n\\t\\t\\tsearchFactory(data, data.opts);\\n\\t\\t}\\n\\t}\\n\\n\\t$: if (search && !Query.isQuery(data)) {\\n\\t\\ttoasts.add(\\n\\t\\t\\t{\\n\\t\\t\\t\\tstatus: 'warning',\\n\\t\\t\\t\\ttitle: 'Search Failed',\\n\\t\\t\\t\\tmessage: 'Please use a query instead.'\\n\\t\\t\\t},\\n\\t\\t\\t5000\\n\\t\\t);\\n\\t}\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// GROUPED DATA\\n\\t// ---------------------------------------------------------------------------------------\\n\\n\\tlet groupedData = {};\\n\\tlet groupRowData = [];\\n\\n\\t$: if (data) {\\n\\t\\tgroupDataPopulated = false;\\n\\t}\\n\\n\\t$: if (!error) {\\n\\t\\tif (groupBy && !groupDataPopulated) {\\n\\t\\t\\tgroupedData = data.reduce((acc, row) => {\\n\\t\\t\\t\\tconst groupName = row[groupBy];\\n\\t\\t\\t\\tif (!acc[groupName]) {\\n\\t\\t\\t\\t\\tacc[groupName] = [];\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tacc[groupName].push(row);\\n\\t\\t\\t\\treturn acc;\\n\\t\\t\\t}, {});\\n\\t\\t\\tgroupDataPopulated = true;\\n\\t\\t}\\n\\n\\t\\t// After groupedData is populated, calculate aggregations for groupRowData\\n\\t\\tgroupRowData = Object.keys(groupedData).reduce((acc, groupName) => {\\n\\t\\t\\tacc[groupName] = {}; // Initialize groupRow object for this group\\n\\n\\t\\t\\tfor (const col of $props.columns) {\\n\\t\\t\\t\\tconst id = col.id;\\n\\t\\t\\t\\tconst colType = columnSummary.find((d) => d.id === id)?.type;\\n\\t\\t\\t\\tconst totalAgg = col.totalAgg;\\n\\t\\t\\t\\tconst weightCol = col.weightCol;\\n\\t\\t\\t\\tconst rows = groupedData[groupName];\\n\\t\\t\\t\\tacc[groupName][id] = aggregateColumn(rows, id, totalAgg, colType, weightCol);\\n\\t\\t\\t}\\n\\n\\t\\t\\treturn acc;\\n\\t\\t}, {});\\n\\n\\t\\t// Update groupToggleStates only for new groups\\n\\t\\tconst existingGroups = Object.keys(groupToggleStates);\\n\\t\\tfor (const groupName of Object.keys(groupedData)) {\\n\\t\\t\\tif (!existingGroups.includes(groupName)) {\\n\\t\\t\\t\\tgroupToggleStates[groupName] = groupsOpen; // Only add new groups with the default state\\n\\t\\t\\t}\\n\\t\\t\\t// Existing states are untouched\\n\\t\\t}\\n\\t}\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// SORTING\\n\\t// ---------------------------------------------------------------------------------------\\n\\n\\t$: sortClick = (column) => {\\n\\t\\tif (sortObj.col === column) {\\n\\t\\t\\t// If the clicked column is the same as inital sort column, switch the sort direction\\n\\t\\t\\tsortObj.ascending = !sortObj.ascending;\\n\\t\\t} else {\\n\\t\\t\\t// If the clicked column is different from initial sort column, change the sort column and sort ascending\\n\\t\\t\\tsortObj.col = column;\\n\\t\\t\\tsortObj.ascending = true;\\n\\t\\t}\\n\\n\\t\\tsortFunc(sortObj);\\n\\t};\\n\\n\\t$: sortFunc = (sortObj) => {\\n\\t\\tconst column = sortObj.col;\\n\\n\\t\\t// Modifier to sorting function for ascending or descending\\n\\t\\tconst sortModifier = sortObj.ascending ? 1 : -1;\\n\\n\\t\\tconst forceTopOfAscending = (val) =>\\n\\t\\t\\tval === undefined || val === null || (typeof val === 'number' && isNaN(val));\\n\\n\\t\\tconst comparator = (a, b) => {\\n\\t\\t\\tconst valA = a[column];\\n\\t\\t\\tconst valB = b[column];\\n\\n\\t\\t\\tif (forceTopOfAscending(valA) && !forceTopOfAscending(valB)) return -1 * sortModifier;\\n\\t\\t\\tif (forceTopOfAscending(valB) && !forceTopOfAscending(valA)) return 1 * sortModifier;\\n\\n\\t\\t\\t// Ensure values are strings for case-insensitive comparison\\n\\t\\t\\tconst normalizedA = typeof valA === 'string' ? valA.toLowerCase() : valA;\\n\\t\\t\\tconst normalizedB = typeof valB === 'string' ? valB.toLowerCase() : valB;\\n\\n\\t\\t\\tif (normalizedA < normalizedB) return -1 * sortModifier;\\n\\t\\t\\tif (normalizedA > normalizedB) return 1 * sortModifier;\\n\\t\\t\\treturn 0;\\n\\t\\t};\\n\\n\\t\\tif (groupBy) {\\n\\t\\t\\tconst sortedGroupedData = {};\\n\\n\\t\\t\\tfor (const groupName of Object.keys(groupedData)) {\\n\\t\\t\\t\\tsortedGroupedData[groupName] = [...groupedData[groupName]].sort(comparator);\\n\\t\\t\\t}\\n\\n\\t\\t\\tgroupedData = sortedGroupedData;\\n\\t\\t} else {\\n\\t\\t\\tconst sortedFilteredData = [...filteredData].sort(comparator);\\n\\t\\t\\tfilteredData = sortedFilteredData;\\n\\t\\t}\\n\\t};\\n\\n\\tlet sortedGroupNames;\\n\\t$: if (groupBy && sortObj.col) {\\n\\t\\t// Sorting groups based on aggregated values or group names\\n\\t\\tsortedGroupNames = Object.entries(groupRowData)\\n\\t\\t\\t.sort((a, b) => {\\n\\t\\t\\t\\tconst valA = a[1][sortObj.col],\\n\\t\\t\\t\\t\\tvalB = b[1][sortObj.col];\\n\\t\\t\\t\\t// Use the existing sort logic but apply it to groupRowData's values\\n\\t\\t\\t\\t// Special case for groupby column\\n\\t\\t\\t\\tif (sortObj.col === groupBy && isNaN(groupBy)) {\\n\\t\\t\\t\\t\\treturn sortObj.ascending ? a[0].localeCompare(b[0]) : b[0].localeCompare(a[0]);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (\\n\\t\\t\\t\\t\\t(valA === undefined || valA === null || isNaN(valA)) &&\\n\\t\\t\\t\\t\\tvalB !== undefined &&\\n\\t\\t\\t\\t\\t!isNaN(valB)\\n\\t\\t\\t\\t) {\\n\\t\\t\\t\\t\\treturn -1 * (sortObj.ascending ? 1 : -1);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (\\n\\t\\t\\t\\t\\t(valB === undefined || valB === null || isNaN(valB)) &&\\n\\t\\t\\t\\t\\tvalA !== undefined &&\\n\\t\\t\\t\\t\\t!isNaN(valA)\\n\\t\\t\\t\\t) {\\n\\t\\t\\t\\t\\treturn 1 * (sortObj.ascending ? 1 : -1);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (valA < valB) {\\n\\t\\t\\t\\t\\treturn -1 * (sortObj.ascending ? 1 : -1);\\n\\t\\t\\t\\t} else if (valA > valB) {\\n\\t\\t\\t\\t\\treturn 1 * (sortObj.ascending ? 1 : -1);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\treturn 0;\\n\\t\\t\\t})\\n\\t\\t\\t.map((entry) => entry[0]); // Extract sorted group names\\n\\t} else {\\n\\t\\t// Default to alphabetical order of group names or another criterion when not sorting by a specific column\\n\\t\\tsortedGroupNames = Object.keys(groupedData).sort();\\n\\t}\\n\\n\\t// Re-run sort on data change (useful for input changes)\\n\\t$: if (data && sort) {\\n\\t\\tsortFunc(sortObj);\\n\\t}\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// PAGINATION\\n\\t// ---------------------------------------------------------------------------------------\\n\\n\\tlet totalRows;\\n\\t$: totalRows = filteredData.length;\\n\\n\\tlet displayedData = filteredData;\\n\\n\\tlet pageCount;\\n\\tlet currentPage = 1;\\n\\n\\t$: currentPage = Math.ceil((index + rows) / rows);\\n\\t$: currentPageElWidth = \`\${(currentPage ?? 1).toString().length}ch\`;\\n\\tlet max;\\n\\n\\t$: goToPage = (pageNumber) => {\\n\\t\\tindex = pageNumber * rows;\\n\\t\\tmax = index + rows;\\n\\t\\tcurrentPage = Math.ceil(max / rows);\\n\\t\\tif (inputPage) {\\n\\t\\t\\tinputPage = Math.ceil(max / rows);\\n\\t\\t}\\n\\t\\ttotalRows = filteredData.length;\\n\\t\\tdisplayedData = filteredData.slice(index, index + rows);\\n\\t};\\n\\n\\tlet displayedPageLength = 0;\\n\\n\\t$: if (paginated) {\\n\\t\\tpageCount = Math.ceil(filteredData.length / rows);\\n\\n\\t\\tdisplayedData = filteredData.slice(index, index + rows);\\n\\t\\tdisplayedPageLength = displayedData.length;\\n\\t\\tif (pageCount < currentPage) {\\n\\t\\t\\tgoToPage(pageCount - 1);\\n\\t\\t} else if (currentPage < 1) {\\n\\t\\t\\tgoToPage(0);\\n\\t\\t}\\n\\t} else {\\n\\t\\tcurrentPage = 1;\\n\\t\\tdisplayedData = filteredData;\\n\\t}\\n\\n\\t// ---------------------------------------------------------------------------------------\\n\\t// DATA FOR EXPORT\\n\\t// ---------------------------------------------------------------------------------------\\n\\n\\tfunction dataSubset(data, selectedCols) {\\n\\t\\treturn data.map((obj) => {\\n\\t\\t\\tconst ret = {};\\n\\t\\t\\tfor (const key of selectedCols) {\\n\\t\\t\\t\\tret[key] = obj[key];\\n\\t\\t\\t}\\n\\t\\t\\treturn ret;\\n\\t\\t});\\n\\t}\\n\\n\\t$: tableData = dataSubset(\\n\\t\\tdata,\\n\\t\\t$props.columns.map((d) => d.id)\\n\\t);\\n\\n\\tlet fullscreen = false;\\n\\t/** @type {number} */\\n\\tlet innerHeight;\\n<\/script>\\n\\n<svelte:window bind:innerHeight />\\n\\n{#if !isFullPage && innerHeight !== undefined}\\n\\t<Fullscreen bind:open={fullscreen} {search}>\\n\\t\\t<!-- when compact middle rows are 17.5, middle rows are 23 -->\\n\\t\\t{@const ROW_HEIGHT = compact ? 17.5 : 23}\\n\\t\\t<!-- header and last row are 22.5+22.5 = 45px -->\\n\\t\\t{@const HEADER_LAST_ROW_HEIGHT = 45}\\n\\t\\t<!-- Add additional padding for search bar + 24px-->\\n\\t\\t{@const SEARCHBAR_HEIGHT = 24}\\n\\t\\t<!-- Calculation of total padding -->\\n\\t\\t{@const Y_AXIS_PADDING = search\\n\\t\\t\\t? SEARCHBAR_HEIGHT + HEADER_LAST_ROW_HEIGHT + 234\\n\\t\\t\\t: HEADER_LAST_ROW_HEIGHT + 234}\\n\\t\\t<div class=\\"pt-4\\">\\n\\t\\t\\t<svelte:self\\n\\t\\t\\t\\t{...$$props}\\n\\t\\t\\t\\trows={1 + Math.round((innerHeight - Y_AXIS_PADDING) / ROW_HEIGHT)}\\n\\t\\t\\t\\tisFullPage\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#each $props.columns as column}\\n\\t\\t\\t\\t\\t<Column {...column} />\\n\\t\\t\\t\\t{/each}\\n\\t\\t\\t</svelte:self>\\n\\t\\t</div>\\n\\t</Fullscreen>\\n{/if}\\n\\n{#if error === undefined}\\n\\t<slot>\\n\\t\\t<!-- default to every column with no customization -->\\n\\t\\t{#each columnSummary as column}\\n\\t\\t\\t<Column id={column.id} />\\n\\t\\t{/each}\\n\\t</slot>\\n\\n\\t{#if link}\\n\\t\\t<InvisibleLinks {data} {link} />\\n\\t{/if}\\n\\t{#each $props.columns.filter((column) => column.contentType === 'link') as column}\\n\\t\\t<InvisibleLinks {data} link={column.id} />\\n\\t{/each}\\n\\n\\t<div\\n\\t\\tdata-testid={isFullPage ? undefined : \`DataTable-\${data?.id ?? 'no-id'}\`}\\n\\t\\trole=\\"none\\"\\n\\t\\tclass=\\"table-container mt-2 {paginated ? 'mb-5' : 'mb-2'}\\"\\n\\t\\ttransition:slide|local\\n\\t\\ton:mouseenter={() => (hovering = true)}\\n\\t\\ton:mouseleave={() => (hovering = false)}\\n\\t>\\n\\t\\t{#if title || subtitle}\\n\\t\\t\\t<ComponentTitle {title} {subtitle} />\\n\\t\\t{/if}\\n\\n\\t\\t{#if search}\\n\\t\\t\\t<SearchBar bind:value={searchValue} searchFunction={() => {}} />\\n\\t\\t{/if}\\n\\n\\t\\t<div class=\\"scrollbox pretty-scrollbar\\" style:background-color={$backgroundColorStore}>\\n\\t\\t\\t<table>\\n\\t\\t\\t\\t<TableHeader\\n\\t\\t\\t\\t\\t{rowNumbers}\\n\\t\\t\\t\\t\\theaderColor={$headerColorStore}\\n\\t\\t\\t\\t\\theaderFontColor={$headerFontColorStore}\\n\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t{sortable}\\n\\t\\t\\t\\t\\t{sortClick}\\n\\t\\t\\t\\t\\t{formatColumnTitles}\\n\\t\\t\\t\\t\\t{sortObj}\\n\\t\\t\\t\\t\\t{wrapTitles}\\n\\t\\t\\t\\t\\t{link}\\n\\t\\t\\t\\t/>\\n\\n\\t\\t\\t\\t<QueryLoad data={filteredData}>\\n\\t\\t\\t\\t\\t<svelte:fragment slot=\\"skeleton\\">\\n\\t\\t\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t\\t\\t<td colspan={filteredData.columns.length} class=\\"h-32\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t<Skeleton />\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\t\\t</svelte:fragment>\\n\\t\\t\\t\\t\\t{#if groupBy && groupedData && searchValue === ''}\\n\\t\\t\\t\\t\\t\\t{#each sortedGroupNames as groupName}\\n\\t\\t\\t\\t\\t\\t\\t{#if groupType === 'accordion'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<GroupRow\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupName}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tcurrentGroupData={groupedData[groupName]}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ttoggled={groupToggleStates[groupName]}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ton:toggle={handleToggle}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\trowColor={$accordionRowColorStore}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowNumbers}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{subtotals}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t\\t\\t{#if groupToggleStates[groupName]}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<TableRow\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tdisplayedData={groupedData[groupName]}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupType}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowShading}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{link}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowNumbers}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowLines}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{index}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tgrouped={true}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tgroupColumn={groupBy}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t\\t{:else if groupType === 'section'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<TableRow\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tgroupColumn={groupBy}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupType}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\trowSpan={groupedData[groupName].length}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tdisplayedData={groupedData[groupName]}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowShading}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{link}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowNumbers}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{rowLines}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{index}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tgrouped={true}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupNamePosition}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t\\t\\t{#if subtotals}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<SubtotalRow\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupName}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcurrentGroupData={groupedData[groupName]}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trowColor={$subtotalRowColorStore}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tfontColor={$subtotalFontColorStore}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupType}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{groupBy}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t<TableRow\\n\\t\\t\\t\\t\\t\\t\\t{displayedData}\\n\\t\\t\\t\\t\\t\\t\\t{rowShading}\\n\\t\\t\\t\\t\\t\\t\\t{link}\\n\\t\\t\\t\\t\\t\\t\\t{rowNumbers}\\n\\t\\t\\t\\t\\t\\t\\t{rowLines}\\n\\t\\t\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t\\t\\t{index}\\n\\t\\t\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{/if}\\n\\n\\t\\t\\t\\t\\t{#if totalRow && searchValue === ''}\\n\\t\\t\\t\\t\\t\\t<TotalRow\\n\\t\\t\\t\\t\\t\\t\\t{data}\\n\\t\\t\\t\\t\\t\\t\\t{rowNumbers}\\n\\t\\t\\t\\t\\t\\t\\t{columnSummary}\\n\\t\\t\\t\\t\\t\\t\\trowColor={$totalRowColorStore}\\n\\t\\t\\t\\t\\t\\t\\tfontColor={$totalFontColorStore}\\n\\t\\t\\t\\t\\t\\t\\t{groupType}\\n\\t\\t\\t\\t\\t\\t\\t{compact}\\n\\t\\t\\t\\t\\t\\t\\t{orderedColumns}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t</QueryLoad>\\n\\t\\t\\t</table>\\n\\t\\t</div>\\n\\n\\t\\t<div class=\\"noresults\\" class:shownoresults={showNoResults}>No Results</div>\\n\\n\\t\\t{#if paginated && pageCount > 1}\\n\\t\\t\\t<div class=\\"pagination text-base-content-muted\\">\\n\\t\\t\\t\\t<div class=\\"page-labels mr-auto\\">\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\taria-label=\\"first-page\\"\\n\\t\\t\\t\\t\\t\\tclass=\\"page-changer disabled:text-base-content-muted/25\\"\\n\\t\\t\\t\\t\\t\\tclass:hovering\\n\\t\\t\\t\\t\\t\\tdisabled={currentPage === 1}\\n\\t\\t\\t\\t\\t\\ton:click={() => goToPage(0)}\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<div class=\\"page-icon flex items-center\\">\\n\\t\\t\\t\\t\\t\\t\\t<Icon src={ChevronsLeft} />\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\taria-label=\\"previous-page\\"\\n\\t\\t\\t\\t\\t\\tclass=\\"page-changer disabled:text-base-content-muted/25\\"\\n\\t\\t\\t\\t\\t\\tclass:hovering\\n\\t\\t\\t\\t\\t\\tdisabled={currentPage === 1}\\n\\t\\t\\t\\t\\t\\ton:click={() => goToPage(currentPage - 2)}\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<div class=\\"page-icon h-[0.83em] flex items-center\\">\\n\\t\\t\\t\\t\\t\\t\\t<Icon src={ChevronLeft} class=\\"h-[0.83em]\\" />\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t\\t<span class=\\"page-count\\">\\n\\t\\t\\t\\t\\t\\tPage\\n\\t\\t\\t\\t\\t\\t<input\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"page-input bg-base-200 text-base-content-muted\\"\\n\\t\\t\\t\\t\\t\\t\\tclass:hovering\\n\\t\\t\\t\\t\\t\\t\\tclass:error={inputPage > pageCount}\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width: {inputPage ? inputPageElWidth : currentPageElWidth};\\"\\n\\t\\t\\t\\t\\t\\t\\ttype=\\"number\\"\\n\\t\\t\\t\\t\\t\\t\\tbind:value={inputPage}\\n\\t\\t\\t\\t\\t\\t\\ton:keyup={() => goToPage((inputPage ?? 1) - 1)}\\n\\t\\t\\t\\t\\t\\t\\ton:change={() => goToPage((inputPage ?? 1) - 1)}\\n\\t\\t\\t\\t\\t\\t\\tplaceholder={currentPage}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t/\\n\\t\\t\\t\\t\\t\\t<span class=\\"page-count ml-1\\">{pageCount.toLocaleString()}</span>\\n\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t<span class=\\"print-page-count\\">\\n\\t\\t\\t\\t\\t\\t{displayedPageLength.toLocaleString()} of {totalRows.toLocaleString()} records\\n\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\taria-label=\\"next-page\\"\\n\\t\\t\\t\\t\\t\\tclass=\\"page-changer disabled:text-base-content-muted/25\\"\\n\\t\\t\\t\\t\\t\\tclass:hovering\\n\\t\\t\\t\\t\\t\\tdisabled={currentPage === pageCount}\\n\\t\\t\\t\\t\\t\\ton:click={() => goToPage(currentPage)}\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<div class=\\"page-icon h-[0.83em] flex items-center\\">\\n\\t\\t\\t\\t\\t\\t\\t<Icon src={ChevronRight} class=\\"h-[0.83em]\\" />\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\taria-label=\\"last-page\\"\\n\\t\\t\\t\\t\\t\\tclass=\\"page-changer disabled:text-base-content-muted/25\\"\\n\\t\\t\\t\\t\\t\\tclass:hovering\\n\\t\\t\\t\\t\\t\\tdisabled={currentPage === pageCount}\\n\\t\\t\\t\\t\\t\\ton:click={() => goToPage(pageCount - 1)}\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<div class=\\"page-icon flex items-center\\">\\n\\t\\t\\t\\t\\t\\t\\t<Icon src={ChevronsRight} />\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{#if downloadable}\\n\\t\\t\\t\\t\\t<DownloadData class=\\"download-button\\" data={tableData} {queryID} display={hovering} />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{#if !isFullPage}\\n\\t\\t\\t\\t\\t<EnterFullScreen on:click={() => (fullscreen = true)} display={hovering} />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t{:else}\\n\\t\\t\\t<div class=\\"table-footer mt-3\\">\\n\\t\\t\\t\\t{#if downloadable}\\n\\t\\t\\t\\t\\t<DownloadData class=\\"download-button\\" data={tableData} {queryID} display={hovering} />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{#if !isFullPage}\\n\\t\\t\\t\\t\\t<EnterFullScreen on:click={() => (fullscreen = true)} display={hovering} />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\t</div>\\n\\n\\t{#if generateMarkdown}\\n\\t\\t{#if queryID}\\n\\t\\t\\t<CodeBlock>\\n\\t\\t\\t\\t{\`<DataTable data={\${queryID}}>\`}\\n\\t\\t\\t\\t<br />\\n\\t\\t\\t\\t{#each Object.keys(data[0]) as column}\\n\\t\\t\\t\\t\\t{\`   <Column id=\${column.includes(' ') ? \`'\${column}'\` : column}/>\`}\\n\\t\\t\\t\\t\\t<br />\\n\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t{\`</DataTable>\`}\\n\\t\\t\\t</CodeBlock>\\n\\t\\t{/if}\\n\\t{/if}\\n{:else}\\n\\t<ErrorChart {error} title=\\"Data Table\\" />\\n{/if}\\n\\n<style>\\n\\t.table-container {\\n\\t\\tfont-size: 9.5pt;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\twidth: 100%;\\n\\t\\toverflow-x: auto;\\n\\t\\tscrollbar-width: thin;\\n\\t}\\n\\n\\ttable {\\n\\t\\tdisplay: table;\\n\\t\\twidth: 100%;\\n\\t\\tborder-collapse: collapse;\\n\\t\\tfont-variant-numeric: tabular-nums;\\n\\t}\\n\\n\\t.page-changer {\\n\\t\\tpadding: 0;\\n\\t\\theight: 1.1em;\\n\\t\\twidth: 1.1em;\\n\\t}\\n\\n\\t.pagination {\\n\\t\\tfont-size: 12px;\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: flex-end;\\n\\t\\theight: 2em;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-moz-user-select: none;\\n\\t\\tuser-select: none;\\n\\t\\ttext-align: right;\\n\\t\\tmargin-top: 0.5em;\\n\\t\\tmargin-bottom: 0;\\n\\t\\tfont-variant-numeric: tabular-nums;\\n\\t}\\n\\n\\t.page-labels {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-start;\\n\\t\\talign-items: center;\\n\\t\\tgap: 3px;\\n\\t}\\n\\n\\t.page-changer {\\n\\t\\tfont-size: 20px;\\n\\t\\tbackground: none;\\n\\t\\tborder: none;\\n\\t\\tcursor: pointer;\\n\\t\\ttransition: color 200ms;\\n\\t}\\n\\n\\t.page-changer.hovering {\\n\\t\\tcolor: var(--color-primary);\\n\\t\\ttransition: color 200ms;\\n\\t}\\n\\n\\t.page-changer:disabled {\\n\\t\\tcursor: auto;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-moz-user-select: none;\\n\\t\\tuser-select: none;\\n\\t\\ttransition: color 200ms;\\n\\t}\\n\\n\\t.page-icon {\\n\\t\\theight: 1em;\\n\\t\\twidth: 1em;\\n\\t}\\n\\n\\t.page-input {\\n\\t\\tbox-sizing: content-box;\\n\\t\\ttext-align: center;\\n\\t\\tpadding: 0.25em 0.5em;\\n\\t\\tmargin: 0;\\n\\t\\tborder: 1px solid transparent;\\n\\t\\tborder-radius: 4px;\\n\\t\\tfont-size: 12px;\\n\\t}\\n\\n\\t.table-footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\talign-items: center;\\n\\t\\tfont-size: 12px;\\n\\t\\theight: 9px;\\n\\t}\\n\\n\\t/* Remove number buttons in input box*/\\n\\t.page-input::-webkit-outer-spin-button,\\n\\t.page-input::-webkit-inner-spin-button {\\n\\t\\t-webkit-appearance: none;\\n\\t\\tmargin: 0;\\n\\t}\\n\\n\\t/* Firefox */\\n\\t.page-input[type='number'] {\\n\\t\\t-moz-appearance: textfield;\\n\\t\\t-webkit-appearance: textfield;\\n\\t\\tappearance: textfield;\\n\\t}\\n\\n\\t.page-input.hovering {\\n\\t\\tborder: 1px solid var(--base-300);\\n\\t}\\n\\n\\t.page-input.error {\\n\\t\\tborder: 1px solid var(--negative);\\n\\t}\\n\\n\\t.page-input::-moz-placeholder {\\n\\t\\tcolor: var(--base-content-muted);\\n\\t}\\n\\n\\t.page-input::placeholder {\\n\\t\\tcolor: var(--base-content-muted);\\n\\t}\\n\\n\\tbutton:enabled > .page-icon:hover {\\n\\t\\tfilter: brightness(0.8);\\n\\t}\\n\\n\\t*:focus {\\n\\t\\toutline: none;\\n\\t}\\n\\n\\t::-moz-placeholder {\\n\\t\\t/* Chrome, Firefox, Opera, Safari 10.1+ */\\n\\t\\tcolor: var(--base-content-muted);\\n\\t\\topacity: 1; /* Firefox */\\n\\t}\\n\\n\\t::placeholder {\\n\\t\\t/* Chrome, Firefox, Opera, Safari 10.1+ */\\n\\t\\tcolor: var(--base-content-muted);\\n\\t\\topacity: 1; /* Firefox */\\n\\t}\\n\\n\\t:-ms-input-placeholder {\\n\\t\\t/* Internet Explorer 10-11 */\\n\\t\\tcolor: var(--base-content-muted);\\n\\t}\\n\\n\\t::-ms-input-placeholder {\\n\\t\\t/* Microsoft Edge */\\n\\t\\tcolor: var(--base-content-muted);\\n\\t}\\n\\n\\t.noresults {\\n\\t\\tdisplay: none;\\n\\t\\tcolor: var(--base-content-muted);\\n\\t\\ttext-align: center;\\n\\t\\tmargin-top: 5px;\\n\\t}\\n\\n\\t.shownoresults {\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.print-page-count {\\n\\t\\tdisplay: none;\\n\\t}\\n\\n\\t@media (max-width: 600px) {\\n\\t\\t.page-changer {\\n\\t\\t\\theight: 1.2em;\\n\\t\\t\\twidth: 1.2em;\\n\\t\\t}\\n\\n\\t\\t.page-icon {\\n\\t\\t\\theight: 1.2em;\\n\\t\\t\\twidth: 1.2em;\\n\\t\\t}\\n\\n\\t\\t.page-count {\\n\\t\\t\\tfont-size: 1.1em;\\n\\t\\t}\\n\\n\\t\\t.page-input {\\n\\t\\t\\tfont-size: 1.1em;\\n\\t\\t}\\n\\t}\\n\\n\\t@media print {\\n\\t\\t.pagination {\\n\\t\\t\\t-moz-column-break-inside: avoid;\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\n\\t\\t.page-changer {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\n\\t\\t.page-count {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\n\\t\\t.print-page-count {\\n\\t\\t\\tdisplay: inline;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4xBC,4CAAiB,CAChB,SAAS,CAAE,KACZ,CAEA,sCAAW,CACV,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,eAAe,CAAE,IAClB,CAEA,iCAAM,CACL,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,QAAQ,CACzB,oBAAoB,CAAE,YACvB,CAEA,yCAAc,CACb,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,KAAK,CACb,KAAK,CAAE,KACR,CAEA,uCAAY,CACX,SAAS,CAAE,IAAI,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,QAAQ,CACzB,MAAM,CAAE,GAAG,CACX,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IAAI,CACtB,WAAW,CAAE,IAAI,CACjB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,CAAC,CAChB,oBAAoB,CAAE,YACvB,CAEA,wCAAa,CACZ,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,UAAU,CAC3B,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,GACN,CAEA,yCAAc,CACb,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,KAAK,CAAC,KACnB,CAEA,aAAa,qCAAU,CACtB,KAAK,CAAE,IAAI,eAAe,CAAC,CAC3B,UAAU,CAAE,KAAK,CAAC,KACnB,CAEA,yCAAa,SAAU,CACtB,MAAM,CAAE,IAAI,CACZ,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IAAI,CACtB,WAAW,CAAE,IAAI,CACjB,UAAU,CAAE,KAAK,CAAC,KACnB,CAEA,sCAAW,CACV,MAAM,CAAE,GAAG,CACX,KAAK,CAAE,GACR,CAEA,uCAAY,CACX,UAAU,CAAE,WAAW,CACvB,UAAU,CAAE,MAAM,CAClB,OAAO,CAAE,MAAM,CAAC,KAAK,CACrB,MAAM,CAAE,CAAC,CACT,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CAC7B,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,IACZ,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,MAAM,CACnB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,GACT,CAGA,uCAAW,2BAA2B,CACtC,uCAAW,2BAA4B,CACtC,kBAAkB,CAAE,IAAI,CACxB,MAAM,CAAE,CACT,CAGA,WAAW,CAAC,IAAI,CAAC,QAAQ,6BAAE,CAC1B,eAAe,CAAE,SAAS,CAC1B,kBAAkB,CAAE,SAAS,CAC7B,UAAU,CAAE,SACb,CAEA,WAAW,qCAAU,CACpB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CACjC,CAEA,WAAW,kCAAO,CACjB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CACjC,CAEA,uCAAW,kBAAmB,CAC7B,KAAK,CAAE,IAAI,oBAAoB,CAChC,CAEA,uCAAW,aAAc,CACxB,KAAK,CAAE,IAAI,oBAAoB,CAChC,CAEA,oBAAM,QAAQ,CAAG,wBAAU,MAAO,CACjC,MAAM,CAAE,WAAW,GAAG,CACvB,CAEA,4BAAC,MAAO,CACP,OAAO,CAAE,IACV,6BAEA,kBAAmB,CAElB,KAAK,CAAE,IAAI,oBAAoB,CAAC,CAChC,OAAO,CAAE,CACV,6BAEA,aAAc,CAEb,KAAK,CAAE,IAAI,oBAAoB,CAAC,CAChC,OAAO,CAAE,CACV,6BAEA,sBAAuB,CAEtB,KAAK,CAAE,IAAI,oBAAoB,CAChC,6BAEA,uBAAwB,CAEvB,KAAK,CAAE,IAAI,oBAAoB,CAChC,CAEA,sCAAW,CACV,OAAO,CAAE,IAAI,CACb,KAAK,CAAE,IAAI,oBAAoB,CAAC,CAChC,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,GACb,CAEA,0CAAe,CACd,OAAO,CAAE,KACV,CAEA,6CAAkB,CACjB,OAAO,CAAE,IACV,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,yCAAc,CACb,MAAM,CAAE,KAAK,CACb,KAAK,CAAE,KACR,CAEA,sCAAW,CACV,MAAM,CAAE,KAAK,CACb,KAAK,CAAE,KACR,CAEA,uCAAY,CACX,SAAS,CAAE,KACZ,CAEA,uCAAY,CACX,SAAS,CAAE,KACZ,CACD,CAEA,OAAO,KAAM,CACZ,uCAAY,CACX,wBAAwB,CAAE,KAAK,CAC/B,YAAY,CAAE,KACf,CAEA,yCAAc,CACb,OAAO,CAAE,IACV,CAEA,uCAAY,CACX,OAAO,CAAE,IACV,CAEA,6CAAkB,CACjB,OAAO,CAAE,MACV,CACD"}`};function eo(o,t){return o.map(e=>{const g={};for(const a of t)g[a]=e[a];return g})}const ze=F((o,t,e,g)=>{let a,r,i,c,s,m,A,h,d,l,v,p,B,b,x,k,D,w,R=$,n=()=>(R(),R=z(h,u=>w=u),h),L,T=$,W=()=>(T(),T=z(m,u=>L=u),m),y,I=$,Y=()=>(I(),I=z(A,u=>y=u),A),ct,H=$,yt=()=>(H(),H=z(a,u=>ct=u),a),P,G=$,nt=()=>(G(),G=z(r,u=>P=u),r),j,Lt=$,ne=()=>(Lt(),Lt=z(i,u=>j=u),i),Xt,vt=$,oe=()=>(vt(),vt=z(c,u=>Xt=u),c),Zt,Pt=$,ae=()=>(Pt(),Pt=z(s,u=>Zt=u),s);const{resolveColor:ot}=ee();let Mt=Sn({});D=z(Mt,u=>k=u),an(qe,Mt);let{data:E}=t,{queryID:ht=void 0}=t,{rows:Q=10}=t,{title:Ot=void 0}=t,{subtitle:xt=void 0}=t,{rowNumbers:X=!1}=t,{sort:St=void 0}=t,at,Dt,pt,_={},{groupBy:V=void 0}=t,{groupsOpen:ut=!0}=t,{groupType:tt="accordion"}=t,{accordionRowColor:Ut=void 0}=t,{groupNamePosition:Kt="middle"}=t;tt==="section"&&(X=!1);let{subtotals:gt=!1}=t,{subtotalRowColor:zt=void 0}=t,{subtotalFontColor:kt=void 0}=t,_t={},Gt,Nt=!1,{generateMarkdown:lt=!1}=t,{search:At=!1}=t,{sortable:Et=!0}=t,{downloadable:rt=!0}=t,{totalRow:Wt=!1}=t,{totalRowColor:M=void 0}=t,{totalFontColor:Bt=void 0}=t,{isFullPage:Vt=!1}=t,{link:wt=void 0}=t,{showLinkCol:le=!1}=t,Jt,ve=!1;Mt.update(u=>({...u,data:E,columns:[]}));let{rowShading:Qt=!1}=t,{rowLines:qt=!0}=t,{wrapTitles:re=!1}=t,{headerColor:he=void 0}=t,{headerFontColor:pe=void 0}=t,{formatColumnTitles:ie=!0}=t,{backgroundColor:ge=void 0}=t,{compact:Tt=void 0}=t,Z,tn=[V];Mt.update(u=>({...u,priorityColumns:tn}));let bt=0,Ie=null,Ft="",et,Ae,J={},je=[],we,be,se=et,Rt,mt=1,Me,Oe=0;t.data===void 0&&e.data&&E!==void 0&&e.data(E),t.queryID===void 0&&e.queryID&&ht!==void 0&&e.queryID(ht),t.rows===void 0&&e.rows&&Q!==void 0&&e.rows(Q),t.title===void 0&&e.title&&Ot!==void 0&&e.title(Ot),t.subtitle===void 0&&e.subtitle&&xt!==void 0&&e.subtitle(xt),t.rowNumbers===void 0&&e.rowNumbers&&X!==void 0&&e.rowNumbers(X),t.sort===void 0&&e.sort&&St!==void 0&&e.sort(St),t.groupBy===void 0&&e.groupBy&&V!==void 0&&e.groupBy(V),t.groupsOpen===void 0&&e.groupsOpen&&ut!==void 0&&e.groupsOpen(ut),t.groupType===void 0&&e.groupType&&tt!==void 0&&e.groupType(tt),t.accordionRowColor===void 0&&e.accordionRowColor&&Ut!==void 0&&e.accordionRowColor(Ut),t.groupNamePosition===void 0&&e.groupNamePosition&&Kt!==void 0&&e.groupNamePosition(Kt),t.subtotals===void 0&&e.subtotals&&gt!==void 0&&e.subtotals(gt),t.subtotalRowColor===void 0&&e.subtotalRowColor&&zt!==void 0&&e.subtotalRowColor(zt),t.subtotalFontColor===void 0&&e.subtotalFontColor&&kt!==void 0&&e.subtotalFontColor(kt),t.generateMarkdown===void 0&&e.generateMarkdown&&lt!==void 0&&e.generateMarkdown(lt),t.search===void 0&&e.search&&At!==void 0&&e.search(At),t.sortable===void 0&&e.sortable&&Et!==void 0&&e.sortable(Et),t.downloadable===void 0&&e.downloadable&&rt!==void 0&&e.downloadable(rt),t.totalRow===void 0&&e.totalRow&&Wt!==void 0&&e.totalRow(Wt),t.totalRowColor===void 0&&e.totalRowColor&&M!==void 0&&e.totalRowColor(M),t.totalFontColor===void 0&&e.totalFontColor&&Bt!==void 0&&e.totalFontColor(Bt),t.isFullPage===void 0&&e.isFullPage&&Vt!==void 0&&e.isFullPage(Vt),t.link===void 0&&e.link&&wt!==void 0&&e.link(wt),t.showLinkCol===void 0&&e.showLinkCol&&le!==void 0&&e.showLinkCol(le),t.rowShading===void 0&&e.rowShading&&Qt!==void 0&&e.rowShading(Qt),t.rowLines===void 0&&e.rowLines&&qt!==void 0&&e.rowLines(qt),t.wrapTitles===void 0&&e.wrapTitles&&re!==void 0&&e.wrapTitles(re),t.headerColor===void 0&&e.headerColor&&he!==void 0&&e.headerColor(he),t.headerFontColor===void 0&&e.headerFontColor&&pe!==void 0&&e.headerFontColor(pe),t.formatColumnTitles===void 0&&e.formatColumnTitles&&ie!==void 0&&e.formatColumnTitles(ie),t.backgroundColor===void 0&&e.backgroundColor&&ge!==void 0&&e.backgroundColor(ge),t.compact===void 0&&e.compact&&Tt!==void 0&&e.compact(Tt),o.css.add(to);let ye,De,en=o.head;do{if(ye=!0,o.head=en,Q=Number.parseInt(Q),X=K(X),St){const[u,S]=St.split(" ");at=u,pt=S,Dt=S!=="desc",_=at?{col:at,ascending:Dt}:{col:null,ascending:null}}ut=K(ut),yt(a=ot(Ut)),gt=K(gt),nt(r=ot(zt)),ne(i=ot(kt)),le=K(le);try{if(Jt=void 0,Ee(E),Z=Be(E,"array"),at){if(!Z.map(S=>S.id).includes(at))throw Error(`${at} is not a column in the dataset. sort should contain one column name and optionally a direction (asc or desc). E.g., sort=my_column or sort="my_column desc"`);if(pt&&!["asc","desc"].includes(pt))throw Error(`${pt} is not a valid sort direction. Please use asc or desc`)}const u=Z.filter(S=>S.type==="date"&&!(E[0]?.[S.id]instanceof Date)).map(S=>S.id);for(let S=0;S<u.length;S++)E=bn(E,u[S]);if(wt){const S=Z.findIndex(N=>N.id===wt);S!==-1&&!le&&Z.splice(S,1)}}catch(u){if(Jt=u.message,ue)throw Jt}if(Gt=E.length>Q&&!V,lt=lt==="true"||lt===!0,At=K(At),Et=K(Et),rt=K(rt),Wt=K(Wt),oe(c=ot(M)),ae(s=ot(Bt)),Qt=K(Qt),qt=K(qt),re=K(re),W(m=ot(he)),Y(A=ot(pe)),ie=K(ie),n(h=ot(ge)),d=Dn(k.columns.map(u=>u.id),k.priorityColumns),l=[...k.columns].sort((u,S)=>d.indexOf(u.id)-d.indexOf(S.id)),et=E,fe.isQuery(E)&&At&&(Ae=ln(fe.createReactive({loadGracePeriod:1e3,callback:u=>{et=u},execFn:sn},E.opts,E))),p=u=>{const S=u.col,N=u.ascending?1:-1,U=it=>it==null||typeof it=="number"&&isNaN(it),$t=(it,te)=>{const Yt=it[S],Ht=te[S];if(U(Yt)&&!U(Ht))return-1*N;if(U(Ht)&&!U(Yt))return 1*N;const me=typeof Yt=="string"?Yt.toLowerCase():Yt,_e=typeof Ht=="string"?Ht.toLowerCase():Ht;return me<_e?-1*N:me>_e?1*N:0};if(V){const it={};for(const te of Object.keys(J))it[te]=[...J[te]].sort($t);J=it}else et=[...et].sort($t)},b=u=>{bt=u*Q,Me=bt+Q,mt=Math.ceil(Me/Q),be=et.length,se=et.slice(bt,bt+Q)},Ae&&Ae(Ft?E.search(Ft,k.columns.map(u=>u.id),Ft.length===1?.5:Ft.length>=6?.9:.8):E,E.opts),At&&!fe.isQuery(E)&&rn.add({status:"warning",title:"Search Failed",message:"Please use a query instead."},5e3),E&&(ve=!1),!Jt){V&&!ve&&(J=E.reduce((S,N)=>{const U=N[V];return S[U]||(S[U]=[]),S[U].push(N),S},{}),ve=!0),je=Object.keys(J).reduce((S,N)=>{S[N]={};for(const U of k.columns){const $t=U.id,it=Z.find(me=>me.id===$t)?.type,te=U.totalAgg,Yt=U.weightCol,Ht=J[N];S[N][$t]=ce(Ht,$t,te,it,Yt)}return S},{});const u=Object.keys(_t);for(const S of Object.keys(J))u.includes(S)||(_t[S]=ut)}v=u=>{_.col===u?_.ascending=!_.ascending:(_.col=u,_.ascending=!0),p(_)},V&&_.col?we=Object.entries(je).sort((u,S)=>{const N=u[1][_.col],U=S[1][_.col];return _.col===V&&isNaN(V)?_.ascending?u[0].localeCompare(S[0]):S[0].localeCompare(u[0]):(N==null||isNaN(N))&&U!==void 0&&!isNaN(U)?-1*(_.ascending?1:-1):(U==null||isNaN(U))&&N!==void 0&&!isNaN(N)?1*(_.ascending?1:-1):N<U?-1*(_.ascending?1:-1):N>U?1*(_.ascending?1:-1):0}).map(u=>u[0]):we=Object.keys(J).sort(),E&&St&&p(_),be=et.length,mt=Math.ceil((bt+Q)/Q),Gt?(Rt=Math.ceil(et.length/Q),se=et.slice(bt,bt+Q),Oe=se.length,Rt<mt?b(Rt-1):mt<1&&b(0)):(mt=1,se=et),B=`${(mt??1).toString().length}ch`,x=eo(E,k.columns.map(u=>u.id)),De=`  ${Jt===void 0?`${g.default?g.default({}):`  ${st(Z,u=>`${f($n,"Column").$$render(o,{id:u.id},{},{})}`)} `} ${wt?`${f(Le,"InvisibleLinks").$$render(o,{data:E,link:wt},{},{})}`:""} ${st(k.columns.filter(u=>u.contentType==="link"),u=>`${f(Le,"InvisibleLinks").$$render(o,{data:E,link:u.id},{},{})}`)} <div${O("data-testid",Vt?void 0:`DataTable-${E?.id??"no-id"}`,0)} role="none" class="${"table-container mt-2 "+C(Gt?"mb-5":"mb-2",!0)+" svelte-xwrh5j"}">${Ot||xt?`${f(jn,"ComponentTitle").$$render(o,{title:Ot,subtitle:xt},{},{})}`:""} ${At?`${f(On,"SearchBar").$$render(o,{searchFunction:()=>{},value:Ft},{value:u=>{Ft=u,ye=!1}},{})}`:""} <div class="scrollbox pretty-scrollbar svelte-xwrh5j"${ft({"background-color":w})}><table class="svelte-xwrh5j">${f(Vn,"TableHeader").$$render(o,{rowNumbers:X,headerColor:L,headerFontColor:y,orderedColumns:l,columnSummary:Z,compact:Tt,sortable:Et,sortClick:v,formatColumnTitles:ie,sortObj:_,wrapTitles:re,link:wt},{},{})} ${f(Te,"QueryLoad").$$render(o,{data:et},{},{skeleton:()=>`<tr class="svelte-xwrh5j"><td${O("colspan",et.columns.length,0)} class="h-32 svelte-xwrh5j">${f(yn,"Skeleton").$$render(o,{},{},{})}</td></tr> `,default:()=>`${V&&J&&Ft===""?`${st(we,u=>`${tt==="accordion"?`${f(Hn,"GroupRow").$$render(o,{groupName:u,currentGroupData:J[u],toggled:_t[u],columnSummary:Z,rowColor:ct,rowNumbers:X,subtotals:gt,compact:Tt,orderedColumns:l},{},{})} ${_t[u]?`${f(Se,"TableRow").$$render(o,{displayedData:J[u],groupType:tt,rowShading:Qt,link:wt,rowNumbers:X,rowLines:qt,compact:Tt,index:bt,columnSummary:Z,grouped:!0,groupColumn:V,orderedColumns:l},{},{})}`:""}`:`${tt==="section"?`${f(Se,"TableRow").$$render(o,{groupColumn:V,groupType:tt,rowSpan:J[u].length,displayedData:J[u],rowShading:Qt,link:wt,rowNumbers:X,rowLines:qt,compact:Tt,index:bt,columnSummary:Z,grouped:!0,groupNamePosition:Kt,orderedColumns:l},{},{})} ${gt?`${f(zn,"SubtotalRow").$$render(o,{groupName:u,currentGroupData:J[u],columnSummary:Z,rowColor:P,fontColor:j,groupType:tt,groupBy:V,compact:Tt,orderedColumns:l},{},{})}`:""}`:""}`}`)}`:`${f(Se,"TableRow").$$render(o,{displayedData:se,rowShading:Qt,link:wt,rowNumbers:X,rowLines:qt,compact:Tt,index:bt,columnSummary:Z,orderedColumns:l},{},{})}`} ${Wt&&Ft===""?`${f(Kn,"TotalRow").$$render(o,{data:E,rowNumbers:X,columnSummary:Z,rowColor:Xt,fontColor:Zt,groupType:tt,compact:Tt,orderedColumns:l},{},{})}`:""}`})}</table></div> <div class="${["noresults svelte-xwrh5j",""].join(" ").trim()}" data-svelte-h="svelte-l3t0gi">No Results</div> ${Gt&&Rt>1?`<div class="pagination text-base-content-muted svelte-xwrh5j"><div class="page-labels mr-auto svelte-xwrh5j"><button aria-label="first-page" class="${["page-changer disabled:text-base-content-muted/25 svelte-xwrh5j",""].join(" ").trim()}" ${mt===1?"disabled":""}><div class="page-icon flex items-center svelte-xwrh5j">${f(Ct,"Icon").$$render(o,{src:Cn},{},{})}</div></button> <button aria-label="previous-page" class="${["page-changer disabled:text-base-content-muted/25 svelte-xwrh5j",""].join(" ").trim()}" ${mt===1?"disabled":""}><div class="page-icon h-[0.83em] flex items-center svelte-xwrh5j">${f(Ct,"Icon").$$render(o,{src:vn,class:"h-[0.83em]"},{},{})}</div></button> <span class="page-count svelte-xwrh5j">Page
						<input class="${["page-input bg-base-200 text-base-content-muted svelte-xwrh5j"," "+(Ie>Rt?"error":"")].join(" ").trim()}" style="${"width: "+C(B,!0)+";"}" type="number"${O("placeholder",mt,0)}${O("value",Ie,0)}>
						/
						<span class="page-count ml-1 svelte-xwrh5j">${C(Rt.toLocaleString())}</span></span> <span class="print-page-count svelte-xwrh5j">${C(Oe.toLocaleString())} of ${C(be.toLocaleString())} records</span> <button aria-label="next-page" class="${["page-changer disabled:text-base-content-muted/25 svelte-xwrh5j",""].join(" ").trim()}" ${mt===Rt?"disabled":""}><div class="page-icon h-[0.83em] flex items-center svelte-xwrh5j">${f(Ct,"Icon").$$render(o,{src:Ye,class:"h-[0.83em]"},{},{})}</div></button> <button aria-label="last-page" class="${["page-changer disabled:text-base-content-muted/25 svelte-xwrh5j",""].join(" ").trim()}" ${mt===Rt?"disabled":""}><div class="page-icon flex items-center svelte-xwrh5j">${f(Ct,"Icon").$$render(o,{src:hn},{},{})}</div></button></div> ${rt?`${f(Ne,"DownloadData").$$render(o,{class:"download-button",data:x,queryID:ht,display:Nt},{},{})}`:""} ${Vt?"":`${f(Ke,"EnterFullScreen").$$render(o,{display:Nt},{},{})}`}</div>`:`<div class="table-footer mt-3 svelte-xwrh5j">${rt?`${f(Ne,"DownloadData").$$render(o,{class:"download-button",data:x,queryID:ht,display:Nt},{},{})}`:""} ${Vt?"":`${f(Ke,"EnterFullScreen").$$render(o,{display:Nt},{},{})}`}</div>`}</div> ${lt?`${ht?`${f(xn,"CodeBlock").$$render(o,{},{},{default:()=>`${C(`<DataTable data={${ht}}>`)} <br class="svelte-xwrh5j"> ${st(Object.keys(E[0]),u=>`${C(`   <Column id=${u.includes(" ")?`'${u}'`:u}/>`)} <br class="svelte-xwrh5j">`)} ${C("</DataTable>")}`})}`:""}`:""}`:`${f(Je,"ErrorChart").$$render(o,{error:Jt,title:"Data Table"},{},{})}`}`}while(!ye);return D(),R(),T(),I(),H(),G(),Lt(),vt(),Pt(),De}),{Object:We}=Xe;let Ve="Data Table";const no=F((o,t,e,g)=>{let a,r=un(g),{data:i}=t;const c=fe.isQuery(i)?i.hash:void 0;let s=i?.hash===c,{emptySet:m=void 0}=t,{emptyMessage:A=void 0}=t,h=i?.id;return t.data===void 0&&e.data&&i!==void 0&&e.data(i),t.emptySet===void 0&&e.emptySet&&m!==void 0&&e.emptySet(m),t.emptyMessage===void 0&&e.emptyMessage&&A!==void 0&&e.emptyMessage(A),s=i?.hash===c,a=Object.fromEntries(Object.entries(t).filter(([,d])=>d!==void 0)),` ${f(Te,"QueryLoad").$$render(o,{data:i},{},{error:({loaded:d})=>`${f(Je,"ErrorChart").$$render(o,{slot:"error",title:Ve,error:d.error.message},{},{})}`,empty:()=>`${f(Ze,"EmptyChart").$$render(o,{slot:"empty",emptyMessage:A,emptySet:m,chartType:Ve,isInitial:s},{},{})}`,default:({loaded:d})=>` ${r.default?`${f(ze,"DataTable").$$render(o,We.assign({},a,{data:d},{queryID:h}),{},{default:()=>`${g.default?g.default({}):""}`})}`:`${f(ze,"DataTable").$$render(o,We.assign({},a,{data:d},{queryID:h}),{},{})}`}`})}`}),oo=F((o,t,e,g)=>{let a,r,i=$,c=()=>(i(),i=z(l,B=>r=B),l),{hideErrors:s=!1}=t,{initialQuery:m="select 'ABC' as category, 123 as num, 26400000 as sales_usd"}=t,{showResults:A=!0}=t,{disabled:h=!1}=t,d=m,{data:l=Fe(d)}=t;c();let v;const{theme:p}=ee();return a=z(p,B=>B),t.hideErrors===void 0&&e.hideErrors&&s!==void 0&&e.hideErrors(s),t.initialQuery===void 0&&e.initialQuery&&m!==void 0&&e.initialQuery(m),t.showResults===void 0&&e.showResults&&A!==void 0&&e.showResults(A),t.disabled===void 0&&e.disabled&&h!==void 0&&e.disabled(h),t.data===void 0&&e.data&&l!==void 0&&e.data(l),d&&(c(l=Fe(d)),l.fetch()),a(),i(),`<h1 class="markdown" data-svelte-h="svelte-7ylf69">SQL Console</h1> <section class="px-0 py-2 flex flex-col gap-2 min-h-[8rem]" role="none"><div class="w-full relative rounded-sm border border-base-300 min-h-[8rem] cursor-text **:[&amp;.cm-editor]:min-h-[8rem] **:[&amp;.cm-editor]:rounded-sm"${O("this",v,0)}>${h?"":`<div class="absolute bottom-2 right-2 z-10 flex gap-2">${f(Re,"Button").$$render(o,{size:"sm",outline:!0,icon:A?pn:gn},{},{default:()=>`${A?"Hide Results":"Show Results"}`})} ${f(Re,"Button").$$render(o,{size:"sm",variant:"positive",outline:!0,icon:wn},{},{default:()=>"Submit"})}</div>`}</div> ${r.error&&!s&&d?`<pre class="text-negative text-xs font-mono">${C(r.error)}</pre>`:""}  ${A?`<div>${f(no,"DataTable").$$render(o,{data:r},{},{})}</div>`:""}</section>`}),No=F((o,t,e,g)=>`${f(oo,"SqlConsole").$$render(o,{},{},{})}`);export{No as default};
