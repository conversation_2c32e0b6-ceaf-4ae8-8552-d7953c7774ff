import{a as U,d as R}from"./utils.js";import{c,b as C,o as j,v as m,h as u,s as q,g as G}from"./ssr.js";import{c as F,t as L}from"./index7.js";import{Q as p,r as z}from"./Query.js";import{Query as K,sql as A}from"@uwdata/mosaic-sql";import{query as N}from"@evidence-dev/universal-sql/client-duckdb";import{w as H,d as W}from"./index2.js";import{T as D,U as Y,W as Z,X,Y as J,Z as P,O as $,_ as ee,$ as te,a0 as re}from"./VennDiagram.svelte_svelte_type_style_lang.js";import{tidy as v,mutate as O,summarize as x,sum as ne,mean as oe,median as ae,max as se,min as ie,nDistinct as w,n as E}from"@tidyjs/tidy";import le from"ssf";import{ExportToCsv as de}from"export-to-csv";import{I as ue}from"./Info.js";const Le=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global,ce=c((t,e,r,o)=>{let{class:n=void 0}=e;return e.class===void 0&&r.class&&n!==void 0&&r.class(n),`<div${C("class",F("animate-pulse h-full w-full mt-2 mb-4",n),0)}><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="h-full w-full rounded-md bg-base-300"></div></div>`});function Ae(t){return!t||!t[0]||!t.length}const ze=c((t,e,r,o)=>{let n=U(o),{data:a}=e,{height:s=200}=e,{skeletonClass:i=void 0}=e,d=()=>{},l;return j(d),e.data===void 0&&r.data&&a!==void 0&&r.data(a),e.height===void 0&&r.height&&s!==void 0&&r.height(s),e.skeletonClass===void 0&&r.skeletonClass&&i!==void 0&&r.skeletonClass(i),p.isQuery(a)&&(a.fetch(),d(),d=a.subscribe(f=>{l=f})),`${a?`${p.isQuery(a)?`${!l||!l.dataLoaded&&!l.error?`${o.skeleton?o.skeleton({loaded:l}):` <div class="w-full" style="${"height: "+u(s,!0)+"px"}">${m(ce,"Skeleton").$$render(t,{class:i},{},{})}</div> `}`:`${l.error&&n.error?`${o.error?o.error({loaded:l}):""}`:`${!l.length&&!l.error&&n.empty?`${o.empty?o.empty({loaded:l}):""}`:`${o.default?o.default({loaded:l}):""}`}`}`}`:` ${(Array.isArray(a)||!a)&&Ae(a)&&n.empty?` ${o.empty?o.empty({loaded:a}):""}`:` ${o.default?o.default({loaded:a}):""}`}`}`:` ${o.default?o.default({loaded:a}):""}`}`}),fe="___usql_query";let B=N;const Ke=t=>{q(fe,t),B=t},Ne=(t,e,r)=>{const o=H(T(t,e,r));let n;return{results:W(o,a=>a),update:async a=>{const{hasQuery:s,query:i}=T(a,e);s?z(()=>{i.hash!==n?.hash&&(n=i,o.set({hasQuery:s,query:i}))},i.fetch()):o.set({hasQuery:!1})}}},T=({value:t,label:e,select:r,data:o,where:n,order:a},s,i)=>{if(!o||!(t||r))return{hasQuery:!1};let d=!1;const l=new K().distinct();if(t&&l.select({value:A`${t}`}),e?l.select({label:A`${e}`}):l.select({label:A`${t}`}),r&&l.select(r),typeof o=="string")o.trim().match(/^[\w]+$/)?l.from(o.trim()):l.from(A(o.trim()));else if(p.isQuery(o))l.from(A`(${o.text})`),d=o.opts.noResolve??!1;else return{hasQuery:!1};n&&l.where(n),a&&(l.orderby(A`${a}`),l.select({ordinal:A`row_number() over (ORDER BY ${a})`}));const f=me(l.toString(),s,i,{noResolve:d});return f.fetch(),{hasQuery:!0,query:f}},me=(t,e,r,o)=>p.create(t,B,e,{...o,initialData:r}),pe="customFormattingSettings";function g(t){if(t&&typeof t=="string"){let e=t.split(" ");t.includes(":")||(t=t+"T00:00:00"),e.length>2&&(t=e[0]+" "+e[1]);const r=/\.([^\s]+)/;t=t.replace(r,""),t=t.replace("Z",""),t=t.replace(" ","T")}return t}function He(t,e){return t=v(t,O({[e]:r=>r[e]?new Date(g(r[e])):null})),t}function We(t,e){return t=v(t,O({[e]:r=>g(r[e])})),t}const S="axis",y="value",k=()=>{try{return G(pe)?.getCustomFormats()||[]}catch{return[]}},ye=(t,e,r)=>{let o=be(t);if(e.evidenceType==="string")return;if(o){let a=k(),s=[...D,...a].find(i=>i.formatTag?.toLowerCase()===o?.toLowerCase?.());if(s)return s}let n=Y(t,e,r);if(n)return n};function Ce(t,e=void 0){let r=t,o=k(),n=[...D,...o].find(s=>s.formatTag?.toLowerCase()===r?.toLowerCase?.()),a={};return n||(a={formatTag:"custom",formatCode:r},e&&(a.valueType=e),a)}const ve=(t,e=void 0,r=void 0)=>{try{return M(t,e,r,y)}catch(o){return console.warn(`Unexpected error calling applyFormatting(${t}, ${e}, ${y}, ${r}). Error=${o}`),t}},Ye=(t,e=void 0,r=void 0)=>{try{return M(t,e,r,S)}catch{}return t},ge=(t,e)=>{let r=t;if(t&&e?.formatTag){let o=t.toLowerCase().lastIndexOf(`_${e.formatTag.toLowerCase()}`),n="";o>0&&(typeof e?.titleTagReplacement=="string"&&(n=e.titleTagReplacement),r=t.substring(0,o)+n)}return r};function M(t,e=void 0,r=void 0,o=y){if(t==null)return"-";let n;if(e)try{let a=he(e,o),s;try{e.valueType==="date"&&typeof t=="string"?s=new Date(g(t)):t instanceof Date?s=new Date(t.toISOString().slice(0,-1)):e.valueType==="number"&&typeof t!="number"&&!Number.isNaN(t)?s=Number(t):s=t}catch{s=t}if(X(e,a))try{n=J(s,e,r)}catch(i){console.warn(`Unexpected error applying auto formatting. Error=${i}`)}else n=le.format(a,s)}catch(a){console.warn(`Unexpected error applying formatting ${a}`)}return n===void 0&&(n=P(t)),n}function he(t,e=y){return typeof t=="string"?t:e===S&&t?.axisFormatCode?t.axisFormatCode:t?.formatCode}function be(t){let e=t.toLowerCase(),r=e.lastIndexOf("_");if(r>0)return e.substr(r).replace("_","")}function Ze(t,e){let r=Ce(e),o=Z(t);return r.valueType=o,ve(t,r)}function xe(t,e){let r=ge(t,e),o=["id","gdp"],n=["of","the","and","in","on"];function a(s){return s.replace(/\S*/g,function(i){return!o.includes(i)&&!n.includes(i)?i.charAt(0).toUpperCase()+i.substr(1).toLowerCase():o.includes(i)?i.toUpperCase():i.toLowerCase()})}return r=a(t.replace(/"/g,"").replace(/_/g," ")),r}const we=c((t,e,r,o)=>{let{error:n}=e,{title:a}=e,{height:s=200}=e;return e.error===void 0&&r.error&&n!==void 0&&r.error(n),e.title===void 0&&r.title&&a!==void 0&&r.title(a),e.height===void 0&&r.height&&s!==void 0&&r.height(s),`<div width="100%"${C("class",`grid grid-rows-auto grid-cols-1 justify-center relative
			bg-negative/10 text-negative
			font-ui font-normal
			rounded-sm border border-negative/50
			py-5 px-8 mt-2 mb-4
			print:break-inside-avoid`,0)} style="${"min-height: "+u(s,!0)+"px"}"><div class="m-auto w-full"><div class="font-bold text-center text-lg">${u(a)}</div> <div class="w-full [word-wrap:break-work] text-xs"><pre class="text-left font-sans mx-auto w-fit select-text text-wrap">${u(n)}</pre></div></div></div>`}),Ee=c((t,e,r,o)=>{let{error:n=void 0}=e;return e.error===void 0&&r.error&&n!==void 0&&r.error(n),`<span class="group inline-flex items-center relative cursor-help cursor-helpfont-sans px-1 border border-negative/50 py-[1px] bg-negative/10 rounded-sm"><span class="inline font-sans font-medium text-xs text-negative" data-svelte-h="svelte-1e4f3hi">error</span> <span class="hidden font-sans group-hover:inline absolute -top-1 left-[105%] text-sm z-10 px-2 py-1 bg-base-100 border border-base-300 leading-relaxed min-w-[150px] max-w-[400px] rounded-md">${u(n)}</span></span>`}),Te=c((t,e,r,o)=>{let{error:n}=e;return e.error===void 0&&r.error&&n!==void 0&&r.error(n),`<div width="100%" class="inline-block group w-[100px] relative cursor-help cursor-helpfont-sans box-content grid-cols-1 justify-center bg-negative/10 font-ui font-normal rounded-sm border border-negative/50 h-[38px] mt-0.5 py-3 px-3 print:break-inside-avoid"><div class="font-bold text-center text-sm text-negative" data-svelte-h="svelte-irqt29">Big Value</div> <div class="m-auto w-[100px]"><div class="text-center [word-wrap:break-work] w-full font-medium text-xs text-negative">error
			<span class="hidden font-sans group-hover:inline-block absolute top-[50%] left-[50%] text-sm px-2 py-1 bg-base-100 border border-base-300 leading-relaxed min-w-[150px] max-w-[400px] rounded-md z-50 overflow-visible">${u(n)}</span></div></div></div>`}),Xe=c((t,e,r,o)=>{let{isInitial:n=!0}=e,{emptySet:a="error"}=e,{emptyMessage:s="No Records"}=e,{chartType:i="Component"}=e,d="Dataset is empty - query ran successfully, but no data was returned from the database";if(i==="Big Value"&&(d="Dataset is empty"),a==="error"&&n){if(console.error("\x1B[31m%s\x1B[0m",`Error in ${i}: ${d}`),$)throw Error(d)}else a==="warn"&&n&&console.warn(`Warning in ${i}: Dataset is empty - query ran successfully, but no data was returned from the database`);return e.isInitial===void 0&&r.isInitial&&n!==void 0&&r.isInitial(n),e.emptySet===void 0&&r.emptySet&&a!==void 0&&r.emptySet(a),e.emptyMessage===void 0&&r.emptyMessage&&s!==void 0&&r.emptyMessage(s),e.chartType===void 0&&r.chartType&&i!==void 0&&r.chartType(i),`${["warn","pass"].includes(a)||!n?`${i==="Value"?`<span class="text-xs text-base-content-muted p-2 my-2 w-full border border-base-300 border-dashed rounded-sm">${u(s)}</span>`:`${i==="Big Value"?`<p class="text-xs text-base-content-muted p-2 pt-[32px] my-0 text-center w-full align-middle h-[80px] border border-base-300 border-dashed rounded-sm min-w-[120px]">${u(s)}</p>`:`<p class="text-xs text-base-content-muted p-2 my-2 w-full border border-base-300 border-dashed rounded-sm">${u(s)}</p>`}`}`:`${i==="Value"?`${m(Ee,"ValueError").$$render(t,{error:d},{},{})}`:`${i==="Big Value"?`${m(Te,"BigValueError").$$render(t,{error:d},{},{})}`:`${m(we,"ErrorChart").$$render(t,{title:i,error:d},{},{})}`}`}`}`});function Ie(t,e,r){let o=[];if(t===void 0)throw Error("No data provided");if(typeof t!="object")throw Error("'"+t+"' is not a recognized query result. Data should be provided in the format: data = {"+t.replace("data.","")+"}");if(t[0]===void 0||t.length===0)throw Error("Dataset is empty: query ran successfully, but no data was returned from the database");if(t[0]?.error_object?.error!=null)throw Error("SQL Error: "+t[0]?.error_object?.error?.message);if(e!=null){if(!(e instanceof Array))throw Error("reqCols must be passed in as an array");for(var n=0;n<e.length;n++){if(e[n]==null)throw Error(`Missing required column(s): ${e[n]} not found in data set.`);if(e[n]=="")throw Error("Missing required column(s): A Empty string was provided for one of your props.")}if(p.isQuery(t))if(!t.columnsLoaded&&t.dataLoaded){const s=Object.keys(t[0]);for(const i of s)o.push(i)}else for(const s of t.columns)o.push(s.column_name);else for(const s of Object.keys(t[0]))o.push(s);let a;for(n=0;n<e.length;n++)if(a=e[n],!o.includes(a))throw Error("'"+a+"' is not a column in the dataset");if(r!=null&&r.some(s=>s!=null)){for(n=0;n<r.length;n++)if(a=r[n],a!=null&&!o.includes(a))throw Error("'"+a+"' is not a column in the dataset")}}}function I(t,e,r=!0){const o=v(t,r?x({count:E(e),countDistinct:w(e),min:ie(e),max:se(e),median:ae(e),mean:oe(e),sum:ne(e)}):x({count:E(e),countDistinct:w(e)}))[0],{maxDecimals:n,unitType:a}=De(t.map(s=>s[e]));return{min:o.min,max:o.max,median:o.median,mean:o.mean,count:o.count,countDistinct:o.countDistinct,sum:o.sum,maxDecimals:n,unitType:a}}function De(t){if(t==null||t.length===0)return{maxDecimals:0,unitType:"unknown"};{let e=0;for(const r of t){const o=r?.toString().split(".")[1]?.length;o>e&&(e=o)}return{maxDecimals:e,unitType:"number"}}}function Je(t,e="object"){const r={},o=ee(t);for(const n of Object.keys(t[0])){const a=o.find(l=>l.name?.toLowerCase()===n?.toLowerCase())??{name:n,evidenceType:re.NUMBER,typeFidelity:te.INFERRED},s=a.evidenceType;let i=a.evidenceType==="number"?I(t,n,!0):I(t,n,!1);a.evidenceType!=="number"&&(i.maxDecimals=0,i.unitType=a.evidenceType);const d=ye(n,a,i);r[n]={title:xe(n,d),type:s,evidenceColumnType:a,format:d,columnUnitSummary:i}}return e!=="object"?Object.entries(r).map(([n,a])=>({id:n,...a})):r}const Oe={code:"button.svelte-1uc1g2y svg{stroke:var(--base-content);margin-top:auto;margin-bottom:auto;transition:stroke 200ms}button.svelte-1uc1g2y{display:flex;cursor:pointer;font-family:var(--ui-font-family);font-size:1em;color:var(--base-content);opacity:0.5;justify-items:flex-end;align-items:baseline;background-color:transparent;border:none;padding:0;margin:0 5px;gap:3px;transition:all 200ms;-moz-user-select:none;-webkit-user-select:none;-o-user-select:none;user-select:none}button.svelte-1uc1g2y:hover{opacity:1;color:var(--primary);transition:all 200ms}button.svelte-1uc1g2y:hover svg{stroke:var(--primary);transition:all 200ms}@media(max-width: 600px){button.svelte-1uc1g2y{display:none}}@media print{button.svelte-1uc1g2y{display:none}}",map:`{"version":3,"file":"DownloadData.svelte","sources":["DownloadData.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { ExportToCsv } from 'export-to-csv';\\n\\timport { fade } from 'svelte/transition';\\n\\timport checkInputs from '@evidence-dev/component-utilities/checkInputs';\\n\\timport InlineError from '../../atoms/inputs/InlineError.svelte';\\n\\timport { toBoolean } from '../../utils.js';\\n\\n\\texport let data = undefined;\\n\\texport let queryID = undefined;\\n\\texport let text = 'Download';\\n\\texport let display = true;\\n\\t$: display = toBoolean(display);\\n\\tlet errors = [];\\n\\n\\tconst date = new Date();\\n\\tconst localISOTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)\\n\\t\\t.toISOString()\\n\\t\\t.slice(0, 19)\\n\\t\\t.replaceAll(':', '-');\\n\\n\\texport let downloadData = (data) => {\\n\\t\\ttry {\\n\\t\\t\\tcheckInputs(data);\\n\\t\\t} catch (e) {\\n\\t\\t\\terrors = [...errors, e.message];\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tconst options = {\\n\\t\\t\\tfieldSeparator: ',',\\n\\t\\t\\tquoteStrings: '\\"',\\n\\t\\t\\tdecimalSeparator: '.',\\n\\t\\t\\tshowLabels: true,\\n\\t\\t\\tshowTitle: false,\\n\\t\\t\\tfilename: (queryID ?? 'evidence_download') + \` \${localISOTime}\`,\\n\\t\\t\\tuseTextFile: false,\\n\\t\\t\\tuseBom: true,\\n\\t\\t\\tuseKeysAsHeaders: true\\n\\t\\t};\\n\\n\\t\\tconst data_copy = JSON.parse(JSON.stringify(Array.from(data)));\\n\\n\\t\\tconst csvExporter = new ExportToCsv(options);\\n\\n\\t\\tcsvExporter.generateCsv(data_copy);\\n\\t};\\n<\/script>\\n\\n{#if errors.length > 0}\\n\\t<InlineError inputType=\\"DownloadData\\" height=\\"32\\" width=\\"160\\" error={errors} />\\n{:else if display}\\n\\t<div transition:fade|local={errors.length > 0 ? { duration: 0 } : { duration: 200 }}>\\n\\t\\t<button type=\\"button\\" aria-label={text} class={$$props.class} on:click={downloadData(data)}>\\n\\t\\t\\t<span>{text}</span>\\n\\t\\t\\t<slot>\\n\\t\\t\\t\\t<svg\\n\\t\\t\\t\\t\\twidth=\\"12\\"\\n\\t\\t\\t\\t\\theight=\\"12\\"\\n\\t\\t\\t\\t\\tviewBox=\\"0 0 24 24\\"\\n\\t\\t\\t\\t\\tfill=\\"none\\"\\n\\t\\t\\t\\t\\tstroke-width=\\"2\\"\\n\\t\\t\\t\\t\\tstroke-linecap=\\"round\\"\\n\\t\\t\\t\\t\\tstroke-linejoin=\\"round\\"\\n\\t\\t\\t\\t\\t><path d=\\"M3 15v4c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2v-4M17 9l-5 5-5-5M12 12.8V2.5\\" /></svg\\n\\t\\t\\t\\t>\\n\\t\\t\\t</slot>\\n\\t\\t</button>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\tbutton :global(svg) {\\n\\t\\tstroke: var(--base-content);\\n\\t\\tmargin-top: auto;\\n\\t\\tmargin-bottom: auto;\\n\\t\\ttransition: stroke 200ms;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tdisplay: flex;\\n\\t\\tcursor: pointer;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-size: 1em;\\n\\t\\tcolor: var(--base-content);\\n\\t\\topacity: 0.5;\\n\\t\\tjustify-items: flex-end;\\n\\t\\talign-items: baseline;\\n\\t\\tbackground-color: transparent;\\n\\t\\tborder: none;\\n\\t\\tpadding: 0;\\n\\t\\tmargin: 0 5px;\\n\\t\\tgap: 3px;\\n\\t\\ttransition: all 200ms;\\n\\t\\t-moz-user-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-o-user-select: none;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\tbutton:hover {\\n\\t\\topacity: 1;\\n\\t\\tcolor: var(--primary);\\n\\t\\ttransition: all 200ms;\\n\\t}\\n\\n\\tbutton:hover :global(svg) {\\n\\t\\tstroke: var(--primary);\\n\\t\\ttransition: all 200ms;\\n\\t}\\n\\n\\t@media (max-width: 600px) {\\n\\t\\tbutton {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n\\n\\t@media print {\\n\\t\\tbutton {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA0EC,qBAAM,CAAS,GAAK,CACnB,MAAM,CAAE,IAAI,cAAc,CAAC,CAC3B,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,MAAM,CAAC,KACpB,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,OAAO,CACf,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,SAAS,CAAE,GAAG,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,QAAQ,CACrB,gBAAgB,CAAE,WAAW,CAC7B,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CAAC,CAAC,GAAG,CACb,GAAG,CAAE,GAAG,CACR,UAAU,CAAE,GAAG,CAAC,KAAK,CACrB,gBAAgB,CAAE,IAAI,CACtB,mBAAmB,CAAE,IAAI,CACzB,cAAc,CAAE,IAAI,CACpB,WAAW,CAAE,IACd,CAEA,qBAAM,MAAO,CACZ,OAAO,CAAE,CAAC,CACV,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,qBAAM,MAAM,CAAS,GAAK,CACzB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qBAAO,CACN,OAAO,CAAE,IACV,CACD,CAEA,OAAO,KAAM,CACZ,qBAAO,CACN,OAAO,CAAE,IACV,CACD"}`},Pe=c((t,e,r,o)=>{let{data:n=void 0}=e,{queryID:a=void 0}=e,{text:s="Download"}=e,{display:i=!0}=e,d=[];const l=new Date,f=new Date(l.getTime()-l.getTimezoneOffset()*6e4).toISOString().slice(0,19).replaceAll(":","-");let{downloadData:h=b=>{try{Ie(b)}catch(Q){d=[...d,Q.message];return}const _={fieldSeparator:",",quoteStrings:'"',decimalSeparator:".",showLabels:!0,showTitle:!1,filename:(a??"evidence_download")+` ${f}`,useTextFile:!1,useBom:!0,useKeysAsHeaders:!0},V=JSON.parse(JSON.stringify(Array.from(b)));new de(_).generateCsv(V)}}=e;return e.data===void 0&&r.data&&n!==void 0&&r.data(n),e.queryID===void 0&&r.queryID&&a!==void 0&&r.queryID(a),e.text===void 0&&r.text&&s!==void 0&&r.text(s),e.display===void 0&&r.display&&i!==void 0&&r.display(i),e.downloadData===void 0&&r.downloadData&&h!==void 0&&r.downloadData(h),t.css.add(Oe),i=L(i),`${d.length>0?`${m(ue,"InlineError").$$render(t,{inputType:"DownloadData",height:"32",width:"160",error:d},{},{})}`:`${i?`<div><button type="button"${C("aria-label",s,0)} class="${u(R(e.class),!0)+" svelte-1uc1g2y"}"><span>${u(s)}</span> ${o.default?o.default({}):' <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 15v4c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2v-4M17 9l-5 5-5-5M12 12.8V2.5"></path></svg> '}</button></div>`:""}`}`}),$e=c((t,e,r,o)=>{let{source:n}=e,{copyToClipboard:a=!1}=e,{language:s=void 0}=e;return e.source===void 0&&r.source&&n!==void 0&&r.source(n),e.copyToClipboard===void 0&&r.copyToClipboard&&a!==void 0&&r.copyToClipboard(a),e.language===void 0&&r.language&&s!==void 0&&r.language(s),`<div class="mt-2 mb-4 bg-base-200 border border-base-300 rounded-md px-3 py-2 relative group">${a?'<button class="absolute opacity-0 rounded p-1 group-hover:opacity-100 hover:bg-base-300/30 top-2 right-2 h-6 w-6 z-10 transition-all duration-200 ease-in-out text-base-content-muted active:bg-base-300/50"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"><path d="M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"></path><path d="M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"></path></svg></button>':""} <pre class="overflow-auto pretty-scrollbar"><code class="${"language-"+u(s,!0)+" text-sm"}">${n?`${u(n)}`:`${o.default?o.default({}):""}`}</code></pre></div>`});export{$e as C,Pe as D,we as E,ze as Q,ce as S,Ee as V,ve as a,Ne as b,Ie as c,Ce as d,Ye as e,xe as f,Je as g,Le as h,Xe as i,Ze as j,pe as k,Ke as l,He as m,me as n,We as s};
