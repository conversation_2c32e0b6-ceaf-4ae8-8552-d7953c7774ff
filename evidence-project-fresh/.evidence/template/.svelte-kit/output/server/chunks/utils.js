function o(){}function c(n){return!!n&&(typeof n=="object"||typeof n=="function")&&typeof n.then=="function"}function r(n){return n()}function f(){return Object.create(null)}function i(n){n.forEach(r)}function _(n){return typeof n=="function"}function a(n,t){return n!=n?t==t:n!==t||n&&typeof n=="object"||typeof n=="function"}function s(n,...t){if(n==null){for(const e of t)e(void 0);return o}const u=n.subscribe(...t);return u.unsubscribe?()=>u.unsubscribe():u}function l(n){let t;return s(n,u=>t=u)(),t}function b(n,t){const u={};t=new Set(t);for(const e in n)!t.has(e)&&e[0]!=="$"&&(u[e]=n[e]);return u}function p(n){const t={};for(const u in n)t[u]=!0;return t}function y(n){return n??""}function h(n,t,u){return n.set(u),t}export{p as a,h as b,b as c,y as d,a as e,_ as f,l as g,f as h,c as i,o as n,i as r,s};
