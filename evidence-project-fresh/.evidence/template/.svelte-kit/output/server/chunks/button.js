import{n as O,t as z,Q as M,a as b,m as B,R as V}from"./VennDiagram.svelte_svelte_type_style_lang.js";import"dequal";import{s as W,t as j}from"./helpers.js";import{c as _}from"./utils.js";import{c as x,a as S,b as w,e as m,d,v as D}from"./ssr.js";import{c as R}from"./index7.js";const X=/^(?:area|base|br|col|command|embed|hr|img|input|keygen|link|meta|param|source|track|wbr)$/;function v(o){return X.test(o)||o.toLowerCase()==="!doctype"}const L=()=>typeof window<"u";function F(){return navigator.userAgentData?.platform??navigator.platform}const g=o=>L()&&o.test(F().toLowerCase()),I=()=>L()&&!!navigator.maxTouchPoints,N=()=>g(/^mac/)&&!I(),q=()=>g(/mac|iphone|ipad|ipod/i),H=()=>q()&&!N(),h="data-melt-scroll-lock";function P(o,t){if(!o)return;const e=o.style.cssText;return Object.assign(o.style,t),()=>{o.style.cssText=e}}function K(o,t,e){if(!o)return;const r=o.style.getPropertyValue(t);return o.style.setProperty(t,e),()=>{r?o.style.setProperty(t,r):o.style.removeProperty(t)}}function Q(o){const t=o.getBoundingClientRect().left;return Math.round(t)+o.scrollLeft?"paddingLeft":"paddingRight"}function rt(o){const t=document,e=t.defaultView??window,{documentElement:r,body:a}=t;if(a.hasAttribute(h))return O;a.setAttribute(h,"");const n=e.innerWidth-r.clientWidth,i=()=>K(r,"--scrollbar-width",`${n}px`),c=Q(r),u=e.getComputedStyle(a)[c],l=()=>P(a,{overflow:"hidden",[c]:`calc(${u} + ${n}px)`}),A=()=>{const{scrollX:f,scrollY:p,visualViewport:y}=e,k=y?.offsetLeft??0,C=y?.offsetTop??0,E=P(a,{position:"fixed",overflow:"hidden",top:`${-(p-Math.floor(C))}px`,left:`${-(f-Math.floor(k))}px`,right:"0",[c]:`calc(${u} + ${n}px)`});return()=>{E?.(),e.scrollTo(f,p)}},T=[i(),H()?A():l()];return()=>{T.forEach(f=>f?.()),a.removeAttribute(h)}}async function at(o){const{prop:t,defaultEl:e}=o;if(await Promise.all([W(1),z]),t===void 0){e?.focus();return}const r=M(t)?t(e):t;if(typeof r=="string"){const a=document.querySelector(r);if(!b(a))return;a.focus()}else b(r)&&r.focus()}const Y={orientation:"horizontal",decorative:!1},st=o=>{const t={...Y,...o},e=j(t),{orientation:r,decorative:a}=e;return{elements:{root:B("separator",{stores:[r,a],returned:([n,i])=>({role:i?"none":"separator","aria-orientation":n==="vertical"?n:void 0,"aria-hidden":i,"data-orientation":n})})},options:e}};function G(o){const t={};return o.forEach(e=>{Object.keys(e).forEach(r=>{r!=="action"&&(t[r]=e[r])})}),t}const J=x((o,t,e,r)=>{let a=_(t,["href","type","builders","el"]),{href:s=void 0}=t,{type:n=void 0}=t,{builders:i=[]}=t,{el:c=void 0}=t;const u={"data-button-root":""};return t.href===void 0&&e.href&&s!==void 0&&e.href(s),t.type===void 0&&e.type&&n!==void 0&&e.type(n),t.builders===void 0&&e.builders&&i!==void 0&&e.builders(i),t.el===void 0&&e.el&&c!==void 0&&e.el(c),`${i&&i.length?` ${(l=>l?`<${s?"a":"button"}${S([{type:m(s?void 0:n)},{href:m(s)},{tabindex:"0"},d(G(i)),d(a),d(u)],{})}${w("this",c,0)}>${v(l)?"":`${r.default?r.default({}):""}`}${v(l)?"":`</${l}>`}`:"")(s?"a":"button")}`:` ${(l=>l?`<${s?"a":"button"}${S([{type:m(s?void 0:n)},{href:m(s)},{tabindex:"0"},d(a),d(u)],{})}${w("this",c,0)}>${v(l)?"":`${r.default?r.default({}):""}`}${v(l)?"":`</${l}>`}`:"")(s?"a":"button")}`}`}),nt=x((o,t,e,r)=>{let a=_(t,["class","variant","size","builders"]),{class:s=void 0}=t,{variant:n="default"}=t,{size:i="default"}=t,{builders:c=[]}=t;return t.class===void 0&&e.class&&s!==void 0&&e.class(s),t.variant===void 0&&e.variant&&n!==void 0&&e.variant(n),t.size===void 0&&e.size&&i!==void 0&&e.size(i),t.builders===void 0&&e.builders&&c!==void 0&&e.builders(c),`${D(J,"ButtonPrimitive.Root").$$render(o,Object.assign({},{builders:c},{class:R(V({variant:n,size:i,className:s}),"hover:bg-base-200 shadow-base-200")},{type:"button"},a),{},{default:()=>`${r.default?r.default({}):""}`})}`});export{nt as B,st as c,at as h,v as i,rt as r};
