import{w as $,a as w,n as v,r as A,u as M,b as T,t as k,j as b}from"./VennDiagram.svelte_svelte_type_style_lang.js";import{nanoid as S}from"nanoid/non-secure";import{d as j,w as D,a as I}from"./index2.js";import{flip as W,offset as V,shift as U,arrow as H,size as K,autoUpdate as L,computePosition as R}from"@floating-ui/dom";import"dequal";import{createFocusTrap as q}from"focus-trap";import{f as N}from"./ssr.js";function z(e){return e[e.length-1]}function mt(e,t){return e.map((s,n)=>e[(t+n)%e.length])}const gt=(e,t)=>{const s=$(e),n=(o,i)=>{s.update(c=>{const a=o(c);let l=a;return t&&(l=t({curr:c,next:a})),i?.(l),l})};return{...s,update:n,set:o=>{n(()=>o)}}};function B(e){return new Promise(t=>setTimeout(t,e))}function _(){return S(10)}function yt(e){return e.reduce((t,s)=>(t[s]=_(),t),{})}function ht(e){const{open:t,forceVisible:s,activeTrigger:n}=e;return j([t,s,n],([r,o,i])=>(r||o)&&i!==null)}function bt(e){const t={};return Object.keys(e).forEach(s=>{const n=s,r=e[n];t[n]=$(D(r))}),t}function G(e){let t=e.parentElement;for(;w(t)&&!t.hasAttribute("data-portal");)t=t.parentElement;return t||"body"}function wt(e,t){return t!==void 0?t:G(e)==="body"?document.body:null}const J={strategy:"absolute",placement:"top",gutter:5,flip:!0,sameWidth:!1,overflowPadding:8},Q={bottom:"rotate(45deg)",left:"rotate(135deg)",top:"rotate(225deg)",right:"rotate(315deg)"};function X(e,t,s={}){if(!t||!e||s===null)return{destroy:v};const n={...J,...s},r=t.querySelector("[data-arrow=true]"),o=[];n.flip&&o.push(W({boundary:n.boundary,padding:n.overflowPadding}));const i=w(r)?r.offsetHeight/2:0;if(n.gutter||n.offset){const a=n.gutter?{mainAxis:n.gutter}:n.offset;a?.mainAxis!=null&&(a.mainAxis+=i),o.push(V(a))}o.push(U({boundary:n.boundary,crossAxis:n.overlap,padding:n.overflowPadding})),r&&o.push(H({element:r,padding:8})),o.push(K({padding:n.overflowPadding,apply({rects:a,availableHeight:l,availableWidth:u}){n.sameWidth&&Object.assign(t.style,{width:`${Math.round(a.reference.width)}px`,minWidth:"unset"}),n.fitViewport&&Object.assign(t.style,{maxWidth:`${u}px`,maxHeight:`${l}px`})}}));function c(){if(!e||!t||w(e)&&!e.ownerDocument.documentElement.contains(e))return;const{placement:a,strategy:l}=n;R(e,t,{placement:a,middleware:o,strategy:l}).then(u=>{const p=Math.round(u.x),O=Math.round(u.y),[P,d]=Y(u.placement);if(t.setAttribute("data-side",P),t.setAttribute("data-align",d),Object.assign(t.style,{position:n.strategy,top:`${O}px`,left:`${p}px`}),w(r)&&u.middlewareData.arrow){const{x:g,y:m}=u.middlewareData.arrow,f=u.placement.split("-")[0];r.setAttribute("data-side",f),Object.assign(r.style,{position:"absolute",left:g!=null?`${g}px`:"",top:m!=null?`${m}px`:"",[f]:`calc(100% - ${i}px)`,transform:Q[f],backgroundColor:"inherit",zIndex:"inherit"})}return u})}return Object.assign(t.style,{position:n.strategy}),{destroy:L(e,t,c)}}function Y(e){const[t,s="center"]=e.split("-");return[t,s]}function Z(e={}){let t;const{immediate:s,...n}=e,r=D(!1),o=D(!1),i=p=>t?.activate(p),c=p=>{t?.deactivate(p)},a=()=>{t&&(t.pause(),o.set(!0))},l=()=>{t&&(t.unpause(),o.set(!1))};return{useFocusTrap:p=>(t=q(p,{...n,onActivate(){r.set(!0),e.onActivate?.()},onDeactivate(){r.set(!1),e.onDeactivate?.()}}),s&&i(),{destroy(){c(),t=void 0}}),hasFocus:I(r),isPaused:I(o),activate:i,deactivate:c,pause:a,unpause:l}}const x=[],tt=(e,t)=>{let s=v;function n(){const o=x.indexOf(e);o>=0&&x.splice(o,1)}function r(o){s();const{open:i,onClose:c,shouldCloseOnInteractOutside:a,closeOnInteractOutside:l}=o;B(100).then(()=>{i?x.push(e):n()});function u(){return z(x)===e}function p(){u()&&c&&(c(),n())}function O(d){const g=d.target;A(g)&&g&&u()&&(d.preventDefault(),d.stopPropagation(),d.stopImmediatePropagation())}function P(d){a?.(d)&&u()&&(d.preventDefault(),d.stopPropagation(),d.stopImmediatePropagation(),p())}s=st(e,{onInteractOutsideStart:O,onInteractOutside:l?P:void 0,enabled:i}).destroy}return r(t),{update:r,destroy(){n(),s()}}},et={floating:{},focusTrap:{},modal:{},escapeKeydown:{},portal:"body"},vt=(e,t)=>{e.dataset.escapee="";const{anchorElement:s,open:n,options:r}=t;if(!s||!n||!r)return{destroy:v};const o={...et,...r},i=[];if(o.portal!==null&&i.push(nt(e,o.portal).destroy),i.push(X(s,e,o.floating).destroy),o.focusTrap!==null){const{useFocusTrap:a}=Z({immediate:!0,escapeDeactivates:!1,allowOutsideClick:!0,returnFocusOnDeactivate:!1,fallbackFocus:e,...o.focusTrap});i.push(a(e).destroy)}o.modal!==null&&i.push(tt(e,{onClose:()=>{w(s)&&(n.set(!1),s.focus())},shouldCloseOnInteractOutside:a=>!(a.defaultPrevented||w(s)&&s.contains(a.target)),...o.modal}).destroy),o.escapeKeydown!==null&&i.push(M(e,{enabled:n,handler:()=>{n.set(!1)},...o.escapeKeydown}).destroy);const c=T(...i);return{destroy(){c()}}},nt=(e,t="body")=>{let s;if(!w(t)&&typeof t!="string")return{destroy:v};async function n(o){if(t=o,typeof t=="string"){if(s=document.querySelector(t),s===null&&(await k(),s=document.querySelector(t)),s===null)throw new Error(`No element found matching css selector: "${t}"`)}else if(t instanceof HTMLElement)s=t;else throw new TypeError(`Unknown portal target type: ${t===null?"null":typeof t}. Allowed types: string (CSS selector) or HTMLElement.`);e.dataset.portal="",s.appendChild(e),e.hidden=!1}function r(){e.remove()}return n(t),{update:n,destroy:r}},st=(e,t)=>{let s=v,n=v,r=!1,o=!1,i=!1;function c(u){s(),n();const{onInteractOutside:p,onInteractOutsideStart:O,enabled:P}=u;if(!P)return;function d(f){p&&C(f,e)&&O?.(f);const h=f.target;A(h)&&F(e,h)&&(o=!0),r=!0}function g(f){p?.(f)}const m=ot(e);if(typeof PointerEvent<"u"){const f=h=>{n();const y=E=>{a(E)&&g(E),l()};if(h.pointerType==="touch"){n=b(m,"click",y,{capture:!0,once:!0});return}y(h)};s=T(b(m,"pointerdown",d,!0),b(m,"pointerup",f,!0))}else{const f=y=>{i?i=!1:a(y)&&g(y),l()},h=y=>{i=!0,a(y)&&g(y),l()};s=T(b(m,"mousedown",d,!0),b(m,"mouseup",f,!0),b(m,"touchstart",d,!0),b(m,"touchend",h,!0))}}function a(u){return!!(r&&!o&&C(u,e))}function l(){r=!1,o=!1}return c(t),{update:c,destroy(){s(),n()}}};function C(e,t){if("button"in e&&e.button>0)return!1;const s=e.target;if(!A(s))return!1;const n=s.ownerDocument;return!n||!n.documentElement.contains(s)?!1:t&&!F(t,s)}function F(e,t){return e===t||e.contains(t)}function ot(e){return e?.ownerDocument??document}function Ot(e,t){const s={};return t.forEach(n=>{s[n]={[`data-${e}-${n}`]:""}}),n=>s[n]}function Pt(e){return e?{"aria-disabled":"true","data-disabled":""}:{"aria-disabled":void 0,"data-disabled":void 0}}function xt(){const e=N();return t=>{const{originalEvent:s}=t.detail,{cancelable:n}=t,r=s.type;e(r,{originalEvent:s,currentTarget:s.currentTarget},{cancelable:n})||t.preventDefault()}}function Tt(e){const t={};for(const s in e){const n=e[s];n!==void 0&&(t[s]=n)}return t}function Dt(e){return function(t,s){if(s===void 0)return;const n=e[t];n&&n.set(s)}}function At(e){return(t={})=>rt(e,t)}function rt(e,t){const n={...{side:"bottom",align:"center",sideOffset:0,alignOffset:0,sameWidth:!1,avoidCollisions:!0,collisionPadding:8,fitViewport:!1,strategy:"absolute",overlap:!1},...t};e.update(r=>({...r,placement:it(n.side,n.align),offset:{...r.offset,mainAxis:n.sideOffset,crossAxis:n.alignOffset},gutter:0,sameWidth:n.sameWidth,flip:n.avoidCollisions,overflowPadding:n.collisionPadding,boundary:n.collisionBoundary,fitViewport:n.fitViewport,strategy:n.strategy,overlap:n.overlap}))}function it(e,t){return t==="center"?e:`${e}-${t}`}export{wt as a,nt as b,Ot as c,ht as d,Dt as e,At as f,yt as g,xt as h,Pt as i,_ as j,gt as o,Tt as r,B as s,bt as t,vt as u,mt as w};
