const a=Symbol("Unset"),l=Symbol("IsSetTracked"),b=Symbol("GetModKeys"),h=Symbol("GetOwnKey"),S=Symbol("GetOwnPath"),m=Symbol("GetParent"),y=(s={},n={},c=void 0,t=void 0)=>{if(c&&!c[l])throw new Error("SetTracked parent must be SetTracked");const d=Object.assign(()=>{},n??{}),f=Object.keys(d),u=new Proxy(d,{get(i,e){switch(e){case a:return!c?.[b].includes(t);case b:return f;case h:return t;case m:return c;case S:{const r=[t];let o=c;for(;o!==void 0;)r.unshift(o[h]),o=o[m];return r.join(".")}case l:return!0;case"toJSON":return()=>({...i});case"toString":case"toPrimitive":case Symbol.toPrimitive:return u[a]?t&&t in s?()=>s[t]:()=>"":n.toString.bind(n);default:return e in i||(i[e]=y(s,void 0,u,e)),i[e]}},set(i,e,r){return f.push(e),typeof r=="object"&&(r=y(s,r,u,e)),i[e]=r,!0}});return u},P=(s,...n)=>n.filter(t=>t?.[a]).length!==0;function G(s,...n){return s.call(this,...n)}export{P as h,G as p,y as s};
