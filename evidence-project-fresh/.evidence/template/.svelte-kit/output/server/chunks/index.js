class o{constructor(e,t){this.status=e,typeof t=="string"?this.body={message:t}:t?this.body=t:this.body={message:`Error: ${e}`}}toString(){return JSON.stringify(this.body)}}class i{constructor(e,t){this.status=e,this.location=t}}class a extends Error{constructor(e,t,n){super(n),this.status=e,this.text=t}}class c{constructor(e,t){this.status=e,this.data=t}}function h(s,e){throw isNaN(s)||s<400||s>599?new Error(`HTTP error status codes must be between 400 and 599 — ${s} is invalid`):new o(s,e)}function d(s,e){const t=JSON.stringify(s),n=new Headers(e?.headers);return n.has("content-length")||n.set("content-length",r.encode(t).byteLength.toString()),n.has("content-type")||n.set("content-type","application/json"),new Response(t,{...e,headers:n})}const r=new TextEncoder;function l(s,e){const t=new Headers(e?.headers);if(!t.has("content-length")){const n=r.encode(s);return t.set("content-length",n.byteLength.toString()),new Response(n,{...e,headers:t})}return new Response(s,{...e,headers:t})}function u(s,e){return new c(s,e)}export{c as A,o as H,i as R,a as S,h as e,u as f,d as j,l as t};
