import{l as e}from"./index5.js";const n=r=>r instanceof Error?{message:r.message,stack:r.stack,name:r.name,cause:r.cause?n(r.cause):void 0}:JSON.parse(JSON.stringify(r)),u=r=>(e.error(`${r.message} | ${r.event.route.id??""}`,{url:r.event.url.href,status:r.status}),e.debug(r),n(r.error));function c({event:r,resolve:s}){const a="";return s(r,{transformPageChunk:({html:t})=>t.replace("/*loading*/",a)})}export{c as handle,u as handleError};
