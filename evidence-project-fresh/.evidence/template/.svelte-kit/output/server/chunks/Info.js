import{s as ce,g as fe,c as I,a as Z,b as M,d as x,v as V,h as U,j as ue}from"./ssr.js";import{I as ne,l as ve,f as Ce,c as me}from"./index7.js";import{s as R,c as b,n as he}from"./utils.js";import{w as G,o as ge,m as q,b as ee,c as H,r as Oe,a1 as ye,a2 as K,d as we,e as J,t as Te,a as Q,n as te,p as xe,s as oe,i as ie,f as ke,l as Se}from"./VennDiagram.svelte_svelte_type_style_lang.js";import Ee from"chroma-js";import"dequal";import{w as X,d as De}from"./index2.js";import{o as He,t as le,g as Le,d as Pe,u as _e,a as Me,s as Ve,c as Ie,r as Ae,e as Be,f as Ne,h as ae}from"./helpers.js";import"clsx";function Ze(d){const e=[],t=document.createTreeWalker(d,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;t.nextNode();)e.push(t.currentNode);return e}const{name:Y}=we("hover-card"),Ue={defaultOpen:!1,openDelay:1e3,closeDelay:100,positioning:{placement:"bottom"},arrowSize:8,closeOnOutsideClick:!0,forceVisible:!1,portal:void 0,closeOnEscape:!0,onOutsideClick:void 0},We=["trigger","content"];function ze(d={}){const e={...Ue,...d},t=e.open??X(e.defaultOpen),o=He(t,e?.onOpenChange),i=G.writable(!1),a=G.writable(!1),l=X(!1),s=X(null),c=le(ge(e,"ids")),{openDelay:f,closeDelay:v,positioning:g,arrowSize:m,closeOnOutsideClick:w,forceVisible:y,portal:h,closeOnEscape:k,onOutsideClick:D}=c,C=le({...Le(We),...e.ids});let O=null,L;const P=G.derived(f,n=>()=>{O&&(window.clearTimeout(O),O=null),O=window.setTimeout(()=>{o.set(!0)},n)}),_=G.derived([v,a,i],([n,u,r])=>()=>{O&&(window.clearTimeout(O),O=null),!u&&!r&&(O=window.setTimeout(()=>{o.set(!1)},n))}),W=q(Y("trigger"),{stores:[o,C.trigger,C.content],returned:([n,u,r])=>({role:"button","aria-haspopup":"dialog","aria-expanded":n,"data-state":n?"open":"closed","aria-controls":r,id:u}),action:n=>({destroy:ee(H(n,"pointerenter",r=>{K(r)||P.get()()}),H(n,"pointerleave",r=>{K(r)||_.get()()}),H(n,"focus",r=>{!Oe(r.currentTarget)||!ye(r.currentTarget)||P.get()()}),H(n,"blur",()=>_.get()()))})}),A=Pe({open:o,forceVisible:y,activeTrigger:s}),z=q(Y("content"),{stores:[A,h,C.content],returned:([n,u,r])=>({hidden:n?void 0:!0,tabindex:-1,style:oe({"pointer-events":n?void 0:"none",opacity:n?1:0,userSelect:"text",WebkitUserSelect:"text"}),id:r,"data-state":n?"open":"closed","data-portal":xe(u)}),action:n=>{let u=te;const r=()=>{O&&window.clearTimeout(O)};let S=te;const B=J([A,s,g,w,h,k],([T,E,N,F,se,de])=>{S(),!(!T||!E)&&Te().then(()=>{S(),S=_e(n,{anchorElement:E,open:o,options:{floating:N,modal:{closeOnInteractOutside:F,onClose:()=>{o.set(!1),E.focus()},shouldCloseOnInteractOutside:p=>(D.get()?.(p),!(p.defaultPrevented||Q(E)&&E.contains(p.target))),open:T},portal:Me(n,se),focusTrap:null,escapeKeydown:de?void 0:null}}).destroy})});return u=ee(H(n,"pointerdown",T=>{const E=T.currentTarget,N=T.target;!Q(E)||!Q(N)||(E.contains(N)&&l.set(!0),i.set(!1),a.set(!0))}),H(n,"pointerenter",T=>{K(T)||P.get()()}),H(n,"pointerleave",T=>{K(T)||_.get()()}),H(n,"focusout",T=>{T.preventDefault()})),{destroy(){u(),S(),r(),B()}}}}),j=q(Y("arrow"),{stores:m,returned:n=>({"data-arrow":!0,style:oe({position:"absolute",width:`var(--arrow-size, ${n}px)`,height:`var(--arrow-size, ${n}px)`})})});return J([l],([n])=>{if(!ie||!n)return;const u=document.body,r=document.getElementById(C.content.get());if(!r)return;L=u.style.userSelect||u.style.webkitUserSelect;const S=r.style.userSelect||r.style.webkitUserSelect;return u.style.userSelect="none",u.style.webkitUserSelect="none",r.style.userSelect="text",r.style.webkitUserSelect="text",()=>{u.style.userSelect=L,u.style.webkitUserSelect=L,r.style.userSelect=S,r.style.webkitUserSelect=S}}),ke(()=>{const n=document.getElementById(C.trigger.get());n&&s.set(n)}),J([o],([n])=>{if(!ie||!n){i.set(!1);return}const u=()=>{l.set(!1),a.set(!1),Ve(1).then(()=>{document.getSelection()?.toString()!==""&&i.set(!0)})};document.addEventListener("pointerup",u);const r=document.getElementById(C.content.get());return r?(Ze(r).forEach(B=>B.setAttribute("tabindex","-1")),()=>{document.removeEventListener("pointerup",u),i.set(!1),a.set(!1)}):void 0}),{ids:C,elements:{trigger:W,content:z,arrow:j},states:{open:o},options:c}}function re(){return{NAME:"link-preview",PARTS:["arrow","content","trigger"]}}function je(d){const{NAME:e,PARTS:t}=re(),o=Ie(e,t),i={...ze({...Ae(d),forceVisible:!0}),getAttrs:o};return ce(e,i),{...i,updateOption:Be(i.options)}}function $(){const{NAME:d}=re();return fe(d)}function Fe(d){const t={...{side:"bottom",align:"center"},...d},{options:{positioning:o}}=$();Ne(o)(t)}const Re=I((d,e,t,o)=>{let i,a,{open:l=void 0}=e,{onOpenChange:s=void 0}=e,{openDelay:c=700}=e,{closeDelay:f=300}=e,{closeOnOutsideClick:v=void 0}=e,{closeOnEscape:g=void 0}=e,{portal:m=void 0}=e,{onOutsideClick:w=void 0}=e;const{states:{open:y},updateOption:h,ids:k}=je({defaultOpen:l,openDelay:c,closeDelay:f,closeOnOutsideClick:v,closeOnEscape:g,portal:m,onOutsideClick:w,onOpenChange:({next:C})=>(l!==C&&(s?.(C),l=C),C)}),D=De([k.content,k.trigger],([C,O])=>({content:C,trigger:O}));return a=R(D,C=>i=C),e.open===void 0&&t.open&&l!==void 0&&t.open(l),e.onOpenChange===void 0&&t.onOpenChange&&s!==void 0&&t.onOpenChange(s),e.openDelay===void 0&&t.openDelay&&c!==void 0&&t.openDelay(c),e.closeDelay===void 0&&t.closeDelay&&f!==void 0&&t.closeDelay(f),e.closeOnOutsideClick===void 0&&t.closeOnOutsideClick&&v!==void 0&&t.closeOnOutsideClick(v),e.closeOnEscape===void 0&&t.closeOnEscape&&g!==void 0&&t.closeOnEscape(g),e.portal===void 0&&t.portal&&m!==void 0&&t.portal(m),e.onOutsideClick===void 0&&t.onOutsideClick&&w!==void 0&&t.onOutsideClick(w),l!==void 0&&y.set(l),h("openDelay",c),h("closeDelay",f),h("closeOnOutsideClick",v),h("closeOnEscape",g),h("portal",m),h("onOutsideClick",w),a(),`${o.default?o.default({ids:i}):""}`}),Ge=I((d,e,t,o)=>{let i,a=b(e,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]),l,s,c,f,{transition:v=void 0}=e,{transitionConfig:g=void 0}=e,{inTransition:m=void 0}=e,{inTransitionConfig:w=void 0}=e,{outTransition:y=void 0}=e,{outTransitionConfig:h=void 0}=e,{asChild:k=!1}=e,{id:D=void 0}=e,{side:C="bottom"}=e,{align:O="center"}=e,{sideOffset:L=0}=e,{alignOffset:P=0}=e,{collisionPadding:_=8}=e,{avoidCollisions:W=!0}=e,{collisionBoundary:A=void 0}=e,{sameWidth:z=!1}=e,{fitViewport:j=!1}=e,{strategy:n="absolute"}=e,{overlap:u=!1}=e,{el:r=void 0}=e;const{elements:{content:S},states:{open:B},ids:T,getAttrs:E}=$();f=R(S,F=>c=F),s=R(B,F=>l=F);const N=E("content");return ae(),e.transition===void 0&&t.transition&&v!==void 0&&t.transition(v),e.transitionConfig===void 0&&t.transitionConfig&&g!==void 0&&t.transitionConfig(g),e.inTransition===void 0&&t.inTransition&&m!==void 0&&t.inTransition(m),e.inTransitionConfig===void 0&&t.inTransitionConfig&&w!==void 0&&t.inTransitionConfig(w),e.outTransition===void 0&&t.outTransition&&y!==void 0&&t.outTransition(y),e.outTransitionConfig===void 0&&t.outTransitionConfig&&h!==void 0&&t.outTransitionConfig(h),e.asChild===void 0&&t.asChild&&k!==void 0&&t.asChild(k),e.id===void 0&&t.id&&D!==void 0&&t.id(D),e.side===void 0&&t.side&&C!==void 0&&t.side(C),e.align===void 0&&t.align&&O!==void 0&&t.align(O),e.sideOffset===void 0&&t.sideOffset&&L!==void 0&&t.sideOffset(L),e.alignOffset===void 0&&t.alignOffset&&P!==void 0&&t.alignOffset(P),e.collisionPadding===void 0&&t.collisionPadding&&_!==void 0&&t.collisionPadding(_),e.avoidCollisions===void 0&&t.avoidCollisions&&W!==void 0&&t.avoidCollisions(W),e.collisionBoundary===void 0&&t.collisionBoundary&&A!==void 0&&t.collisionBoundary(A),e.sameWidth===void 0&&t.sameWidth&&z!==void 0&&t.sameWidth(z),e.fitViewport===void 0&&t.fitViewport&&j!==void 0&&t.fitViewport(j),e.strategy===void 0&&t.strategy&&n!==void 0&&t.strategy(n),e.overlap===void 0&&t.overlap&&u!==void 0&&t.overlap(u),e.el===void 0&&t.el&&r!==void 0&&t.el(r),D&&T.content.set(D),i=c,Object.assign(i,N),l&&Fe({side:C,align:O,sideOffset:L,alignOffset:P,collisionPadding:_,avoidCollisions:W,collisionBoundary:A,sameWidth:z,fitViewport:j,strategy:n,overlap:u}),s(),f(),`${k&&l?`${o.default?o.default({builder:i}):""}`:`${v&&l?`<div${Z([x(i),x(a)],{})}${M("this",r,0)}>${o.default?o.default({builder:i}):""}</div>`:`${m&&y&&l?`<div${Z([x(i),x(a)],{})}${M("this",r,0)}>${o.default?o.default({builder:i}):""}</div>`:`${m&&l?`<div${Z([x(i),x(a)],{})}${M("this",r,0)}>${o.default?o.default({builder:i}):""}</div>`:`${y&&l?`<div${Z([x(i),x(a)],{})}${M("this",r,0)}>${o.default?o.default({builder:i}):""}</div>`:`${l?`<div${Z([x(i),x(a)],{})}${M("this",r,0)}>${o.default?o.default({builder:i}):""}</div>`:""}`}`}`}`}`}`}),Ke=I((d,e,t,o)=>{let i,a=b(e,["asChild","id","el"]),l,s,{asChild:c=!1}=e,{id:f=void 0}=e,{el:v=void 0}=e;const{elements:{trigger:g},ids:m,getAttrs:w}=$();s=R(g,h=>l=h),ae();const y=w("trigger");return e.asChild===void 0&&t.asChild&&c!==void 0&&t.asChild(c),e.id===void 0&&t.id&&f!==void 0&&t.id(f),e.el===void 0&&t.el&&v!==void 0&&t.el(v),f&&m.trigger.set(f),i=l,Object.assign(i,y),s(),`${c?`${o.default?o.default({builder:i}):""}`:` <a${Z([x(i),x(a),x(y)],{})}${M("this",v,0)}>${o.default?o.default({builder:i}):""}</a>`}`}),at={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:"currentColor"}]}},rt={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z",fill:"currentColor"}]}},st={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z",fill:"currentColor"}]}},dt={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2 2.5C2 2.22386 2.22386 2 2.5 2H5.5C5.77614 2 6 2.22386 6 2.5C6 2.77614 5.77614 3 5.5 3H3V5.5C3 5.77614 2.77614 6 2.5 6C2.22386 6 2 5.77614 2 5.5V2.5ZM9 2.5C9 2.22386 9.22386 2 9.5 2H12.5C12.7761 2 13 2.22386 13 2.5V5.5C13 5.77614 12.7761 6 12.5 6C12.2239 6 12 5.77614 12 5.5V3H9.5C9.22386 3 9 2.77614 9 2.5ZM2.5 9C2.77614 9 3 9.22386 3 9.5V12H5.5C5.77614 12 6 12.2239 6 12.5C6 12.7761 5.77614 13 5.5 13H2.5C2.22386 13 2 12.7761 2 12.5V9.5C2 9.22386 2.22386 9 2.5 9ZM12.5 9C12.7761 9 13 9.22386 13 9.5V12.5C13 12.7761 12.7761 13 12.5 13H9.5C9.22386 13 9 12.7761 9 12.5C9 12.2239 9.22386 12 9.5 12H12V9.5C12 9.22386 12.2239 9 12.5 9Z",fill:"currentColor"}]}},pe={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.49991 0.876892C3.84222 0.876892 0.877075 3.84204 0.877075 7.49972C0.877075 11.1574 3.84222 14.1226 7.49991 14.1226C11.1576 14.1226 14.1227 11.1574 14.1227 7.49972C14.1227 3.84204 11.1576 0.876892 7.49991 0.876892ZM1.82707 7.49972C1.82707 4.36671 4.36689 1.82689 7.49991 1.82689C10.6329 1.82689 13.1727 4.36671 13.1727 7.49972C13.1727 10.6327 10.6329 13.1726 7.49991 13.1726C4.36689 13.1726 1.82707 10.6327 1.82707 7.49972ZM8.24992 4.49999C8.24992 4.9142 7.91413 5.24999 7.49992 5.24999C7.08571 5.24999 6.74992 4.9142 6.74992 4.49999C6.74992 4.08577 7.08571 3.74999 7.49992 3.74999C7.91413 3.74999 8.24992 4.08577 8.24992 4.49999ZM6.00003 5.99999H6.50003H7.50003C7.77618 5.99999 8.00003 6.22384 8.00003 6.49999V9.99999H8.50003H9.00003V11H8.50003H7.50003H6.50003H6.00003V9.99999H6.50003H7.00003V6.99999H6.50003H6.00003V5.99999Z",fill:"currentColor"}]}},ct={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 6.5C10 8.433 8.433 10 6.5 10C4.567 10 3 8.433 3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5ZM9.30884 10.0159C8.53901 10.6318 7.56251 11 6.5 11C4.01472 11 2 8.98528 2 6.5C2 4.01472 4.01472 2 6.5 2C8.98528 2 11 4.01472 11 6.5C11 7.56251 10.6318 8.53901 10.0159 9.30884L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L9.30884 10.0159Z",fill:"currentColor"}]}},ft=I((d,e,t,o)=>{let{inputType:i=void 0}=e,{error:a=void 0}=e,{height:l=""}=e,{width:s=""}=e;return Array.isArray(a)||(a=[a]),a.length>0&&(a=a.join(`
`)),e.inputType===void 0&&t.inputType&&i!==void 0&&t.inputType(i),e.error===void 0&&t.error&&a!==void 0&&t.error(a),e.height===void 0&&t.height&&l!==void 0&&t.height(l),e.width===void 0&&t.width&&s!==void 0&&t.width(s),`<div style="${"height: "+U(l,!0)+"px; width: "+U(s,!0)+"px"}" class="group relative cursor-help cursor-helpfont-sans bg-negative/10 font-ui font-normal rounded border border-negative/50 print:break-inside-avoid text-center text-negative flex items-center justify-center mb-4 mt-4"><div>${V(ne,"Icon").$$render(d,{src:ve,class:"mb-[2px] h-4 w-4 stroke-[1.8px] text-negative inline"},{},{})} <span class="font-medium text-center text-sm">${U(i)}</span></div> <div class="hidden font-sans group-hover:inline-block absolute top-[50%] left-[50%] text-xs px-2 py-1 bg-base-100 border border-base-300 leading-relaxed rounded-md text-left z-50 w-fit"><pre>${U(a)}</pre></div></div>`}),qe=I((d,e,t,o)=>{let i=b(e,["class","transition","transitionConfig","align","sideOffset"]),{class:a=void 0}=e,{transition:l=Ce}=e,{transitionConfig:s=void 0}=e,{align:c="center"}=e,{sideOffset:f=4}=e;return e.class===void 0&&t.class&&a!==void 0&&t.class(a),e.transition===void 0&&t.transition&&l!==void 0&&t.transition(l),e.transitionConfig===void 0&&t.transitionConfig&&s!==void 0&&t.transitionConfig(s),e.align===void 0&&t.align&&c!==void 0&&t.align(c),e.sideOffset===void 0&&t.sideOffset&&f!==void 0&&t.sideOffset(f),`${V(Ge,"HoverCardPrimitive.Content").$$render(d,Object.assign({},{transition:l},{transitionConfig:s},{align:c},{sideOffset:f},{class:me("z-50 my-2 rounded-md border bg-base-100 shadow-md outline-none",a)},i),{},{default:()=>`${o.default?o.default({}):""}`})}`}),Je=Re,Qe=Ke,Xe=I((d,e,t,o)=>{let{align:i="center"}=e,{side:a="bottom"}=e,{alignOffset:l=0}=e,{sideOffset:s=4}=e,{openDelay:c=0}=e,{closeDelay:f=0}=e,{open:v=!1}=e;return e.align===void 0&&t.align&&i!==void 0&&t.align(i),e.side===void 0&&t.side&&a!==void 0&&t.side(a),e.alignOffset===void 0&&t.alignOffset&&l!==void 0&&t.alignOffset(l),e.sideOffset===void 0&&t.sideOffset&&s!==void 0&&t.sideOffset(s),e.openDelay===void 0&&t.openDelay&&c!==void 0&&t.openDelay(c),e.closeDelay===void 0&&t.closeDelay&&f!==void 0&&t.closeDelay(f),e.open===void 0&&t.open&&v!==void 0&&t.open(v),`${V(Je,"HoverCard").$$render(d,{open:v,openDelay:c,closeDelay:f},{},{default:()=>`${V(Qe,"HoverCardTrigger").$$render(d,{},{},{default:()=>`${o.trigger?o.trigger({}):""}`})} ${V(qe,"HoverCardContent").$$render(d,{align:i,side:a,alignOffset:l,sideOffset:s},{},{default:()=>`${o.content?o.content({}):""}`})}`})}`}),ut=I((d,e,t,o)=>{let i,a,l,s=he,c=()=>(s(),s=R(i,k=>l=k),i),f=pe,{description:v=""}=e,{size:g=4}=e,{className:m=void 0}=e;const{resolveColor:w}=Se();let{color:y="base-content-muted"}=e,h=!1;return e.description===void 0&&t.description&&v!==void 0&&t.description(v),e.size===void 0&&t.size&&g!==void 0&&t.size(g),e.className===void 0&&t.className&&m!==void 0&&t.className(m),e.color===void 0&&t.color&&y!==void 0&&t.color(y),c(i=w(y)),a=Ee(l).css(),s(),`${V(Xe,"HoverCard").$$render(d,{open:h,align:"start",side:"right",alignOffset:-8,sideOffset:4},{},{content:()=>`<div slot="content" class="bg-base-100 p-2 rounded-md text-base-content text-xs max-w-52"><p class="leading-relaxed text-pretty">${U(v)}</p></div>`,trigger:()=>`<span slot="trigger" class="${"inline-block align-middle pb-0.5 pr-1 leading-4 w-fit "+U(m,!0)+" cursor-pointer"}" role="button"${M("aria-expanded",h,0)} aria-label="Toggle tooltip" tabindex="0"${ue({"--textColor":a})}>${o.handle?o.handle({}):` ${V(ne,"Icon").$$render(d,{src:f,class:"w-"+g+" h-"+g+" text-(--textColor)"},{},{})} `}</span>`})}`});export{st as C,dt as E,ft as I,ct as M,ut as a,rt as b,at as c};
