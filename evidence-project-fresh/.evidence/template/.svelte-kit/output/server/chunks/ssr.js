import{r as E,h as $}from"./utils.js";function h(t,e,{bubbles:n=!1,cancelable:s=!1}={}){return new CustomEvent(t,{detail:e,bubbles:n,cancelable:s})}let l;function f(t){l=t}function F(){if(!l)throw new Error("Function called outside component initialization");return l}function D(t){F().$$.on_destroy.push(t)}function k(){const t=F();return(e,n,{cancelable:s=!1}={})=>{const o=t.$$.callbacks[e];if(o){const r=h(e,n,{cancelable:s});return o.slice().forEach(u=>{u.call(t,r)}),!r.defaultPrevented}return!0}}function A(t,e){return F().$$.context.set(t,e),e}function O(t){return F().$$.context.get(t)}function S(){return F().$$.context}function y(t){return t?.length!==void 0?t:Array.from(t)}const b=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"],g=new Set([...b]),w=/[&"<]/g,v=/[&<]/g;function d(t,e=!1){const n=String(t),s=e?w:v;s.lastIndex=0;let o="",r=0;for(;s.test(n);){const u=s.lastIndex-1,c=n[u];o+=n.substring(r,u)+(c==="&"?"&amp;":c==='"'?"&quot;":"&lt;"),r=u+1}return o+n.substring(r)}const x=/[\s'">/=\u{FDD0}-\u{FDEF}\u{FFFE}\u{FFFF}\u{1FFFE}\u{1FFFF}\u{2FFFE}\u{2FFFF}\u{3FFFE}\u{3FFFF}\u{4FFFE}\u{4FFFF}\u{5FFFE}\u{5FFFF}\u{6FFFE}\u{6FFFF}\u{7FFFE}\u{7FFFF}\u{8FFFE}\u{8FFFF}\u{9FFFE}\u{9FFFF}\u{AFFFE}\u{AFFFF}\u{BFFFE}\u{BFFFF}\u{CFFFE}\u{CFFFF}\u{DFFFE}\u{DFFFF}\u{EFFFE}\u{EFFFF}\u{FFFFE}\u{FFFFF}\u{10FFFE}\u{10FFFF}]/u;function R(t,e){const n=Object.assign({},...t);if(e){const o=e.classes,r=e.styles;o&&(n.class==null?n.class=o:n.class+=" "+o),r&&(n.style==null?n.style=i(r):n.style=i(C(n.style,r)))}let s="";return Object.keys(n).forEach(o=>{if(x.test(o))return;const r=n[o];r===!0?s+=" "+o:g.has(o.toLowerCase())?r&&(s+=" "+o):r!=null&&(s+=` ${o}="${r}"`)}),s}function C(t,e){const n={};for(const s of t.split(";")){const o=s.indexOf(":"),r=s.slice(0,o).trim(),u=s.slice(o+1).trim();r&&(n[r]=u)}for(const s in e){const o=e[s];o?n[s]=o:delete n[s]}return n}function p(t){return typeof t=="string"||t&&typeof t=="object"?d(t,!0):t}function T(t){const e={};for(const n in t)e[n]=p(t[n]);return e}function q(t,e){t=y(t);let n="";for(let s=0;s<t.length;s+=1)n+=e(t[s],s);return n}const B={$$render:()=>""};function G(t,e){if(!t||!t.$$render)throw e==="svelte:component"&&(e+=" this={...}"),new Error(`<${e}> is not a valid SSR component. You may need to review your build config to ensure that dependencies are compiled, rather than imported as pre-compiled modules. Otherwise you may need to fix a <${e}>.`);return t}let a;function I(t){function e(n,s,o,r,u){const c=l,_={on_destroy:a,context:new Map(u||(c?c.$$.context:[])),on_mount:[],before_update:[],after_update:[],callbacks:$()};f({$$:_});const m=t(n,s,o,r);return f(c),m}return{render:(n={},{$$slots:s={},context:o=new Map}={})=>{a=[];const r={title:"",head:"",css:new Set},u=e(r,n,{},s,o);return E(a),{html:u,css:{code:Array.from(r.css).map(c=>c.code).join(`
`),map:null},head:r.title+r.head}},$$render:e}}function M(t,e,n){if(e==null||n)return"";const s=`="${d(e,!0)}"`;return` ${t}${s}`}function i(t){return Object.keys(t).filter(e=>t[e]!=null&&t[e]!=="").map(e=>`${e}: ${p(t[e])};`).join(" ")}function N(t){const e=i(t);return e?` style="${e}"`:""}export{R as a,M as b,I as c,T as d,p as e,k as f,O as g,d as h,q as i,N as j,f as k,l,B as m,S as n,D as o,A as s,G as v};
