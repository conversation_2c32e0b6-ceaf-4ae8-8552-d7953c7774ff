import{o as i,i as n}from"./debug.js";class a{static EvidenceLogLevels={fatal:0,error:1,warn:2,info:3,debug:4,verbose:5};#s;logLevel=3;constructor(){i(()=>this.logLevel=4),this.#s={log:(e,r,s)=>{const o=[r];if(s&&o.push(s),!(a.EvidenceLogLevels[e]>this.logLevel))switch(e){case"fatal":case"error":console.error(`[${e.toUpperCase()}]: `,...o);break;case"warn":console.warn(`[${e.toUpperCase()}]: `,...o);break;default:case"info":console.info(`[${e.toUpperCase()}]: `,...o);break;case"debug":console.debug(`[${e.toUpperCase()}]: `,...o);break;case"verbose":console.debug(`[${e.toUpperCase()}]: `,...o);break}}}}die(e,r,s,o){this.fatal(`${e}
${r?r.join(`
`):""}`,{...s,...n()?o:{}}),typeof process<"u"&&process.exit(1)}#e=e=>(r,s)=>{let o=r;this.#s.log(e,o,s)};fatal=this.#e("fatal");error=this.#e("error");warn=this.#e("warn");info=this.#e("info");debug=this.#e("debug");verbose=this.#e("verbose");#o=[];measure=e=>{if(!n())return{meta:()=>{},done:()=>{}};const r=performance.now(),s={};return this.#o.push(e),{meta:(o,t)=>{s[o]=t},done:()=>{const t=performance.now()-r;this.#o.pop(),this.#e("debug")(`Measure: "${e}"`,{duration:t,meta:s,parents:this.#o})}}}}const f=new a;export{f as l};
