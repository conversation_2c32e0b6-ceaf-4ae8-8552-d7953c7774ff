{"/": ["src/pages/+layout.js", "src/pages/+layout.js"], "/api/customFormattingSettings.json": ["src/pages/api/customFormattingSettings.json/+server.js"], "/api/customFormattingSettings.json/GET.json": ["src/pages/api/customFormattingSettings.json/GET.json/+server.js"], "/api/pagesManifest.json": ["src/pages/api/pagesManifest.json/+server.js"], "/api/prerendered_queries/[query_hash].arrow": ["src/pages/api/prerendered_queries/[query_hash].arrow/+server.js"], "/api/settings.json": ["src/pages/api/settings.json/+server.js"], "/api/[...route]/evidencemeta.json": ["src/pages/api/[...route]/evidencemeta.json/+server.js"], "/api/[route_hash]/[additional_hash]/all-queries.json": ["src/pages/api/[route_hash]/[additional_hash]/all-queries.json/+server.js"], "/explore": ["src/pages/explore/+layout.js", "src/pages/+layout.js"], "/explore/console": ["src/pages/explore/+layout.js", "src/pages/+layout.js"], "/explore/schema": ["src/pages/explore/+layout.js", "src/pages/+layout.js"], "/fix-tprotocol-service-worker.js": ["src/pages/fix-tprotocol-service-worker.js/+server.js"], "/manifest.webmanifest": ["src/pages/manifest.webmanifest/+server.js"], "/settings": ["src/pages/settings/+page.js", "src/pages/settings/+page.server.js", "src/pages/settings/+layout.server.js", "src/pages/+layout.js", "src/pages/settings/+layout.server.js", "src/pages/+layout.js"]}