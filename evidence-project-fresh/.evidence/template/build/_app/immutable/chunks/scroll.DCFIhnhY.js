import{n as v}from"./VennDiagram.svelte_svelte_type_style_lang.DUDiH_b8.js";const g=()=>typeof window<"u";function L(){const t=navigator.userAgentData;return(t==null?void 0:t.platform)??navigator.platform}const y=t=>g()&&t.test(L().toLowerCase()),w=()=>g()&&!!navigator.maxTouchPoints,A=()=>y(/^mac/)&&!w(),T=()=>y(/mac|iphone|ipad|ipod/i),$=()=>T()&&!A(),d="data-melt-scroll-lock";function p(t,o){if(!t)return;const e=t.style.cssText;return Object.assign(t.style,o),()=>{t.style.cssText=e}}function C(t,o,e){if(!t)return;const s=t.style.getPropertyValue(o);return t.style.setProperty(o,e),()=>{s?t.style.setProperty(o,s):t.style.removeProperty(o)}}function M(t){const o=t.getBoundingClientRect().left;return Math.round(o)+t.scrollLeft?"paddingLeft":"paddingRight"}function D(t){const o=document,e=o.defaultView??window,{documentElement:s,body:n}=o;if(n.hasAttribute(d))return v;n.setAttribute(d,"");const i=e.innerWidth-s.clientWidth,h=()=>C(s,"--scrollbar-width",`${i}px`),l=M(s),f=e.getComputedStyle(n)[l],m=()=>p(n,{overflow:"hidden",[l]:`calc(${f} + ${i}px)`}),P=()=>{const{scrollX:r,scrollY:u,visualViewport:c}=e,b=(c==null?void 0:c.offsetLeft)??0,x=(c==null?void 0:c.offsetTop)??0,a=p(n,{position:"fixed",overflow:"hidden",top:`${-(u-Math.floor(x))}px`,left:`${-(r-Math.floor(b))}px`,right:"0",[l]:`calc(${f} + ${i}px)`});return()=>{a==null||a(),e.scrollTo(r,u)}},S=[h(),$()?P():m()];return()=>{S.forEach(r=>r==null?void 0:r()),n.removeAttribute(d)}}export{D as r};
