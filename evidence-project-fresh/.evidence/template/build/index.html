<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="/favicon.ico" sizes="32x32" />
		<link rel="icon" href="/icon.svg" type="image/svg+xml" />
		<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
		<link rel="manifest" href="/manifest.webmanifest" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			try {
				/** @type {'light' | 'dark' | 'system'} */
				const savedTheme = localStorage.getItem('evidence-theme') ?? 'system';
				const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				const theme = savedTheme === 'system' ? (prefersDark ? 'dark' : 'light') : savedTheme;
				document.documentElement.classList.add(`theme-${theme}`);
			} catch (e) {}
		</script>
		
		<link href="/_app/immutable/assets/0.CJA0Iyeh.css" rel="stylesheet">
		<link href="/_app/immutable/assets/VennDiagram.D7OGjfZg.css" rel="stylesheet">
		<link href="/_app/immutable/assets/4.B0TfKtdA.css" rel="stylesheet">
		<link rel="modulepreload" href="/_app/immutable/entry/start.CNk9FNAi.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/entry.DjKcjswP.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/scheduler.CXt6djuF.js">
		<link rel="modulepreload" href="/_app/immutable/entry/app.CNBD44OK.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/preload-helper.D7HrI6pR.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.DP2zcclO.js">
		<link rel="modulepreload" href="/_app/immutable/nodes/0.C2i_5rWd.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/VennDiagram.svelte_svelte_type_style_lang.DUDiH_b8.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.rV6zwFgL.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/button.BJbxU9QN.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/stores.DIhw49CQ.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.Dybe8my-.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/scroll.DCFIhnhY.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/AccordionItem.C267eZ3W.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/checkRequiredProps.o_C_V3S5.js">
		<link rel="modulepreload" href="/_app/immutable/nodes/4.DtQwYZ_C.js"><title>Evidence Dashboard for Domo</title><!-- HEAD_svelte-2igo1p_START --> <meta property="og:title" content="Evidence Dashboard for Domo"> <meta name="twitter:title" content="Evidence Dashboard for Domo"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev"> <!-- HEAD_svelte-2igo1p_END -->
	</head>
	<body>
		<script>
			
		</script>
		<div>
			<!-- SvelteKit Hydrated Content -->
			   <div class="z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80"></div> <div data-sveltekit-preload-data="hover" class="antialiased"> <header class="fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden"><div class=" max-w-7xl mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between" style="max-width:undefinedpx;"><div class="flex gap-x-4 items-center"><button type="button" class="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 md:hidden"><span class="sr-only" data-svelte-h="svelte-73kebv">Open sidebar</span> <svg class="w-5 h-5" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M4 6l16 0"></path><path d="M4 12l16 0"></path><path d="M4 18l16 0"></path></svg></button> <a href="/" class="text-sm font-bold text-base-content hidden md:block"><img src="/_app/immutable/assets/wordmark-black.rfl-FBgf.png" alt="Home" class="h-5 aspect-auto block dark:hidden" href="/"> <img src="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png" alt="Home" class="h-5 aspect-auto hidden dark:block" href="/"></a></div> <div class="flex gap-2 text-sm items-center"> <div class="flex gap-2 items-center">   </div> <div class="relative"> <button type="button" tabindex="0" aria-controls="2TjOD758n7" aria-expanded="false" data-state="closed" id="c4oxVyevyp" data-melt-dropdown-menu-trigger="" data-menu-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 hover:text-base-content h-8 rounded-md text-xs px-1 hover:bg-base-200 shadow-base-200" aria-label="Menu" data-button-root=""><svg class="h-6 w-6" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path></svg></button> </div></div></div></header> <div class=" max-w-7xl print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start" style="max-width:undefinedpx;"><div class="print:hidden">  <aside class="w-48 flex-none hidden md:flex"><div class="hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"><div class="flex flex-col pb-6"><a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading" href="/">Home</a> </div> </div> <div class="fixed bottom-0 text-xs py-2" data-svelte-h="svelte-fworv4"><a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a></div></aside></div> <main class="md:pl-8 md:pr-8  mt-16 sm:mt-20 flex-grow overflow-x-hidden print:px-0 print:mt-8"><div class="print:hidden"><div class="flex items-start mt-0 whitespace-nowrap overflow-auto"><div class="inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"><a href="/" class="hover:underline">Home </a></div></div></div> <article id="evidence-main-article" class="select-text markdown pb-10">  <h1 class="title">Evidence Dashboard for Domo</h1>     <h1 class="markdown" id="evidence-dashboard-for-domo-ddx" data-svelte-h="svelte-dwxo2v"><a href="#evidence-dashboard-for-domo-ddx">Evidence Dashboard for Domo DDX</a></h1> <div class="dev-banner svelte-16omzez" data-svelte-h="svelte-1b532x0"><h3 class="svelte-16omzez">🛠️ Development Mode</h3> <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p></div> <p class="markdown" data-svelte-h="svelte-17kjwqn">Welcome to your Evidence dashboard! This application combines the power of Evidence&#39;s analytics framework with Domo&#39;s data platform, allowing you to create interactive dashboards and reports using your Domo datasets.</p> <h2 class="markdown" id="domo-dataset-workflow--analysis" data-svelte-h="svelte-d64fj6"><a href="#domo-dataset-workflow--analysis">Domo Dataset Workflow &amp; Analysis</a></h2> <div class="workflow-toggle svelte-16omzez"><button class="btn-primary svelte-16omzez">📊 Load Domo Dataset</button> </div>  <h2 class="markdown" id="sample-analysis" data-svelte-h="svelte-1941s2m"><a href="#sample-analysis">Sample Analysis</a></h2> <div id="sample-analysis"></div>  <div class="mb-4 mt-2"><button class="text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"><span class="marker svelte-v9l93j"></span> <span>How Evidence Works with Your Data</span></button> </div> <div class="over-container svelte-1ursthx"> </div>   <div class="contents print:hidden"><div class="mt-2 mb-4 ml-0 mr-2 inline-block"> <button type="button" tabindex="0" role="combobox" aria-haspopup="dialog" aria-expanded="false" data-state="closed" id="DR47aJ56Dn" data-melt-popover-trigger="" data-popover-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 bg-transparent shadow-sm rounded-md px-3 text-xs min-w-5 h-8 border border-base-300 hover:bg-base-200 shadow-base-200" aria-label="Category" data-button-root="">All Categories <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" class="ml-2 h-4 w-4"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z" fill="currentColor"></path></svg> </button> </div></div>      <div class="contents print:hidden"><div class="mt-2 mb-4 ml-0 mr-2 inline-block"> <button type="button" tabindex="0" role="combobox" aria-haspopup="dialog" aria-expanded="false" data-state="closed" id="45uFUNvdqE" data-melt-popover-trigger="" data-popover-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 bg-transparent shadow-sm rounded-md px-3 text-xs min-w-5 h-8 border border-base-300 hover:bg-base-200 shadow-base-200" aria-label="Year" data-button-root="">All Years <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" class="ml-2 h-4 w-4"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z" fill="currentColor"></path></svg> </button> </div></div> <div class="over-container svelte-1ursthx"> </div>     <div role="none" class="chart-container mt-2 mb-3 svelte-db4qxn"><div role="status" class="animate-pulse"><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="bg-base-100 rounded-md max-w-[100%]" style="height:237px; margin-top: 15px; margin-bottom: 31px;"></div></div>  <div class="chart-footer svelte-db4qxn"> </div> </div>   <h2 class="markdown" id="whats-next" data-svelte-h="svelte-fy128a"><a href="#whats-next">What&#39;s Next?</a></h2> <div class="next-steps svelte-16omzez" data-svelte-h="svelte-pm10gv"><div class="step svelte-16omzez"><h4 class="svelte-16omzez">1. Load Your Data</h4> <p class="svelte-16omzez">Use the workflow picker above to select and load Domo datasets into DuckDB</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">2. Create Queries</h4> <p class="svelte-16omzez">Write SQL queries against your loaded data using Evidence&#39;s query blocks</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">3. Build Visualizations</h4> <p class="svelte-16omzez">Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">4. Deploy to Domo</h4> <p class="svelte-16omzez">Package your Evidence app and deploy it to Domo DDX for your team to use</p></div></div></article></main> <div class="print:hidden"><aside class="hidden lg:block w-48"><div class="fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"></div></aside></div></div></div>  
			<script type="application/json" data-sveltekit-fetched data-url="/api/customFormattingSettings.json/GET.json">{"status":200,"statusText":"","headers":{},"body":"{\"customFormattingSettings\":{\"version\":\"1.0\",\"customFormats\":[]}}"}</script>
			<script type="application/json" data-sveltekit-fetched data-url="/api///evidencemeta.json">{"status":200,"statusText":"","headers":{},"body":"{\"queries\":[{\"id\":\"categories\",\"compiledQueryString\":\"select\\n      category\\n  from needful_things.orders\\n  group by category\",\"inputQueryString\":\"select\\n      category\\n  from needful_things.orders\\n  group by category\",\"compiled\":false,\"inline\":true},{\"id\":\"orders_by_category\",\"compiledQueryString\":\"select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '${inputs.category.value}'\\n  and date_part('year', order_datetime) like '${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\",\"inputQueryString\":\"select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '${inputs.category.value}'\\n  and date_part('year', order_datetime) like '${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\",\"compiled\":false,\"inline\":true}]}"}</script>
			<script type="application/json" data-sveltekit-fetched data-url="/api/pagesManifest.json">{"status":200,"statusText":"","headers":{},"body":"{\"label\":\"Home\",\"href\":\"/\",\"children\":{},\"frontMatter\":{\"title\":\"Evidence Dashboard for Domo\"},\"isTemplated\":false,\"isPage\":true}"}</script>
			<script>
				{
					__sveltekit_1li61bx = {
						base: ""
					};

					const element = document.currentScript.parentElement;

					const data = [null,null];

					Promise.all([
						import("/_app/immutable/entry/start.CNk9FNAi.js"),
						import("/_app/immutable/entry/app.CNBD44OK.js")
					]).then(([kit, app]) => {
						kit.start(app, element, {
							node_ids: [0, 4],
							data,
							form: null,
							error: null
						});
					});
				}
			</script>
		

			<!-- SplashScreen -->
			<div
				aria-disabled
				id="__evidence_project_splash"
				data-test-id="__evidence_project_splash"
				style="visibility: hidden"
			>
				<svg width="100" height="100" viewBox="-8 -8 588 588" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M7.19462e-05 74.3583C109.309 74.3583 195.795 86.2578 286.834 37.825C377.872 -10.6077 466.416 1.29174 573.667 1.29175L573.667 126.549C466.416 126.549 377.373 114.91 286.834 163.082C196.294 211.254 109.309 199.615 6.11417e-05 199.615L7.19462e-05 74.3583Z"
						class="draw-path"
					/>
					<path
						d="M573.669 499.31C464.36 499.31 377.874 487.411 286.835 535.843C195.797 584.276 107.252 572.377 0.0014801 572.377V447.12C107.252 447.12 196.295 458.758 286.835 410.586C377.375 362.415 464.36 374.053 573.669 374.053V499.31Z"
						class="draw-path"
					/>
					<path
						d="M452.896 186.499C395.028 187.686 341.581 194.947 286.835 224.074C211.396 264.212 136.995 262.826 52.2355 261.247C35.2696 260.931 17.8887 260.608 0.0014801 260.608V385.865C18.1032 385.865 35.6721 386.204 52.81 386.534C137.212 388.162 211.162 389.589 286.835 349.331C341.838 320.07 395.18 312.831 452.896 311.685V186.499Z"
						class="draw-path"
					/>
				</svg>
			</div>
		</div>
	</body>
</html>

<style>
	#__evidence_project_splash {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: #ffffff;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.theme-dark #__evidence_project_splash {
		background-color: #000000;
	}

	.draw-path {
		fill: #000000;
		animation: blinking-logo 2s;
		animation-fill-mode: both;
		animation-iteration-count: infinite;
		animation-timing-function: ease-in-out;
	}

	.theme-dark .draw-path {
		fill: #ffffff;
	}

	@keyframes blinking-logo {
		0% {
			fill-opacity: 1;
		}
		50% {
			fill-opacity: 0.2;
		}
		100% {
			fill-opacity: 1;
		}
	}
</style>
