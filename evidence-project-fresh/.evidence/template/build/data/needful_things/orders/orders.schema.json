[{"name": "id", "evidenceType": "number", "typeFidelity": "precise"}, {"name": "order_datetime", "evidenceType": "date", "typeFidelity": "precise"}, {"name": "order_month", "evidenceType": "date", "typeFidelity": "precise"}, {"name": "first_name", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "last_name", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "email", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "address", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "state", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "zipcode", "evidenceType": "number", "typeFidelity": "precise"}, {"name": "item", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "category", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "sales", "evidenceType": "number", "typeFidelity": "precise"}, {"name": "channel", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "channel_group", "evidenceType": "string", "typeFidelity": "precise"}, {"name": "channel_month", "evidenceType": "string", "typeFidelity": "precise"}]