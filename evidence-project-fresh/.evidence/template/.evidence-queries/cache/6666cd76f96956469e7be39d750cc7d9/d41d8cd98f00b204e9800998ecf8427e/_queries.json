{"categories_columns": {"sql_string": "---- Columns categories (1dt042y)\nDESCRIBE SELECT * FROM (select\n      category\n  from needful_things.orders\n  group by category\n) AS \"inputQuery-hU\"\n", "query_hash": "8f1ebed03abde742929986e420698858"}, "categories_length": {"sql_string": "---- Length categories (1dt042y)\nSELECT COUNT(*) as rowCount FROM (SELECT * FROM (select\n      category\n  from needful_things.orders\n  group by category\n) AS \"inputQuery-hU\")\n", "query_hash": "58551acc587eaa879c80b8d24b44fcd2"}, "categories_data": {"sql_string": "---- Data categories 1dt042y\nSELECT * FROM (select\n      category\n  from needful_things.orders\n  group by category\n) AS \"inputQuery-hU\"\n", "query_hash": "e350c9e77699ef2d0585b8c666374c58"}, "Dropdown-category_columns": {"sql_string": "---- Columns Dropdown-category (wwxqp3)\nDESCRIBE SELECT * FROM (SELECT DISTINCT category AS \"value\", category AS \"label\" FROM (SELECT * FROM (select\n      category\n  from needful_things.orders\n  group by category\n) AS \"inputQuery-hU\")\n) AS \"inputQuery-SO\"\n", "query_hash": "311bf7133a8d84a820b251247a8e1eb3"}, "Dropdown-category_length": {"sql_string": "---- Length Dropdown-category (wwxqp3)\nSELECT COUNT(*) as rowCount FROM (SELECT * FROM (SELECT DISTINCT category AS \"value\", category AS \"label\" FROM (SELECT * FROM (select\n      category\n  from needful_things.orders\n  group by category\n) AS \"inputQuery-hU\")\n) AS \"inputQuery-SO\")\n", "query_hash": "58551acc587eaa879c80b8d24b44fcd2"}, "Dropdown-category_data": {"sql_string": "---- Data Dropdown-category wwxqp3\nSELECT * FROM (SELECT DISTINCT category AS \"value\", category AS \"label\" FROM (SELECT * FROM (select\n      category\n  from needful_things.orders\n  group by category\n) AS \"inputQuery-hU\")\n) AS \"inputQuery-SO\"\n", "query_hash": "c3162de2a1dcc7c18f520d55ac5a6d8e"}, "orders_by_category_columns": {"sql_string": "---- Columns orders_by_category (vpxucl)\nDESCRIBE SELECT * FROM (select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '%'\n  and date_part('year', order_datetime) like '%'\n  group by all\n  order by sales_usd desc\n) AS \"inputQuery-Kj\"\n", "query_hash": "41e3fc7783ee65844fdc83769551d198"}, "orders_by_category_length": {"sql_string": "---- Length orders_by_category (vpxucl)\nSELECT COUNT(*) as rowCount FROM (SELECT * FROM (select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '%'\n  and date_part('year', order_datetime) like '%'\n  group by all\n  order by sales_usd desc\n) AS \"inputQuery-Kj\")\n", "query_hash": "ce2689e8d64b544591af02a62bdd9273"}, "orders_by_category_data": {"sql_string": "---- Data orders_by_category vpxucl\nSELECT * FROM (select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '%'\n  and date_part('year', order_datetime) like '%'\n  group by all\n  order by sales_usd desc\n) AS \"inputQuery-Kj\"\n", "query_hash": "76f344de4c6976c49ad9d64ffffec95b"}}