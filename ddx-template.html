<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="favicon.ico" sizes="32x32" />
    <link rel="icon" href="icon.svg" type="image/svg+xml" />
    <link rel="apple-touch-icon" href="apple-touch-icon.png" />
    <link rel="manifest" href="manifest.webmanifest" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <script>
        try {
            /** @type {'light' | 'dark' | 'system'} */
            const savedTheme = localStorage.getItem('evidence-theme') ?? 'system';
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const theme = savedTheme === 'system' ? (prefersDark ? 'dark' : 'light') : savedTheme;
            document.documentElement.classList.add(`theme-${theme}`);
        } catch (e) {}
    </script>
    <title>Domo Data Loader</title>
    <meta property="og:title" content="Domo Data Loader">
    <meta name="twitter:title" content="Domo Data Loader">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@evidence_dev">
</head>
<body>
    <script></script>
    <div>
        <div class="z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80"></div>
        <div data-sveltekit-preload-data="hover" class="antialiased">
            <header class="fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden">
                <div class="max-w-7xl mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between" style="max-width:undefinedpx">
                    <div class="flex gap-x-4 items-center">
                        <button type="button" class="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 md:hidden">
                            <span class="sr-only">Open sidebar</span>
                            <svg class="w-5 h-5" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M4 6l16 0"></path>
                                <path d="M4 12l16 0"></path>
                                <path d="M4 18l16 0"></path>
                            </svg>
                        </button>
                        <a href="/" class="text-sm font-bold text-base-content hidden md:block">
                            <img src="_app/_app/immutable/assets/wordmark-black.rfl-FBgf.png" alt="Home" class="h-5 aspect-auto block dark:hidden" href="/">
                            <img src="_app/_app/immutable/assets/wordmark-white.C8ZS96Ri.png" alt="Home" class="h-5 aspect-auto hidden dark:block" href="/">
                        </a>
                    </div>
                    <div class="flex gap-2 text-sm items-center">
                        <div class="flex gap-2 items-center"></div>
                        <div class="relative">
                            <button type="button" tabindex="0" aria-controls="kmd2uclcUd" aria-expanded="false" data-state="closed" id="ocPXKmnF80" data-melt-dropdown-menu-trigger="" data-menu-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 hover:text-base-content h-8 rounded-md text-xs px-1 hover:bg-base-200 shadow-base-200" aria-label="Menu" data-button-root="">
                                <svg class="h-6 w-6" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    <path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </header>
            <div class="max-w-7xl print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start" style="max-width:undefinedpx">
                <div class="print:hidden">
                    <aside class="w-48 flex-none hidden md:flex">
                        <div class="hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar">
                            <div class="flex flex-col pb-6">
                                <a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading" href="/">Home</a>
                            </div>
                        </div>
                        <div class="fixed bottom-0 text-xs py-2">
                            <a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a>
                        </div>
                    </aside>
                </div>
                <main class="md:pl-8 md:pr-8 mt-16 sm:mt-20 flex-grow overflow-x-hidden print:px-0 print:mt-8">
                    <div class="print:hidden">
                        <div class="flex items-start mt-0 whitespace-nowrap overflow-auto">
                            <div class="inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4">
                                <a href="/" class="hover:underline">Home</a>
                            </div>
                        </div>
                    </div>
                    <article id="evidence-main-article" class="select-text markdown pb-10">
                        <h1 class="title">Domo Data Loader</h1>
                        <p>Load datasets from Domo into DuckDB for analysis.</p>
                        
                        <!-- Domo Dataset Loader -->
                        <div id="workflow-picker-section" class="workflow-picker-section">

                            <div id="domo-workflow-picker" class="workflow-picker">
                                <div class="workflow-step">
                                    <label for="dataset-selector">Select Dataset:</label>
                                    <select id="dataset-selector" class="dataset-dropdown">
                                        <option value="">Choose a dataset...</option>
                                    </select>
                                </div>

                                <div id="dataset-preview" class="dataset-preview" style="display: none;">
                                    <h4>Dataset Information</h4>
                                    <div id="preview-content" class="preview-content">
                                        <div id="dataset-info" class="dataset-info"></div>

                                        <h5>Schema</h5>
                                        <div id="schema-table" class="schema-table"></div>

                                        <div class="preview-actions">
                                            <button id="preview-btn" class="btn btn-secondary">Preview Data</button>
                                        </div>

                                        <div id="data-preview" class="data-preview" style="display: none;"></div>
                                    </div>
                                </div>

                                <div id="loading-config" class="workflow-step" style="display: none;">
                                    <h4>Loading Configuration</h4>
                                    <div class="config-grid">
                                        <div class="config-item">
                                            <label for="table-name">Table Name in DuckDB:</label>
                                            <input id="table-name" type="text" placeholder="Enter table name" />
                                        </div>

                                        <div class="config-item">
                                            <label for="refresh-mode">Refresh Mode:</label>
                                            <select id="refresh-mode">
                                                <option value="replace">Replace existing data</option>
                                                <option value="append">Append to existing data</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div id="workflow-actions" class="workflow-actions" style="display: none;">
                                    <button id="load-dataset-btn" class="btn btn-primary">📊 Load Dataset into DuckDB</button>
                                </div>

                                <div id="loading-status" class="loading-status" style="display: none;">
                                    <div class="loading-spinner"></div>
                                    <p>Loading...</p>
                                </div>
                            </div>
                        </div>

                        <div id="loaded-datasets-section" style="display: none;">
                            <h2 class="markdown">Loaded Datasets</h2>
                            <div id="loaded-datasets-list"></div>
                        </div>
                    </article>
                </main>
                <div class="print:hidden">
                    <aside class="hidden lg:block w-48">
                        <div class="fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"></div>
                    </aside>
                </div>
            </div>
        </div>

        <!-- SplashScreen -->
        <div aria-disabled id="__evidence_project_splash" data-test-id="__evidence_project_splash" style="visibility: hidden">
            <svg width="100" height="100" viewBox="-8 -8 588 588" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.19462e-05 74.3583C109.309 74.3583 195.795 86.2578 286.834 37.825C377.872 -10.6077 466.416 1.29174 573.667 1.29175L573.667 126.549C466.416 126.549 377.373 114.91 286.834 163.082C196.294 211.254 109.309 199.615 6.11417e-05 199.615L7.19462e-05 74.3583Z" class="draw-path"/>
                <path d="M573.669 499.31C464.36 499.31 377.874 487.411 286.835 535.843C195.797 584.276 107.252 572.377 0.0014801 572.377V447.12C107.252 447.12 196.295 458.758 286.835 410.586C377.375 362.415 464.36 374.053 573.669 374.053V499.31Z" class="draw-path"/>
                <path d="M452.896 186.499C395.028 187.686 341.581 194.947 286.835 224.074C211.396 264.212 136.995 262.826 52.2355 261.247C35.2696 260.931 17.8887 260.608 0.0014801 260.608V385.865C18.1032 385.865 35.6721 386.204 52.81 386.534C137.212 388.162 211.162 389.589 286.835 349.331C341.838 320.07 395.18 312.831 452.896 311.685V186.499Z" class="draw-path"/>
            </svg>
        </div>
    </div>

    <script>
        // Simple workflow picker toggle
        function toggleWorkflowPicker() {
            const section = document.getElementById('workflow-picker-section');
            if (section.style.display === 'none') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }
    </script>

    <style>
        /* Evidence Dashboard Styles */
        #__evidence_project_splash {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .theme-dark #__evidence_project_splash {
            background-color: #000000;
        }

        .draw-path {
            fill: #000000;
            animation: blinking-logo 2s;
            animation-fill-mode: both;
            animation-iteration-count: infinite;
            animation-timing-function: ease-in-out;
        }

        .theme-dark .draw-path {
            fill: #ffffff;
        }

        @keyframes blinking-logo {
            0% { fill-opacity: 1; }
            50% { fill-opacity: 0.2; }
            100% { fill-opacity: 1; }
        }

        /* Domo Integration Styles */
        .dev-banner {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #f59e0b;
            border-radius: 12px;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            color: #92400e;
            text-align: center;
        }

        .dev-banner h3 {
            margin: 0 0 10px 0;
            color: #d97706;
        }

        .btn-primary, .btn-secondary {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            text-decoration: none;
            color: white;
        }

        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background-color: #e5e7eb;
            text-decoration: none;
            color: #374151;
        }

        .next-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .step {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .step h4 {
            margin: 0 0 10px 0;
            color: #1f2937;
            font-size: 16px;
        }

        .step p {
            margin: 0;
            color: #6b7280;
            line-height: 1.5;
        }

        /* Workflow Toggle */
        .workflow-toggle {
            margin: 30px 0;
            text-align: center;
        }

        .workflow-toggle button {
            margin: 0 10px;
        }

        /* Workflow Picker Section */
        .workflow-picker-section {
            margin: 30px 0;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background-color: #f9fafb;
        }

        .workflow-picker-section h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }

        .workflow-picker-section p {
            margin: 0 0 20px 0;
            color: #6b7280;
        }

        /* Workflow Picker Styles */
        .workflow-picker {
            max-width: 800px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .workflow-step {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: white;
        }

        .workflow-step label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .dataset-dropdown,
        .workflow-step input,
        .workflow-step select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }

        .dataset-dropdown:focus,
        .workflow-step input:focus,
        .workflow-step select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .dataset-preview {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: white;
        }

        .dataset-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            padding: 10px;
            background-color: #f3f4f6;
            border-radius: 4px;
        }

        .schema-table table,
        .data-preview table,
        .data-preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .schema-table th,
        .schema-table td,
        .data-preview th,
        .data-preview td,
        .data-preview-table th,
        .data-preview-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #e5e7eb;
        }

        .schema-table th,
        .data-preview th,
        .data-preview-table th {
            background-color: #f3f4f6;
            font-weight: 600;
        }

        .data-preview {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background-color: #f9fafb;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .workflow-actions {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin: 0 5px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background-color: #2563eb;
        }

        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover:not(:disabled) {
            background-color: #e5e7eb;
        }

        .loading-status {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
            text-align: center;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
