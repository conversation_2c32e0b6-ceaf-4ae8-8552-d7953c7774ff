import{s as Y,d as h,z as ne,i as k,e as Q,A as re,l as V,b as C,h as S,q as le,k as B,j as q,m as R,n as D,v as Z,B as ie,t as ae,w as oe,x as H,y as T,p as ue,C as fe,D as ce,r as N,E as me}from"../chunks/scheduler.CXt6djuF.js";import{S as $,i as x,t as E,a as p,g as O,c as j,d as z,m as A,b as I,e as P,f as L}from"../chunks/index.DP2zcclO.js";import{s as de,b as M,a as _e,g as he,E as W,c as F,d as be,D as ge,e as G}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.bgj4tQOV.js";import"../chunks/entry.Ba3I0i56.js";import{B as J}from"../chunks/Button.B-NRfsxT.js";function K(e){let t,s,n,a,r;return s=new J({props:{size:"sm",outline:!0,icon:e[1]?W:F,$$slots:{default:[ye]},$$scope:{ctx:e}}}),s.$on("click",e[12]),a=new J({props:{size:"sm",variant:"positive",outline:!0,icon:be,$$slots:{default:[we]},$$scope:{ctx:e}}}),a.$on("click",e[13]),{c(){t=R("div"),P(s.$$.fragment),n=D(),P(a.$$.fragment),this.h()},l(e){t=S(e,"DIV",{class:!0});var r=q(t);I(s.$$.fragment,r),n=B(r),I(a.$$.fragment,r),r.forEach(h),this.h()},h(){C(t,"class","absolute bottom-2 right-2 z-10 flex gap-2")},m(e,l){k(e,t,l),A(s,t,null),Q(t,n),A(a,t,null),r=!0},p(e,t){const n={};2&t&&(n.icon=e[1]?W:F),131074&t&&(n.$$scope={dirty:t,ctx:e}),s.$set(n);const r={};131072&t&&(r.$$scope={dirty:t,ctx:e}),a.$set(r)},i(e){r||(p(s.$$.fragment,e),p(a.$$.fragment,e),r=!0)},o(e){E(s.$$.fragment,e),E(a.$$.fragment,e),r=!1},d(e){e&&h(t),z(s),z(a)}}}function pe(e){let t;return{c(){t=T("Show Results")},l(e){t=H(e,"Show Results")},m(e,s){k(e,t,s)},d(e){e&&h(t)}}}function ke(e){let t;return{c(){t=T("Hide Results")},l(e){t=H(e,"Hide Results")},m(e,s){k(e,t,s)},d(e){e&&h(t)}}}function ye(e){let t;function s(e,t){return e[1]?ke:pe}let n=s(e),a=n(e);return{c(){a.c(),t=N()},l(e){a.l(e),t=N()},m(e,s){a.m(e,s),k(e,t,s)},p(e,r){n!==(n=s(e))&&(a.d(1),a=n(e),a&&(a.c(),a.m(t.parentNode,t)))},d(e){e&&h(t),a.d(e)}}}function we(e){let t;return{c(){t=T("Submit")},l(e){t=H(e,"Submit")},m(e,s){k(e,t,s)},d(e){e&&h(t)}}}function U(e){let t,s,n=e[9].error+"";return{c(){t=R("pre"),s=T(n),this.h()},l(e){t=S(e,"PRE",{class:!0});var a=q(t);s=H(a,n),a.forEach(h),this.h()},h(){C(t,"class","text-negative text-xs font-mono")},m(e,n){k(e,t,n),Q(t,s)},p(e,t){512&t&&n!==(n=e[9].error+"")&&oe(s,n)},d(e){e&&h(t)}}}function X(e){let t,s,n,a;return s=new ge({props:{data:e[9]}}),{c(){t=R("div"),P(s.$$.fragment)},l(e){t=S(e,"DIV",{});var n=q(t);I(s.$$.fragment,n),n.forEach(h)},m(e,n){k(e,t,n),A(s,t,null),a=!0},p(e,t){const n={};512&t&&(n.data=e[9]),s.$set(n)},i(e){a||(p(s.$$.fragment,e),e&&ue((()=>{a&&(n||(n=L(t,G,{},!0)),n.run(1))})),a=!0)},o(e){E(s.$$.fragment,e),e&&(n||(n=L(t,G,{},!1)),n.run(0)),a=!1},d(e){e&&h(t),z(s),e&&n&&n.end()}}}function Ee(e){let t,s,n,a,r,l,o,i,c,u,d="SQL Console",m=e[9].error&&!e[2]&&!!e[4],$=!e[3]&&K(e),f=m&&U(e),g=e[1]&&X(e);return{c(){t=R("h1"),t.textContent=d,s=D(),n=R("section"),a=R("div"),$&&$.c(),l=D(),f&&f.c(),o=D(),g&&g.c(),this.h()},l(e){t=S(e,"H1",{class:!0,"data-svelte-h":!0}),"svelte-7ylf69"!==le(t)&&(t.textContent=d),s=B(e),n=S(e,"SECTION",{class:!0,role:!0});var r=q(n);a=S(r,"DIV",{class:!0});var i=q(a);$&&$.l(i),i.forEach(h),l=B(r),f&&f.l(r),o=B(r),g&&g.l(r),r.forEach(h),this.h()},h(){C(t,"class","markdown"),C(a,"class","w-full relative rounded-sm border border-base-300 min-h-[8rem] cursor-text **:[&.cm-editor]:min-h-[8rem] **:[&.cm-editor]:rounded-sm"),C(n,"class","px-0 py-2 flex flex-col gap-2 min-h-[8rem]"),C(n,"role","none")},m(d,h){k(d,t,h),k(d,s,h),k(d,n,h),Q(n,a),$&&$.m(a,null),e[14](a),Q(n,l),f&&f.m(n,null),Q(n,o),g&&g.m(n,null),i=!0,c||(u=[re(r=de.call(null,a,{...e[5],theme:e[8]})),V(n,"click",e[15]),V(n,"keydown",e[16])],c=!0)},p(e,[t]){e[3]?$&&(O(),E($,1,1,(()=>{$=null})),j()):$?($.p(e,t),8&t&&p($,1)):($=K(e),$.c(),p($,1),$.m(a,null)),r&&me(r.update)&&288&t&&r.update.call(null,{...e[5],theme:e[8]}),532&t&&(m=e[9].error&&!e[2]&&!!e[4]),m?f?f.p(e,t):(f=U(e),f.c(),f.m(n,o)):f&&(f.d(1),f=null),e[1]?g?(g.p(e,t),2&t&&p(g,1)):(g=X(e),g.c(),p(g,1),g.m(n,null)):g&&(O(),E(g,1,1,(()=>{g=null})),j())},i(e){i||(p($),p(g),i=!0)},o(e){E($),E(g),i=!1},d(a){a&&(h(t),h(s),h(n)),$&&$.d(),e[14](null),f&&f.d(),g&&g.d(),c=!1,ne(u)}}}function Ce(e,t,s){let n,a,r=Z,l=()=>(r(),r=fe(p,(e=>s(9,a=e))),p);e.$$.on_destroy.push((()=>r()));let o,i,{hideErrors:c=!1}=t,{initialQuery:u="select 'ABC' as category, 123 as num, 26400000 as sales_usd"}=t,{showResults:d=!0}=t,{disabled:h=!1}=t,m=u,$=m,{data:p=M(m)}=t;l(),ie((async()=>{p&&p.fetch(),s(5,i={initialState:u,disabled:h,schema:await _e(),onChange:e=>{e.docChanged&&$.trim()!==e.state.doc.toString().trim()&&s(6,$=e.state.doc.toString())},onSubmit:()=>(s(4,m=$.trim()),m.endsWith(";")&&s(4,m=m.substring(0,m.length-1)),s(1,d=!0),!0)})}));const{theme:f}=he();return ae(e,f,(e=>s(8,n=e))),e.$$set=e=>{"hideErrors"in e&&s(2,c=e.hideErrors),"initialQuery"in e&&s(11,u=e.initialQuery),"showResults"in e&&s(1,d=e.showResults),"disabled"in e&&s(3,h=e.disabled),"data"in e&&l(s(0,p=e.data))},e.$$.update=()=>{17&e.$$.dirty&&m&&(l(s(0,p=M(m))),p.fetch()),40&e.$$.dirty&&i&&s(5,i.disabled=h,i)},[p,d,c,h,m,i,$,o,n,a,f,u,()=>s(1,d=!d),()=>{s(4,m=$),s(1,d=!0)},function(e){ce[e?"unshift":"push"]((()=>{o=e,s(7,o)}))},()=>null==o?void 0:o.focus(),e=>"Enter"===e.key&&(null==o?void 0:o.focus())]}class Se extends ${constructor(e){super(),x(this,e,Ce,Ee,Y,{hideErrors:2,initialQuery:11,showResults:1,disabled:3,data:0})}}function Re(e){let t,s;return t=new Se({}),{c(){P(t.$$.fragment)},l(e){I(t.$$.fragment,e)},m(e,n){A(t,e,n),s=!0},p:Z,i(e){s||(p(t.$$.fragment,e),s=!0)},o(e){E(t.$$.fragment,e),s=!1},d(e){z(t,e)}}}class ze extends ${constructor(e){super(),x(this,e,null,Re,Y,{})}}export{ze as component};