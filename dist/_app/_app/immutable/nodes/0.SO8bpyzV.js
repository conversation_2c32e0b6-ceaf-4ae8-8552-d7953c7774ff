/*! For license information please see 0.SO8bpyzV.js.LICENSE.txt */
var Eg=Object.defineProperty,Lc=t=>{throw TypeError(t)},Ag=(t,e,n)=>e in t?Eg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Ya=(t,e,n)=>Ag(t,"symbol"!=typeof e?e+"":e,n),Rc=(t,e,n)=>e.has(t)||Lc("Cannot "+n),Dr=(t,e,n)=>(Rc(t,e,"read from private field"),n?n.call(t):e.get(t)),eo=(t,e,n)=>e.has(t)?Lc("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),Ka=(t,e,n,r)=>(Rc(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),Uc=(t,e,n,r)=>({set _(r){Ka(t,e,r,n)},get _(){return Dr(t,e,r)}});import{cl as Gn,cm as Wt,cn as Cs,co as Ti,cp as Mt,cq as E,cr as zr,cs as Tg,ct as Bg,b4 as xt,cu as uh,cv as Mg,cw as Pg,cx as Ng,cy as Cg,w as Xe,t as Fs,F as Vc,$ as xc,v as dn,a0 as Wn,K as or,L as Gt,p as Ct,ba as zc,O as rn,a1 as Fg,N as no,H as jg,cz as _l,a2 as Lg,n as ro,bb as sr,m as ki,_ as Wc,cA as Wr,G as js,J as $a,B as fh,l as mi,a3 as Hc,a4 as qc,a6 as Rg,S as Ug,R as Vg,U as xg,a7 as zg,V as Jl,cB as Wg,x as An,bm as bl,cb as ei,h as ue,u as dh,o as Hg,cC as Yc,A as Ql,y as Fi,am as qg,aA as Kc,g as hh,cD as Yg,cE as Kg,I as Tn,cF as $g,cG as Js,cH as Jg,f as xo,cI as Qg,cJ as Gg,cK as Xg,cL as Zg,cM as mh,cN as t_,cO as ph,cP as e_,cQ as n_,cR as r_,b1 as i_,cS as yl,aZ as $c,ch as o_,cT as s_}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.nOdVbBr4.js";import{p as Io,s as a_,c as l_,h as c_,B as u_}from"../chunks/button.BBPrIXH5.js";import{L as _s,a4 as gh,a5 as f_,s as ee,d as S,i as R,r as ft,P as ln,t as Ae,Q as me,R as ri,c as Oe,u as ke,g as De,a as Ee,o as lr,D as Kn,z as wr,S as Jc,A as Rr,l as he,h as q,j as $,m as Y,a6 as $e,p as On,w as Le,b as N,e as z,k as nt,x as vt,n as rt,y as wt,al as d_,v as Qt,as as bs,B as Pr,N as Qs,q as yr,K as h_,ae as kn,J as Nr,ad as m_,C as p_,ab as _h}from"../chunks/scheduler.CXt6djuF.js";import{S as ae,i as le,t as D,a as O,g as jt,c as Lt,f as In,h as es,j as Hi,d as st,m as at,b as lt,e as ct,k as bh}from"../chunks/index.DP2zcclO.js";import{w as qe,d as fo,b as g_,c as __}from"../chunks/entry.Dyop2LGn.js";import{p as Ma,n as yh}from"../chunks/stores.CV4JZkJh.js";import{d as vl}from"../chunks/index.rV6zwFgL.js";import{c as b_,h as Ge,g as y_,b as v_,r as ai,a as w_,G as S_,X as I_,B as O_,S as k_}from"../chunks/index.DzsjtLAS.js";import{r as D_}from"../chunks/scroll.DWlsEOxT.js";import{A as E_,a as Ja}from"../chunks/AccordionItem.hRBgbSKG.js";const A_=new TextDecoder("utf-8"),wl=t=>A_.decode(t),T_=new TextEncoder,Gl=t=>T_.encode(t),B_=t=>"number"==typeof t,M_=t=>"boolean"==typeof t,Ze=t=>"function"==typeof t,Ln=t=>null!=t&&Object(t)===t,zo=t=>Ln(t)&&Ze(t.then),Pa=t=>Ln(t)&&Ze(t[Symbol.iterator]),Xl=t=>Ln(t)&&Ze(t[Symbol.asyncIterator]),Sl=t=>Ln(t)&&Ln(t.schema),vh=t=>Ln(t)&&"done"in t&&"value"in t,wh=t=>Ln(t)&&Ze(t.stat)&&B_(t.fd),Sh=t=>Ln(t)&&Zl(t.body),Ih=t=>"_getDOMStream"in t&&"_getNodeStream"in t,Zl=t=>Ln(t)&&Ze(t.cancel)&&Ze(t.getReader)&&!Ih(t),Oh=t=>Ln(t)&&Ze(t.read)&&Ze(t.pipe)&&M_(t.readable)&&!Ih(t),P_=t=>Ln(t)&&Ze(t.clear)&&Ze(t.bytes)&&Ze(t.position)&&Ze(t.setPosition)&&Ze(t.capacity)&&Ze(t.getBufferIdentifier)&&Ze(t.createLong),tc=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function N_(t){const e=t[0]?[t[0]]:[];let n,r,i,o;for(let s,a,l=0,c=0,u=t.length;++l<u;)s=e[c],a=t[l],!s||!a||s.buffer!==a.buffer||a.byteOffset<s.byteOffset?a&&(e[++c]=a):(({byteOffset:n,byteLength:i}=s),({byteOffset:r,byteLength:o}=a),n+i<r||r+o<n?a&&(e[++c]=a):e[c]=new Uint8Array(s.buffer,n,r-n+o));return e}function Qc(t,e,n=0,r=e.byteLength){const i=t.byteLength,o=new Uint8Array(t.buffer,t.byteOffset,i),s=new Uint8Array(e.buffer,e.byteOffset,Math.min(r,i));return o.set(s,n),t}function er(t,e){const n=N_(t),r=n.reduce(((t,e)=>t+e.byteLength),0);let i,o,s,a=0,l=-1;const c=Math.min(e||Number.POSITIVE_INFINITY,r);for(const t=n.length;++l<t;){if(i=n[l],o=i.subarray(0,Math.min(i.length,c-a)),c<=a+o.length){o.length<i.length?n[l]=i.subarray(o.length):o.length===i.length&&l++,s?Qc(s,o,a):s=o;break}Qc(s||(s=new Uint8Array(c)),o,a),a+=o.length}return[s||new Uint8Array(0),n.slice(l),r-(s?s.byteLength:0)]}function se(t,e){let n=vh(e)?e.value:e;return n instanceof t?t===Uint8Array?new t(n.buffer,n.byteOffset,n.byteLength):n:n?("string"==typeof n&&(n=Gl(n)),n instanceof ArrayBuffer||n instanceof tc?new t(n):P_(n)?se(t,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new t(0):new t(n.buffer,n.byteOffset,n.byteLength/t.BYTES_PER_ELEMENT):t.from(n)):new t(0)}const io=t=>se(Int32Array,t),Gc=t=>se(BigInt64Array,t),Jt=t=>se(Uint8Array,t),Il=t=>(t.next(),t);function*C_(t,e){const n=function*(t){yield t},r="string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof tc?n(e):Pa(e)?e:n(e);return yield*Il(function*(e){let n=null;do{n=e.next(yield se(t,n))}while(!n.done)}(r[Symbol.iterator]())),new t}const F_=t=>C_(Uint8Array,t);function kh(t,e){return Gn(this,arguments,(function*(){if(zo(e))return yield Wt(yield Wt(yield*Cs(Ti(kh(t,yield Wt(e))))));const n=function(t){return Gn(this,arguments,(function*(){yield yield Wt(yield Wt(t))}))},r="string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof tc?n(e):Pa(e)?function(t){return Gn(this,arguments,(function*(){yield Wt(yield*Cs(Ti(Il(function*(t){let e=null;do{e=t.next(yield null==e?void 0:e.value)}while(!e.done)}(t[Symbol.iterator]())))))}))}(e):Xl(e)?e:n(e);return yield Wt(yield*Cs(Ti(Il(function(e){return Gn(this,arguments,(function*(){let n=null;do{n=yield Wt(e.next(yield yield Wt(se(t,n))))}while(!n.done)}))}(r[Symbol.asyncIterator]()))))),yield Wt(new t)}))}const j_=t=>kh(Uint8Array,t);function L_(t,e){let n=0;const r=t.length;if(r!==e.length)return!1;if(r>0)do{if(t[n]!==e[n])return!1}while(++n<r);return!0}const yn={fromIterable:t=>ys(R_(t)),fromAsyncIterable:t=>ys(U_(t)),fromDOMStream:t=>ys(V_(t)),fromNodeStream:t=>ys(z_(t)),toDOMStream(t,e){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(t,e){throw new Error('"toNodeStream" not available in this environment')}},ys=t=>(t.next(),t);function*R_(t){let e,n,r,i,o=!1,s=[],a=0;({cmd:r,size:i}=(yield null)||{cmd:"read",size:0});const l=F_(t)[Symbol.iterator]();try{do{if(({done:e,value:n}=Number.isNaN(i-a)?l.next():l.next(i-a)),!e&&n.byteLength>0&&(s.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield"peek"===r?er(s,i)[0]:([n,s,a]=er(s,i),n))}while(i<a)}while(!e)}catch(t){(o=!0)&&"function"==typeof l.throw&&l.throw(t)}finally{!1===o&&"function"==typeof l.return&&l.return(null)}return null}function U_(t){return Gn(this,arguments,(function*(){let e,n,r,i,o=!1,s=[],a=0;({cmd:r,size:i}=(yield yield Wt(null))||{cmd:"read",size:0});const l=j_(t)[Symbol.asyncIterator]();try{do{if(({done:e,value:n}=Number.isNaN(i-a)?yield Wt(l.next()):yield Wt(l.next(i-a))),!e&&n.byteLength>0&&(s.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield yield Wt("peek"===r?er(s,i)[0]:([n,s,a]=er(s,i),n)))}while(i<a)}while(!e)}catch(t){(o=!0)&&"function"==typeof l.throw&&(yield Wt(l.throw(t)))}finally{!1===o&&"function"==typeof l.return&&(yield Wt(l.return(new Uint8Array(0))))}return yield Wt(null)}))}function V_(t){return Gn(this,arguments,(function*(){let e,n,r,i=!1,o=!1,s=[],a=0;({cmd:n,size:r}=(yield yield Wt(null))||{cmd:"read",size:0});const l=new x_(t);try{do{if(({done:i,value:e}=Number.isNaN(r-a)?yield Wt(l.read()):yield Wt(l.read(r-a))),!i&&e.byteLength>0&&(s.push(Jt(e)),a+=e.byteLength),i||r<=a)do{({cmd:n,size:r}=yield yield Wt("peek"===n?er(s,r)[0]:([e,s,a]=er(s,r),e)))}while(r<a)}while(!i)}catch(t){(o=!0)&&(yield Wt(l.cancel(t)))}finally{!1===o?yield Wt(l.cancel()):t.locked&&l.releaseLock()}return yield Wt(null)}))}class x_{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch((()=>{}))}get closed(){return this.reader?this.reader.closed.catch((()=>{})):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return Mt(this,void 0,void 0,(function*(){const{reader:e,source:n}=this;e&&(yield e.cancel(t).catch((()=>{}))),n&&n.locked&&this.releaseLock()}))}read(t){return Mt(this,void 0,void 0,(function*(){if(0===t)return{done:null==this.reader,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=Jt(e)),e}))}}const Qa=(t,e)=>{const n=t=>r([e,t]);let r;return[e,n,new Promise((i=>(r=i)&&t.once(e,n)))]};function z_(t){return Gn(this,arguments,(function*(){const e=[];let n,r,i,o="error",s=!1,a=null,l=0,c=[];if(({cmd:n,size:r}=(yield yield Wt(null))||{cmd:"read",size:0}),t.isTTY)return yield yield Wt(new Uint8Array(0)),yield Wt(null);try{e[0]=Qa(t,"end"),e[1]=Qa(t,"error");do{if(e[2]=Qa(t,"readable"),[o,a]=yield Wt(Promise.race(e.map((t=>t[2])))),"error"===o)break;if((s="end"===o)||(Number.isFinite(r-l)?(i=Jt(t.read(r-l)),i.byteLength<r-l&&(i=Jt(t.read()))):i=Jt(t.read()),i.byteLength>0&&(c.push(i),l+=i.byteLength)),s||r<=l)do{({cmd:n,size:r}=yield yield Wt("peek"===n?er(c,r)[0]:([i,c,l]=er(c,r),i)))}while(r<l)}while(!s)}finally{yield Wt((u=e,d="error"===o?a:null,i=c=null,new Promise(((e,n)=>{for(const[e,n]of u)t.off(e,n);try{const e=t.destroy;e&&e.call(t,d),d=void 0}catch(t){d=t||d}finally{null!=d?n(d):e()}}))))}var u,d;return yield Wt(null)}))}var Ce,cn,Ye,Dn,yt,nr;!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(Ce||(Ce={})),function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(cn||(cn={})),function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(Ye||(Ye={})),function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(Dn||(Dn={})),function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(yt||(yt={})),function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(nr||(nr={}));const Ga=2,$n=4,fr=4,ne=4,Br=new Int32Array(2),Xc=new Float32Array(Br.buffer),Zc=new Float64Array(Br.buffer),vs=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0];var Ol;!function(t){t[t.UTF8_BYTES=1]="UTF8_BYTES",t[t.UTF16_STRING=2]="UTF16_STRING"}(Ol||(Ol={}));let ji=class t{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(e){return new t(new Uint8Array(e))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return Br[0]=this.readInt32(t),Xc[0]}readFloat64(t){return Br[vs?0:1]=this.readInt32(t),Br[vs?1:0]=this.readInt32(t+4),Zc[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){Xc[0]=e,this.writeInt32(t,Br[0])}writeFloat64(t,e){Zc[0]=e,this.writeInt32(t,Br[vs?0:1]),this.writeInt32(t+4,Br[vs?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+4+4)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<4;e++)t+=String.fromCharCode(this.readInt8(this.position_+4+e));return t}__offset(t,e){const n=t-this.readInt32(t);return e<this.readInt16(n)?this.readInt16(n+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const n=this.readInt32(t);t+=4;const r=this.bytes_.subarray(t,t+n);return e===Ol.UTF8_BYTES?r:this.text_decoder_.decode(r)}__union_with_string(t,e){return"string"==typeof t?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+4}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=0;e<4;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+4+e))return!1;return!0}createScalarList(t,e){const n=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&n.push(e)}return n}createObjList(t,e){const n=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&n.push(e.unpack())}return n}},Eh=class t{constructor(t){let e;this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder,e=t||1024,this.bb=ji.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(e,n){e>this.minalign&&(this.minalign=e);const r=1+~(this.bb.capacity()-this.space+n)&e-1;for(;this.space<r+e+n;){const e=this.bb.capacity();this.bb=t.growByteBuffer(this.bb),this.space+=this.bb.capacity()-e}this.pad(r)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,n){(this.force_defaults||e!=n)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,n){(this.force_defaults||e!=n)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,n){(this.force_defaults||e!=n)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,n){(this.force_defaults||e!==n)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,n){(this.force_defaults||e!=n)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,n){e!=n&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){null!==this.vtable&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const n=e<<1,r=ji.allocate(n);return r.setPosition(n-e),r.bytes().set(t.bytes(),n-e),r}addOffset(t){this.prep(4,0),this.writeInt32(this.offset()-t+4)}startObject(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&0==this.vtable[e];e--);const n=e+1;for(;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);this.addInt16(t-this.object_start);const r=2*(n+2);this.addInt16(r);let i=0;const o=this.space;t:for(e=0;e<this.vtables.length;e++){const t=this.bb.capacity()-this.vtables[e];if(r==this.bb.readInt16(t)){for(let e=2;e<r;e+=2)if(this.bb.readInt16(o+e)!=this.bb.readInt16(t+e))continue t;i=this.vtables[e];break}}return i?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,i-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,n){const r=n?4:0;if(e){const t=e;if(this.prep(this.minalign,8+r),4!=t.length)throw new TypeError("FlatBuffers: file identifier must be length 4");for(let e=3;e>=0;e--)this.writeInt8(t.charCodeAt(e))}this.prep(this.minalign,4+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const n=this.bb.capacity()-t,r=n-this.bb.readInt32(n);if(!(e<this.bb.readInt16(r)&&0!=this.bb.readInt16(r+e)))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,n){this.notNested(),this.vector_num_elems=e,this.prep(4,t*e),this.prep(n,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(null==t)return 0;let e;return e=t instanceof Uint8Array?t:this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return null==t?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return null===t?0:"string"==typeof t?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let n=0;n<t.length;++n){const r=t[n];if(null===r)throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.");e.push(this.createObjectOffset(r))}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};var Gs,Xs;!function(t){t[t.BUFFER=0]="BUFFER"}(Gs||(Gs={})),function(t){t[t.LZ4_FRAME=0]="LZ4_FRAME",t[t.ZSTD=1]="ZSTD"}(Xs||(Xs={}));class Mr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Mr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+4),(e||new Mr).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):Xs.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):Gs.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,Xs.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,Gs.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,n){return Mr.startBodyCompression(t),Mr.addCodec(t,e),Mr.addMethod(t,n),Mr.endBodyCompression(t)}}class Th{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,n){return t.prep(8,16),t.writeInt64(BigInt(n??0)),t.writeInt64(BigInt(e??0)),t.offset()}}let Bh=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,n){return t.prep(8,16),t.writeInt64(BigInt(n??0)),t.writeInt64(BigInt(e??0)),t.offset()}},cr=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsRecordBatch(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}nodes(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new Bh).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Th).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Mr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}},pi=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDictionaryBatch(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new cr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}};var Li,Zs;!function(t){t[t.Little=0]="Little",t[t.Big=1]="Big"}(Li||(Li={})),function(t){t[t.DenseArray=0]="DenseArray"}(Zs||(Zs={}));class mn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new mn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+4),(e||new mn).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,n){return mn.startInt(t),mn.addBitWidth(t,e),mn.addIsSigned(t,n),mn.endInt(t)}}class dr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new dr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+4),(e||new dr).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new mn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):Zs.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,Zs.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class je{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new je).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+4),(e||new je).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,n){return je.startKeyValue(t),je.addKey(t,e),je.addValue(t,n),je.endKeyValue(t)}}let tu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBinary(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(e){return t.startBinary(e),t.endBinary(e)}},eu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBool(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(e){return t.startBool(e),t.endBool(e)}},Ls=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDate(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dn.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,Dn.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(e,n){return t.startDate(e),t.addUnit(e,n),t.endDate(e)}},_i=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDecimal(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(e,n,r,i){return t.startDecimal(e),t.addPrecision(e,n),t.addScale(e,r),t.addBitWidth(e,i),t.endDecimal(e)}},Rs=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDuration(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static endDuration(t){return t.endObject()}static createDuration(e,n){return t.startDuration(e),t.addUnit(e,n),t.endDuration(e)}},Us=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeBinary(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(e,n){return t.startFixedSizeBinary(e),t.addByteWidth(e,n),t.endFixedSizeBinary(e)}},Vs=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeList(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(e,n){return t.startFixedSizeList(e),t.addListSize(e,n),t.endFixedSizeList(e)}};class Jn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Jn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+4),(e||new Jn).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ye.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,Ye.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Jn.startFloatingPoint(t),Jn.addPrecision(t,e),Jn.endFloatingPoint(t)}}class Qn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Qn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+4),(e||new Qn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):nr.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,nr.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Qn.startInterval(t),Qn.addUnit(t,e),Qn.endInterval(t)}}let nu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsLargeBinary(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){return t.endObject()}static createLargeBinary(e){return t.startLargeBinary(e),t.endLargeBinary(e)}},ru=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsLargeUtf8(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){return t.endObject()}static createLargeUtf8(e){return t.startLargeUtf8(e),t.endLargeUtf8(e)}},iu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsList(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(e){return t.startList(e),t.endList(e)}},xs=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMap(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(e,n){return t.startMap(e),t.addKeysSorted(e,n),t.endMap(e)}},ou=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsNull(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(e){return t.startNull(e),t.endNull(e)}};class ti{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new ti).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+4),(e||new ti).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return ti.startStruct_(t),ti.endStruct_(t)}}class vn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new vn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+4),(e||new vn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,n){return vn.startTime(t),vn.addUnit(t,e),vn.addBitWidth(t,n),vn.endTime(t)}}class wn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new wn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+4),(e||new wn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,n){return wn.startTimestamp(t),wn.addUnit(t,e),wn.addTimezone(t,n),wn.endTimestamp(t)}}class sn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new sn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+4),(e||new sn).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):cn.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,cn.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,n){return sn.startUnion(t),sn.addMode(t,e),sn.addTypeIds(t,n),sn.endUnion(t)}}let su=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsUtf8(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(e){return t.startUtf8(e),t.endUtf8(e)}};var de;!function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.FloatingPoint=3]="FloatingPoint",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct_=13]="Struct_",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.LargeList=21]="LargeList",t[t.RunEndEncoded=22]="RunEndEncoded"}(de||(de={}));let bn=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsField(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):de.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new dr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(e,n){const r=this.bb.__offset(this.bb_pos,14);return r?(n||new t).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,16);return n?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,de.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}},qn=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsSchema(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Li.Little}fields(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new bn).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):BigInt(0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Li.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(e,n,r,i,o){return t.startSchema(e),t.addEndianness(e,n),t.addFields(e,r),t.addCustomMetadata(e,i),t.addFeatures(e,o),t.endSchema(e)}};var Zt;!function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(Zt||(Zt={}));const W_=void 0;function Wo(t){if(null===t)return"null";if(t===W_)return"undefined";switch(typeof t){case"number":case"bigint":return`${t}`;case"string":return`"${t}"`}return"function"==typeof t[Symbol.toPrimitive]?t[Symbol.toPrimitive]("string"):ArrayBuffer.isView(t)?t instanceof BigInt64Array||t instanceof BigUint64Array?`[${[...t].map((t=>Wo(t)))}]`:`[${t}]`:ArrayBuffer.isView(t)?`[${t}]`:JSON.stringify(t,((t,e)=>"bigint"==typeof e?`${e}`:e))}function _e(t){if("bigint"==typeof t&&(t<Number.MIN_SAFE_INTEGER||t>Number.MAX_SAFE_INTEGER))throw new TypeError(`${t} is not safe to convert to a number.`);return Number(t)}function Mh(t,e){return _e(t/e)+_e(t%e)/_e(e)}const H_=Symbol.for("isArrowBigNum");function Un(t,...e){return 0===e.length?Object.setPrototypeOf(se(this.TypedArray,t),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(t,...e),this.constructor.prototype)}function Bi(...t){return Un.apply(this,t)}function Mi(...t){return Un.apply(this,t)}function Ho(...t){return Un.apply(this,t)}Un.prototype[H_]=!0,Un.prototype.toJSON=function(){return`"${qo(this)}"`},Un.prototype.valueOf=function(t){return Ph(this,t)},Un.prototype.toString=function(){return qo(this)},Un.prototype[Symbol.toPrimitive]=function(t="default"){switch(t){case"number":return Ph(this);case"string":return qo(this);case"default":return K_(this)}return qo(this)},Object.setPrototypeOf(Bi.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(Mi.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(Ho.prototype,Object.create(Uint32Array.prototype)),Object.assign(Bi.prototype,Un.prototype,{constructor:Bi,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array}),Object.assign(Mi.prototype,Un.prototype,{constructor:Mi,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array}),Object.assign(Ho.prototype,Un.prototype,{constructor:Ho,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const q_=BigInt(4294967296)*BigInt(4294967296),Y_=q_-BigInt(1);function Ph(t,e){const{buffer:n,byteOffset:r,byteLength:i,signed:o}=t,s=new BigUint64Array(n,r,i/8),a=o&&s.at(-1)&BigInt(1)<<BigInt(63);let l=BigInt(0),c=0;if(a){for(const t of s)l|=(t^Y_)*(BigInt(1)<<BigInt(64*c++));l*=BigInt(-1),l-=BigInt(1)}else for(const t of s)l|=t*(BigInt(1)<<BigInt(64*c++));if("number"==typeof e){const t=BigInt(Math.pow(10,e)),n=l%t;return _e(l/t)+_e(n)/_e(t)}return _e(l)}function qo(t){if(8===t.byteLength)return`${new t.BigIntArray(t.buffer,t.byteOffset,1)[0]}`;if(!t.signed)return Xa(t);let e=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);if(new Int16Array([e.at(-1)])[0]>=0)return Xa(t);e=e.slice();let n=1;for(let t=0;t<e.length;t++){const r=e[t],i=~r+n;e[t]=i,n&=0===r?1:0}return`-${Xa(e)}`}function K_(t){return 8===t.byteLength?new t.BigIntArray(t.buffer,t.byteOffset,1)[0]:qo(t)}function Xa(t){let e="";const n=new Uint32Array(2);let r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let o=-1;const s=r.length-1;do{for(n[0]=r[o=0];o<s;)r[o++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[o];r[o]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],e=`${n[0]}${e}`}while(i[0]||i[1]||i[2]||i[3]);return e??"0"}class ec{static new(t,e){switch(e){case!0:return new Bi(t);case!1:return new Mi(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new Bi(t)}return 16===t.byteLength?new Ho(t):new Mi(t)}static signed(t){return new Bi(t)}static unsigned(t){return new Mi(t)}static decimal(t){return new Ho(t)}constructor(t,e){return ec.new(t,e)}}var Nh,Ch,Fh,jh,Lh,Rh,Uh,Vh,xh,zh,Wh,Hh,qh,Yh,Kh,$h,Jh,Qh,Gh,Xh,Zh,tm;class ut{static isNull(t){return(null==t?void 0:t.typeId)===E.Null}static isInt(t){return(null==t?void 0:t.typeId)===E.Int}static isFloat(t){return(null==t?void 0:t.typeId)===E.Float}static isBinary(t){return(null==t?void 0:t.typeId)===E.Binary}static isLargeBinary(t){return(null==t?void 0:t.typeId)===E.LargeBinary}static isUtf8(t){return(null==t?void 0:t.typeId)===E.Utf8}static isLargeUtf8(t){return(null==t?void 0:t.typeId)===E.LargeUtf8}static isBool(t){return(null==t?void 0:t.typeId)===E.Bool}static isDecimal(t){return(null==t?void 0:t.typeId)===E.Decimal}static isDate(t){return(null==t?void 0:t.typeId)===E.Date}static isTime(t){return(null==t?void 0:t.typeId)===E.Time}static isTimestamp(t){return(null==t?void 0:t.typeId)===E.Timestamp}static isInterval(t){return(null==t?void 0:t.typeId)===E.Interval}static isDuration(t){return(null==t?void 0:t.typeId)===E.Duration}static isList(t){return(null==t?void 0:t.typeId)===E.List}static isStruct(t){return(null==t?void 0:t.typeId)===E.Struct}static isUnion(t){return(null==t?void 0:t.typeId)===E.Union}static isFixedSizeBinary(t){return(null==t?void 0:t.typeId)===E.FixedSizeBinary}static isFixedSizeList(t){return(null==t?void 0:t.typeId)===E.FixedSizeList}static isMap(t){return(null==t?void 0:t.typeId)===E.Map}static isDictionary(t){return(null==t?void 0:t.typeId)===E.Dictionary}static isDenseUnion(t){return ut.isUnion(t)&&t.mode===cn.Dense}static isSparseUnion(t){return ut.isUnion(t)&&t.mode===cn.Sparse}constructor(t){this.typeId=t}}Nh=Symbol.toStringTag,ut[Nh]=(t=>(t.children=null,t.ArrayType=Array,t.OffsetArrayType=Int32Array,t[Symbol.toStringTag]="DataType"))(ut.prototype);class Cr extends ut{constructor(){super(E.Null)}toString(){return"Null"}}Ch=Symbol.toStringTag,Cr[Ch]=Cr.prototype[Symbol.toStringTag]="Null";class ni extends ut{constructor(t,e){super(E.Int),this.isSigned=t,this.bitWidth=e}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}Fh=Symbol.toStringTag,ni[Fh]=(t=>(t.isSigned=null,t.bitWidth=null,t[Symbol.toStringTag]="Int"))(ni.prototype);class Yo extends ni{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Yo.prototype,"ArrayType",{value:Int32Array});class ta extends ut{constructor(t){super(E.Float),this.precision=t}get ArrayType(){switch(this.precision){case Ye.HALF:return Uint16Array;case Ye.SINGLE:return Float32Array;case Ye.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}jh=Symbol.toStringTag,ta[jh]=(t=>(t.precision=null,t[Symbol.toStringTag]="Float"))(ta.prototype);class ea extends ut{constructor(){super(E.Binary)}toString(){return"Binary"}}Lh=Symbol.toStringTag,ea[Lh]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Binary"))(ea.prototype);class na extends ut{constructor(){super(E.LargeBinary)}toString(){return"LargeBinary"}}Rh=Symbol.toStringTag,na[Rh]=(t=>(t.ArrayType=Uint8Array,t.OffsetArrayType=BigInt64Array,t[Symbol.toStringTag]="LargeBinary"))(na.prototype);class ra extends ut{constructor(){super(E.Utf8)}toString(){return"Utf8"}}Uh=Symbol.toStringTag,ra[Uh]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Utf8"))(ra.prototype);class ia extends ut{constructor(){super(E.LargeUtf8)}toString(){return"LargeUtf8"}}Vh=Symbol.toStringTag,ia[Vh]=(t=>(t.ArrayType=Uint8Array,t.OffsetArrayType=BigInt64Array,t[Symbol.toStringTag]="LargeUtf8"))(ia.prototype);class oa extends ut{constructor(){super(E.Bool)}toString(){return"Bool"}}xh=Symbol.toStringTag,oa[xh]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Bool"))(oa.prototype);class sa extends ut{constructor(t,e,n=128){super(E.Decimal),this.scale=t,this.precision=e,this.bitWidth=n}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}zh=Symbol.toStringTag,sa[zh]=(t=>(t.scale=null,t.precision=null,t.ArrayType=Uint32Array,t[Symbol.toStringTag]="Decimal"))(sa.prototype);class aa extends ut{constructor(t){super(E.Date),this.unit=t}toString(){return`Date${32*(this.unit+1)}<${Dn[this.unit]}>`}get ArrayType(){return this.unit===Dn.DAY?Int32Array:BigInt64Array}}Wh=Symbol.toStringTag,aa[Wh]=(t=>(t.unit=null,t[Symbol.toStringTag]="Date"))(aa.prototype);class la extends ut{constructor(t,e){super(E.Time),this.unit=t,this.bitWidth=e}toString(){return`Time${this.bitWidth}<${yt[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}Hh=Symbol.toStringTag,la[Hh]=(t=>(t.unit=null,t.bitWidth=null,t[Symbol.toStringTag]="Time"))(la.prototype);class ca extends ut{constructor(t,e){super(E.Timestamp),this.unit=t,this.timezone=e}toString(){return`Timestamp<${yt[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}qh=Symbol.toStringTag,ca[qh]=(t=>(t.unit=null,t.timezone=null,t.ArrayType=BigInt64Array,t[Symbol.toStringTag]="Timestamp"))(ca.prototype);class ua extends ut{constructor(t){super(E.Interval),this.unit=t}toString(){return`Interval<${nr[this.unit]}>`}}Yh=Symbol.toStringTag,ua[Yh]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Interval"))(ua.prototype);class fa extends ut{constructor(t){super(E.Duration),this.unit=t}toString(){return`Duration<${yt[this.unit]}>`}}Kh=Symbol.toStringTag,fa[Kh]=(t=>(t.unit=null,t.ArrayType=BigInt64Array,t[Symbol.toStringTag]="Duration"))(fa.prototype);class da extends ut{constructor(t){super(E.List),this.children=[t]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}$h=Symbol.toStringTag,da[$h]=(t=>(t.children=null,t[Symbol.toStringTag]="List"))(da.prototype);class tn extends ut{constructor(t){super(E.Struct),this.children=t}toString(){return`Struct<{${this.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}Jh=Symbol.toStringTag,tn[Jh]=(t=>(t.children=null,t[Symbol.toStringTag]="Struct"))(tn.prototype);class ha extends ut{constructor(t,e,n){super(E.Union),this.mode=t,this.children=n,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce(((t,e,n)=>(t[e]=n)&&t||t),Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map((t=>`${t.type}`)).join(" | ")}>`}}Qh=Symbol.toStringTag,ha[Qh]=(t=>(t.mode=null,t.typeIds=null,t.children=null,t.typeIdToChildIndex=null,t.ArrayType=Int8Array,t[Symbol.toStringTag]="Union"))(ha.prototype);class ma extends ut{constructor(t){super(E.FixedSizeBinary),this.byteWidth=t}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}Gh=Symbol.toStringTag,ma[Gh]=(t=>(t.byteWidth=null,t.ArrayType=Uint8Array,t[Symbol.toStringTag]="FixedSizeBinary"))(ma.prototype);class pa extends ut{constructor(t,e){super(E.FixedSizeList),this.listSize=t,this.children=[e]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}Xh=Symbol.toStringTag,pa[Xh]=(t=>(t.children=null,t.listSize=null,t[Symbol.toStringTag]="FixedSizeList"))(pa.prototype);class ga extends ut{constructor(t,e=!1){var n,r,i;if(super(E.Map),this.children=[t],this.keysSorted=e,t&&(t.name="entries",null!==(n=null==t?void 0:t.type)&&void 0!==n&&n.children)){const e=null===(r=null==t?void 0:t.type)||void 0===r?void 0:r.children[0];e&&(e.name="key");const n=null===(i=null==t?void 0:t.type)||void 0===i?void 0:i.children[1];n&&(n.name="value")}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}Zh=Symbol.toStringTag,ga[Zh]=(t=>(t.children=null,t.keysSorted=null,t[Symbol.toStringTag]="Map_"))(ga.prototype);const $_=(t=>()=>++t)(-1);class Ri extends ut{constructor(t,e,n,r){super(E.Dictionary),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=null==n?$_():_e(n)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}function ur(t){const e=t;switch(t.typeId){case E.Decimal:return t.bitWidth/32;case E.Interval:return 1+e.unit;case E.FixedSizeList:return e.listSize;case E.FixedSizeBinary:return e.byteWidth;default:return 1}}tm=Symbol.toStringTag,Ri[tm]=(t=>(t.id=null,t.indices=null,t.isOrdered=null,t.dictionary=null,t[Symbol.toStringTag]="Dictionary"))(Ri.prototype);class zt{visitMany(t,...e){return t.map(((t,n)=>this.visit(t,...e.map((t=>t[n])))))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return J_(this,t,e)}getVisitFnByTypeId(t,e=!0){return Si(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitLargeUtf8(t,...e){return null}visitBinary(t,...e){return null}visitLargeBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitDuration(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function J_(t,e,n=!0){return"number"==typeof e?Si(t,e,n):"string"==typeof e&&e in E?Si(t,E[e],n):e&&e instanceof ut?Si(t,au(e),n):null!=e&&e.type&&e.type instanceof ut?Si(t,au(e.type),n):Si(t,E.NONE,n)}function Si(t,e,n=!0){let r=null;switch(e){case E.Null:r=t.visitNull;break;case E.Bool:r=t.visitBool;break;case E.Int:r=t.visitInt;break;case E.Int8:r=t.visitInt8||t.visitInt;break;case E.Int16:r=t.visitInt16||t.visitInt;break;case E.Int32:r=t.visitInt32||t.visitInt;break;case E.Int64:r=t.visitInt64||t.visitInt;break;case E.Uint8:r=t.visitUint8||t.visitInt;break;case E.Uint16:r=t.visitUint16||t.visitInt;break;case E.Uint32:r=t.visitUint32||t.visitInt;break;case E.Uint64:r=t.visitUint64||t.visitInt;break;case E.Float:r=t.visitFloat;break;case E.Float16:r=t.visitFloat16||t.visitFloat;break;case E.Float32:r=t.visitFloat32||t.visitFloat;break;case E.Float64:r=t.visitFloat64||t.visitFloat;break;case E.Utf8:r=t.visitUtf8;break;case E.LargeUtf8:r=t.visitLargeUtf8;break;case E.Binary:r=t.visitBinary;break;case E.LargeBinary:r=t.visitLargeBinary;break;case E.FixedSizeBinary:r=t.visitFixedSizeBinary;break;case E.Date:r=t.visitDate;break;case E.DateDay:r=t.visitDateDay||t.visitDate;break;case E.DateMillisecond:r=t.visitDateMillisecond||t.visitDate;break;case E.Timestamp:r=t.visitTimestamp;break;case E.TimestampSecond:r=t.visitTimestampSecond||t.visitTimestamp;break;case E.TimestampMillisecond:r=t.visitTimestampMillisecond||t.visitTimestamp;break;case E.TimestampMicrosecond:r=t.visitTimestampMicrosecond||t.visitTimestamp;break;case E.TimestampNanosecond:r=t.visitTimestampNanosecond||t.visitTimestamp;break;case E.Time:r=t.visitTime;break;case E.TimeSecond:r=t.visitTimeSecond||t.visitTime;break;case E.TimeMillisecond:r=t.visitTimeMillisecond||t.visitTime;break;case E.TimeMicrosecond:r=t.visitTimeMicrosecond||t.visitTime;break;case E.TimeNanosecond:r=t.visitTimeNanosecond||t.visitTime;break;case E.Decimal:r=t.visitDecimal;break;case E.List:r=t.visitList;break;case E.Struct:r=t.visitStruct;break;case E.Union:r=t.visitUnion;break;case E.DenseUnion:r=t.visitDenseUnion||t.visitUnion;break;case E.SparseUnion:r=t.visitSparseUnion||t.visitUnion;break;case E.Dictionary:r=t.visitDictionary;break;case E.Interval:r=t.visitInterval;break;case E.IntervalDayTime:r=t.visitIntervalDayTime||t.visitInterval;break;case E.IntervalYearMonth:r=t.visitIntervalYearMonth||t.visitInterval;break;case E.Duration:r=t.visitDuration;break;case E.DurationSecond:r=t.visitDurationSecond||t.visitDuration;break;case E.DurationMillisecond:r=t.visitDurationMillisecond||t.visitDuration;break;case E.DurationMicrosecond:r=t.visitDurationMicrosecond||t.visitDuration;break;case E.DurationNanosecond:r=t.visitDurationNanosecond||t.visitDuration;break;case E.FixedSizeList:r=t.visitFixedSizeList;break;case E.Map:r=t.visitMap}if("function"==typeof r)return r;if(!n)return()=>null;throw new Error(`Unrecognized type '${E[e]}'`)}function au(t){switch(t.typeId){case E.Null:return E.Null;case E.Int:{const{bitWidth:e,isSigned:n}=t;switch(e){case 8:return n?E.Int8:E.Uint8;case 16:return n?E.Int16:E.Uint16;case 32:return n?E.Int32:E.Uint32;case 64:return n?E.Int64:E.Uint64}return E.Int}case E.Float:switch(t.precision){case Ye.HALF:return E.Float16;case Ye.SINGLE:return E.Float32;case Ye.DOUBLE:return E.Float64}return E.Float;case E.Binary:return E.Binary;case E.LargeBinary:return E.LargeBinary;case E.Utf8:return E.Utf8;case E.LargeUtf8:return E.LargeUtf8;case E.Bool:return E.Bool;case E.Decimal:return E.Decimal;case E.Time:switch(t.unit){case yt.SECOND:return E.TimeSecond;case yt.MILLISECOND:return E.TimeMillisecond;case yt.MICROSECOND:return E.TimeMicrosecond;case yt.NANOSECOND:return E.TimeNanosecond}return E.Time;case E.Timestamp:switch(t.unit){case yt.SECOND:return E.TimestampSecond;case yt.MILLISECOND:return E.TimestampMillisecond;case yt.MICROSECOND:return E.TimestampMicrosecond;case yt.NANOSECOND:return E.TimestampNanosecond}return E.Timestamp;case E.Date:switch(t.unit){case Dn.DAY:return E.DateDay;case Dn.MILLISECOND:return E.DateMillisecond}return E.Date;case E.Interval:switch(t.unit){case nr.DAY_TIME:return E.IntervalDayTime;case nr.YEAR_MONTH:return E.IntervalYearMonth}return E.Interval;case E.Duration:switch(t.unit){case yt.SECOND:return E.DurationSecond;case yt.MILLISECOND:return E.DurationMillisecond;case yt.MICROSECOND:return E.DurationMicrosecond;case yt.NANOSECOND:return E.DurationNanosecond}return E.Duration;case E.Map:return E.Map;case E.List:return E.List;case E.Struct:return E.Struct;case E.Union:switch(t.mode){case cn.Dense:return E.DenseUnion;case cn.Sparse:return E.SparseUnion}return E.Union;case E.FixedSizeBinary:return E.FixedSizeBinary;case E.FixedSizeList:return E.FixedSizeList;case E.Dictionary:return E.Dictionary}throw new Error(`Unrecognized type '${E[t.typeId]}'`)}zt.prototype.visitInt8=null,zt.prototype.visitInt16=null,zt.prototype.visitInt32=null,zt.prototype.visitInt64=null,zt.prototype.visitUint8=null,zt.prototype.visitUint16=null,zt.prototype.visitUint32=null,zt.prototype.visitUint64=null,zt.prototype.visitFloat16=null,zt.prototype.visitFloat32=null,zt.prototype.visitFloat64=null,zt.prototype.visitDateDay=null,zt.prototype.visitDateMillisecond=null,zt.prototype.visitTimestampSecond=null,zt.prototype.visitTimestampMillisecond=null,zt.prototype.visitTimestampMicrosecond=null,zt.prototype.visitTimestampNanosecond=null,zt.prototype.visitTimeSecond=null,zt.prototype.visitTimeMillisecond=null,zt.prototype.visitTimeMicrosecond=null,zt.prototype.visitTimeNanosecond=null,zt.prototype.visitDenseUnion=null,zt.prototype.visitSparseUnion=null,zt.prototype.visitIntervalDayTime=null,zt.prototype.visitIntervalYearMonth=null,zt.prototype.visitDuration=null,zt.prototype.visitDurationSecond=null,zt.prototype.visitDurationMillisecond=null,zt.prototype.visitDurationMicrosecond=null,zt.prototype.visitDurationNanosecond=null;const em=new Float64Array(1),li=new Uint32Array(em.buffer);function nm(t){const e=(31744&t)>>10,n=(1023&t)/1024,r=Math.pow(-1,(32768&t)>>15);switch(e){case 31:return r*(n?Number.NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,e-15)*(1+n)}function Q_(t){if(t!=t)return 32256;em[0]=t;const e=(2147483648&li[1])>>16&65535;let n=2146435072&li[1],r=0;return n>=1089470464?li[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&li[1])>>10):n<=1056964608?(r=1048576+(1048575&li[1]),r=1048576+(r<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&li[1])>>10),e|n|65535&r}class St extends zt{}function kt(t){return(e,n,r)=>{if(e.setValid(n,null!=r))return t(e,n,r)}}const G_=(t,e,n)=>{t[e]=Math.floor(n/864e5)},rm=(t,e,n,r)=>{if(n+1<e.length){const i=_e(e[n]),o=_e(e[n+1]);t.set(r.subarray(0,o-i),i)}},X_=({offset:t,values:e},n,r)=>{const i=t+n;r?e[i>>3]|=1<<i%8:e[i>>3]&=~(1<<i%8)},Sr=({values:t},e,n)=>{t[e]=n},nc=({values:t},e,n)=>{t[e]=n},im=({values:t},e,n)=>{t[e]=Q_(n)},Z_=(t,e,n)=>{switch(t.type.precision){case Ye.HALF:return im(t,e,n);case Ye.SINGLE:case Ye.DOUBLE:return nc(t,e,n)}},om=({values:t},e,n)=>{G_(t,e,n.valueOf())},sm=({values:t},e,n)=>{t[e]=BigInt(n)},tb=({stride:t,values:e},n,r)=>{e.set(r.subarray(0,t),t*n)},am=({values:t,valueOffsets:e},n,r)=>rm(t,e,n,r),lm=({values:t,valueOffsets:e},n,r)=>rm(t,e,n,Gl(r)),eb=(t,e,n)=>{t.type.unit===Dn.DAY?om(t,e,n):sm(t,e,n)},cm=({values:t},e,n)=>{t[e]=BigInt(n/1e3)},um=({values:t},e,n)=>{t[e]=BigInt(n)},fm=({values:t},e,n)=>{t[e]=BigInt(1e3*n)},dm=({values:t},e,n)=>{t[e]=BigInt(1e6*n)},nb=(t,e,n)=>{switch(t.type.unit){case yt.SECOND:return cm(t,e,n);case yt.MILLISECOND:return um(t,e,n);case yt.MICROSECOND:return fm(t,e,n);case yt.NANOSECOND:return dm(t,e,n)}},hm=({values:t},e,n)=>{t[e]=n},mm=({values:t},e,n)=>{t[e]=n},pm=({values:t},e,n)=>{t[e]=n},gm=({values:t},e,n)=>{t[e]=n},rb=(t,e,n)=>{switch(t.type.unit){case yt.SECOND:return hm(t,e,n);case yt.MILLISECOND:return mm(t,e,n);case yt.MICROSECOND:return pm(t,e,n);case yt.NANOSECOND:return gm(t,e,n)}},ib=({values:t,stride:e},n,r)=>{t.set(r.subarray(0,e),e*n)},ob=(t,e,n)=>{const r=t.children[0],i=t.valueOffsets,o=En.getVisitFn(r);if(Array.isArray(n))for(let t=-1,s=i[e],a=i[e+1];s<a;)o(r,s++,n[++t]);else for(let t=-1,s=i[e],a=i[e+1];s<a;)o(r,s++,n.get(++t))},sb=(t,e,n)=>{const r=t.children[0],{valueOffsets:i}=t,o=En.getVisitFn(r);let{[e]:s,[e+1]:a}=i;const l=n instanceof Map?n.entries():Object.entries(n);for(const t of l)if(o(r,s,t),++s>=a)break},ab=(t,e)=>(n,r,i,o)=>r&&n(r,t,e[o]),lb=(t,e)=>(n,r,i,o)=>r&&n(r,t,e.get(o)),cb=(t,e)=>(n,r,i,o)=>r&&n(r,t,e.get(i.name)),ub=(t,e)=>(n,r,i,o)=>r&&n(r,t,e[i.name]),fb=(t,e,n)=>{const r=t.type.children.map((t=>En.getVisitFn(t.type))),i=n instanceof Map?cb(e,n):n instanceof ce?lb(e,n):Array.isArray(n)?ab(e,n):ub(e,n);t.type.children.forEach(((e,n)=>i(r[n],t.children[n],e,n)))},db=(t,e,n)=>{t.type.mode===cn.Dense?_m(t,e,n):bm(t,e,n)},_m=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];En.visit(i,t.valueOffsets[e],n)},bm=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];En.visit(i,e,n)},hb=(t,e,n)=>{var r;null===(r=t.dictionary)||void 0===r||r.set(t.values[e],n)},mb=(t,e,n)=>{t.type.unit===nr.DAY_TIME?ym(t,e,n):vm(t,e,n)},ym=({values:t},e,n)=>{t.set(n.subarray(0,2),2*e)},vm=({values:t},e,n)=>{t[e]=12*n[0]+n[1]%12},wm=({values:t},e,n)=>{t[e]=n},Sm=({values:t},e,n)=>{t[e]=n},Im=({values:t},e,n)=>{t[e]=n},Om=({values:t},e,n)=>{t[e]=n},pb=(t,e,n)=>{switch(t.type.unit){case yt.SECOND:return wm(t,e,n);case yt.MILLISECOND:return Sm(t,e,n);case yt.MICROSECOND:return Im(t,e,n);case yt.NANOSECOND:return Om(t,e,n)}},gb=(t,e,n)=>{const{stride:r}=t,i=t.children[0],o=En.getVisitFn(i);if(Array.isArray(n))for(let t=-1,s=e*r;++t<r;)o(i,s+t,n[t]);else for(let t=-1,s=e*r;++t<r;)o(i,s+t,n.get(t))};St.prototype.visitBool=kt(X_),St.prototype.visitInt=kt(Sr),St.prototype.visitInt8=kt(Sr),St.prototype.visitInt16=kt(Sr),St.prototype.visitInt32=kt(Sr),St.prototype.visitInt64=kt(Sr),St.prototype.visitUint8=kt(Sr),St.prototype.visitUint16=kt(Sr),St.prototype.visitUint32=kt(Sr),St.prototype.visitUint64=kt(Sr),St.prototype.visitFloat=kt(Z_),St.prototype.visitFloat16=kt(im),St.prototype.visitFloat32=kt(nc),St.prototype.visitFloat64=kt(nc),St.prototype.visitUtf8=kt(lm),St.prototype.visitLargeUtf8=kt(lm),St.prototype.visitBinary=kt(am),St.prototype.visitLargeBinary=kt(am),St.prototype.visitFixedSizeBinary=kt(tb),St.prototype.visitDate=kt(eb),St.prototype.visitDateDay=kt(om),St.prototype.visitDateMillisecond=kt(sm),St.prototype.visitTimestamp=kt(nb),St.prototype.visitTimestampSecond=kt(cm),St.prototype.visitTimestampMillisecond=kt(um),St.prototype.visitTimestampMicrosecond=kt(fm),St.prototype.visitTimestampNanosecond=kt(dm),St.prototype.visitTime=kt(rb),St.prototype.visitTimeSecond=kt(hm),St.prototype.visitTimeMillisecond=kt(mm),St.prototype.visitTimeMicrosecond=kt(pm),St.prototype.visitTimeNanosecond=kt(gm),St.prototype.visitDecimal=kt(ib),St.prototype.visitList=kt(ob),St.prototype.visitStruct=kt(fb),St.prototype.visitUnion=kt(db),St.prototype.visitDenseUnion=kt(_m),St.prototype.visitSparseUnion=kt(bm),St.prototype.visitDictionary=kt(hb),St.prototype.visitInterval=kt(mb),St.prototype.visitIntervalDayTime=kt(ym),St.prototype.visitIntervalYearMonth=kt(vm),St.prototype.visitDuration=kt(pb),St.prototype.visitDurationSecond=kt(wm),St.prototype.visitDurationMillisecond=kt(Sm),St.prototype.visitDurationMicrosecond=kt(Im),St.prototype.visitDurationNanosecond=kt(Om),St.prototype.visitFixedSizeList=kt(gb),St.prototype.visitMap=kt(sb);const En=new St,Cn=Symbol.for("parent"),Pi=Symbol.for("rowIndex");class rc{constructor(t,e){return this[Cn]=t,this[Pi]=e,new Proxy(this,new bb)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Pi],e=this[Cn],n=e.type.children,r={};for(let i=-1,o=n.length;++i<o;)r[n[i].name]=un.visit(e.children[i],t);return r}toString(){return`{${[...this].map((([t,e])=>`${Wo(t)}: ${Wo(e)}`)).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new _b(this[Cn],this[Pi])}}class _b{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,un.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(rc.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Cn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Pi]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class bb{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Cn].type.children.map((t=>t.name))}has(t,e){return-1!==t[Cn].type.children.findIndex((t=>t.name===e))}getOwnPropertyDescriptor(t,e){if(-1!==t[Cn].type.children.findIndex((t=>t.name===e)))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[Cn].type.children.findIndex((t=>t.name===e));if(-1!==n){const r=un.visit(t[Cn].children[n],t[Pi]);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[Cn].type.children.findIndex((t=>t.name===e));return-1!==r?(En.visit(t[Cn].children[r],t[Pi],n),Reflect.set(t,e,n)):!(!Reflect.has(t,e)&&"symbol"!=typeof e)&&Reflect.set(t,e,n)}}class pt extends zt{}function It(t){return(e,n)=>e.getValid(n)?t(e,n):null}const yb=(t,e)=>864e5*t[e],vb=(t,e)=>null,km=(t,e,n)=>{if(n+1>=e.length)return null;const r=_e(e[n]),i=_e(e[n+1]);return t.subarray(r,i)},wb=({offset:t,values:e},n)=>{const r=t+n;return!!(e[r>>3]&1<<r%8)},Dm=({values:t},e)=>yb(t,e),Em=({values:t},e)=>_e(t[e]),Ur=({stride:t,values:e},n)=>e[t*n],Sb=({stride:t,values:e},n)=>nm(e[t*n]),Am=({values:t},e)=>t[e],Ib=({stride:t,values:e},n)=>e.subarray(t*n,t*(n+1)),Tm=({values:t,valueOffsets:e},n)=>km(t,e,n),Bm=({values:t,valueOffsets:e},n)=>{const r=km(t,e,n);return null!==r?wl(r):null},Ob=({values:t},e)=>t[e],kb=({type:t,values:e},n)=>t.precision!==Ye.HALF?e[n]:nm(e[n]),Db=(t,e)=>t.type.unit===Dn.DAY?Dm(t,e):Em(t,e),Mm=({values:t},e)=>1e3*_e(t[e]),Pm=({values:t},e)=>_e(t[e]),Nm=({values:t},e)=>Mh(t[e],BigInt(1e3)),Cm=({values:t},e)=>Mh(t[e],BigInt(1e6)),Eb=(t,e)=>{switch(t.type.unit){case yt.SECOND:return Mm(t,e);case yt.MILLISECOND:return Pm(t,e);case yt.MICROSECOND:return Nm(t,e);case yt.NANOSECOND:return Cm(t,e)}},Fm=({values:t},e)=>t[e],jm=({values:t},e)=>t[e],Lm=({values:t},e)=>t[e],Rm=({values:t},e)=>t[e],Ab=(t,e)=>{switch(t.type.unit){case yt.SECOND:return Fm(t,e);case yt.MILLISECOND:return jm(t,e);case yt.MICROSECOND:return Lm(t,e);case yt.NANOSECOND:return Rm(t,e)}},Tb=({values:t,stride:e},n)=>ec.decimal(t.subarray(e*n,e*(n+1))),Bb=(t,e)=>{const{valueOffsets:n,stride:r,children:i}=t,{[e*r]:o,[e*r+1]:s}=n,a=i[0].slice(o,s-o);return new ce([a])},Mb=(t,e)=>{const{valueOffsets:n,children:r}=t,{[e]:i,[e+1]:o}=n,s=r[0];return new ic(s.slice(i,o-i))},Pb=(t,e)=>new rc(t,e),Nb=(t,e)=>t.type.mode===cn.Dense?Um(t,e):Vm(t,e),Um=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return un.visit(r,t.valueOffsets[e])},Vm=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return un.visit(r,e)},Cb=(t,e)=>{var n;return null===(n=t.dictionary)||void 0===n?void 0:n.get(t.values[e])},Fb=(t,e)=>t.type.unit===nr.DAY_TIME?xm(t,e):zm(t,e),xm=({values:t},e)=>t.subarray(2*e,2*(e+1)),zm=({values:t},e)=>{const n=t[e],r=new Int32Array(2);return r[0]=Math.trunc(n/12),r[1]=Math.trunc(n%12),r},Wm=({values:t},e)=>t[e],Hm=({values:t},e)=>t[e],qm=({values:t},e)=>t[e],Ym=({values:t},e)=>t[e],jb=(t,e)=>{switch(t.type.unit){case yt.SECOND:return Wm(t,e);case yt.MILLISECOND:return Hm(t,e);case yt.MICROSECOND:return qm(t,e);case yt.NANOSECOND:return Ym(t,e)}},Lb=(t,e)=>{const{stride:n,children:r}=t,i=r[0].slice(e*n,n);return new ce([i])};pt.prototype.visitNull=It(vb),pt.prototype.visitBool=It(wb),pt.prototype.visitInt=It(Ob),pt.prototype.visitInt8=It(Ur),pt.prototype.visitInt16=It(Ur),pt.prototype.visitInt32=It(Ur),pt.prototype.visitInt64=It(Am),pt.prototype.visitUint8=It(Ur),pt.prototype.visitUint16=It(Ur),pt.prototype.visitUint32=It(Ur),pt.prototype.visitUint64=It(Am),pt.prototype.visitFloat=It(kb),pt.prototype.visitFloat16=It(Sb),pt.prototype.visitFloat32=It(Ur),pt.prototype.visitFloat64=It(Ur),pt.prototype.visitUtf8=It(Bm),pt.prototype.visitLargeUtf8=It(Bm),pt.prototype.visitBinary=It(Tm),pt.prototype.visitLargeBinary=It(Tm),pt.prototype.visitFixedSizeBinary=It(Ib),pt.prototype.visitDate=It(Db),pt.prototype.visitDateDay=It(Dm),pt.prototype.visitDateMillisecond=It(Em),pt.prototype.visitTimestamp=It(Eb),pt.prototype.visitTimestampSecond=It(Mm),pt.prototype.visitTimestampMillisecond=It(Pm),pt.prototype.visitTimestampMicrosecond=It(Nm),pt.prototype.visitTimestampNanosecond=It(Cm),pt.prototype.visitTime=It(Ab),pt.prototype.visitTimeSecond=It(Fm),pt.prototype.visitTimeMillisecond=It(jm),pt.prototype.visitTimeMicrosecond=It(Lm),pt.prototype.visitTimeNanosecond=It(Rm),pt.prototype.visitDecimal=It(Tb),pt.prototype.visitList=It(Bb),pt.prototype.visitStruct=It(Pb),pt.prototype.visitUnion=It(Nb),pt.prototype.visitDenseUnion=It(Um),pt.prototype.visitSparseUnion=It(Vm),pt.prototype.visitDictionary=It(Cb),pt.prototype.visitInterval=It(Fb),pt.prototype.visitIntervalDayTime=It(xm),pt.prototype.visitIntervalYearMonth=It(zm),pt.prototype.visitDuration=It(jb),pt.prototype.visitDurationSecond=It(Wm),pt.prototype.visitDurationMillisecond=It(Hm),pt.prototype.visitDurationMicrosecond=It(qm),pt.prototype.visitDurationNanosecond=It(Ym),pt.prototype.visitFixedSizeList=It(Lb),pt.prototype.visitMap=It(Mb);const un=new pt,Ii=Symbol.for("keys"),Ni=Symbol.for("vals"),Oi=Symbol.for("kKeysAsStrings"),El=Symbol.for("_kKeysAsStrings");class ic{constructor(t){return this[Ii]=new ce([t.children[0]]).memoize(),this[Ni]=t.children[1],new Proxy(this,new Ub)}get[Oi](){return this[El]||(this[El]=Array.from(this[Ii].toArray(),String))}[Symbol.iterator](){return new Rb(this[Ii],this[Ni])}get size(){return this[Ii].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ii],e=this[Ni],n={};for(let r=-1,i=t.length;++r<i;)n[t.get(r)]=un.visit(e,r);return n}toString(){return`{${[...this].map((([t,e])=>`${Wo(t)}: ${Wo(e)}`)).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class Rb{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),un.visit(this.vals,t)]})}}class Ub{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Oi]}has(t,e){return t[Oi].includes(e)}getOwnPropertyDescriptor(t,e){if(-1!==t[Oi].indexOf(e))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[Oi].indexOf(e);if(-1!==n){const r=un.visit(Reflect.get(t,Ni),n);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[Oi].indexOf(e);return-1!==r?(En.visit(Reflect.get(t,Ni),r,n),Reflect.set(t,e,n)):!!Reflect.has(t,e)&&Reflect.set(t,e,n)}}let lu;function Km(t,e,n,r){const{length:i=0}=t;let o="number"!=typeof e?0:e,s="number"!=typeof n?i:n;return o<0&&(o=(o%i+i)%i),s<0&&(s=(s%i+i)%i),s<o&&(lu=o,o=s,s=lu),s>i&&(s=i),r?r(t,o,s):[o,s]}Object.defineProperties(ic.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Ii]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ni]:{writable:!0,enumerable:!1,configurable:!1,value:null},[El]:{writable:!0,enumerable:!1,configurable:!1,value:null}});const oc=(t,e)=>t<0?e+t:t,cu=t=>t!=t;function qi(t){if("object"!=typeof t||null===t)return cu(t)?cu:e=>e===t;if(t instanceof Date){const e=t.valueOf();return t=>t instanceof Date&&t.valueOf()===e}return ArrayBuffer.isView(t)?e=>!!e&&L_(t,e):t instanceof Map?xb(t):Array.isArray(t)?Vb(t):t instanceof ce?zb(t):Wb(t,!0)}function Vb(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=qi(t[n]);return Na(e)}function xb(t){let e=-1;const n=[];for(const r of t.values())n[++e]=qi(r);return Na(n)}function zb(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=qi(t.get(n));return Na(e)}function Wb(t,e=!1){const n=Object.keys(t);if(!e&&0===n.length)return()=>!1;const r=[];for(let e=-1,i=n.length;++e<i;)r[e]=qi(t[n[e]]);return Na(r,n)}function Na(t,e){return n=>{if(!n||"object"!=typeof n)return!1;switch(n.constructor){case Array:return Hb(t,n);case Map:return uu(t,n,n.keys());case ic:case rc:case Object:case void 0:return uu(t,n,e||Object.keys(n))}return n instanceof ce&&qb(t,n)}}function Hb(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e[r]))return!1;return!0}function qb(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e.get(r)))return!1;return!0}function uu(t,e,n){const r=n[Symbol.iterator](),i=e instanceof Map?e.keys():Object.keys(e)[Symbol.iterator](),o=e instanceof Map?e.values():Object.values(e)[Symbol.iterator]();let s=0;const a=t.length;let l=o.next(),c=r.next(),u=i.next();for(;s<a&&!c.done&&!u.done&&!l.done&&c.value===u.value&&t[s](l.value);++s,c=r.next(),u=i.next(),l=o.next());return!!(s===a&&c.done&&u.done&&l.done)||(r.return&&r.return(),i.return&&i.return(),o.return&&o.return(),!1)}function $m(t,e,n,r){return!!(n&1<<r)}function Yb(t,e,n,r){return(n&1<<r)>>r}function fu(t,e,n){const r=n.byteLength+7&-8;if(t>0||n.byteLength<r){const i=new Uint8Array(r);return i.set(t%8==0?n.subarray(t>>3):Al(new sc(n,t,e,null,$m)).subarray(0,r)),i}return n}function Al(t){const e=[];let n=0,r=0,i=0;for(const o of t)o&&(i|=1<<r),8===++r&&(e[n++]=i,i=r=0);(0===n||r>0)&&(e[n++]=i);const o=new Uint8Array(e.length+7&-8);return o.set(e),o}class sc{constructor(t,e,n,r,i){this.bytes=t,this.length=n,this.context=r,this.get=i,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(8===this.bit&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Tl(t,e,n){if(n-e<=0)return 0;if(n-e<8){let r=0;for(const i of new sc(t,e,n-e,t,Yb))r+=i;return r}const r=n>>3<<3,i=e+(e%8==0?0:8-e%8);return Tl(t,e,i)+Tl(t,r,n)+Kb(t,i>>3,r-i>>3)}function Kb(t,e,n){let r=0,i=Math.trunc(e);const o=new DataView(t.buffer,t.byteOffset,t.byteLength),s=void 0===n?t.byteLength:i+n;for(;s-i>=4;)r+=Za(o.getUint32(i)),i+=4;for(;s-i>=2;)r+=Za(o.getUint16(i)),i+=2;for(;s-i>=1;)r+=Za(o.getUint8(i)),i+=1;return r}function Za(t){let e=Math.trunc(t);return e-=e>>>1&1431655765,e=(858993459&e)+(e>>>2&858993459),16843009*(e+(e>>>4)&252645135)>>>24}const $b=-1;class re{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(0!==this._nullCount){const{type:t}=this;return ut.isSparseUnion(t)||ut.isDenseUnion(t)?this.children.some((t=>t.nullable)):this.nullBitmap&&this.nullBitmap.byteLength>0}return!0}get byteLength(){let t=0;const{valueOffsets:e,values:n,nullBitmap:r,typeIds:i}=this;return e&&(t+=e.byteLength),n&&(t+=n.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),this.children.reduce(((t,e)=>t+e.byteLength),t)}get nullCount(){if(ut.isUnion(this.type))return this.children.reduce(((t,e)=>t+e.nullCount),0);let t,e=this._nullCount;return e<=-1&&(t=this.nullBitmap)&&(this._nullCount=e=0===t.length?0:this.length-Tl(t,this.offset,this.offset+this.length)),e}constructor(t,e,n,r,i,o=[],s){let a;this.type=t,this.children=o,this.dictionary=s,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(n||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),i instanceof re?(this.stride=i.stride,this.values=i.values,this.typeIds=i.typeIds,this.nullBitmap=i.nullBitmap,this.valueOffsets=i.valueOffsets):(this.stride=ur(t),i&&((a=i[0])&&(this.valueOffsets=a),(a=i[1])&&(this.values=a),(a=i[2])&&(this.nullBitmap=a),(a=i[3])&&(this.typeIds=a)))}getValid(t){const{type:e}=this;if(ut.isUnion(e)){const n=e,r=this.children[n.typeIdToChildIndex[this.typeIds[t]]],i=n.mode===cn.Dense?this.valueOffsets[t]:t;return r.getValid(i)}if(this.nullable&&this.nullCount>0){const e=this.offset+t;return!!(this.nullBitmap[e>>3]&1<<e%8)}return!0}setValid(t,e){let n;const{type:r}=this;if(ut.isUnion(r)){const i=r,o=this.children[i.typeIdToChildIndex[this.typeIds[t]]],s=i.mode===cn.Dense?this.valueOffsets[t]:t;n=o.getValid(s),o.setValid(s,e)}else{let{nullBitmap:r}=this;const{offset:i,length:o}=this,s=i+t,a=1<<s%8,l=s>>3;(!r||r.byteLength<=l)&&(r=new Uint8Array((i+o+63&-64)>>3).fill(255),this.nullCount>0?(r.set(fu(i,o,this.nullBitmap),0),Object.assign(this,{nullBitmap:r})):Object.assign(this,{nullBitmap:r,_nullCount:0}));const c=r[l];n=0!==(c&a),r[l]=e?c|a:c&~a}return n!==!!e&&(this._nullCount=this.nullCount+(e?-1:1)),e}clone(t=this.type,e=this.offset,n=this.length,r=this._nullCount,i=this,o=this.children){return new re(t,e,n,r,i,o,this.dictionary)}slice(t,e){const{stride:n,typeId:r,children:i}=this,o=+(0===this._nullCount)-1,s=16===r?n:1,a=this._sliceBuffers(t,e,n,r);return this.clone(this.type,this.offset+t,e,o,a,0===i.length||this.valueOffsets?i:this._sliceChildren(i,s*t,s*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===E.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:n}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(-8&e))-1,n>0&&r.set(fu(this.offset,e,this.nullBitmap),0);const i=this.buffers;return i[zr.VALIDITY]=r,this.clone(this.type,0,t,n+(t-e),i)}_sliceBuffers(t,e,n,r){let i;const{buffers:o}=this;return(i=o[zr.TYPE])&&(o[zr.TYPE]=i.subarray(t,t+e)),(i=o[zr.OFFSET])&&(o[zr.OFFSET]=i.subarray(t,t+e+1))||(i=o[zr.DATA])&&(o[zr.DATA]=6===r?i:i.subarray(n*t,n*(t+e))),o}_sliceChildren(t,e,n){return t.map((t=>t.slice(e,n)))}}re.prototype.children=Object.freeze([]);class Oo extends zt{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{type:e,offset:n=0,length:r=0}=t;return new re(e,n,r,r)}visitBool(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length>>3,nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitInt(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitFloat(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitUtf8(t){const{type:e,offset:n=0}=t,r=Jt(t.data),i=Jt(t.nullBitmap),o=io(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[o,r,i])}visitLargeUtf8(t){const{type:e,offset:n=0}=t,r=Jt(t.data),i=Jt(t.nullBitmap),o=Gc(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[o,r,i])}visitBinary(t){const{type:e,offset:n=0}=t,r=Jt(t.data),i=Jt(t.nullBitmap),o=io(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[o,r,i])}visitLargeBinary(t){const{type:e,offset:n=0}=t,r=Jt(t.data),i=Jt(t.nullBitmap),o=Gc(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[o,r,i])}visitFixedSizeBinary(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitDate(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitTimestamp(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitTime(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitDecimal(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitList(t){const{type:e,offset:n=0,child:r}=t,i=Jt(t.nullBitmap),o=io(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[o,void 0,i],[r])}visitStruct(t){const{type:e,offset:n=0,children:r=[]}=t,i=Jt(t.nullBitmap),{length:o=r.reduce(((t,{length:e})=>Math.max(t,e)),0),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,void 0,i],r)}visitUnion(t){const{type:e,offset:n=0,children:r=[]}=t,i=se(e.ArrayType,t.typeIds),{length:o=i.length,nullCount:s=-1}=t;if(ut.isSparseUnion(e))return new re(e,n,o,s,[void 0,void 0,void 0,i],r);const a=io(t.valueOffsets);return new re(e,n,o,s,[a,void 0,void 0,i],r)}visitDictionary(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.indices.ArrayType,t.data),{dictionary:o=new ce([(new Oo).visit({type:e.dictionary})])}=t,{length:s=i.length,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[void 0,i,r],[],o)}visitInterval(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitDuration(t){const{type:e,offset:n=0}=t,r=Jt(t.nullBitmap),i=se(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,i,r])}visitFixedSizeList(t){const{type:e,offset:n=0,child:r=(new Oo).visit({type:e.valueType})}=t,i=Jt(t.nullBitmap),{length:o=r.length/ur(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new re(e,n,o,s,[void 0,void 0,i],[r])}visitMap(t){const{type:e,offset:n=0,child:r=(new Oo).visit({type:e.childType})}=t,i=Jt(t.nullBitmap),o=io(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new re(e,n,s,a,[o,void 0,i],[r])}}const Jb=new Oo;function Vt(t){return Jb.visit(t)}class du{constructor(t=0,e){this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function Qb(t){return t.some((t=>t.nullable))}function Jm(t){return t.reduce(((t,e)=>t+e.nullCount),0)}function Qm(t){return t.reduce(((t,e,n)=>(t[n+1]=t[n]+e.length,t)),new Uint32Array(t.length+1))}function Gm(t,e,n,r){const i=[];for(let o=-1,s=t.length;++o<s;){const s=t[o],a=e[o],{length:l}=s;if(a>=r)break;if(n>=a+l)continue;if(a>=n&&a+l<=r){i.push(s);continue}const c=Math.max(0,n-a),u=Math.min(r-a,l);i.push(s.slice(c,u-c))}return 0===i.length&&i.push(t[0].slice(0,0)),i}function ac(t,e,n,r){let i=0,o=0,s=e.length-1;do{if(i>=s-1)return n<e[s]?r(t,i,n-e[i]):null;o=i+Math.trunc(.5*(s-i)),n<e[o]?s=o:i=o}while(i<s)}function lc(t,e){return t.getValid(e)}function _a(t){function e(e,n,r){return t(e[n],r)}return function(t){return ac(this.data,this._offsets,t,e)}}function Xm(t){let e;function n(n,r,i){return t(n[r],i,e)}return function(t,r){const i=this.data;e=r;const o=ac(i,this._offsets,t,n);return e=void 0,o}}function Zm(t){let e;function n(n,r,i){let o=i,s=0,a=0;for(let i=r-1,l=n.length;++i<l;){const r=n[i];if(~(s=t(r,e,o)))return a+s;o=0,a+=r.length}return-1}return function(t,r){e=t;const i=this.data,o="number"!=typeof r?n(i,0,0):ac(i,this._offsets,r,n);return e=void 0,o}}class gt extends zt{}function Gb(t,e){return null===e&&t.length>0?0:-1}function Xb(t,e){const{nullBitmap:n}=t;if(!n||t.nullCount<=0)return-1;let r=0;for(const i of new sc(n,t.offset+(e||0),t.length,n,$m)){if(!i)return r;++r}return-1}function Tt(t,e,n){if(void 0===e)return-1;if(null===e)switch(t.typeId){case E.Union:case E.Dictionary:break;default:return Xb(t,n)}const r=un.getVisitFn(t),i=qi(e);for(let e=(n||0)-1,o=t.length;++e<o;)if(i(r(t,e)))return e;return-1}function tp(t,e,n){const r=un.getVisitFn(t),i=qi(e);for(let e=(n||0)-1,o=t.length;++e<o;)if(i(r(t,e)))return e;return-1}gt.prototype.visitNull=Gb,gt.prototype.visitBool=Tt,gt.prototype.visitInt=Tt,gt.prototype.visitInt8=Tt,gt.prototype.visitInt16=Tt,gt.prototype.visitInt32=Tt,gt.prototype.visitInt64=Tt,gt.prototype.visitUint8=Tt,gt.prototype.visitUint16=Tt,gt.prototype.visitUint32=Tt,gt.prototype.visitUint64=Tt,gt.prototype.visitFloat=Tt,gt.prototype.visitFloat16=Tt,gt.prototype.visitFloat32=Tt,gt.prototype.visitFloat64=Tt,gt.prototype.visitUtf8=Tt,gt.prototype.visitLargeUtf8=Tt,gt.prototype.visitBinary=Tt,gt.prototype.visitLargeBinary=Tt,gt.prototype.visitFixedSizeBinary=Tt,gt.prototype.visitDate=Tt,gt.prototype.visitDateDay=Tt,gt.prototype.visitDateMillisecond=Tt,gt.prototype.visitTimestamp=Tt,gt.prototype.visitTimestampSecond=Tt,gt.prototype.visitTimestampMillisecond=Tt,gt.prototype.visitTimestampMicrosecond=Tt,gt.prototype.visitTimestampNanosecond=Tt,gt.prototype.visitTime=Tt,gt.prototype.visitTimeSecond=Tt,gt.prototype.visitTimeMillisecond=Tt,gt.prototype.visitTimeMicrosecond=Tt,gt.prototype.visitTimeNanosecond=Tt,gt.prototype.visitDecimal=Tt,gt.prototype.visitList=Tt,gt.prototype.visitStruct=Tt,gt.prototype.visitUnion=Tt,gt.prototype.visitDenseUnion=tp,gt.prototype.visitSparseUnion=tp,gt.prototype.visitDictionary=Tt,gt.prototype.visitInterval=Tt,gt.prototype.visitIntervalDayTime=Tt,gt.prototype.visitIntervalYearMonth=Tt,gt.prototype.visitDuration=Tt,gt.prototype.visitDurationSecond=Tt,gt.prototype.visitDurationMillisecond=Tt,gt.prototype.visitDurationMicrosecond=Tt,gt.prototype.visitDurationNanosecond=Tt,gt.prototype.visitFixedSizeList=Tt,gt.prototype.visitMap=Tt;const ba=new gt;class _t extends zt{}function Ot(t){const{type:e}=t;if(0===t.nullCount&&1===t.stride&&(ut.isInt(e)&&64!==e.bitWidth||ut.isTime(e)&&64!==e.bitWidth||ut.isFloat(e)&&e.precision!==Ye.HALF))return new du(t.data.length,(e=>{const n=t.data[e];return n.values.subarray(0,n.length)[Symbol.iterator]()}));let n=0;return new du(t.data.length,(e=>{const r=t.data[e].length,i=t.slice(n,n+r);return n+=r,new Zb(i)}))}class Zb{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}_t.prototype.visitNull=Ot,_t.prototype.visitBool=Ot,_t.prototype.visitInt=Ot,_t.prototype.visitInt8=Ot,_t.prototype.visitInt16=Ot,_t.prototype.visitInt32=Ot,_t.prototype.visitInt64=Ot,_t.prototype.visitUint8=Ot,_t.prototype.visitUint16=Ot,_t.prototype.visitUint32=Ot,_t.prototype.visitUint64=Ot,_t.prototype.visitFloat=Ot,_t.prototype.visitFloat16=Ot,_t.prototype.visitFloat32=Ot,_t.prototype.visitFloat64=Ot,_t.prototype.visitUtf8=Ot,_t.prototype.visitLargeUtf8=Ot,_t.prototype.visitBinary=Ot,_t.prototype.visitLargeBinary=Ot,_t.prototype.visitFixedSizeBinary=Ot,_t.prototype.visitDate=Ot,_t.prototype.visitDateDay=Ot,_t.prototype.visitDateMillisecond=Ot,_t.prototype.visitTimestamp=Ot,_t.prototype.visitTimestampSecond=Ot,_t.prototype.visitTimestampMillisecond=Ot,_t.prototype.visitTimestampMicrosecond=Ot,_t.prototype.visitTimestampNanosecond=Ot,_t.prototype.visitTime=Ot,_t.prototype.visitTimeSecond=Ot,_t.prototype.visitTimeMillisecond=Ot,_t.prototype.visitTimeMicrosecond=Ot,_t.prototype.visitTimeNanosecond=Ot,_t.prototype.visitDecimal=Ot,_t.prototype.visitList=Ot,_t.prototype.visitStruct=Ot,_t.prototype.visitUnion=Ot,_t.prototype.visitDenseUnion=Ot,_t.prototype.visitSparseUnion=Ot,_t.prototype.visitDictionary=Ot,_t.prototype.visitInterval=Ot,_t.prototype.visitIntervalDayTime=Ot,_t.prototype.visitIntervalYearMonth=Ot,_t.prototype.visitDuration=Ot,_t.prototype.visitDurationSecond=Ot,_t.prototype.visitDurationMillisecond=Ot,_t.prototype.visitDurationMicrosecond=Ot,_t.prototype.visitDurationNanosecond=Ot,_t.prototype.visitFixedSizeList=Ot,_t.prototype.visitMap=Ot;const cc=new _t;var ep;const np={},rp={};class ce{constructor(t){var e,n,r;const i=t[0]instanceof ce?t.flatMap((t=>t.data)):t;if(0===i.length||i.some((t=>!(t instanceof re))))throw new TypeError("Vector constructor expects an Array of Data instances.");const o=null===(e=i[0])||void 0===e?void 0:e.type;switch(i.length){case 0:this._offsets=[0];break;case 1:{const{get:t,set:e,indexOf:n}=np[o.typeId],r=i[0];this.isValid=t=>lc(r,t),this.get=e=>t(r,e),this.set=(t,n)=>e(r,t,n),this.indexOf=t=>n(r,t),this._offsets=[0,r.length];break}default:Object.setPrototypeOf(this,rp[o.typeId]),this._offsets=Qm(i)}this.data=i,this.type=o,this.stride=ur(o),this.numChildren=null!==(r=null===(n=o.children)||void 0===n?void 0:n.length)&&void 0!==r?r:0,this.length=this._offsets.at(-1)}get byteLength(){return this.data.reduce(((t,e)=>t+e.byteLength),0)}get nullable(){return Qb(this.data)}get nullCount(){return Jm(this.data)}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${E[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}at(t){return this.get(oc(t,this.length))}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>-1}[Symbol.iterator](){return cc.visit(this)}concat(...t){return new ce(this.data.concat(t.flatMap((t=>t.data)).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new ce(Km(this,t,e,(({data:t,_offsets:e},n,r)=>Gm(t,e,n,r))))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:n,stride:r,ArrayType:i}=this;switch(t.typeId){case E.Int:case E.Float:case E.Decimal:case E.Time:case E.Timestamp:switch(e.length){case 0:return new i;case 1:return e[0].values.subarray(0,n*r);default:return e.reduce(((t,{values:e,length:n})=>(t.array.set(e.subarray(0,n*r),t.offset),t.offset+=n*r,t)),{array:new i(n*r),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt(null===(e=this.type.children)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.numChildren?new ce(this.data.map((({children:e})=>e[t]))):null}get isMemoized(){return!!ut.isDictionary(this.type)&&this.data[0].dictionary.isMemoized}memoize(){if(ut.isDictionary(this.type)){const t=new ya(this.data[0].dictionary),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new ce(e)}return new ya(this)}unmemoize(){if(ut.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new ce(e)}return this}}ep=Symbol.toStringTag,ce[ep]=(t=>{t.type=ut.prototype,t.data=[],t.length=0,t.stride=1,t.numChildren=0,t._offsets=new Uint32Array([0]),t[Symbol.isConcatSpreadable]=!0;const e=Object.keys(E).map((t=>E[t])).filter((t=>"number"==typeof t&&t!==E.NONE));for(const n of e){const e=un.getVisitFnByTypeId(n),r=En.getVisitFnByTypeId(n),i=ba.getVisitFnByTypeId(n);np[n]={get:e,set:r,indexOf:i},rp[n]=Object.create(t,{isValid:{value:_a(lc)},get:{value:_a(un.getVisitFnByTypeId(n))},set:{value:Xm(En.getVisitFnByTypeId(n))},indexOf:{value:Zm(ba.getVisitFnByTypeId(n))}})}return"Vector"})(ce.prototype);class ya extends ce{constructor(t){super(t.data);const e=this.get,n=this.set,r=this.slice,i=new Array(this.length);Object.defineProperty(this,"get",{value(t){const n=i[t];if(void 0!==n)return n;const r=e.call(this,t);return i[t]=r,r}}),Object.defineProperty(this,"set",{value(t,e){n.call(this,t,e),i[t]=e}}),Object.defineProperty(this,"slice",{value:(t,e)=>new ya(r.call(this,t,e))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new ce(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Bl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,n,r){return t.prep(8,24),t.writeInt64(BigInt(r??0)),t.pad(4),t.writeInt32(n),t.writeInt64(BigInt(e??0)),t.offset()}}class hn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new hn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+4),(e||new hn).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ce.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new qn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Bl).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const n=this.bb.__offset(this.bb_pos,10);return n?(e||new Bl).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Ce.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}class te{constructor(t=[],e,n,r=Ce.V5){this.fields=t||[],this.metadata=e||new Map,n||(n=Ml(this.fields)),this.dictionaries=n,this.metadataVersion=r}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map((t=>t.name))}toString(){return`Schema<{ ${this.fields.map(((t,e)=>`${e}: ${t}`)).join(", ")} }>`}select(t){const e=new Set(t),n=this.fields.filter((t=>e.has(t.name)));return new te(n,this.metadata)}selectAt(t){const e=t.map((t=>this.fields[t])).filter(Boolean);return new te(e,this.metadata)}assign(...t){const e=t[0]instanceof te?t[0]:Array.isArray(t[0])?new te(t[0]):new te(t),n=[...this.fields],r=ws(ws(new Map,this.metadata),e.metadata),i=e.fields.filter((t=>{const e=n.findIndex((e=>e.name===t.name));return!~e||(n[e]=t.clone({metadata:ws(ws(new Map,n[e].metadata),t.metadata)}))&&!1})),o=Ml(i,new Map);return new te([...n,...i],r,new Map([...this.dictionaries,...o]))}}te.prototype.fields=null,te.prototype.metadata=null,te.prototype.dictionaries=null;class be{static new(...t){let[e,n,r,i]=t;return t[0]&&"object"==typeof t[0]&&(({name:e}=t[0]),void 0===n&&(n=t[0].type),void 0===r&&(r=t[0].nullable),void 0===i&&(i=t[0].metadata)),new be(`${e}`,n,r,i)}constructor(t,e,n=!1,r){this.name=t,this.type=e,this.nullable=n,this.metadata=r||new Map}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[e,n,r,i]=t;return t[0]&&"object"==typeof t[0]?({name:e=this.name,type:n=this.type,nullable:r=this.nullable,metadata:i=this.metadata}=t[0]):[e=this.name,n=this.type,r=this.nullable,i=this.metadata]=t,be.new(e,n,r,i)}}function ws(t,e){return new Map([...t||new Map,...e||new Map])}function Ml(t,e=new Map){for(let n=-1,r=t.length;++n<r;){const r=t[n].type;if(ut.isDictionary(r))if(e.has(r.id)){if(e.get(r.id)!==r.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else e.set(r.id,r.dictionary);r.children&&r.children.length>0&&Ml(r.children,e)}return e}be.prototype.type=null,be.prototype.name=null,be.prototype.nullable=null,be.prototype.metadata=null;var ty=Eh,ey=ji;class uc{static decode(t){t=new ey(Jt(t));const e=hn.getRootAsFooter(t),n=te.decode(e.schema(),new Map,e.version());return new ny(n,e)}static encode(t){const e=new ty,n=te.encode(e,t.schema);hn.startRecordBatchesVector(e,t.numRecordBatches);for(const n of[...t.recordBatches()].slice().reverse())Ui.encode(e,n);const r=e.endVector();hn.startDictionariesVector(e,t.numDictionaries);for(const n of[...t.dictionaryBatches()].slice().reverse())Ui.encode(e,n);const i=e.endVector();return hn.startFooter(e),hn.addSchema(e,n),hn.addVersion(e,Ce.V5),hn.addRecordBatches(e,r),hn.addDictionaries(e,i),hn.finishFooterBuffer(e,hn.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}constructor(t,e=Ce.V5,n,r){this.schema=t,this.version=e,n&&(this._recordBatches=n),r&&(this._dictionaryBatches=r)}*recordBatches(){for(let t,e=-1,n=this.numRecordBatches;++e<n;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,n=this.numDictionaries;++e<n;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class ny extends uc{get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}constructor(t,e){super(t,e.version()),this._footer=e}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return Ui.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return Ui.decode(e)}return null}}class Ui{static decode(t){return new Ui(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:n}=e,r=BigInt(e.offset),i=BigInt(e.bodyLength);return Bl.createBlock(t,r,n,i)}constructor(t,e,n){this.metaDataLength=t,this.offset=_e(n),this.bodyLength=_e(e)}}const Ie=Object.freeze({done:!0,value:void 0});class hu{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class ip{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class ry extends ip{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}get closed(){return this._closedPromise}cancel(t){return Mt(this,void 0,void 0,(function*(){yield this.return(t)}))}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Ie);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return yn.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return yn.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return Mt(this,void 0,void 0,(function*(){return yield this.abort(t),Ie}))}return(t){return Mt(this,void 0,void 0,(function*(){return yield this.close(),Ie}))}read(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(((t,e)=>{this.resolvers.push({resolve:t,reject:e})})):Promise.resolve(Ie)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class iy extends ry{write(t){if((t=Jt(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?wl(this.toUint8Array(!0)):this.toUint8Array(!1).then(wl)}toUint8Array(t=!1){return t?er(this._values)[0]:Mt(this,void 0,void 0,(function*(){var t,e,n,r;const i=[];let o=0;try{for(var s,a=!0,l=Ti(this);!(t=(s=yield l.next()).done);a=!0){r=s.value,a=!1;const t=r;i.push(t),o+=t.byteLength}}catch(t){e={error:t}}finally{try{!a&&!t&&(n=l.return)&&(yield n.call(l))}finally{if(e)throw e.error}}return er(i,o)[0]}))}}class va{constructor(t){t&&(this.source=new oy(yn.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Vi{constructor(t){t instanceof Vi?this.source=t.source:t instanceof iy?this.source=new Hr(yn.fromAsyncIterable(t)):Oh(t)?this.source=new Hr(yn.fromNodeStream(t)):Zl(t)?this.source=new Hr(yn.fromDOMStream(t)):Sh(t)?this.source=new Hr(yn.fromDOMStream(t.body)):Pa(t)?this.source=new Hr(yn.fromIterable(t)):(zo(t)||Xl(t))&&(this.source=new Hr(yn.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class oy{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Ie)}return(t){return Object.create(this.source.return&&this.source.return(t)||Ie)}}class Hr{constructor(t){this.source=t,this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}cancel(t){return Mt(this,void 0,void 0,(function*(){yield this.return(t)}))}get closed(){return this._closedPromise}read(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(t){return Mt(this,arguments,void 0,(function*(t,e="read"){return yield this.source.next({cmd:e,size:t})}))}throw(t){return Mt(this,void 0,void 0,(function*(){const e=this.source.throw&&(yield this.source.throw(t))||Ie;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}return(t){return Mt(this,void 0,void 0,(function*(){const e=this.source.return&&(yield this.source.return(t))||Ie;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}}class mu extends va{constructor(t,e){super(),this.position=0,this.buffer=Jt(t),this.size=void 0===e?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:n}=this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:n,position:r}=this;return e&&r<n?("number"!=typeof t&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(n,r+Math.min(n-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const n=this.buffer,r=Math.min(this.size,t+e);return n?n.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class wa extends Vi{constructor(t,e){super(),this.position=0,this._handle=t,"number"==typeof e?this.size=e:this._pending=Mt(this,void 0,void 0,(function*(){this.size=(yield t.stat()).size,delete this._pending}))}readInt32(t){return Mt(this,void 0,void 0,(function*(){const{buffer:e,byteOffset:n}=yield this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}))}seek(t){return Mt(this,void 0,void 0,(function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size}))}read(t){return Mt(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:e,size:n,position:r}=this;if(e&&r<n){"number"!=typeof t&&(t=Number.POSITIVE_INFINITY);let i=r,o=0,s=0;const a=Math.min(n,i+Math.min(n-i,t)),l=new Uint8Array(Math.max(0,(this.position=a)-i));for(;(i+=s)<a&&(o+=s)<l.byteLength;)({bytesRead:s}=yield e.read(l,o,l.byteLength-o,i));return l}return null}))}readAt(t,e){return Mt(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:n,size:r}=this;if(n&&t+e<r){const i=Math.min(r,t+e),o=new Uint8Array(i-t);return(yield n.read(o,0,e,t)).buffer}return new Uint8Array(e)}))}close(){return Mt(this,void 0,void 0,(function*(){const t=this._handle;this._handle=null,t&&(yield t.close())}))}throw(t){return Mt(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}return(t){return Mt(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}}const sy=65536;function Di(t){return t<0&&(t=4294967295+t+1),`0x${t.toString(16)}`}const xi=8,fc=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class op{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([t.buffer[1]>>>16,65535&t.buffer[1],t.buffer[0]>>>16,65535&t.buffer[0]]);let r=e[3]*n[3];this.buffer[0]=65535&r;let i=r>>>16;return r=e[2]*n[3],i+=r,r=e[3]*n[2]>>>0,i+=r,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?sy:0,this.buffer[1]+=i>>>16,this.buffer[1]+=e[1]*n[3]+e[2]*n[2]+e[3]*n[1],this.buffer[1]+=e[0]*n[3]+e[1]*n[2]+e[2]*n[1]+e[3]*n[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Di(this.buffer[1])} ${Di(this.buffer[0])}`}}class ie extends op{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return ie.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return ie.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const n=t.length,r=new ie(e);for(let e=0;e<n;){const i=8<n-e?8:n-e,o=new ie(new Uint32Array([Number.parseInt(t.slice(e,e+i),10),0])),s=new ie(new Uint32Array([fc[i],0]));r.times(s),r.plus(o),e+=i}return r}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)ie.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new ie(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ie(new Uint32Array(t.buffer)).plus(e)}}class on extends op{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=0|this.buffer[1],n=0|t.buffer[1];return e<n||e===n&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return on.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return on.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const n=t.startsWith("-"),r=t.length,i=new on(e);for(let e=n?1:0;e<r;){const n=8<r-e?8:r-e,o=new on(new Uint32Array([Number.parseInt(t.slice(e,e+n),10),0])),s=new on(new Uint32Array([fc[n],0]));i.times(s),i.plus(o),e+=n}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)on.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new on(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new on(new Uint32Array(t.buffer)).plus(e)}}class Yn{constructor(t){this.buffer=t}high(){return new on(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new on(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(t){const e=new ie(new Uint32Array([this.buffer[3],0])),n=new ie(new Uint32Array([this.buffer[2],0])),r=new ie(new Uint32Array([this.buffer[1],0])),i=new ie(new Uint32Array([this.buffer[0],0])),o=new ie(new Uint32Array([t.buffer[3],0])),s=new ie(new Uint32Array([t.buffer[2],0])),a=new ie(new Uint32Array([t.buffer[1],0])),l=new ie(new Uint32Array([t.buffer[0],0]));let c=ie.multiply(i,l);this.buffer[0]=c.low();const u=new ie(new Uint32Array([c.high(),0]));return c=ie.multiply(r,l),u.plus(c),c=ie.multiply(i,a),u.plus(c),this.buffer[1]=u.low(),this.buffer[3]=u.lessThan(c)?1:0,this.buffer[2]=u.high(),new ie(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(ie.multiply(n,l)).plus(ie.multiply(r,a)).plus(ie.multiply(i,s)),this.buffer[3]+=ie.multiply(e,l).plus(ie.multiply(n,a)).plus(ie.multiply(r,s)).plus(ie.multiply(i,o)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Di(this.buffer[3])} ${Di(this.buffer[2])} ${Di(this.buffer[1])} ${Di(this.buffer[0])}`}static multiply(t,e){return new Yn(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new Yn(new Uint32Array(t.buffer)).plus(e)}static from(t,e=new Uint32Array(4)){return Yn.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return Yn.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const n=t.startsWith("-"),r=t.length,i=new Yn(e);for(let e=n?1:0;e<r;){const n=8<r-e?8:r-e,o=new Yn(new Uint32Array([Number.parseInt(t.slice(e,e+n),10),0,0,0])),s=new Yn(new Uint32Array([fc[n],0,0,0]));i.times(s),i.plus(o),e+=n}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(4*t.length);for(let n=-1,r=t.length;++n<r;)Yn.from(t[n],new Uint32Array(e.buffer,e.byteOffset+16*n,4));return e}}class sp extends zt{constructor(t,e,n,r,i=Ce.V5){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=n,this.dictionaries=r,this.metadataVersion=i}visit(t){return super.visit(t instanceof be?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return Vt({type:t,length:e})}visitBool(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitInt(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFloat(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitUtf8(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeUtf8(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDate(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTimestamp(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTime(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDecimal(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitList(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),children:this.visitMany(t.children)})}visitUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return this.metadataVersion<Ce.V5&&this.readNullBitmap(t,n),t.mode===cn.Sparse?this.visitSparseUnion(t,{length:e,nullCount:n}):this.visitDenseUnion(t,{length:e,nullCount:n})}visitDenseUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDuration(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFixedSizeList(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),child:this.visit(t.children[0])})}visitMap(t,{length:e,nullCount:n}=this.nextFieldNode()){return Vt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,n=this.nextBufferRange()){return e>0&&this.readData(t,n)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:n}=this.nextBufferRange()){return this.bytes.subarray(n,n+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class ay extends sp{constructor(t,e,n,r,i){super(new Uint8Array(0),e,n,r,i),this.sources=t}readNullBitmap(t,e,{offset:n}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Al(this.sources[n])}readOffsets(t,{offset:e}=this.nextBufferRange()){return se(Uint8Array,se(t.OffsetArrayType,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return se(Uint8Array,se(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:n}=this;return ut.isTimestamp(t)||(ut.isInt(t)||ut.isTime(t))&&64===t.bitWidth||ut.isDuration(t)||ut.isDate(t)&&t.unit===Dn.MILLISECOND?se(Uint8Array,on.convertArray(n[e])):ut.isDecimal(t)?se(Uint8Array,Yn.convertArray(n[e])):ut.isBinary(t)||ut.isLargeBinary(t)||ut.isFixedSizeBinary(t)?ly(n[e]):ut.isBool(t)?Al(n[e]):ut.isUtf8(t)||ut.isLargeUtf8(t)?Gl(n[e].join("")):se(Uint8Array,se(t.ArrayType,n[e].map((t=>+t))))}}function ly(t){const e=t.join(""),n=new Uint8Array(e.length/2);for(let t=0;t<e.length;t+=2)n[t>>1]=Number.parseInt(e.slice(t,t+2),16);return n}class bt extends zt{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every(((t,n)=>this.compareFields(t,e[n])))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function en(t,e){return e instanceof t.constructor}function ii(t,e){return t===e||en(t,e)}function Ir(t,e){return t===e||en(t,e)&&t.bitWidth===e.bitWidth&&t.isSigned===e.isSigned}function Ca(t,e){return t===e||en(t,e)&&t.precision===e.precision}function cy(t,e){return t===e||en(t,e)&&t.byteWidth===e.byteWidth}function dc(t,e){return t===e||en(t,e)&&t.unit===e.unit}function ns(t,e){return t===e||en(t,e)&&t.unit===e.unit&&t.timezone===e.timezone}function rs(t,e){return t===e||en(t,e)&&t.unit===e.unit&&t.bitWidth===e.bitWidth}function uy(t,e){return t===e||en(t,e)&&t.children.length===e.children.length&&Fr.compareManyFields(t.children,e.children)}function fy(t,e){return t===e||en(t,e)&&t.children.length===e.children.length&&Fr.compareManyFields(t.children,e.children)}function hc(t,e){return t===e||en(t,e)&&t.mode===e.mode&&t.typeIds.every(((t,n)=>t===e.typeIds[n]))&&Fr.compareManyFields(t.children,e.children)}function dy(t,e){return t===e||en(t,e)&&t.id===e.id&&t.isOrdered===e.isOrdered&&Fr.visit(t.indices,e.indices)&&Fr.visit(t.dictionary,e.dictionary)}function mc(t,e){return t===e||en(t,e)&&t.unit===e.unit}function is(t,e){return t===e||en(t,e)&&t.unit===e.unit}function hy(t,e){return t===e||en(t,e)&&t.listSize===e.listSize&&t.children.length===e.children.length&&Fr.compareManyFields(t.children,e.children)}function my(t,e){return t===e||en(t,e)&&t.keysSorted===e.keysSorted&&t.children.length===e.children.length&&Fr.compareManyFields(t.children,e.children)}bt.prototype.visitNull=ii,bt.prototype.visitBool=ii,bt.prototype.visitInt=Ir,bt.prototype.visitInt8=Ir,bt.prototype.visitInt16=Ir,bt.prototype.visitInt32=Ir,bt.prototype.visitInt64=Ir,bt.prototype.visitUint8=Ir,bt.prototype.visitUint16=Ir,bt.prototype.visitUint32=Ir,bt.prototype.visitUint64=Ir,bt.prototype.visitFloat=Ca,bt.prototype.visitFloat16=Ca,bt.prototype.visitFloat32=Ca,bt.prototype.visitFloat64=Ca,bt.prototype.visitUtf8=ii,bt.prototype.visitLargeUtf8=ii,bt.prototype.visitBinary=ii,bt.prototype.visitLargeBinary=ii,bt.prototype.visitFixedSizeBinary=cy,bt.prototype.visitDate=dc,bt.prototype.visitDateDay=dc,bt.prototype.visitDateMillisecond=dc,bt.prototype.visitTimestamp=ns,bt.prototype.visitTimestampSecond=ns,bt.prototype.visitTimestampMillisecond=ns,bt.prototype.visitTimestampMicrosecond=ns,bt.prototype.visitTimestampNanosecond=ns,bt.prototype.visitTime=rs,bt.prototype.visitTimeSecond=rs,bt.prototype.visitTimeMillisecond=rs,bt.prototype.visitTimeMicrosecond=rs,bt.prototype.visitTimeNanosecond=rs,bt.prototype.visitDecimal=ii,bt.prototype.visitList=uy,bt.prototype.visitStruct=fy,bt.prototype.visitUnion=hc,bt.prototype.visitDenseUnion=hc,bt.prototype.visitSparseUnion=hc,bt.prototype.visitDictionary=dy,bt.prototype.visitInterval=mc,bt.prototype.visitIntervalDayTime=mc,bt.prototype.visitIntervalYearMonth=mc,bt.prototype.visitDuration=is,bt.prototype.visitDurationSecond=is,bt.prototype.visitDurationMillisecond=is,bt.prototype.visitDurationMicrosecond=is,bt.prototype.visitDurationNanosecond=is,bt.prototype.visitFixedSizeList=hy,bt.prototype.visitMap=my;const Fr=new bt;function py(t,e){return Fr.compareSchemas(t,e)}function tl(t,e){return gy(t,e.map((t=>t.data.concat())))}function gy(t,e){const n=[...t.fields],r=[],i={numBatches:e.reduce(((t,e)=>Math.max(t,e.length)),0)};let o=0,s=0,a=-1;const l=e.length;let c,u=[];for(;i.numBatches-- >0;){for(s=Number.POSITIVE_INFINITY,a=-1;++a<l;)u[a]=c=e[a].shift(),s=Math.min(s,c?c.length:s);Number.isFinite(s)&&(u=_y(n,s,u,e,i),s>0&&(r[o++]=Vt({type:new tn(n),length:s,nullCount:0,children:u.slice()})))}return[t=t.assign(n),r.map((e=>new Fn(t,e)))]}function _y(t,e,n,r,i){var o;const s=(e+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const l=n[a],c=null==l?void 0:l.length;if(c>=e)c===e?n[a]=l:(n[a]=l.slice(0,e),i.numBatches=Math.max(i.numBatches,r[a].unshift(l.slice(e,c-e))));else{const r=t[a];t[a]=r.clone({nullable:!0}),n[a]=null!==(o=null==l?void 0:l._changeLengthAndBackfillNullBitmap(e))&&void 0!==o?o:Vt({type:r.type,length:e,nullCount:e,nullBitmap:new Uint8Array(s)})}}return n}var ap,lp;class Sn{constructor(...t){var e,n;if(0===t.length)return this.batches=[],this.schema=new te([]),this._offsets=[0],this;let r,i;t[0]instanceof te&&(r=t.shift()),t.at(-1)instanceof Uint32Array&&(i=t.pop());const o=t=>{if(t){if(t instanceof Fn)return[t];if(t instanceof Sn)return t.batches;if(t instanceof re){if(t.type instanceof tn)return[new Fn(new te(t.type.children),t)]}else{if(Array.isArray(t))return t.flatMap((t=>o(t)));if("function"==typeof t[Symbol.iterator])return[...t].flatMap((t=>o(t)));if("object"==typeof t){const e=Object.keys(t),n=e.map((e=>new ce([t[e]]))),i=r??new te(e.map(((t,e)=>new be(String(t),n[e].type,n[e].nullable)))),[,o]=tl(i,n);return 0===o.length?[new Fn(t)]:o}}}return[]},s=t.flatMap((t=>o(t)));if(r=null!==(n=r??(null===(e=s[0])||void 0===e?void 0:e.schema))&&void 0!==n?n:new te([]),!(r instanceof te))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const t of s){if(!(t instanceof Fn))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!py(r,t.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=r,this.batches=s,this._offsets=i??Qm(this.data)}get data(){return this.batches.map((({data:t})=>t))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce(((t,e)=>t+e.length),0)}get nullCount(){return-1===this._nullCount&&(this._nullCount=Jm(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}at(t){return this.get(oc(t,this.numRows))}set(t,e){}indexOf(t,e){return-1}[Symbol.iterator](){return this.batches.length>0?cc.visit(new ce(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[\n  ${this.toArray().join(",\n  ")}\n]`}concat(...t){const e=this.schema,n=this.data.concat(t.flatMap((({data:t})=>t)));return new Sn(e,n.map((t=>new Fn(e,t))))}slice(t,e){const n=this.schema;[t,e]=Km({length:this.numRows},t,e);const r=Gm(this.data,this._offsets,t,e);return new Sn(n,r.map((t=>new Fn(n,t))))}getChild(t){return this.getChildAt(this.schema.fields.findIndex((e=>e.name===t)))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map((e=>e.children[t]));if(0===e.length){const{type:n}=this.schema.fields[t],r=Vt({type:n,length:0,nullCount:0});e.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new ce(e)}return null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let n=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new ce([Vt({type:new Cr,length:this.numRows})]));const i=n.fields.slice(),o=i[t].clone({type:e.type}),s=this.schema.fields.map(((t,e)=>this.getChildAt(e)));[i[t],s[t]]=[o,e],[n,r]=tl(n,s)}return new Sn(n,r)}select(t){const e=this.schema.fields.reduce(((t,e,n)=>t.set(e.name,n)),new Map);return this.selectAt(t.map((t=>e.get(t))).filter((t=>t>-1)))}selectAt(t){const e=this.schema.selectAt(t),n=this.batches.map((e=>e.selectAt(t)));return new Sn(e,n)}assign(t){const e=this.schema.fields,[n,r]=t.schema.fields.reduce(((t,n,r)=>{const[i,o]=t,s=e.findIndex((t=>t.name===n.name));return~s?o[s]=r:i.push(r),t}),[[],[]]),i=this.schema.assign(t.schema),o=[...e.map(((t,e)=>[e,r[e]])).map((([e,n])=>void 0===n?this.getChildAt(e):t.getChildAt(n))),...n.map((e=>t.getChildAt(e)))].filter(Boolean);return new Sn(...tl(i,o))}}ap=Symbol.toStringTag,Sn[ap]=(t=>(t.schema=null,t.batches=[],t._offsets=new Uint32Array([0]),t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,t.isValid=_a(lc),t.get=_a(un.getVisitFn(E.Struct)),t.set=Xm(En.getVisitFn(E.Struct)),t.indexOf=Zm(ba.getVisitFn(E.Struct)),"Table"))(Sn.prototype);let Fn=class t{constructor(...t){switch(t.length){case 2:if([this.schema]=t,!(this.schema instanceof te))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=Vt({nullCount:0,type:new tn(this.schema.fields),children:this.schema.fields.map((t=>Vt({type:t.type,nullCount:0})))})]=t,!(this.data instanceof re))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=pu(this.schema,this.data.children);break;case 1:{const[e]=t,{fields:n,children:r,length:i}=Object.keys(e).reduce(((t,n,r)=>(t.children[r]=e[n],t.length=Math.max(t.length,e[n].length),t.fields[r]=be.new({name:n,type:e[n].type,nullable:!0}),t)),{length:0,fields:new Array,children:new Array}),o=new te(n),s=Vt({type:new tn(n),length:i,children:r,nullCount:0});[this.schema,this.data]=pu(o,s.children,i);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=cp(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return un.visit(this.data,t)}at(t){return this.get(oc(t,this.numRows))}set(t,e){return En.visit(this.data,t,e)}indexOf(t,e){return ba.visit(this.data,t,e)}[Symbol.iterator](){return cc.visit(new ce([this.data]))}toArray(){return[...this]}concat(...t){return new Sn(this.schema,[this,...t])}slice(e,n){const[r]=new ce([this.data]).slice(e,n).data;return new t(this.schema,r)}getChild(t){var e;return this.getChildAt(null===(e=this.schema.fields)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new ce([this.data.children[t]]):null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(e,n){let r=this.schema,i=this.data;if(e>-1&&e<this.numCols){n||(n=new ce([Vt({type:new Cr,length:this.numRows})]));const t=r.fields.slice(),o=i.children.slice(),s=t[e].clone({type:n.type});[t[e],o[e]]=[s,n.data[0]],r=new te(t,new Map(this.schema.metadata)),i=Vt({type:new tn(t),children:o})}return new t(r,i)}select(e){const n=this.schema.select(e),r=new tn(n.fields),i=[];for(const t of e){const e=this.schema.fields.findIndex((e=>e.name===t));~e&&(i[e]=this.data.children[e])}return new t(n,Vt({type:r,length:this.numRows,children:i}))}selectAt(e){const n=this.schema.selectAt(e),r=e.map((t=>this.data.children[t])).filter(Boolean),i=Vt({type:new tn(n.fields),length:this.numRows,children:r});return new t(n,i)}};function pu(t,e,n=e.reduce(((t,e)=>Math.max(t,e.length)),0)){var r;const i=[...t.fields],o=[...e],s=(n+63&-64)>>3;for(const[a,l]of t.fields.entries()){const t=e[a];(!t||t.length!==n)&&(i[a]=l.clone({nullable:!0}),o[a]=null!==(r=null==t?void 0:t._changeLengthAndBackfillNullBitmap(n))&&void 0!==r?r:Vt({type:l.type,length:n,nullCount:n,nullBitmap:new Uint8Array(s)}))}return[t.assign(i),Vt({type:new tn(i),length:n,children:o})]}function cp(t,e,n=new Map){var r,i;if((null!==(r=null==t?void 0:t.length)&&void 0!==r?r:0)>0&&(null==t?void 0:t.length)===(null==e?void 0:e.length))for(let r=-1,o=t.length;++r<o;){const{type:o}=t[r],s=e[r];for(const t of[s,...(null===(i=null==s?void 0:s.dictionary)||void 0===i?void 0:i.data)||[]])cp(o.children,null==t?void 0:t.children,n);if(ut.isDictionary(o)){const{id:t}=o;if(n.has(t)){if(n.get(t)!==s.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else null!=s&&s.dictionary&&n.set(t,s.dictionary)}}return n}lp=Symbol.toStringTag,Fn[lp]=(t=>(t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(Fn.prototype);class up extends Fn{constructor(t){const e=t.fields.map((t=>Vt({type:t.type})));super(t,Vt({type:new tn(t.fields),nullCount:0,children:e}))}}let Er=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMessage(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ce.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):Zt.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new je).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Ce.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,Zt.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,BigInt("0"))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(e,n,r,i,o,s){return t.startMessage(e),t.addVersion(e,n),t.addHeaderType(e,r),t.addHeader(e,i),t.addBodyLength(e,o),t.addCustomMetadata(e,s),t.endMessage(e)}};class by extends zt{visit(t,e){return null==t||null==e?void 0:super.visit(t,e)}visitNull(t,e){return ou.startNull(e),ou.endNull(e)}visitInt(t,e){return mn.startInt(e),mn.addBitWidth(e,t.bitWidth),mn.addIsSigned(e,t.isSigned),mn.endInt(e)}visitFloat(t,e){return Jn.startFloatingPoint(e),Jn.addPrecision(e,t.precision),Jn.endFloatingPoint(e)}visitBinary(t,e){return tu.startBinary(e),tu.endBinary(e)}visitLargeBinary(t,e){return nu.startLargeBinary(e),nu.endLargeBinary(e)}visitBool(t,e){return eu.startBool(e),eu.endBool(e)}visitUtf8(t,e){return su.startUtf8(e),su.endUtf8(e)}visitLargeUtf8(t,e){return ru.startLargeUtf8(e),ru.endLargeUtf8(e)}visitDecimal(t,e){return _i.startDecimal(e),_i.addScale(e,t.scale),_i.addPrecision(e,t.precision),_i.addBitWidth(e,t.bitWidth),_i.endDecimal(e)}visitDate(t,e){return Ls.startDate(e),Ls.addUnit(e,t.unit),Ls.endDate(e)}visitTime(t,e){return vn.startTime(e),vn.addUnit(e,t.unit),vn.addBitWidth(e,t.bitWidth),vn.endTime(e)}visitTimestamp(t,e){const n=t.timezone&&e.createString(t.timezone)||void 0;return wn.startTimestamp(e),wn.addUnit(e,t.unit),void 0!==n&&wn.addTimezone(e,n),wn.endTimestamp(e)}visitInterval(t,e){return Qn.startInterval(e),Qn.addUnit(e,t.unit),Qn.endInterval(e)}visitDuration(t,e){return Rs.startDuration(e),Rs.addUnit(e,t.unit),Rs.endDuration(e)}visitList(t,e){return iu.startList(e),iu.endList(e)}visitStruct(t,e){return ti.startStruct_(e),ti.endStruct_(e)}visitUnion(t,e){sn.startTypeIdsVector(e,t.typeIds.length);const n=sn.createTypeIdsVector(e,t.typeIds);return sn.startUnion(e),sn.addMode(e,t.mode),sn.addTypeIds(e,n),sn.endUnion(e)}visitDictionary(t,e){const n=this.visit(t.indices,e);return dr.startDictionaryEncoding(e),dr.addId(e,BigInt(t.id)),dr.addIsOrdered(e,t.isOrdered),void 0!==n&&dr.addIndexType(e,n),dr.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return Us.startFixedSizeBinary(e),Us.addByteWidth(e,t.byteWidth),Us.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return Vs.startFixedSizeList(e),Vs.addListSize(e,t.listSize),Vs.endFixedSizeList(e)}visitMap(t,e){return xs.startMap(e),xs.addKeysSorted(e,t.keysSorted),xs.endMap(e)}}const el=new by;function yy(t,e=new Map){return new te(wy(t,e),Ws(t.metadata),e)}function fp(t){return new Rn(t.count,dp(t.columns),hp(t.columns))}function vy(t){return new vr(fp(t.data),t.id,t.isDelta)}function wy(t,e){return(t.fields||[]).filter(Boolean).map((t=>be.fromJSON(t,e)))}function gu(t,e){return(t.children||[]).filter(Boolean).map((t=>be.fromJSON(t,e)))}function dp(t){return(t||[]).reduce(((t,e)=>[...t,new Yi(e.count,Sy(e.VALIDITY)),...dp(e.children)]),[])}function hp(t,e=[]){for(let n=-1,r=(t||[]).length;++n<r;){const r=t[n];r.VALIDITY&&e.push(new hr(e.length,r.VALIDITY.length)),r.TYPE_ID&&e.push(new hr(e.length,r.TYPE_ID.length)),r.OFFSET&&e.push(new hr(e.length,r.OFFSET.length)),r.DATA&&e.push(new hr(e.length,r.DATA.length)),e=hp(r.children,e)}return e}function Sy(t){return(t||[]).reduce(((t,e)=>t+ +(0===e)),0)}function Iy(t,e){let n,r,i,o,s,a;return e&&(o=t.dictionary)?e.has(n=o.id)?(r=(r=o.indexType)?_u(r):new Yo,a=new Ri(e.get(n),r,n,o.isOrdered),i=new be(t.name,a,t.nullable,Ws(t.metadata))):(r=(r=o.indexType)?_u(r):new Yo,e.set(n,s=bu(t,gu(t,e))),a=new Ri(s,r,n,o.isOrdered),i=new be(t.name,a,t.nullable,Ws(t.metadata))):(s=bu(t,gu(t,e)),i=new be(t.name,s,t.nullable,Ws(t.metadata))),i||null}function Ws(t=[]){return new Map(t.map((({key:t,value:e})=>[t,e])))}function _u(t){return new ni(t.isSigned,t.bitWidth)}function bu(t,e){const n=t.type.name;switch(n){case"NONE":case"null":return new Cr;case"binary":return new ea;case"largebinary":return new na;case"utf8":return new ra;case"largeutf8":return new ia;case"bool":return new oa;case"list":return new da((e||[])[0]);case"struct":case"struct_":return new tn(e||[])}switch(n){case"int":{const e=t.type;return new ni(e.isSigned,e.bitWidth)}case"floatingpoint":{const e=t.type;return new ta(Ye[e.precision])}case"decimal":{const e=t.type;return new sa(e.scale,e.precision,e.bitWidth)}case"date":{const e=t.type;return new aa(Dn[e.unit])}case"time":{const e=t.type;return new la(yt[e.unit],e.bitWidth)}case"timestamp":{const e=t.type;return new ca(yt[e.unit],e.timezone)}case"interval":{const e=t.type;return new ua(nr[e.unit])}case"duration":{const e=t.type;return new fa(yt[e.unit])}case"union":{const n=t.type,[r,...i]=(n.mode+"").toLowerCase(),o=r.toUpperCase()+i.join("");return new ha(cn[o],n.typeIds||[],e||[])}case"fixedsizebinary":{const e=t.type;return new ma(e.byteWidth)}case"fixedsizelist":{const n=t.type;return new pa(n.listSize,(e||[])[0])}case"map":{const n=t.type;return new ga((e||[])[0],n.keysSorted)}}throw new Error(`Unrecognized type: "${n}"`)}var Oy=Eh,ky=ji;class jn{static fromJSON(t,e){const n=new jn(0,Ce.V5,e);return n._createHeader=Dy(t,e),n}static decode(t){t=new ky(Jt(t));const e=Er.getRootAsMessage(t),n=e.bodyLength(),r=e.version(),i=e.headerType(),o=new jn(n,r,i);return o._createHeader=Ey(e,i),o}static encode(t){const e=new Oy;let n=-1;return t.isSchema()?n=te.encode(e,t.header()):t.isRecordBatch()?n=Rn.encode(e,t.header()):t.isDictionaryBatch()&&(n=vr.encode(e,t.header())),Er.startMessage(e),Er.addVersion(e,Ce.V5),Er.addHeader(e,n),Er.addHeaderType(e,t.headerType),Er.addBodyLength(e,BigInt(t.bodyLength)),Er.finishMessageBuffer(e,Er.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof te)return new jn(0,Ce.V5,Zt.Schema,t);if(t instanceof Rn)return new jn(e,Ce.V5,Zt.RecordBatch,t);if(t instanceof vr)return new jn(e,Ce.V5,Zt.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===Zt.Schema}isRecordBatch(){return this.headerType===Zt.RecordBatch}isDictionaryBatch(){return this.headerType===Zt.DictionaryBatch}constructor(t,e,n,r){this._version=e,this._headerType=n,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength=_e(t)}}class Rn{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,n){this._nodes=e,this._buffers=n,this._length=_e(t)}}class vr{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,n=!1){this._data=t,this._isDelta=n,this._id=_e(e)}}class hr{constructor(t,e){this.offset=_e(t),this.length=_e(e)}}class Yi{constructor(t,e){this.length=_e(t),this.nullCount=_e(e)}}function Dy(t,e){return()=>{switch(e){case Zt.Schema:return te.fromJSON(t);case Zt.RecordBatch:return Rn.fromJSON(t);case Zt.DictionaryBatch:return vr.fromJSON(t)}throw new Error(`Unrecognized Message type: { name: ${Zt[e]}, type: ${e} }`)}}function Ey(t,e){return()=>{switch(e){case Zt.Schema:return te.decode(t.header(new qn),new Map,t.version());case Zt.RecordBatch:return Rn.decode(t.header(new cr),t.version());case Zt.DictionaryBatch:return vr.decode(t.header(new pi),t.version())}throw new Error(`Unrecognized Message type: { name: ${Zt[e]}, type: ${e} }`)}}function Ay(t,e=new Map,n=Ce.V5){const r=Fy(t,e);return new te(r,Hs(t),e,n)}function Ty(t,e=Ce.V5){if(null!==t.compression())throw new Error("Record batch compression not implemented");return new Rn(t.length(),Ny(t),Cy(t,e))}function By(t,e=Ce.V5){return new vr(Rn.decode(t.data(),e),t.id(),t.isDelta())}function My(t){return new hr(t.offset(),t.length())}function Py(t){return new Yi(t.length(),t.nullCount())}function Ny(t){const e=[];for(let n,r=-1,i=-1,o=t.nodesLength();++r<o;)(n=t.nodes(r))&&(e[++i]=Yi.decode(n));return e}function Cy(t,e){const n=[];for(let r,i=-1,o=-1,s=t.buffersLength();++i<s;)(r=t.buffers(i))&&(e<Ce.V4&&(r.bb_pos+=8*(i+1)),n[++o]=hr.decode(r));return n}function Fy(t,e){const n=[];for(let r,i=-1,o=-1,s=t.fieldsLength();++i<s;)(r=t.fields(i))&&(n[++o]=be.decode(r,e));return n}function yu(t,e){const n=[];for(let r,i=-1,o=-1,s=t.childrenLength();++i<s;)(r=t.children(i))&&(n[++o]=be.decode(r,e));return n}function jy(t,e){let n,r,i,o,s,a;return e&&(a=t.dictionary())?e.has(n=_e(a.id()))?(o=(o=a.indexType())?vu(o):new Yo,s=new Ri(e.get(n),o,n,a.isOrdered()),r=new be(t.name(),s,t.nullable(),Hs(t))):(o=(o=a.indexType())?vu(o):new Yo,e.set(n,i=wu(t,yu(t,e))),s=new Ri(i,o,n,a.isOrdered()),r=new be(t.name(),s,t.nullable(),Hs(t))):(i=wu(t,yu(t,e)),r=new be(t.name(),i,t.nullable(),Hs(t))),r||null}function Hs(t){const e=new Map;if(t)for(let n,r,i=-1,o=Math.trunc(t.customMetadataLength());++i<o;)(n=t.customMetadata(i))&&null!=(r=n.key())&&e.set(r,n.value());return e}function vu(t){return new ni(t.isSigned(),t.bitWidth())}function wu(t,e){const n=t.typeType();switch(n){case de.NONE:case de.Null:return new Cr;case de.Binary:return new ea;case de.LargeBinary:return new na;case de.Utf8:return new ra;case de.LargeUtf8:return new ia;case de.Bool:return new oa;case de.List:return new da((e||[])[0]);case de.Struct_:return new tn(e||[])}switch(n){case de.Int:{const e=t.type(new mn);return new ni(e.isSigned(),e.bitWidth())}case de.FloatingPoint:{const e=t.type(new Jn);return new ta(e.precision())}case de.Decimal:{const e=t.type(new _i);return new sa(e.scale(),e.precision(),e.bitWidth())}case de.Date:{const e=t.type(new Ls);return new aa(e.unit())}case de.Time:{const e=t.type(new vn);return new la(e.unit(),e.bitWidth())}case de.Timestamp:{const e=t.type(new wn);return new ca(e.unit(),e.timezone())}case de.Interval:{const e=t.type(new Qn);return new ua(e.unit())}case de.Duration:{const e=t.type(new Rs);return new fa(e.unit())}case de.Union:{const n=t.type(new sn);return new ha(n.mode(),n.typeIdsArray()||[],e||[])}case de.FixedSizeBinary:{const e=t.type(new Us);return new ma(e.byteWidth())}case de.FixedSizeList:{const n=t.type(new Vs);return new pa(n.listSize(),(e||[])[0])}case de.Map:{const n=t.type(new xs);return new ga((e||[])[0],n.keysSorted())}}throw new Error(`Unrecognized type: "${de[n]}" (${n})`)}function Ly(t,e){const n=e.fields.map((e=>be.encode(t,e)));qn.startFieldsVector(t,n.length);const r=qn.createFieldsVector(t,n),i=e.metadata&&e.metadata.size>0?qn.createCustomMetadataVector(t,[...e.metadata].map((([e,n])=>{const r=t.createString(`${e}`),i=t.createString(`${n}`);return je.startKeyValue(t),je.addKey(t,r),je.addValue(t,i),je.endKeyValue(t)}))):-1;return qn.startSchema(t),qn.addFields(t,r),qn.addEndianness(t,Wy?Li.Little:Li.Big),-1!==i&&qn.addCustomMetadata(t,i),qn.endSchema(t)}function Ry(t,e){let n=-1,r=-1,i=-1;const o=e.type;let s=e.typeId;ut.isDictionary(o)?(s=o.dictionary.typeId,i=el.visit(o,t),r=el.visit(o.dictionary,t)):r=el.visit(o,t);const a=(o.children||[]).map((e=>be.encode(t,e))),l=bn.createChildrenVector(t,a),c=e.metadata&&e.metadata.size>0?bn.createCustomMetadataVector(t,[...e.metadata].map((([e,n])=>{const r=t.createString(`${e}`),i=t.createString(`${n}`);return je.startKeyValue(t),je.addKey(t,r),je.addValue(t,i),je.endKeyValue(t)}))):-1;return e.name&&(n=t.createString(e.name)),bn.startField(t),bn.addType(t,r),bn.addTypeType(t,s),bn.addChildren(t,l),bn.addNullable(t,!!e.nullable),-1!==n&&bn.addName(t,n),-1!==i&&bn.addDictionary(t,i),-1!==c&&bn.addCustomMetadata(t,c),bn.endField(t)}function Uy(t,e){const n=e.nodes||[],r=e.buffers||[];cr.startNodesVector(t,n.length);for(const e of n.slice().reverse())Yi.encode(t,e);const i=t.endVector();cr.startBuffersVector(t,r.length);for(const e of r.slice().reverse())hr.encode(t,e);const o=t.endVector();return cr.startRecordBatch(t),cr.addLength(t,BigInt(e.length)),cr.addNodes(t,i),cr.addBuffers(t,o),cr.endRecordBatch(t)}function Vy(t,e){const n=Rn.encode(t,e.data);return pi.startDictionaryBatch(t),pi.addId(t,BigInt(e.id)),pi.addIsDelta(t,e.isDelta),pi.addData(t,n),pi.endDictionaryBatch(t)}function xy(t,e){return Bh.createFieldNode(t,BigInt(e.length),BigInt(e.nullCount))}function zy(t,e){return Th.createBuffer(t,BigInt(e.offset),BigInt(e.length))}be.encode=Ry,be.decode=jy,be.fromJSON=Iy,te.encode=Ly,te.decode=Ay,te.fromJSON=yy,Rn.encode=Uy,Rn.decode=Ty,Rn.fromJSON=fp,vr.encode=Vy,vr.decode=By,vr.fromJSON=vy,Yi.encode=xy,Yi.decode=Py,hr.encode=zy,hr.decode=My;const Wy=(()=>{const t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256===new Int16Array(t)[0]})(),pc=t=>`Expected ${Zt[t]} Message in stream, but was null or length 0.`,gc=t=>`Header pointer of flatbuffer-encoded ${Zt[t]} Message is null or length 0.`,mp=(t,e)=>`Expected to read ${t} metadata bytes, but only read ${e}.`,pp=(t,e)=>`Expected to read ${t} bytes for message body, but only read ${e}.`;class gp{constructor(t){this.source=t instanceof va?t:new va(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||-1===t.value&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Ie:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(pc(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=Jt(this.source.read(t));if(e.byteLength<t)throw new Error(pp(t,e.byteLength));return e.byteOffset%8==0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=Zt.Schema,n=this.readMessage(e),r=null==n?void 0:n.header();if(t&&!r)throw new Error(gc(e));return r}readMetadataLength(){const t=this.source.read(Fa),e=t&&new ji(t),n=(null==e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}readMetadata(t){const e=this.source.read(t);if(!e)return Ie;if(e.byteLength<t)throw new Error(mp(t,e.byteLength));return{done:!1,value:jn.decode(e)}}}class Hy{constructor(t,e){this.source=t instanceof Vi?t:wh(t)?new wa(t,e):new Vi(t)}[Symbol.asyncIterator](){return this}next(){return Mt(this,void 0,void 0,(function*(){let t;return(t=yield this.readMetadataLength()).done||-1===t.value&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Ie:t}))}throw(t){return Mt(this,void 0,void 0,(function*(){return yield this.source.throw(t)}))}return(t){return Mt(this,void 0,void 0,(function*(){return yield this.source.return(t)}))}readMessage(t){return Mt(this,void 0,void 0,(function*(){let e;if((e=yield this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(pc(t));return e.value}))}readMessageBody(t){return Mt(this,void 0,void 0,(function*(){if(t<=0)return new Uint8Array(0);const e=Jt(yield this.source.read(t));if(e.byteLength<t)throw new Error(pp(t,e.byteLength));return e.byteOffset%8==0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}))}readSchema(){return Mt(this,arguments,void 0,(function*(t=!1){const e=Zt.Schema,n=yield this.readMessage(e),r=null==n?void 0:n.header();if(t&&!r)throw new Error(gc(e));return r}))}readMetadataLength(){return Mt(this,void 0,void 0,(function*(){const t=yield this.source.read(Fa),e=t&&new ji(t),n=(null==e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}))}readMetadata(t){return Mt(this,void 0,void 0,(function*(){const e=yield this.source.read(t);if(!e)return Ie;if(e.byteLength<t)throw new Error(mp(t,e.byteLength));return{done:!1,value:jn.decode(e)}}))}}class qy extends gp{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof hu?t:new hu(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:jn.fromJSON(t.schema,Zt.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];return this._body=e.data.columns,{done:!1,value:jn.fromJSON(e,Zt.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];return this._body=e.columns,{done:!1,value:jn.fromJSON(e,Zt.RecordBatch)}}return this._body=[],Ie}readMessageBody(t){return function t(e){return(e||[]).reduce(((e,n)=>[...e,...n.VALIDITY&&[n.VALIDITY]||[],...n.TYPE_ID&&[n.TYPE_ID]||[],...n.OFFSET&&[n.OFFSET]||[],...n.DATA&&[n.DATA]||[],...t(n.children)]),[])}(this._body)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(pc(t));return e.value}readSchema(){const t=Zt.Schema,e=this.readMessage(t),n=null==e?void 0:e.header();if(!e||!n)throw new Error(gc(t));return n}}const Fa=4,Pl="ARROW1",Sa=new Uint8Array(6);for(let t=0;t<6;t+=1)Sa[t]=Pl.codePointAt(t);function _c(t,e=0){for(let n=-1,r=Sa.length;++n<r;)if(Sa[n]!==t[e+n])return!1;return!0}const os=Sa.length,_p=os+Fa,Yy=2*os+Fa;class pr extends ip{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return zo(e)?e.then((()=>this)):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return yn.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return yn.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof pr?t:Sl(t)?Qy(t):wh(t)?Zy(t):zo(t)?Mt(this,void 0,void 0,(function*(){return yield pr.from(yield t)})):Sh(t)||Zl(t)||Oh(t)||Xl(t)?Xy(new Vi(t)):Gy(new va(t))}static readAll(t){return t instanceof pr?t.isSync()?Su(t):Iu(t):Sl(t)||ArrayBuffer.isView(t)||Pa(t)||vh(t)?Su(t):Iu(t)}}class Ia extends pr{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return Gn(this,arguments,(function*(){yield Wt(yield*Cs(Ti(this[Symbol.iterator]())))}))}}class Oa extends pr{constructor(t){super(t),this._impl=t}readAll(){return Mt(this,void 0,void 0,(function*(){var t,e,n,r;const i=new Array;try{for(var o,s=!0,a=Ti(this);!(t=(o=yield a.next()).done);s=!0){r=o.value,s=!1;const t=r;i.push(t)}}catch(t){e={error:t}}finally{try{!s&&!t&&(n=a.return)&&(yield n.call(a))}finally{if(e)throw e.error}}return i}))}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class bp extends Ia{constructor(t){super(t),this._impl=t}}class Ky extends Oa{constructor(t){super(t),this._impl=t}}class yp{get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const n=this._loadVectors(t,e,this.schema.fields),r=Vt({type:new tn(this.schema.fields),length:t.length,children:n});return new Fn(this.schema,r)}_loadDictionaryBatch(t,e){const{id:n,isDelta:r}=t,{dictionaries:i,schema:o}=this,s=i.get(n),a=o.dictionaries.get(n),l=this._loadVectors(t.data,e,[a]);return(s&&r?s.concat(new ce(l)):new ce(l)).memoize()}_loadVectors(t,e,n){return new sp(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(n)}}class ka extends yp{constructor(t,e){super(e),this._reader=Sl(t)?new qy(this._handle=t):new gp(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=wp(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Ie}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Ie}next(){if(this.closed)return Ie;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new up(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class Da extends yp{constructor(t,e){super(e),this._reader=new Hy(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return Mt(this,void 0,void 0,(function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}))}open(t){return Mt(this,void 0,void 0,(function*(){return this.closed||(this.autoDestroy=wp(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this}))}throw(t){return Mt(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Ie}))}return(t){return Mt(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Ie}))}next(){return Mt(this,void 0,void 0,(function*(){if(this.closed)return Ie;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new up(this.schema)}):yield this.return()}))}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,(function*(){return yield this._reader.readMessage(t)}))}}class vp extends ka{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,e){super(t instanceof mu?t:new mu(t),e)}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(Zt.RecordBatch);if(null!=t&&t.isRecordBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}_readDictionaryBatch(t){var e;const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(Zt.DictionaryBatch);if(null!=t&&t.isDictionaryBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}_readFooter(){const{_handle:t}=this,e=t.size-_p,n=t.readInt32(e),r=t.readAt(e-n,n);return uc.decode(r)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(this._recordBatchIndex);if(n&&this._handle.seek(n.offset))return this._reader.readMessage(t)}return null}}class $y extends Da{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,...e){const n="number"!=typeof e[0]?e.shift():void 0,r=e[0]instanceof Map?e.shift():void 0;super(t instanceof wa?t:new wa(t,n),r)}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return Mt(this,void 0,void 0,(function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)}))}readRecordBatch(t){return Mt(this,void 0,void 0,(function*(){var e;if(this.closed)return null;this._footer||(yield this.open());const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(Zt.RecordBatch);if(null!=t&&t.isRecordBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}))}_readDictionaryBatch(t){return Mt(this,void 0,void 0,(function*(){var e;const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(Zt.DictionaryBatch);if(null!=t&&t.isDictionaryBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}))}_readFooter(){return Mt(this,void 0,void 0,(function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-_p,n=yield t.readInt32(e),r=yield t.readAt(e-n,n);return uc.decode(r)}))}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,(function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null}))}}class Jy extends ka{constructor(t,e){super(t,e)}_loadVectors(t,e,n){return new ay(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(n)}}function wp(t,e){return e&&"boolean"==typeof e.autoDestroy?e.autoDestroy:t.autoDestroy}function*Su(t){const e=pr.from(t);try{if(!e.open({autoDestroy:!1}).closed)do{yield e}while(!e.reset().open().closed)}finally{e.cancel()}}function Iu(t){return Gn(this,arguments,(function*(){const e=yield Wt(pr.from(t));try{if(!(yield Wt(e.open({autoDestroy:!1}))).closed)do{yield yield Wt(e)}while(!(yield Wt(e.reset().open())).closed)}finally{yield Wt(e.cancel())}}))}function Qy(t){return new Ia(new Jy(t))}function Gy(t){const e=t.peek(os+7&-8);return e&&e.byteLength>=4?_c(e)?new bp(new vp(t.read())):new Ia(new ka(t)):new Ia(new ka(function*(){}()))}function Xy(t){return Mt(this,void 0,void 0,(function*(){const e=yield t.peek(os+7&-8);return e&&e.byteLength>=4?_c(e)?new bp(new vp(yield t.read())):new Oa(new Da(t)):new Oa(new Da(function(){return Gn(this,arguments,(function*(){}))}()))}))}function Zy(t){return Mt(this,void 0,void 0,(function*(){const{size:e}=yield t.stat(),n=new wa(t,e);return e>=Yy&&_c(yield n.readAt(0,os+7&-8))?new Ky(new $y(n)):new Oa(new Da(n))}))}function Sp(t){const e=pr.from(t);return zo(e)?e.then((t=>Sp(t))):e.isAsync()?e.readAll().then((t=>new Sn(t))):new Sn(e.readAll())}var Ip={exports:{}};!function(t){!function(e){function n(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function r(t,e,r,i,o,s){return n(function(t,e){return t<<e|t>>>32-e}(n(n(e,t),n(i,s)),o),r)}function i(t,e,n,i,o,s,a){return r(e&n|~e&i,t,e,o,s,a)}function o(t,e,n,i,o,s,a){return r(e&i|n&~i,t,e,o,s,a)}function s(t,e,n,i,o,s,a){return r(e^n^i,t,e,o,s,a)}function a(t,e,n,i,o,s,a){return r(n^(e|~i),t,e,o,s,a)}function l(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;var r,l,c,u,d,h=1732584193,f=-271733879,p=-1732584194,m=271733878;for(r=0;r<t.length;r+=16)l=h,c=f,u=p,d=m,h=i(h,f,p,m,t[r],7,-680876936),m=i(m,h,f,p,t[r+1],12,-389564586),p=i(p,m,h,f,t[r+2],17,606105819),f=i(f,p,m,h,t[r+3],22,-1044525330),h=i(h,f,p,m,t[r+4],7,-176418897),m=i(m,h,f,p,t[r+5],12,1200080426),p=i(p,m,h,f,t[r+6],17,-1473231341),f=i(f,p,m,h,t[r+7],22,-45705983),h=i(h,f,p,m,t[r+8],7,1770035416),m=i(m,h,f,p,t[r+9],12,-1958414417),p=i(p,m,h,f,t[r+10],17,-42063),f=i(f,p,m,h,t[r+11],22,-1990404162),h=i(h,f,p,m,t[r+12],7,1804603682),m=i(m,h,f,p,t[r+13],12,-40341101),p=i(p,m,h,f,t[r+14],17,-1502002290),h=o(h,f=i(f,p,m,h,t[r+15],22,1236535329),p,m,t[r+1],5,-165796510),m=o(m,h,f,p,t[r+6],9,-1069501632),p=o(p,m,h,f,t[r+11],14,643717713),f=o(f,p,m,h,t[r],20,-373897302),h=o(h,f,p,m,t[r+5],5,-701558691),m=o(m,h,f,p,t[r+10],9,38016083),p=o(p,m,h,f,t[r+15],14,-660478335),f=o(f,p,m,h,t[r+4],20,-405537848),h=o(h,f,p,m,t[r+9],5,568446438),m=o(m,h,f,p,t[r+14],9,-1019803690),p=o(p,m,h,f,t[r+3],14,-187363961),f=o(f,p,m,h,t[r+8],20,1163531501),h=o(h,f,p,m,t[r+13],5,-1444681467),m=o(m,h,f,p,t[r+2],9,-51403784),p=o(p,m,h,f,t[r+7],14,1735328473),h=s(h,f=o(f,p,m,h,t[r+12],20,-1926607734),p,m,t[r+5],4,-378558),m=s(m,h,f,p,t[r+8],11,-2022574463),p=s(p,m,h,f,t[r+11],16,1839030562),f=s(f,p,m,h,t[r+14],23,-35309556),h=s(h,f,p,m,t[r+1],4,-1530992060),m=s(m,h,f,p,t[r+4],11,1272893353),p=s(p,m,h,f,t[r+7],16,-155497632),f=s(f,p,m,h,t[r+10],23,-1094730640),h=s(h,f,p,m,t[r+13],4,681279174),m=s(m,h,f,p,t[r],11,-358537222),p=s(p,m,h,f,t[r+3],16,-722521979),f=s(f,p,m,h,t[r+6],23,76029189),h=s(h,f,p,m,t[r+9],4,-640364487),m=s(m,h,f,p,t[r+12],11,-421815835),p=s(p,m,h,f,t[r+15],16,530742520),h=a(h,f=s(f,p,m,h,t[r+2],23,-995338651),p,m,t[r],6,-198630844),m=a(m,h,f,p,t[r+7],10,1126891415),p=a(p,m,h,f,t[r+14],15,-1416354905),f=a(f,p,m,h,t[r+5],21,-57434055),h=a(h,f,p,m,t[r+12],6,1700485571),m=a(m,h,f,p,t[r+3],10,-1894986606),p=a(p,m,h,f,t[r+10],15,-1051523),f=a(f,p,m,h,t[r+1],21,-2054922799),h=a(h,f,p,m,t[r+8],6,1873313359),m=a(m,h,f,p,t[r+15],10,-30611744),p=a(p,m,h,f,t[r+6],15,-1560198380),f=a(f,p,m,h,t[r+13],21,1309151649),h=a(h,f,p,m,t[r+4],6,-145523070),m=a(m,h,f,p,t[r+11],10,-1120210379),p=a(p,m,h,f,t[r+2],15,718787259),f=a(f,p,m,h,t[r+9],21,-343485551),h=n(h,l),f=n(f,c),p=n(p,u),m=n(m,d);return[h,f,p,m]}function c(t){var e,n="",r=32*t.length;for(e=0;e<r;e+=8)n+=String.fromCharCode(t[e>>5]>>>e%32&255);return n}function u(t){var e,n=[];for(n[(t.length>>2)-1]=void 0,e=0;e<n.length;e+=1)n[e]=0;var r=8*t.length;for(e=0;e<r;e+=8)n[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return n}function d(t){var e,n,r="0123456789abcdef",i="";for(n=0;n<t.length;n+=1)e=t.charCodeAt(n),i+=r.charAt(e>>>4&15)+r.charAt(15&e);return i}function h(t){return unescape(encodeURIComponent(t))}function f(t){return function(t){return c(l(u(t),8*t.length))}(h(t))}function p(t,e){return function(t,e){var n,r,i=u(t),o=[],s=[];for(o[15]=s[15]=void 0,i.length>16&&(i=l(i,8*t.length)),n=0;n<16;n+=1)o[n]=909522486^i[n],s[n]=1549556828^i[n];return r=l(o.concat(u(e)),512+8*e.length),c(l(s.concat(r),640))}(h(t),h(e))}function m(t,e,n){return e?n?p(e,t):function(t,e){return d(p(t,e))}(e,t):n?f(t):function(t){return d(f(t))}(t)}t.exports?t.exports=m:e.md5=m}(Tg)}(Ip);var tv=Ip.exports;const Ou=Bg(tv),ev=!0,Op=!0,nv="always",rv=async()=>{let t={};{const e=await fetch(xt("/data/manifest.json"));e.ok&&({renderedFiles:t}=await e.json())}await Io(Ng),0===Object.keys(t??{}).length?console.warn('No sources found, execute "npm run sources" to generate'.trim()):(await Io(uh,t,{addBasePath:xt}),await Io(Cg,Object.keys(t)))},ku=Io(rv);async function iv(t,e,n){const r=await n(xt(`/api/${t}/${e}/all-queries.json`));if(!r.ok)return{};const i=await r.json(),o=await Promise.all(Object.entries(i).map((async([t,e])=>{const r=await n(xt(`/api/prerendered_queries/${e}.arrow`));if(!r.ok)return null;const i=await Sp(r);return[t,Mg(i)]})));return Object.fromEntries(o.filter(Boolean))}const ov=["/settings","/explore"],Du=new Map,sv=async({fetch:t,route:e,params:n,url:r})=>{var i,o,s;const[{customFormattingSettings:a},l,c]=await Promise.all([t(xt("/api/customFormattingSettings.json/GET.json")).then((t=>t.json())),t(xt("/api/pagesManifest.json")).then((t=>t.json())),t(xt(`/api/${e.id}/evidencemeta.json`)).then((t=>t.json())).catch((()=>({queries:[]})))]),u=Ou(e.id),d=Ou(Object.entries(n).sort().map((([t,e])=>`${t}${e}`)).join("")),h=e.id&&ov.every((t=>!e.id.startsWith(t)));let f={};const{inputs:p=a_({label:"",value:"(SELECT NULL WHERE 0 /* An Input has not been set */)"})}=Du.get(r.pathname)??{};function m(t,{query_name:e,callback:n=t=>t}={}){return(async()=>{await ku;const e=await Pg(t);return n(e)})()}Du.has(r.pathname),h&&(f=await iv(u,d,t));let b=l;for(const t of(e.id??"").split("/").slice(1)){if(b=b.children[t],!b)break;if(null!=(i=b.frontMatter)&&i.title)b.title=b.frontMatter.title;else if(null!=(o=b.frontMatter)&&o.breadcrumb){let{breadcrumb:t}=b.frontMatter;for(const[e,r]of Object.entries(n))t=t.replaceAll(`\${params.${e}}`,r);b.title=null==(s=(await m(t))[0])?void 0:s.breadcrumb}}return{__db:{query:m,load:async()=>ku,async updateParquetURLs(t){const{renderedFiles:e}=JSON.parse(t);await Io(uh,e,{addBasePath:xt})}},inputs:p,data:f,customFormattingSettings:a,isUserPage:h,evidencemeta:c,pagesManifest:l}},gO=Object.freeze(Object.defineProperty({__proto__:null,load:sv,prerender:Op,ssr:ev,trailingSlash:nv},Symbol.toStringTag,{value:"Module"})),av={ltr:[..._l,rn.ARROW_RIGHT]},lv={ltr:[rn.ARROW_LEFT]},Eu=["menu","trigger"],cv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function uv(t){const{name:e,selector:n}=jg(t.selector),{preventScroll:r,arrowSize:i,positioning:o,closeOnEscape:s,closeOnOutsideClick:a,portal:l,forceVisible:c,typeahead:u,loop:d,closeFocus:h,disableFocusFirstItem:f,closeOnItemClick:p,onOutsideClick:m}=t.rootOptions,b=t.rootOpen,y=t.rootActiveTrigger,g=t.nextFocusable,v=t.prevFocusable,_=Xe.writable(!1),w=Xe(qe(0)),S=Xe(qe(null)),O=Xe(qe("right")),I=Xe(qe(null)),$=Xe(fo([O,S],(([t,e])=>n=>t===(null==e?void 0:e.side)&&fv(n,null==e?void 0:e.area)))),{typed:x,handleTypeaheadSearch:D}=b_(),E=Fs({...Vc(Eu),...t.ids}),k=xc({open:b,forceVisible:c,activeTrigger:y}),A=dn(e(),{stores:[k,l,E.menu,E.trigger],returned:([t,e,n,r])=>({role:"menu",hidden:!t||void 0,style:no({display:t?void 0:"none"}),id:n,"aria-labelledby":r,"data-state":t?"open":"closed","data-portal":Fg(e),tabindex:-1}),action:t=>{let e=ro;const r=Wn([k,y,o,a,l,s],(([r,i,o,s,a,l])=>{e(),r&&i&&_s().then((()=>{e(),so(t,n),e=Hc(t,{anchorElement:i,open:b,options:{floating:o,modal:{closeOnInteractOutside:s,shouldCloseOnInteractOutside:t=>{var e;return null==(e=m.get())||e(t),!(t.defaultPrevented||Ct(i)&&i.contains(t.target))},onClose:()=>{b.set(!1),i.focus()},open:r},portal:qc(t,a),escapeKeydown:l?void 0:null}}).destroy}))})),i=or(Gt(t,"keydown",(t=>{const e=t.target,n=t.currentTarget;if(!Ct(e)||!Ct(n)||e.closest('[role="menu"]')!==n)return;if(zc.includes(t.key)&&Tu(t,d.get()??!1),t.key===rn.TAB)return t.preventDefault(),b.set(!1),void Au(t,g,v);const r=1===t.key.length;!(t.ctrlKey||t.altKey||t.metaKey)&&r&&!0===u.get()&&D(t.key,Jr(n))})));return{destroy(){r(),i(),e()}}}}),T=dn(e("trigger"),{stores:[b,E.menu,E.trigger],returned:([t,e,n])=>({"aria-controls":e,"aria-expanded":t,"data-state":t?"open":"closed",id:n,tabindex:0}),action:t=>(Ss(t),y.update((e=>e||t)),{destroy:or(Gt(t,"click",(t=>{const e=b.get(),n=t.currentTarget;Ct(n)&&(F(n),e||t.preventDefault())})),Gt(t,"keydown",(t=>{const e=t.currentTarget;if(!Ct(e)||!_l.includes(t.key)&&t.key!==rn.ARROW_DOWN)return;t.preventDefault(),F(e);const n=e.getAttribute("aria-controls");if(!n)return;const r=document.getElementById(n);if(!r)return;const i=Jr(r);i.length&&Ge(i[0])})))})}),N=dn(e("arrow"),{stores:i,returned:t=>({"data-arrow":!0,style:no({position:"absolute",width:`var(--arrow-size, ${t}px)`,height:`var(--arrow-size, ${t}px)`})})}),C=dn(e("overlay"),{stores:[k],returned:([t])=>({hidden:!t||void 0,tabindex:-1,style:no({display:t?void 0:"none"}),"aria-hidden":"true","data-state":mv(t)}),action:t=>{let e=ro;if(s.get()){const n=Lg(t,{handler:()=>{b.set(!1);const t=y.get();t&&t.focus()}});n&&n.destroy&&(e=n.destroy)}const n=Wn([l],(([e])=>{if(null===e)return ro;const n=qc(t,e);return null===n?ro:Rg(t,n).destroy}));return{destroy(){e(),n()}}}}),M=dn(e("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:t=>(so(t,n),Ss(t),{destroy:or(Gt(t,"pointerdown",(t=>{const e=t.currentTarget;Ct(e)&&sr(e)&&t.preventDefault()})),Gt(t,"click",(t=>{const e=t.currentTarget;if(Ct(e)){if(sr(e))return void t.preventDefault();if(t.defaultPrevented)return void Ge(e);p.get()&&ki(1).then((()=>{b.set(!1)}))}})),Gt(t,"keydown",(t=>{H(t)})),Gt(t,"pointermove",(t=>{W(t)})),Gt(t,"pointerleave",(t=>{Y(t)})),Gt(t,"focusin",(t=>{U(t)})),Gt(t,"focusout",(t=>{z(t)})))})}),B=dn(e("group"),{returned:()=>t=>({role:"group","aria-labelledby":t})}),j=dn(e("group-label"),{returned:()=>t=>({id:t})}),P={defaultChecked:!1,disabled:!1},{elements:{root:L}}=l_({orientation:"horizontal"}),R={...cv,disabled:!1,positioning:{placement:"right-start",gutter:8}};function F(t){b.update((e=>{const n=!e;return n&&(g.set(y_(t)),v.set(v_(t)),y.set(t)),n}))}function U(t){const e=t.currentTarget;if(!Ct(e))return;const n=I.get();n&&ai(n),w_(e),I.set(e)}function z(t){const e=t.currentTarget;Ct(e)&&ai(e)}function V(t){Q(t)&&t.preventDefault()}function q(t){if(Q(t))return;const e=t.target;if(!Ct(e))return;const n=J(e);n&&Ge(n)}function W(t,e=null){if(!oo(t)||(V(t),t.defaultPrevented))return;if(e)return void Ge(e);const n=t.currentTarget;Ct(n)&&Ge(n)}function Y(t){oo(t)&&q(t)}function H(t){if(x.get().length>0&&t.key===rn.SPACE)t.preventDefault();else if(_l.includes(t.key)){t.preventDefault();const e=t.currentTarget;if(!Ct(e))return;e.click()}}function K(t){return"indeterminate"===t}function G(t){return K(t)?"indeterminate":t?"checked":"unchecked"}function Q(t){return $.get()(t)}function J(t){const e=t.closest('[role="menu"]');return Ct(e)?e:null}return Wc((()=>{const t=document.getElementById(E.trigger.get());Ct(t)&&b.get()&&y.set(t);const e=[],n=()=>_.set(!1);return e.push(Wr(document,"keydown",(()=>{_.set(!0),e.push(or(Wr(document,"pointerdown",n,{capture:!0,once:!0}),Wr(document,"pointermove",n,{capture:!0,once:!0})))}),{capture:!0})),e.push(Wr(document,"keydown",(t=>{t.key===rn.ESCAPE&&s.get()&&b.set(!1)}))),()=>{e.forEach((t=>t()))}})),Wn([b,I],(([t,e])=>{!t&&e&&ai(e)})),Wn([b],(([t])=>{if(mi&&!t){const e=y.get();if(!e)return;const n=h.get();!t&&e&&c_({prop:n,defaultEl:e})}})),Wn([b,r],(([t,e])=>{if(!mi)return;const n=[];return t&&e&&n.push(D_()),ki(1).then((()=>{const e=document.getElementById(E.menu.get());if(e&&t&&_.get()){if(f.get())return void Ge(e);const t=Jr(e);if(!t.length)return;Ge(t[0])}})),()=>{n.forEach((t=>t()))}})),Wn(b,(t=>{if(!mi)return;const e=()=>_.set(!1);return or(Wr(document,"pointerdown",e,{capture:!0,once:!0}),Wr(document,"pointermove",e,{capture:!0,once:!0}),Wr(document,"keydown",(e=>{_.set(!0),e.key===rn.ESCAPE&&t&&s.get()&&b.set(!1)}),{capture:!0}))})),{elements:{trigger:T,menu:A,overlay:C,item:M,group:B,groupLabel:j,arrow:N,separator:L},builders:{createCheckboxItem:t=>{const r={...P,...t},i=r.checked??qe(r.defaultChecked??null),o=js(i,r.onCheckedChange),s=qe(r.disabled),a=dn(e("checkbox-item"),{stores:[o,s],returned:([t,e])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":K(t)?"mixed":t?"true":"false","data-disabled":$a(e),"data-state":G(t)}),action:t=>(so(t,n),Ss(t),{destroy:or(Gt(t,"pointerdown",(t=>{const e=t.currentTarget;Ct(e)&&sr(e)&&t.preventDefault()})),Gt(t,"click",(t=>{const e=t.currentTarget;if(Ct(e)){if(sr(e))return void t.preventDefault();if(t.defaultPrevented)return void Ge(e);o.update((t=>!!K(t)||!t)),p.get()&&_s().then((()=>{b.set(!1)}))}})),Gt(t,"keydown",(t=>{H(t)})),Gt(t,"pointermove",(t=>{const e=t.currentTarget;if(Ct(e)){if(sr(e))return void q(t);W(t,e)}})),Gt(t,"pointerleave",(t=>{Y(t)})),Gt(t,"focusin",(t=>{U(t)})),Gt(t,"focusout",(t=>{z(t)})))})}),l=fo(o,(t=>!0===t)),c=fo(o,(t=>"indeterminate"===t));return{elements:{checkboxItem:a},states:{checked:o},helpers:{isChecked:l,isIndeterminate:c},options:{disabled:s}}},createSubmenu:t=>{const r={...R,...t},i=r.open??qe(!1),o=js(i,null==r?void 0:r.onOpenChange),s=Fs(fh(r,"ids")),{positioning:a,arrowSize:l,disabled:h}=s,f=Xe(qe(null)),p=Xe(qe(null)),m=Xe(qe(0)),y=Fs({...Vc(Eu),...r.ids});Wc((()=>{const t=document.getElementById(y.trigger.get());t&&f.set(t)}));const $=xc({open:o,forceVisible:c,activeTrigger:f}),E=dn(e("submenu"),{stores:[$,y.menu,y.trigger],returned:([t,e,n])=>({role:"menu",hidden:!t||void 0,style:no({display:t?void 0:"none"}),id:e,"aria-labelledby":n,"data-state":t?"open":"closed","data-id":e,tabindex:-1}),action:t=>{let e=ro;const n=Wn([$,a],(([n,r])=>{if(e(),!n)return;const i=f.get();i&&_s().then((()=>{e();const n=J(i);e=Hc(t,{anchorElement:i,open:o,options:{floating:r,portal:Ct(n)?n:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy}))})),r=or(Gt(t,"keydown",(t=>{if(t.key===rn.ESCAPE)return;const e=t.target,n=t.currentTarget;if(!Ct(e)||!Ct(n)||e.closest('[role="menu"]')!==n)return;if(zc.includes(t.key))return t.stopImmediatePropagation(),void Tu(t,d.get()??!1);const r=lv.ltr.includes(t.key),i=t.ctrlKey||t.altKey||t.metaKey,s=1===t.key.length;if(r){const e=f.get();return t.preventDefault(),void o.update((()=>(e&&Ge(e),!1)))}if(t.key===rn.TAB)return t.preventDefault(),b.set(!1),void Au(t,g,v);!i&&s&&!0===u.get()&&D(t.key,Jr(n))})),Gt(t,"pointermove",(t=>{!function(t){if(!oo(t))return;const e=t.target,n=t.currentTarget;if(!Ct(n)||!Ct(e))return;const r=w.get(),i=r!==t.clientX;if(n.contains(e)&&i){const e=t.clientX>r?"right":"left";O.set(e),w.set(t.clientX)}}(t)})),Gt(t,"focusout",(t=>{const e=f.get();if(_.get()){const n=t.target,r=document.getElementById(y.menu.get());if(!Ct(r)||!Ct(n))return;!r.contains(n)&&n!==e&&o.set(!1)}else{const n=t.currentTarget,r=t.relatedTarget;if(!Ct(r)||!Ct(n))return;!n.contains(r)&&r!==e&&o.set(!1)}})));return{destroy(){n(),e(),r()}}}}),k=dn(e("subtrigger"),{stores:[o,h,y.menu,y.trigger],returned:([t,e,n,r])=>({role:"menuitem",id:r,tabindex:-1,"aria-controls":n,"aria-expanded":t,"data-state":t?"open":"closed","data-disabled":$a(e),"aria-haspopop":"menu"}),action:t=>{so(t,n),Ss(t),f.update((e=>e||t));const e=or(Gt(t,"click",(t=>{if(t.defaultPrevented)return;const e=t.currentTarget;!Ct(e)||sr(e)||(Ge(e),o.get()||o.update((t=>t||(f.set(e),!t))))})),Gt(t,"keydown",(t=>{const e=x.get(),n=t.currentTarget;if(!(!Ct(n)||sr(n)||e.length>0&&t.key===rn.SPACE)&&av.ltr.includes(t.key)){if(!o.get())return n.click(),void t.preventDefault();const e=n.getAttribute("aria-controls");if(!e)return;const r=document.getElementById(e);if(!Ct(r))return;const i=Jr(r)[0];Ge(i)}})),Gt(t,"pointermove",(t=>{if(!oo(t)||(V(t),t.defaultPrevented))return;const e=t.currentTarget;if(!Ct(e))return;hv(y.menu.get())||Ge(e);const n=p.get();!o.get()&&!n&&!sr(e)&&p.set(window.setTimeout((()=>{o.update((()=>(f.set(e),!0))),nl(p)}),100))})),Gt(t,"pointerleave",(t=>{if(!oo(t))return;nl(p);const e=document.getElementById(y.menu.get()),n=null==e?void 0:e.getBoundingClientRect();if(n){const r=null==e?void 0:e.dataset.side,i="right"===r,o=i?-5:5,s=n[i?"left":"right"],a=n[i?"right":"left"];S.set({area:[{x:t.clientX+o,y:t.clientY},{x:s,y:n.top},{x:a,y:n.top},{x:a,y:n.bottom},{x:s,y:n.bottom}],side:r}),window.clearTimeout(m.get()),m.set(window.setTimeout((()=>{S.set(null)}),300))}else{if(function(t){Q(t)&&t.preventDefault()}(t),t.defaultPrevented)return;S.set(null)}})),Gt(t,"focusout",(t=>{const e=t.currentTarget;if(!Ct(e))return;ai(e);const n=t.relatedTarget;if(!Ct(n))return;const r=e.getAttribute("aria-controls");if(!r)return;const i=document.getElementById(r);i&&!i.contains(n)&&o.set(!1)})),Gt(t,"focusin",(t=>{U(t)})));return{destroy(){nl(p),window.clearTimeout(m.get()),S.set(null),e()}}}}),A=dn(e("subarrow"),{stores:l,returned:t=>({"data-arrow":!0,style:no({position:"absolute",width:`var(--arrow-size, ${t}px)`,height:`var(--arrow-size, ${t}px)`})})});return Wn([b],(([t])=>{t||(f.set(null),o.set(!1))})),Wn([S],(([t])=>{!mi||t||window.clearTimeout(m.get())})),Wn([o],(([t])=>{if(mi&&(t&&_.get()&&ki(1).then((()=>{const t=document.getElementById(y.menu.get());if(!t)return;const e=Jr(t);e.length&&Ge(e[0])})),!t)){const t=I.get(),e=document.getElementById(y.trigger.get());if(t&&ki(1).then((()=>{const e=document.getElementById(y.menu.get());e&&e.contains(t)&&ai(t)})),!e||document.activeElement===e)return;ai(e)}})),{ids:y,elements:{subTrigger:k,subMenu:E,subArrow:A},states:{subOpen:o},options:s}},createMenuRadioGroup:(t={})=>{const r=t.value??qe(t.defaultValue??null),i=js(r,t.onValueChange),o=dn(e("radio-group"),{returned:()=>({role:"group"})}),s={disabled:!1},a=dn(e("radio-item"),{stores:[i],returned:([t])=>e=>{const{value:n,disabled:r}={...s,...e},i=t===n;return{disabled:r,role:"menuitemradio","data-state":i?"checked":"unchecked","aria-checked":i,"data-disabled":$a(r),"data-value":n,"data-orientation":"vertical",tabindex:-1}},action:t=>(so(t,n),{destroy:or(Gt(t,"pointerdown",(e=>{const n=e.currentTarget;if(!Ct(n))return;const r=t.dataset.value;(t.dataset.disabled||void 0===r)&&e.preventDefault()})),Gt(t,"click",(e=>{const n=e.currentTarget;if(!Ct(n))return;const r=t.dataset.value;if(t.dataset.disabled||void 0===r)e.preventDefault();else if(e.defaultPrevented){if(!Ct(n))return;Ge(n)}else i.set(r),p.get()&&_s().then((()=>{b.set(!1)}))})),Gt(t,"keydown",(t=>{H(t)})),Gt(t,"pointermove",(e=>{const n=e.currentTarget;if(!Ct(n))return;const r=t.dataset.value;t.dataset.disabled||void 0===r?q(e):W(e,n)})),Gt(t,"pointerleave",(t=>{Y(t)})),Gt(t,"focusin",(t=>{U(t)})),Gt(t,"focusout",(t=>{z(t)})))})}),l=fo(i,(t=>e=>t===e));return{elements:{radioGroup:o,radioItem:a},states:{value:i},helpers:{isChecked:l}}}},states:{open:b},helpers:{handleTypeaheadSearch:D},ids:E,options:t.rootOptions}}function Au(t,e,n){if(t.shiftKey){const e=n.get();e&&(t.preventDefault(),ki(1).then((()=>e.focus())),n.set(null))}else{const n=e.get();n&&(t.preventDefault(),ki(1).then((()=>n.focus())),e.set(null))}}function Jr(t){return Array.from(t.querySelectorAll(`[data-melt-menu-id="${t.id}"]`)).filter((t=>Ct(t)))}function Ss(t){!t||!sr(t)||(t.setAttribute("data-disabled",""),t.setAttribute("aria-disabled","true"))}function nl(t){if(!mi)return;const e=t.get();e&&(window.clearTimeout(e),t.set(null))}function oo(t){return"mouse"===t.pointerType}function so(t,e){if(!t)return;const n=t.closest(`${e()}, ${e("submenu")}`);Ct(n)&&t.setAttribute("data-melt-menu-id",n.id)}function Tu(t,e){t.preventDefault();const n=document.activeElement,r=t.currentTarget;if(!Ct(n)||!Ct(r))return;const i=Jr(r);if(!i.length)return;const o=i.filter((t=>!(t.hasAttribute("data-disabled")||"true"===t.getAttribute("disabled")))),s=o.indexOf(n);let a;switch(t.key){case rn.ARROW_DOWN:a=e?s<o.length-1?s+1:0:s<o.length-1?s+1:s;break;case rn.ARROW_UP:a=e?s>0?s-1:o.length-1:s<0?o.length-1:s>0?s-1:0;break;case rn.HOME:a=0;break;case rn.END:a=o.length-1;break;default:return}Ge(o[a])}function fv(t,e){return!!e&&dv({x:t.clientX,y:t.clientY},e)}function dv(t,e){const{x:n,y:r}=t;let i=!1;for(let t=0,o=e.length-1;t<e.length;o=t++){const s=e[t].x,a=e[t].y,l=e[o].x,c=e[o].y;a>r!=c>r&&n<(l-s)*(r-a)/(c-a)+s&&(i=!i)}return i}function hv(t){const e=document.activeElement;if(!Ct(e))return!1;const n=e.closest(`[data-id="${t}"]`);return Ct(n)}function mv(t){return t?"open":"closed"}const pv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function gv(t){const e={...pv,...t},n=Fs(fh(e,"ids")),r=e.open??qe(e.defaultOpen),i=js(r,null==e?void 0:e.onOpenChange),o=Xe(qe(null)),s=Xe(qe(null)),a=Xe(qe(null)),{elements:l,builders:c,ids:u,states:d,options:h}=uv({rootOptions:n,rootOpen:i,rootActiveTrigger:Xe(o),nextFocusable:Xe(s),prevFocusable:Xe(a),selector:"dropdown-menu",ids:e.ids});return{ids:u,elements:l,states:d,builders:c,options:h}}let _v="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",bv=(t=21)=>{let e="",n=0|t;for(;n--;)e+=_v[64*Math.random()|0];return e};function yv(){return bv(10)}function bc(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function ss(){const{NAME:t}=bc();return f_(t)}function vv(t){const{NAME:e,PARTS:n}=bc(),r=Ug("menu",n),i={...gv({...Vg(t),forceVisible:!0}),getAttrs:r};return gh(e,i),{...i,updateOption:xg(i.options)}}function wv(){const{GROUP_NAME:t}=bc(),{elements:{group:e},getAttrs:n}=ss(),r=yv();return gh(t,r),{group:e,id:r,getAttrs:n}}function Sv(t){const e={side:"bottom",align:"center",...t},{options:{positioning:n}}=ss();zg(n)(e)}const Iv=t=>({builder:8&t}),Bu=t=>({builder:t[3]}),Ov=t=>({builder:8&t}),Mu=t=>({builder:t[3]});function kv(t){let e,n,r=t[1]?"a":"div",i=(t[1]?"a":"div")&&rl(t);return{c(){i&&i.c(),e=ft()},l(t){i&&i.l(t),e=ft()},m(t,r){i&&i.m(t,r),R(t,e,r),n=!0},p(t,n){t[1],r?ee(r,t[1]?"a":"div")?(i.d(1),i=rl(t),r=t[1]?"a":"div",i.c(),i.m(e.parentNode,e)):i.p(t,n):(i=rl(t),r=t[1]?"a":"div",i.c(),i.m(e.parentNode,e))},i(t){n||(O(i,t),n=!0)},o(t){D(i,t),n=!1},d(t){t&&S(e),i&&i.d(t)}}}function Dv(t){let e;const n=t[11].default,r=Oe(n,t,t[10],Mu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||1032&i)&&ke(r,n,t,t[10],e?Ee(n,t[10],i,Ov):De(t[10]),Mu)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function rl(t){let e,n,r,i;const o=t[11].default,s=Oe(o,t,t[10],Bu);let a=[{href:t[1]},t[3],t[6]],l={};for(let t=0;t<a.length;t+=1)l=me(l,a[t]);return{c(){e=Y(t[1]?"a":"div"),s&&s.c(),this.h()},l(n){e=q(n,((t[1]?"a":"div")||"null").toUpperCase(),{href:!0});var r=$(e);s&&s.l(r),r.forEach(S),this.h()},h(){Jc(t[1]?"a":"div")(e,l)},m(o,a){R(o,e,a),s&&s.m(e,null),t[13](e),n=!0,r||(i=[Rr(t[3].action(e)),he(e,"m-click",t[5]),he(e,"m-focusin",t[5]),he(e,"m-focusout",t[5]),he(e,"m-keydown",t[5]),he(e,"m-pointerdown",t[5]),he(e,"m-pointerleave",t[5]),he(e,"m-pointermove",t[5]),he(e,"pointerenter",t[12])],r=!0)},p(t,r){s&&s.p&&(!n||1032&r)&&ke(s,o,t,t[10],n?Ee(o,t[10],r,Iv):De(t[10]),Bu),Jc(t[1]?"a":"div")(e,l=An(a,[(!n||2&r)&&{href:t[1]},8&r&&t[3],64&r&&t[6]]))},i(t){n||(O(s,t),n=!0)},o(t){D(s,t),n=!1},d(n){n&&S(e),s&&s.d(n),t[13](null),r=!1,wr(i)}}}function Ev(t){let e,n,r,i;const o=[Dv,kv],s=[];function a(t,e){return t[2]?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ft()},l(t){n.l(t),r=ft()},m(t,n){s[e].m(t,n),R(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),D(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),O(n,1),n.m(r.parentNode,r))},i(t){i||(O(n),i=!0)},o(t){D(n),i=!1},d(t){t&&S(r),s[e].d(t)}}}function Av(t,e,n){let r,i;const o=["href","asChild","disabled","el"];let s,a=ln(e,o),{$$slots:l={},$$scope:c}=e,{href:u}=e,{asChild:d=!1}=e,{disabled:h=!1}=e,{el:f}=e;const{elements:{item:p},getAttrs:m}=ss();Ae(t,p,(t=>n(9,s=t)));const b=Jl();return t.$$set=t=>{e=me(me({},e),ri(t)),n(6,a=ln(e,o)),"href"in t&&n(1,u=t.href),"asChild"in t&&n(2,d=t.asChild),"disabled"in t&&n(7,h=t.disabled),"el"in t&&n(0,f=t.el),"$$scope"in t&&n(10,c=t.$$scope)},t.$$.update=()=>{512&t.$$.dirty&&n(3,r=s),128&t.$$.dirty&&n(8,i={...m("item"),...Wg(h)}),264&t.$$.dirty&&Object.assign(r,i)},[f,u,d,r,p,b,a,h,i,s,c,l,function(e){lr.call(this,t,e)},function(t){Kn[t?"unshift":"push"]((()=>{f=t,n(0,f)}))}]}class Tv extends ae{constructor(t){super(),le(this,t,Av,Ev,ee,{href:1,asChild:2,disabled:7,el:0})}}const Bv=t=>({builder:4&t}),Pu=t=>({builder:t[2]}),Mv=t=>({builder:4&t}),Nu=t=>({builder:t[2]});function Pv(t){let e,n,r,i;const o=t[7].default,s=Oe(o,t,t[6],Pu);let a=[t[2],t[4]],l={};for(let t=0;t<a.length;t+=1)l=me(l,a[t]);return{c(){e=Y("div"),s&&s.c(),this.h()},l(t){e=q(t,"DIV",{});var n=$(e);s&&s.l(n),n.forEach(S),this.h()},h(){$e(e,l)},m(o,a){R(o,e,a),s&&s.m(e,null),t[8](e),n=!0,r||(i=Rr(t[2].action(e)),r=!0)},p(t,r){s&&s.p&&(!n||68&r)&&ke(s,o,t,t[6],n?Ee(o,t[6],r,Bv):De(t[6]),Pu),$e(e,l=An(a,[4&r&&t[2],16&r&&t[4]]))},i(t){n||(O(s,t),n=!0)},o(t){D(s,t),n=!1},d(n){n&&S(e),s&&s.d(n),t[8](null),r=!1,i()}}}function Nv(t){let e;const n=t[7].default,r=Oe(n,t,t[6],Nu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||68&i)&&ke(r,n,t,t[6],e?Ee(n,t[6],i,Mv):De(t[6]),Nu)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function Cv(t){let e,n,r,i;const o=[Nv,Pv],s=[];function a(t,e){return t[1]?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ft()},l(t){n.l(t),r=ft()},m(t,n){s[e].m(t,n),R(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),D(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),O(n,1),n.m(r.parentNode,r))},i(t){i||(O(n),i=!0)},o(t){D(n),i=!1},d(t){t&&S(r),s[e].d(t)}}}function Fv(t,e,n){let r;const i=["asChild","el"];let o,s=ln(e,i),{$$slots:a={},$$scope:l}=e,{asChild:c=!1}=e,{el:u}=e;const{group:d,id:h,getAttrs:f}=wv();Ae(t,d,(t=>n(5,o=t)));const p=f("group");return t.$$set=t=>{e=me(me({},e),ri(t)),n(4,s=ln(e,i)),"asChild"in t&&n(1,c=t.asChild),"el"in t&&n(0,u=t.el),"$$scope"in t&&n(6,l=t.$$scope)},t.$$.update=()=>{32&t.$$.dirty&&n(2,r=o(h)),4&t.$$.dirty&&Object.assign(r,p)},[u,c,r,d,s,o,l,a,function(t){Kn[t?"unshift":"push"]((()=>{u=t,n(0,u)}))}]}class jv extends ae{constructor(t){super(),le(this,t,Fv,Cv,ee,{asChild:1,el:0})}}const Lv=t=>({ids:1&t}),Cu=t=>({ids:t[0]});function Rv(t){let e;const n=t[16].default,r=Oe(n,t,t[15],Cu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,[i]){r&&r.p&&(!e||32769&i)&&ke(r,n,t,t[15],e?Ee(n,t[15],i,Lv):De(t[15]),Cu)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function Uv(t,e,n){let r,{$$slots:i={},$$scope:o}=e,{closeOnOutsideClick:s}=e,{closeOnEscape:a}=e,{portal:l}=e,{open:c}=e,{onOpenChange:u}=e,{preventScroll:d}=e,{loop:h}=e,{dir:f}=e,{typeahead:p}=e,{closeFocus:m}=e,{disableFocusFirstItem:b}=e,{closeOnItemClick:y}=e,{onOutsideClick:g}=e;const{states:{open:v},updateOption:_,ids:w}=vv({closeOnOutsideClick:s,closeOnEscape:a,portal:l,forceVisible:!0,defaultOpen:c,preventScroll:d,loop:h,dir:f,typeahead:p,closeFocus:m,disableFocusFirstItem:b,closeOnItemClick:y,onOutsideClick:g,onOpenChange:({next:t})=>(c!==t&&(null==u||u(t),n(2,c=t)),t)}),S=fo([w.menu,w.trigger],(([t,e])=>({menu:t,trigger:e})));return Ae(t,S,(t=>n(0,r=t))),t.$$set=t=>{"closeOnOutsideClick"in t&&n(3,s=t.closeOnOutsideClick),"closeOnEscape"in t&&n(4,a=t.closeOnEscape),"portal"in t&&n(5,l=t.portal),"open"in t&&n(2,c=t.open),"onOpenChange"in t&&n(6,u=t.onOpenChange),"preventScroll"in t&&n(7,d=t.preventScroll),"loop"in t&&n(8,h=t.loop),"dir"in t&&n(9,f=t.dir),"typeahead"in t&&n(10,p=t.typeahead),"closeFocus"in t&&n(11,m=t.closeFocus),"disableFocusFirstItem"in t&&n(12,b=t.disableFocusFirstItem),"closeOnItemClick"in t&&n(13,y=t.closeOnItemClick),"onOutsideClick"in t&&n(14,g=t.onOutsideClick),"$$scope"in t&&n(15,o=t.$$scope)},t.$$.update=()=>{4&t.$$.dirty&&void 0!==c&&v.set(c),8&t.$$.dirty&&_("closeOnOutsideClick",s),16&t.$$.dirty&&_("closeOnEscape",a),32&t.$$.dirty&&_("portal",l),128&t.$$.dirty&&_("preventScroll",d),256&t.$$.dirty&&_("loop",h),512&t.$$.dirty&&_("dir",f),2048&t.$$.dirty&&_("closeFocus",m),4096&t.$$.dirty&&_("disableFocusFirstItem",b),1024&t.$$.dirty&&_("typeahead",p),8192&t.$$.dirty&&_("closeOnItemClick",y),16384&t.$$.dirty&&_("onOutsideClick",g)},[r,S,c,s,a,l,u,d,h,f,p,m,b,y,g,o,i]}class Vv extends ae{constructor(t){super(),le(this,t,Uv,Rv,ee,{closeOnOutsideClick:3,closeOnEscape:4,portal:5,open:2,onOpenChange:6,preventScroll:7,loop:8,dir:9,typeahead:10,closeFocus:11,disableFocusFirstItem:12,closeOnItemClick:13,onOutsideClick:14})}}const xv=t=>({builder:256&t[0]}),Fu=t=>({builder:t[8]}),zv=t=>({builder:256&t[0]}),ju=t=>({builder:t[8]}),Wv=t=>({builder:256&t[0]}),Lu=t=>({builder:t[8]}),Hv=t=>({builder:256&t[0]}),Ru=t=>({builder:t[8]}),qv=t=>({builder:256&t[0]}),Uu=t=>({builder:t[8]}),Yv=t=>({builder:256&t[0]}),Vu=t=>({builder:t[8]});function Kv(t){let e,n,r,i;const o=t[28].default,s=Oe(o,t,t[27],Fu);let a=[t[8],t[13]],l={};for(let t=0;t<a.length;t+=1)l=me(l,a[t]);return{c(){e=Y("div"),s&&s.c(),this.h()},l(t){e=q(t,"DIV",{});var n=$(e);s&&s.l(n),n.forEach(S),this.h()},h(){$e(e,l)},m(o,a){R(o,e,a),s&&s.m(e,null),t[33](e),n=!0,r||(i=[Rr(t[8].action(e)),he(e,"m-keydown",t[12])],r=!0)},p(t,r){s&&s.p&&(!n||134217984&r[0])&&ke(s,o,t,t[27],n?Ee(o,t[27],r,xv):De(t[27]),Fu),$e(e,l=An(a,[256&r[0]&&t[8],8192&r[0]&&t[13]]))},i(t){n||(O(s,t),n=!0)},o(t){D(s,t),n=!1},d(n){n&&S(e),s&&s.d(n),t[33](null),r=!1,wr(i)}}}function $v(t){let e,n,r,i,o;const s=t[28].default,a=Oe(s,t,t[27],ju);let l=[t[8],t[13]],c={};for(let t=0;t<l.length;t+=1)c=me(c,l[t]);return{c(){e=Y("div"),a&&a.c(),this.h()},l(t){e=q(t,"DIV",{});var n=$(e);a&&a.l(n),n.forEach(S),this.h()},h(){$e(e,c)},m(n,s){R(n,e,s),a&&a.m(e,null),t[32](e),r=!0,i||(o=[Rr(t[8].action(e)),he(e,"m-keydown",t[12])],i=!0)},p(n,i){t=n,a&&a.p&&(!r||134217984&i[0])&&ke(a,s,t,t[27],r?Ee(s,t[27],i,zv):De(t[27]),ju),$e(e,c=An(l,[256&i[0]&&t[8],8192&i[0]&&t[13]]))},i(t){r||(O(a,t),n&&n.end(1),r=!0)},o(i){D(a,i),i&&(n=es(e,t[5],t[6])),r=!1},d(r){r&&S(e),a&&a.d(r),t[32](null),r&&n&&n.end(),i=!1,wr(o)}}}function Jv(t){let e,n,r,i,o;const s=t[28].default,a=Oe(s,t,t[27],Lu);let l=[t[8],t[13]],c={};for(let t=0;t<l.length;t+=1)c=me(c,l[t]);return{c(){e=Y("div"),a&&a.c(),this.h()},l(t){e=q(t,"DIV",{});var n=$(e);a&&a.l(n),n.forEach(S),this.h()},h(){$e(e,c)},m(n,s){R(n,e,s),a&&a.m(e,null),t[31](e),r=!0,i||(o=[Rr(t[8].action(e)),he(e,"m-keydown",t[12])],i=!0)},p(n,i){t=n,a&&a.p&&(!r||134217984&i[0])&&ke(a,s,t,t[27],r?Ee(s,t[27],i,Wv):De(t[27]),Lu),$e(e,c=An(l,[256&i[0]&&t[8],8192&i[0]&&t[13]]))},i(i){r||(O(a,i),i&&(n||On((()=>{n=Hi(e,t[3],t[4]),n.start()}))),r=!0)},o(t){D(a,t),r=!1},d(n){n&&S(e),a&&a.d(n),t[31](null),i=!1,wr(o)}}}function Qv(t){let e,n,r,i,o,s;const a=t[28].default,l=Oe(a,t,t[27],Ru);let c=[t[8],t[13]],u={};for(let t=0;t<c.length;t+=1)u=me(u,c[t]);return{c(){e=Y("div"),l&&l.c(),this.h()},l(t){e=q(t,"DIV",{});var n=$(e);l&&l.l(n),n.forEach(S),this.h()},h(){$e(e,u)},m(n,r){R(n,e,r),l&&l.m(e,null),t[30](e),i=!0,o||(s=[Rr(t[8].action(e)),he(e,"m-keydown",t[12])],o=!0)},p(n,r){t=n,l&&l.p&&(!i||134217984&r[0])&&ke(l,a,t,t[27],i?Ee(a,t[27],r,Hv):De(t[27]),Ru),$e(e,u=An(c,[256&r[0]&&t[8],8192&r[0]&&t[13]]))},i(o){i||(O(l,o),o&&On((()=>{i&&(r&&r.end(1),n=Hi(e,t[3],t[4]),n.start())})),i=!0)},o(o){D(l,o),n&&n.invalidate(),o&&(r=es(e,t[5],t[6])),i=!1},d(n){n&&S(e),l&&l.d(n),t[30](null),n&&r&&r.end(),o=!1,wr(s)}}}function Gv(t){let e,n,r,i,o;const s=t[28].default,a=Oe(s,t,t[27],Uu);let l=[t[8],t[13]],c={};for(let t=0;t<l.length;t+=1)c=me(c,l[t]);return{c(){e=Y("div"),a&&a.c(),this.h()},l(t){e=q(t,"DIV",{});var n=$(e);a&&a.l(n),n.forEach(S),this.h()},h(){$e(e,c)},m(n,s){R(n,e,s),a&&a.m(e,null),t[29](e),r=!0,i||(o=[Rr(t[8].action(e)),he(e,"m-keydown",t[12])],i=!0)},p(n,i){t=n,a&&a.p&&(!r||134217984&i[0])&&ke(a,s,t,t[27],r?Ee(s,t[27],i,qv):De(t[27]),Uu),$e(e,c=An(l,[256&i[0]&&t[8],8192&i[0]&&t[13]]))},i(i){r||(O(a,i),i&&On((()=>{r&&(n||(n=In(e,t[1],t[2],!0)),n.run(1))})),r=!0)},o(i){D(a,i),i&&(n||(n=In(e,t[1],t[2],!1)),n.run(0)),r=!1},d(r){r&&S(e),a&&a.d(r),t[29](null),r&&n&&n.end(),i=!1,wr(o)}}}function Xv(t){let e;const n=t[28].default,r=Oe(n,t,t[27],Vu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||134217984&i[0])&&ke(r,n,t,t[27],e?Ee(n,t[27],i,Yv):De(t[27]),Vu)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function Zv(t){let e,n,r,i;const o=[Xv,Gv,Qv,Jv,$v,Kv],s=[];function a(t,e){return t[7]&&t[9]?0:t[1]&&t[9]?1:t[3]&&t[5]&&t[9]?2:t[3]&&t[9]?3:t[5]&&t[9]?4:t[9]?5:-1}return~(e=a(t))&&(n=s[e]=o[e](t)),{c(){n&&n.c(),r=ft()},l(t){n&&n.l(t),r=ft()},m(t,n){~e&&s[e].m(t,n),R(t,r,n),i=!0},p(t,i){let l=e;e=a(t),e===l?~e&&s[e].p(t,i):(n&&(jt(),D(s[l],1,1,(()=>{s[l]=null})),Lt()),~e?(n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),O(n,1),n.m(r.parentNode,r)):n=null)},i(t){i||(O(n),i=!0)},o(t){D(n),i=!1},d(t){t&&S(r),~e&&s[e].d(t)}}}function t0(t,e,n){let r;const i=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let o,s,a=ln(e,i),{$$slots:l={},$$scope:c}=e,{transition:u}=e,{transitionConfig:d}=e,{inTransition:h}=e,{inTransitionConfig:f}=e,{outTransition:p}=e,{outTransitionConfig:m}=e,{asChild:b=!1}=e,{id:y}=e,{side:g="bottom"}=e,{align:v="center"}=e,{sideOffset:_=0}=e,{alignOffset:w=0}=e,{collisionPadding:S=8}=e,{avoidCollisions:O=!0}=e,{collisionBoundary:I}=e,{sameWidth:$=!1}=e,{fitViewport:x=!1}=e,{strategy:D="absolute"}=e,{overlap:E=!1}=e,{el:k}=e;const{elements:{menu:A},states:{open:T},ids:N,getAttrs:C}=ss();Ae(t,A,(t=>n(26,s=t))),Ae(t,T,(t=>n(9,o=t)));const M=Jl(),B=C("content");return t.$$set=t=>{e=me(me({},e),ri(t)),n(13,a=ln(e,i)),"transition"in t&&n(1,u=t.transition),"transitionConfig"in t&&n(2,d=t.transitionConfig),"inTransition"in t&&n(3,h=t.inTransition),"inTransitionConfig"in t&&n(4,f=t.inTransitionConfig),"outTransition"in t&&n(5,p=t.outTransition),"outTransitionConfig"in t&&n(6,m=t.outTransitionConfig),"asChild"in t&&n(7,b=t.asChild),"id"in t&&n(14,y=t.id),"side"in t&&n(15,g=t.side),"align"in t&&n(16,v=t.align),"sideOffset"in t&&n(17,_=t.sideOffset),"alignOffset"in t&&n(18,w=t.alignOffset),"collisionPadding"in t&&n(19,S=t.collisionPadding),"avoidCollisions"in t&&n(20,O=t.avoidCollisions),"collisionBoundary"in t&&n(21,I=t.collisionBoundary),"sameWidth"in t&&n(22,$=t.sameWidth),"fitViewport"in t&&n(23,x=t.fitViewport),"strategy"in t&&n(24,D=t.strategy),"overlap"in t&&n(25,E=t.overlap),"el"in t&&n(0,k=t.el),"$$scope"in t&&n(27,c=t.$$scope)},t.$$.update=()=>{16384&t.$$.dirty[0]&&y&&N.menu.set(y),67108864&t.$$.dirty[0]&&n(8,r=s),256&t.$$.dirty[0]&&Object.assign(r,B),67076608&t.$$.dirty[0]&&o&&Sv({side:g,align:v,sideOffset:_,alignOffset:w,collisionPadding:S,avoidCollisions:O,collisionBoundary:I,sameWidth:$,fitViewport:x,strategy:D,overlap:E})},[k,u,d,h,f,p,m,b,r,o,A,T,M,a,y,g,v,_,w,S,O,I,$,x,D,E,s,c,l,function(t){Kn[t?"unshift":"push"]((()=>{k=t,n(0,k)}))},function(t){Kn[t?"unshift":"push"]((()=>{k=t,n(0,k)}))},function(t){Kn[t?"unshift":"push"]((()=>{k=t,n(0,k)}))},function(t){Kn[t?"unshift":"push"]((()=>{k=t,n(0,k)}))},function(t){Kn[t?"unshift":"push"]((()=>{k=t,n(0,k)}))}]}class e0 extends ae{constructor(t){super(),le(this,t,t0,Zv,ee,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:14,side:15,align:16,sideOffset:17,alignOffset:18,collisionPadding:19,avoidCollisions:20,collisionBoundary:21,sameWidth:22,fitViewport:23,strategy:24,overlap:25,el:0},null,[-1,-1])}}const n0=t=>({builder:4&t}),xu=t=>({builder:t[2]}),r0=t=>({builder:4&t}),zu=t=>({builder:t[2]});function i0(t){let e,n,r,i;const o=t[9].default,s=Oe(o,t,t[8],xu);let a=[t[2],{type:"button"},t[5]],l={};for(let t=0;t<a.length;t+=1)l=me(l,a[t]);return{c(){e=Y("button"),s&&s.c(),this.h()},l(t){e=q(t,"BUTTON",{type:!0});var n=$(e);s&&s.l(n),n.forEach(S),this.h()},h(){$e(e,l)},m(o,a){R(o,e,a),s&&s.m(e,null),e.autofocus&&e.focus(),t[10](e),n=!0,r||(i=[Rr(t[2].action(e)),he(e,"m-keydown",t[4]),he(e,"m-pointerdown",t[4])],r=!0)},p(t,r){s&&s.p&&(!n||260&r)&&ke(s,o,t,t[8],n?Ee(o,t[8],r,n0):De(t[8]),xu),$e(e,l=An(a,[4&r&&t[2],{type:"button"},32&r&&t[5]]))},i(t){n||(O(s,t),n=!0)},o(t){D(s,t),n=!1},d(n){n&&S(e),s&&s.d(n),t[10](null),r=!1,wr(i)}}}function o0(t){let e;const n=t[9].default,r=Oe(n,t,t[8],zu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||260&i)&&ke(r,n,t,t[8],e?Ee(n,t[8],i,r0):De(t[8]),zu)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function s0(t){let e,n,r,i;const o=[o0,i0],s=[];function a(t,e){return t[1]?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ft()},l(t){n.l(t),r=ft()},m(t,n){s[e].m(t,n),R(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),D(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),O(n,1),n.m(r.parentNode,r))},i(t){i||(O(n),i=!0)},o(t){D(n),i=!1},d(t){t&&S(r),s[e].d(t)}}}function a0(t,e,n){let r;const i=["asChild","id","el"];let o,s=ln(e,i),{$$slots:a={},$$scope:l}=e,{asChild:c=!1}=e,{id:u}=e,{el:d}=e;const{elements:{trigger:h},ids:f,getAttrs:p}=ss();Ae(t,h,(t=>n(7,o=t)));const m=Jl(),b=p("trigger");return t.$$set=t=>{e=me(me({},e),ri(t)),n(5,s=ln(e,i)),"asChild"in t&&n(1,c=t.asChild),"id"in t&&n(6,u=t.id),"el"in t&&n(0,d=t.el),"$$scope"in t&&n(8,l=t.$$scope)},t.$$.update=()=>{64&t.$$.dirty&&u&&f.trigger.set(u),128&t.$$.dirty&&n(2,r=o),4&t.$$.dirty&&Object.assign(r,b)},[d,c,r,h,m,s,u,o,l,a,function(t){Kn[t?"unshift":"push"]((()=>{d=t,n(0,d)}))}]}class l0 extends ae{constructor(t){super(),le(this,t,a0,s0,ee,{asChild:1,id:6,el:0})}}const il=t=>t instanceof Date,yc=t=>0===Object.keys(t).length,gr=t=>null!=t&&"object"==typeof t,vc=(t,...e)=>Object.prototype.hasOwnProperty.call(t,...e),ol=t=>gr(t)&&yc(t),wc=()=>Object.create(null),kp=(t,e)=>t!==e&&gr(t)&&gr(e)?Object.keys(e).reduce(((n,r)=>{if(vc(t,r)){const i=kp(t[r],e[r]);return gr(i)&&yc(i)||(n[r]=i),n}return n[r]=e[r],n}),wc()):{},Dp=(t,e)=>t!==e&&gr(t)&&gr(e)?Object.keys(t).reduce(((n,r)=>{if(vc(e,r)){const i=Dp(t[r],e[r]);return gr(i)&&yc(i)||(n[r]=i),n}return n[r]=void 0,n}),wc()):{},Ep=(t,e)=>t===e?{}:gr(t)&&gr(e)?il(t)||il(e)?t.valueOf()==e.valueOf()?{}:e:Object.keys(e).reduce(((n,r)=>{if(vc(t,r)){const i=Ep(t[r],e[r]);return ol(i)&&!il(i)&&(ol(t[r])||!ol(e[r]))||(n[r]=i),n}return n}),wc()):e,c0=(t,e)=>({added:kp(t,e),deleted:Dp(t,e),updated:Ep(t,e)});var Zo,ts,Ci,Ba;class u0{constructor(){eo(this,Zo,[]),eo(this,ts,{}),eo(this,Ci,new Set),Ya(this,"subscribe",(t=>(Dr(this,Ci).add(t),t(this.generations),()=>Dr(this,Ci).delete(t)))),eo(this,Ba,0),Ya(this,"publish",(()=>{if(Uc(this,Ba)._++>1e5)throw new Error("History published too many times.");Dr(this,Ci).forEach((t=>t(this.generations)))}))}get generations(){return[...Dr(this,Zo)]}push(t){const e=t=>{let e=Object.entries(t);e.sort(((t,e)=>t[0].localeCompare(e[0])));const n=Object.fromEntries(e);return JSON.parse(JSON.stringify(n))},n=e(Dr(this,ts)),r=e(t),i=c0(n,r);Dr(this,Zo).push({...i,before:n,after:r,asof:new Date}),Ka(this,ts,r),this.publish()}}function Wu(t){let e,n;return{c(){e=Y("span"),n=wt(t[1]),this.h()},l(r){e=q(r,"SPAN",{class:!0});var i=$(e);n=vt(i,t[1]),i.forEach(S),this.h()},h(){N(e,"class","cursor-pointer font-bold pr-8 flex items-center")},m(t,r){R(t,e,r),z(e,n)},p(t,e){2&e&&Le(n,t[1])},d(t){t&&S(e)}}}function f0(t){let e,n,r,i,o,s,a,l,c,u,d=t[1]&&Wu(t);return{c(){e=Y("div"),d&&d.c(),n=rt(),r=Y("span"),i=wt(t[2]),this.h()},l(o){e=q(o,"DIV",{role:!0,class:!0});var s=$(e);d&&d.l(s),n=nt(s),r=q(s,"SPAN",{class:!0});var a=$(r);i=vt(a,t[2]),a.forEach(S),s.forEach(S),this.h()},h(){N(r,"class","cursor-pointer"),N(e,"role","none"),N(e,"class",o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+Hu[t[0]])},m(o,s){R(o,e,s),d&&d.m(e,null),z(e,n),z(e,r),z(r,i),l=!0,c||(u=[he(e,"click",t[3]),he(e,"keypress",t[3])],c=!0)},p(t,[r]){t[1]?d?d.p(t,r):(d=Wu(t),d.c(),d.m(e,n)):d&&(d.d(1),d=null),(!l||4&r)&&Le(i,t[2]),(!l||1&r&&o!==(o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+Hu[t[0]]))&&N(e,"class",o)},i(t){l||(t&&On((()=>{l&&(a&&a.end(1),s=Hi(e,bl,{}),s.start())})),l=!0)},o(t){s&&s.invalidate(),t&&(a=es(e,ei,{x:1e3,duration:1e3,delay:0,opacity:.8})),l=!1},d(t){t&&S(e),d&&d.d(),t&&a&&a.end(),c=!1,wr(u)}}}Zo=new WeakMap,ts=new WeakMap,Ci=new WeakMap,Ba=new WeakMap;const Nl={error:"negative",success:"positive"},d0=t=>Object.keys(Nl).includes(t),h0=t=>d0(t)?(console.warn(`[Toast] The status "${t}" is deprecated. Please use "${Nl[t]}" instead.`),Nl[t]):t,Hu={negative:"border-negative/50 bg-negative/10 text-negative",positive:"border-positive/50 bg-positive/10 text-positive",info:"border-info/50 bg-info/10 text-info",warning:"border-warning/50 bg-warning/10 text-warning"};function m0(t,e,n){let{id:r}=e,{status:i="info"}=e,{title:o}=e,{message:s}=e,{dismissable:a=!0}=e;const l=d_();return t.$$set=t=>{"id"in t&&n(4,r=t.id),"status"in t&&n(0,i=t.status),"title"in t&&n(1,o=t.title),"message"in t&&n(2,s=t.message),"dismissable"in t&&n(5,a=t.dismissable)},t.$$.update=()=>{1&t.$$.dirty&&n(0,i=h0(i))},[i,o,s,()=>{a&&l("dismiss",{id:r})},r,a]}class p0 extends ae{constructor(t){super(),le(this,t,m0,f0,ee,{id:4,status:0,title:1,message:2,dismissable:5})}}function qu(t,e,n){const r=t.slice();return r[2]=e[n],r}function Yu(t,e){let n,r,i;const o=[e[2]];let s={};for(let t=0;t<o.length;t+=1)s=me(s,o[t]);return r=new p0({props:s}),r.$on("dismiss",e[1]),{key:t,first:null,c(){n=ft(),ct(r.$$.fragment),this.h()},l(t){n=ft(),lt(r.$$.fragment,t),this.h()},h(){this.first=n},m(t,e){R(t,n,e),at(r,t,e),i=!0},p(t,n){e=t;const i=1&n?An(o,[Ql(e[2])]):{};r.$set(i)},i(t){i||(O(r.$$.fragment,t),i=!0)},o(t){D(r.$$.fragment,t),i=!1},d(t){t&&S(n),st(r,t)}}}function g0(t){let e,n,r=[],i=new Map,o=ue(t[0]);const s=t=>t[2].id;for(let e=0;e<o.length;e+=1){let n=qu(t,o,e),a=s(n);i.set(a,r[e]=Yu(a,n))}return{c(){e=Y("div");for(let t=0;t<r.length;t+=1)r[t].c();this.h()},l(t){e=q(t,"DIV",{class:!0});var n=$(e);for(let t=0;t<r.length;t+=1)r[t].l(n);n.forEach(S),this.h()},h(){N(e,"class","z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80")},m(t,i){R(t,e,i);for(let t=0;t<r.length;t+=1)r[t]&&r[t].m(e,null);n=!0},p(t,[n]){1&n&&(o=ue(t[0]),jt(),r=dh(r,n,s,1,t,o,i,e,Hg,Yu,null,qu),Lt())},i(t){if(!n){for(let t=0;t<o.length;t+=1)O(r[t]);n=!0}},o(t){for(let t=0;t<r.length;t+=1)D(r[t]);n=!1},d(t){t&&S(e);for(let t=0;t<r.length;t+=1)r[t].d()}}}function _0(t,e,n){let r;return Ae(t,Yc,(t=>n(0,r=t))),[r,({detail:t})=>Yc.dismiss(t.id)]}class b0 extends ae{constructor(t){super(),le(this,t,_0,g0,ee,{})}}const Ku="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png",$u="/_app/immutable/assets/wordmark-black.rfl-FBgf.png";function y0(t){let e,n,r,i,o;return{c(){e=Y("img"),r=rt(),i=Y("img"),this.h()},l(t){e=q(t,"IMG",{src:!0,alt:!0,class:!0,href:!0}),r=nt(t),i=q(t,"IMG",{src:!0,alt:!0,class:!0,href:!0}),this.h()},h(){bs(e.src,n=t[0]??t[1]??$u)||N(e,"src",n),N(e,"alt","Home"),N(e,"class","h-5 aspect-auto block dark:hidden"),N(e,"href",xt("/")),bs(i.src,o=t[0]??t[2]??Ku)||N(i,"src",o),N(i,"alt","Home"),N(i,"class","h-5 aspect-auto hidden dark:block"),N(i,"href",xt("/"))},m(t,n){R(t,e,n),R(t,r,n),R(t,i,n)},p(t,r){3&r&&!bs(e.src,n=t[0]??t[1]??$u)&&N(e,"src",n),5&r&&!bs(i.src,o=t[0]??t[2]??Ku)&&N(i,"src",o)},d(t){t&&(S(e),S(r),S(i))}}}function v0(t){let e;return{c(){e=wt(t[3])},l(n){e=vt(n,t[3])},m(t,n){R(t,e,n)},p(t,n){8&n&&Le(e,t[3])},d(t){t&&S(e)}}}function w0(t){let e;function n(t,e){return t[3]?v0:y0}let r=n(t),i=r(t);return{c(){i.c(),e=ft()},l(t){i.l(t),e=ft()},m(t,n){i.m(t,n),R(t,e,n)},p(t,[o]){r===(r=n(t))&&i?i.p(t,o):(i.d(1),i=r(t),i&&(i.c(),i.m(e.parentNode,e)))},i:Qt,o:Qt,d(t){t&&S(e),i.d(t)}}}function S0(t,e,n){let{logo:r}=e,{lightLogo:i}=e,{darkLogo:o}=e,{title:s}=e;return t.$$set=t=>{"logo"in t&&n(0,r=t.logo),"lightLogo"in t&&n(1,i=t.lightLogo),"darkLogo"in t&&n(2,o=t.darkLogo),"title"in t&&n(3,s=t.title)},[r,i,o,s]}class Sc extends ae{constructor(t){super(),le(this,t,S0,w0,ee,{logo:0,lightLogo:1,darkLogo:2,title:3})}}function Cl(){return Cl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Cl.apply(null,arguments)}function mt(t){return(mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var as,Rt,Ap,Xr,Ju,Tp,Fl,Bp,Ic,jl,Ll,Mp,Ko={},Pp=[],I0=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,ja=Array.isArray;function mr(t,e){for(var n in e)t[n]=e[n];return t}function Oc(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function Xn(t,e,n){var r,i,o,s={};for(o in e)"key"==o?r=e[o]:"ref"==o?i=e[o]:s[o]=e[o];if(arguments.length>2&&(s.children=arguments.length>3?as.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===s[o]&&(s[o]=t.defaultProps[o]);return ko(t,s,r,i,null)}function ko(t,e,n,r,i){var o={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:i??++Ap,__i:-1,__u:0};return null==i&&null!=Rt.vnode&&Rt.vnode(o),o}function _r(t){return t.children}function Zn(t,e){this.props=t,this.context=e}function zi(t,e){if(null==e)return t.__?zi(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?zi(t):null}function Np(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return Np(t)}}function Rl(t){(!t.__d&&(t.__d=!0)&&Xr.push(t)&&!Ea.__r++||Ju!==Rt.debounceRendering)&&((Ju=Rt.debounceRendering)||Tp)(Ea)}function Ea(){var t,e,n,r,i,o,s,a;for(Xr.sort(Fl);t=Xr.shift();)t.__d&&(e=Xr.length,r=void 0,o=(i=(n=t).__v).__e,s=[],a=[],n.__P&&((r=mr({},i)).__v=i.__v+1,Rt.vnode&&Rt.vnode(r),kc(n.__P,r,i,n.__n,n.__P.namespaceURI,32&i.__u?[o]:null,s,o??zi(i),!!(32&i.__u),a),r.__v=i.__v,r.__.__k[r.__i]=r,jp(s,r,a),r.__e!=o&&Np(r)),Xr.length>e&&Xr.sort(Fl));Ea.__r=0}function Cp(t,e,n,r,i,o,s,a,l,c,u){var d,h,f,p,m,b,y=r&&r.__k||Pp,g=e.length;for(l=function(t,e,n,r,i){var o,s,a,l,c,u=n.length,d=u,h=0;for(t.__k=new Array(i),o=0;o<i;o++)null!=(s=e[o])&&"boolean"!=typeof s&&"function"!=typeof s?(l=o+h,(s=t.__k[o]="string"==typeof s||"number"==typeof s||"bigint"==typeof s||s.constructor==String?ko(null,s,null,null,null):ja(s)?ko(_r,{children:s},null,null,null):void 0===s.constructor&&s.__b>0?ko(s.type,s.props,s.key,s.ref?s.ref:null,s.__v):s).__=t,s.__b=t.__b+1,a=null,-1!==(c=s.__i=O0(s,n,l,d))&&(d--,(a=n[c])&&(a.__u|=2)),null==a||null===a.__v?(-1==c&&h--,"function"!=typeof s.type&&(s.__u|=4)):c!=l&&(c==l-1?h--:c==l+1?h++:(c>l?h--:h++,s.__u|=4))):t.__k[o]=null;if(d)for(o=0;o<u;o++)null!=(a=n[o])&&!(2&a.__u)&&(a.__e==r&&(r=zi(a)),Lp(a,a));return r}(n,e,y,l,g),d=0;d<g;d++)null!=(f=n.__k[d])&&(h=-1===f.__i?Ko:y[f.__i]||Ko,f.__i=d,b=kc(t,f,h,i,o,s,a,l,c,u),p=f.__e,f.ref&&h.ref!=f.ref&&(h.ref&&Dc(h.ref,null,f),u.push(f.ref,f.__c||p,f)),null==m&&null!=p&&(m=p),4&f.__u||h.__k===f.__k?l=Fp(f,l,t):"function"==typeof f.type&&void 0!==b?l=b:p&&(l=p.nextSibling),f.__u&=-7);return n.__e=m,l}function Fp(t,e,n){var r,i;if("function"==typeof t.type){for(r=t.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=t,e=Fp(r[i],e,n));return e}t.__e!=e&&(e&&t.type&&!n.contains(e)&&(e=zi(t)),n.insertBefore(t.__e,e||null),e=t.__e);do{e=e&&e.nextSibling}while(null!=e&&8==e.nodeType);return e}function br(t,e){return e=e||[],null==t||"boolean"==typeof t||(ja(t)?t.some((function(t){br(t,e)})):e.push(t)),e}function O0(t,e,n,r){var i,o,s=t.key,a=t.type,l=e[n];if(null===l||l&&s==l.key&&a===l.type&&!(2&l.__u))return n;if(r>(null==l||2&l.__u?0:1))for(i=n-1,o=n+1;i>=0||o<e.length;){if(i>=0){if((l=e[i])&&!(2&l.__u)&&s==l.key&&a===l.type)return i;i--}if(o<e.length){if((l=e[o])&&!(2&l.__u)&&s==l.key&&a===l.type)return o;o++}}return-1}function Qu(t,e,n){"-"==e[0]?t.setProperty(e,n??""):t[e]=null==n?"":"number"!=typeof n||I0.test(e)?n:n+"px"}function Is(t,e,n,r,i){var o;t:if("style"==e)if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||Qu(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||Qu(t.style,e,n[e])}else if("o"==e[0]&&"n"==e[1])o=e!=(e=e.replace(Bp,"$1")),e=e.toLowerCase()in t||"onFocusOut"==e||"onFocusIn"==e?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=n,n?r?n.u=r.u:(n.u=Ic,t.addEventListener(e,o?Ll:jl,o)):t.removeEventListener(e,o?Ll:jl,o);else{if("http://www.w3.org/2000/svg"==i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=e&&"height"!=e&&"href"!=e&&"list"!=e&&"form"!=e&&"tabIndex"!=e&&"download"!=e&&"rowSpan"!=e&&"colSpan"!=e&&"role"!=e&&"popover"!=e&&e in t)try{t[e]=n??"";break t}catch{}"function"==typeof n||(null==n||!1===n&&"-"!=e[4]?t.removeAttribute(e):t.setAttribute(e,"popover"==e&&1==n?"":n))}}function Gu(t){return function(e){if(this.l){var n=this.l[e.type+t];if(null==e.t)e.t=Ic++;else if(e.t<n.u)return;return n(Rt.event?Rt.event(e):e)}}}function kc(t,e,n,r,i,o,s,a,l,c){var u,d,h,f,p,m,b,y,g,v,_,w,S,O,I,$,x,D=e.type;if(void 0!==e.constructor)return null;128&n.__u&&(l=!!(32&n.__u),o=[a=e.__e=n.__e]),(u=Rt.__b)&&u(e);t:if("function"==typeof D)try{if(y=e.props,g="prototype"in D&&D.prototype.render,v=(u=D.contextType)&&r[u.__c],_=u?v?v.props.value:u.__:r,n.__c?b=(d=e.__c=n.__c).__=d.__E:(g?e.__c=d=new D(y,_):(e.__c=d=new Zn(y,_),d.constructor=D,d.render=D0),v&&v.sub(d),d.props=y,d.state||(d.state={}),d.context=_,d.__n=r,h=d.__d=!0,d.__h=[],d._sb=[]),g&&null==d.__s&&(d.__s=d.state),g&&null!=D.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=mr({},d.__s)),mr(d.__s,D.getDerivedStateFromProps(y,d.__s))),f=d.props,p=d.state,d.__v=e,h)g&&null==D.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),g&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(g&&null==D.getDerivedStateFromProps&&y!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(y,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(y,d.__s,_)||e.__v==n.__v)){for(e.__v!=n.__v&&(d.props=y,d.state=d.__s,d.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.some((function(t){t&&(t.__=e)})),w=0;w<d._sb.length;w++)d.__h.push(d._sb[w]);d._sb=[],d.__h.length&&s.push(d);break t}null!=d.componentWillUpdate&&d.componentWillUpdate(y,d.__s,_),g&&null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(f,p,m)}))}if(d.context=_,d.props=y,d.__P=t,d.__e=!1,S=Rt.__r,O=0,g){for(d.state=d.__s,d.__d=!1,S&&S(e),u=d.render(d.props,d.state,d.context),I=0;I<d._sb.length;I++)d.__h.push(d._sb[I]);d._sb=[]}else do{d.__d=!1,S&&S(e),u=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++O<25);d.state=d.__s,null!=d.getChildContext&&(r=mr(mr({},r),d.getChildContext())),g&&!h&&null!=d.getSnapshotBeforeUpdate&&(m=d.getSnapshotBeforeUpdate(f,p)),a=Cp(t,ja($=null!=u&&u.type===_r&&null==u.key?u.props.children:u)?$:[$],e,n,r,i,o,s,a,l,c),d.base=e.__e,e.__u&=-161,d.__h.length&&s.push(d),b&&(d.__E=d.__=null)}catch(t){if(e.__v=null,l||null!=o)if(t.then){for(e.__u|=l?160:128;a&&8==a.nodeType&&a.nextSibling;)a=a.nextSibling;o[o.indexOf(a)]=null,e.__e=a}else for(x=o.length;x--;)Oc(o[x]);else e.__e=n.__e,e.__k=n.__k;Rt.__e(t,e,n)}else null==o&&e.__v==n.__v?(e.__k=n.__k,e.__e=n.__e):a=e.__e=k0(n.__e,e,n,r,i,o,s,l,c);return(u=Rt.diffed)&&u(e),128&e.__u?void 0:a}function jp(t,e,n){for(var r=0;r<n.length;r++)Dc(n[r],n[++r],n[++r]);Rt.__c&&Rt.__c(e,t),t.some((function(e){try{t=e.__h,e.__h=[],t.some((function(t){t.call(e)}))}catch(t){Rt.__e(t,e.__v)}}))}function k0(t,e,n,r,i,o,s,a,l){var c,u,d,h,f,p,m,b=n.props,y=e.props,g=e.type;if("svg"==g?i="http://www.w3.org/2000/svg":"math"==g?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=o)for(c=0;c<o.length;c++)if((f=o[c])&&"setAttribute"in f==!!g&&(g?f.localName==g:3==f.nodeType)){t=f,o[c]=null;break}if(null==t){if(null==g)return document.createTextNode(y);t=document.createElementNS(i,g,y.is&&y),a&&(Rt.__m&&Rt.__m(e,o),a=!1),o=null}if(null===g)b===y||a&&t.data===y||(t.data=y);else{if(o=o&&as.call(t.childNodes),b=n.props||Ko,!a&&null!=o)for(b={},c=0;c<t.attributes.length;c++)b[(f=t.attributes[c]).name]=f.value;for(c in b)if(f=b[c],"children"!=c)if("dangerouslySetInnerHTML"==c)d=f;else if(!(c in y)){if("value"==c&&"defaultValue"in y||"checked"==c&&"defaultChecked"in y)continue;Is(t,c,null,f,i)}for(c in y)f=y[c],"children"==c?h=f:"dangerouslySetInnerHTML"==c?u=f:"value"==c?p=f:"checked"==c?m=f:a&&"function"!=typeof f||b[c]===f||Is(t,c,f,b[c],i);if(u)a||d&&(u.__html===d.__html||u.__html===t.innerHTML)||(t.innerHTML=u.__html),e.__k=[];else if(d&&(t.innerHTML=""),Cp(t,ja(h)?h:[h],e,n,r,"foreignObject"==g?"http://www.w3.org/1999/xhtml":i,o,s,o?o[0]:n.__k&&zi(n,0),a,l),null!=o)for(c=o.length;c--;)Oc(o[c]);a||(c="value","progress"==g&&null==p?t.removeAttribute("value"):void 0!==p&&(p!==t[c]||"progress"==g&&!p||"option"==g&&p!==b[c])&&Is(t,c,p,b[c],i),c="checked",void 0!==m&&m!==t[c]&&Is(t,c,m,b[c],i))}return t}function Dc(t,e,n){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==e||(t.__u=t(e))}else t.current=e}catch(t){Rt.__e(t,n)}}function Lp(t,e,n){var r,i;if(Rt.unmount&&Rt.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||Dc(r,null,e)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){Rt.__e(t,e)}r.base=r.__P=null}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&Lp(r[i],e,n||"function"!=typeof t.type);n||Oc(t.__e),t.__c=t.__=t.__e=void 0}function D0(t,e,n){return this.constructor(t,n)}function $o(t,e,n){var r,i,o,s;e==document&&(e=document.documentElement),Rt.__&&Rt.__(t,e),i=(r="function"==typeof n)?null:n&&n.__k||e.__k,o=[],s=[],kc(e,t=(!r&&n||e).__k=Xn(_r,null,[t]),i||Ko,Ko,e.namespaceURI,!r&&n?[n]:i?null:e.firstChild?as.call(e.childNodes):null,o,!r&&n?n:i?i.__e:e.firstChild,r,s),jp(o,t,s)}function Rp(t,e){$o(t,e,Rp)}function E0(t,e,n){var r,i,o,s,a=mr({},t.props);for(o in t.type&&t.type.defaultProps&&(s=t.type.defaultProps),e)"key"==o?r=e[o]:"ref"==o?i=e[o]:a[o]=void 0===e[o]&&void 0!==s?s[o]:e[o];return arguments.length>2&&(a.children=arguments.length>3?as.call(arguments,2):n),ko(t.type,a,r||t.key,i||t.ref,null)}as=Pp.slice,Rt={__e:function(t,e,n,r){for(var i,o,s;e=e.__;)if((i=e.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(t)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),s=i.__d),s)return i.__E=i}catch(e){t=e}throw t}},Ap=0,Zn.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=mr({},this.state),"function"==typeof t&&(t=t(mr({},n),this.props)),t&&mr(n,t),null!=t&&this.__v&&(e&&this._sb.push(e),Rl(this))},Zn.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),Rl(this))},Zn.prototype.render=_r,Xr=[],Tp="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Fl=function(t,e){return t.__v.__b-e.__v.__b},Ea.__r=0,Bp=/(PointerCapture)$|Capture$/i,Ic=0,jl=Gu(!1),Ll=Gu(!0),Mp=0;var jr,ge,sl,Xu,Wi=0,Up=[],Se=Rt,Zu=Se.__b,tf=Se.__r,ef=Se.diffed,nf=Se.__c,rf=Se.unmount,of=Se.__;function Ki(t,e){Se.__h&&Se.__h(ge,t,Wi||e),Wi=0;var n=ge.__H||(ge.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function ls(t){return Wi=1,Ec(Yp,t)}function Ec(t,e,n){var r=Ki(jr++,2);if(r.t=t,!r.__c&&(r.__=[n?n(e):Yp(void 0,e),function(t){var e=r.__N?r.__N[0]:r.__[0],n=r.t(e,t);e!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=ge,!ge.u)){var i=function(t,e,n){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter((function(t){return!!t.__c}));if(i.every((function(t){return!t.__N})))return!o||o.call(this,t,e,n);var s=r.__c.props!==t;return i.forEach((function(t){if(t.__N){var e=t.__[0];t.__=t.__N,t.__N=void 0,e!==t.__[0]&&(s=!0)}})),o&&o.call(this,t,e,n)||s};ge.u=!0;var o=ge.shouldComponentUpdate,s=ge.componentWillUpdate;ge.componentWillUpdate=function(t,e,n){if(this.__e){var r=o;o=void 0,i(t,e,n),o=r}s&&s.call(this,t,e,n)},ge.shouldComponentUpdate=i}return r.__N||r.__}function cs(t,e){var n=Ki(jr++,3);!Se.__s&&Ac(n.__H,e)&&(n.__=t,n.i=e,ge.__H.__h.push(n))}function us(t,e){var n=Ki(jr++,4);!Se.__s&&Ac(n.__H,e)&&(n.__=t,n.i=e,ge.__h.push(n))}function Vp(t){return Wi=5,La((function(){return{current:t}}),[])}function xp(t,e,n){Wi=6,us((function(){return"function"==typeof t?(t(e()),function(){return t(null)}):t?(t.current=e(),function(){return t.current=null}):void 0}),null==n?n:n.concat(t))}function La(t,e){var n=Ki(jr++,7);return Ac(n.__H,e)&&(n.__=t(),n.__H=e,n.__h=t),n.__}function zp(t,e){return Wi=8,La((function(){return t}),e)}function Wp(t){var e=ge.context[t.__c],n=Ki(jr++,9);return n.c=t,e?(null==n.__&&(n.__=!0,e.sub(ge)),e.props.value):t.__}function Hp(t,e){Se.useDebugValue&&Se.useDebugValue(e?e(t):t)}function qp(){var t=Ki(jr++,11);if(!t.__){for(var e=ge.__v;null!==e&&!e.__m&&null!==e.__;)e=e.__;var n=e.__m||(e.__m=[0,0]);t.__="P"+n[0]+"-"+n[1]++}return t.__}function A0(){for(var t;t=Up.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(qs),t.__H.__h.forEach(Ul),t.__H.__h=[]}catch(e){t.__H.__h=[],Se.__e(e,t.__v)}}Se.__b=function(t){ge=null,Zu&&Zu(t)},Se.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),of&&of(t,e)},Se.__r=function(t){tf&&tf(t),jr=0;var e=(ge=t.__c).__H;e&&(sl===ge?(e.__h=[],ge.__h=[],e.__.forEach((function(t){t.__N&&(t.__=t.__N),t.i=t.__N=void 0}))):(e.__h.forEach(qs),e.__h.forEach(Ul),e.__h=[],jr=0)),sl=ge},Se.diffed=function(t){ef&&ef(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(1!==Up.push(e)&&Xu===Se.requestAnimationFrame||((Xu=Se.requestAnimationFrame)||T0)(A0)),e.__H.__.forEach((function(t){t.i&&(t.__H=t.i),t.i=void 0}))),sl=ge=null},Se.__c=function(t,e){e.some((function(t){try{t.__h.forEach(qs),t.__h=t.__h.filter((function(t){return!t.__||Ul(t)}))}catch(n){e.some((function(t){t.__h&&(t.__h=[])})),e=[],Se.__e(n,t.__v)}})),nf&&nf(t,e)},Se.unmount=function(t){rf&&rf(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach((function(t){try{qs(t)}catch(t){e=t}})),n.__H=void 0,e&&Se.__e(e,n.__v))};var sf="function"==typeof requestAnimationFrame;function T0(t){var e,n=function(){clearTimeout(r),sf&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);sf&&(e=requestAnimationFrame(n))}function qs(t){var e=ge,n=t.__c;"function"==typeof n&&(t.__c=void 0,n()),ge=e}function Ul(t){var e=ge;t.__c=t.__(),ge=e}function Ac(t,e){return!t||t.length!==e.length||e.some((function(e,n){return e!==t[n]}))}function Yp(t,e){return"function"==typeof e?e(t):e}function Kp(t,e){for(var n in e)t[n]=e[n];return t}function Vl(t,e){for(var n in t)if("__source"!==n&&!(n in e))return!0;for(var r in e)if("__source"!==r&&t[r]!==e[r])return!0;return!1}function $p(t,e){var n=e(),r=ls({t:{__:n,u:e}}),i=r[0].t,o=r[1];return us((function(){i.__=n,i.u=e,al(i)&&o({t:i})}),[t,n,e]),cs((function(){return al(i)&&o({t:i}),t((function(){al(i)&&o({t:i})}))}),[t]),n}function al(t){var e,n,r=t.u,i=t.__;try{var o=r();return!((e=i)===(n=o)&&(0!==e||1/e==1/n)||e!=e&&n!=n)}catch{return!0}}function Jp(t){t()}function Qp(t){return t}function Gp(){return[!1,Jp]}var Xp=us;function xl(t,e){this.props=t,this.context=e}(xl.prototype=new Zn).isPureReactComponent=!0,xl.prototype.shouldComponentUpdate=function(t,e){return Vl(this.props,t)||Vl(this.state,e)};var af=Rt.__b;Rt.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),af&&af(t)};var B0=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911,lf=function(t,e){return null==t?null:br(br(t).map(e))},M0={map:lf,forEach:lf,count:function(t){return t?br(t).length:0},only:function(t){var e=br(t);if(1!==e.length)throw"Children.only";return e[0]},toArray:br},P0=Rt.__e;Rt.__e=function(t,e,n,r){if(t.then)for(var i,o=e;o=o.__;)if((i=o.__c)&&i.__c)return null==e.__e&&(e.__e=n.__e,e.__k=n.__k),i.__c(t,e);P0(t,e,n,r)};var cf=Rt.unmount;function Zp(t,e,n){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(t){"function"==typeof t.__c&&t.__c()})),t.__c.__H=null),null!=(t=Kp({},t)).__c&&(t.__c.__P===n&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return Zp(t,e,n)}))),t}function tg(t,e,n){return t&&n&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return tg(t,e,n)})),t.__c&&t.__c.__P===e&&(t.__e&&n.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=n)),t}function Ys(){this.__u=0,this.o=null,this.__b=null}function eg(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function wo(){this.i=null,this.l=null}Rt.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&32&t.__u&&(t.type=null),cf&&cf(t)},(Ys.prototype=new Zn).__c=function(t,e){var n=e.__c,r=this;null==r.o&&(r.o=[]),r.o.push(n);var i=eg(r.__v),o=!1,s=function(){o||(o=!0,n.__R=null,i?i(a):a())};n.__R=s;var a=function(){if(! --r.__u){if(r.state.__a){var t=r.state.__a;r.__v.__k[0]=tg(t,t.__c.__P,t.__c.__O)}var e;for(r.setState({__a:r.__b=null});e=r.o.pop();)e.forceUpdate()}};r.__u++||32&e.__u||r.setState({__a:r.__b=r.__v.__k[0]}),t.then(s,s)},Ys.prototype.componentWillUnmount=function(){this.o=[]},Ys.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Zp(this.__b,n,r.__O=r.__P)}this.__b=null}var i=e.__a&&Xn(_r,null,t.fallback);return i&&(i.__u&=-33),[Xn(_r,null,e.__a?null:t.children),i]};var uf=function(t,e,n){if(++n[1]===n[0]&&t.l.delete(e),t.props.revealOrder&&("t"!==t.props.revealOrder[0]||!t.l.size))for(n=t.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;t.i=n=n[2]}};function N0(t){return this.getChildContext=function(){return t.context},t.children}function C0(t){var e=this,n=t.h;e.componentWillUnmount=function(){$o(null,e.v),e.v=null,e.h=null},e.h&&e.h!==n&&e.componentWillUnmount(),e.v||(e.h=n,e.v={nodeType:1,parentNode:n,childNodes:[],contains:function(){return!0},appendChild:function(t){this.childNodes.push(t),e.h.appendChild(t)},insertBefore:function(t,n){this.childNodes.push(t),e.h.insertBefore(t,n)},removeChild:function(t){this.childNodes.splice(this.childNodes.indexOf(t)>>>1,1),e.h.removeChild(t)}}),$o(Xn(N0,{context:e.context},t.__v),e.v)}function ng(t,e){var n=Xn(C0,{__v:t,h:e});return n.containerInfo=e,n}(wo.prototype=new Zn).__a=function(t){var e=this,n=eg(e.__v),r=e.l.get(t);return r[0]++,function(i){var o=function(){e.props.revealOrder?(r.push(i),uf(e,t,r)):i()};n?n(o):o()}},wo.prototype.render=function(t){this.i=null,this.l=new Map;var e=br(t.children);t.revealOrder&&"b"===t.revealOrder[0]&&e.reverse();for(var n=e.length;n--;)this.l.set(e[n],this.i=[1,0,this.i]);return t.children},wo.prototype.componentDidUpdate=wo.prototype.componentDidMount=function(){var t=this;this.l.forEach((function(e,n){uf(t,n,e)}))};var rg=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,F0=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,j0=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,L0=/[A-Z0-9]/g,R0=typeof document<"u",U0=function(t){return(typeof Symbol<"u"&&"symbol"==mt(Symbol())?/fil|che|rad/:/fil|che|ra/).test(t)};function ig(t,e,n){return null==e.__k&&(e.textContent=""),$o(t,e),"function"==typeof n&&n(),t?t.__c:null}Zn.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(t){Object.defineProperty(Zn.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})}));var ff=Rt.event;function V0(){}function x0(){return this.cancelBubble}function z0(){return this.defaultPrevented}Rt.event=function(t){return ff&&(t=ff(t)),t.persist=V0,t.isPropagationStopped=x0,t.isDefaultPrevented=z0,t.nativeEvent=t};var Tc,W0={enumerable:!1,configurable:!0,get:function(){return this.class}},df=Rt.vnode;Rt.vnode=function(t){"string"==typeof t.type&&function(t){var e=t.props,n=t.type,r={},i=-1===n.indexOf("-");for(var o in e){var s=e[o];if(!("value"===o&&"defaultValue"in e&&null==s||R0&&"children"===o&&"noscript"===n||"class"===o||"className"===o)){var a=o.toLowerCase();"defaultValue"===o&&"value"in e&&null==e.value?o="value":"download"===o&&!0===s?s="":"translate"===a&&"no"===s?s=!1:"o"===a[0]&&"n"===a[1]?"ondoubleclick"===a?o="ondblclick":"onchange"!==a||"input"!==n&&"textarea"!==n||U0(e.type)?"onfocus"===a?o="onfocusin":"onblur"===a?o="onfocusout":j0.test(o)&&(o=a):a=o="oninput":i&&F0.test(o)?o=o.replace(L0,"-$&").toLowerCase():null===s&&(s=void 0),"oninput"===a&&r[o=a]&&(o="oninputCapture"),r[o]=s}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=br(e.children).forEach((function(t){t.props.selected=-1!=r.value.indexOf(t.props.value)}))),"select"==n&&null!=r.defaultValue&&(r.value=br(e.children).forEach((function(t){t.props.selected=r.multiple?-1!=r.defaultValue.indexOf(t.props.value):r.defaultValue==t.props.value}))),e.class&&!e.className?(r.class=e.class,Object.defineProperty(r,"className",W0)):(e.className&&!e.class||e.class&&e.className)&&(r.class=r.className=e.className),t.props=r}(t),t.$$typeof=rg,df&&df(t)};var hf=Rt.__r;Rt.__r=function(t){hf&&hf(t),Tc=t.__c};var mf=Rt.diffed;Rt.diffed=function(t){mf&&mf(t);var e=t.props,n=t.__e;null!=n&&"textarea"===t.type&&"value"in e&&e.value!==n.value&&(n.value=null==e.value?"":e.value),Tc=null};var H0={ReactCurrentDispatcher:{current:{readContext:function(t){return Tc.__n[t.__c].props.value},useCallback:zp,useContext:Wp,useDebugValue:Hp,useDeferredValue:Qp,useEffect:cs,useId:qp,useImperativeHandle:xp,useInsertionEffect:Xp,useLayoutEffect:us,useMemo:La,useReducer:Ec,useRef:Vp,useState:ls,useSyncExternalStore:$p,useTransition:Gp}}};function Os(t){return!!t&&t.$$typeof===rg}var k={useState:ls,useId:qp,useReducer:Ec,useEffect:cs,useLayoutEffect:us,useInsertionEffect:Xp,useTransition:Gp,useDeferredValue:Qp,useSyncExternalStore:$p,startTransition:Jp,useRef:Vp,useImperativeHandle:xp,useMemo:La,useCallback:zp,useContext:Wp,useDebugValue:Hp,version:"18.3.1",Children:M0,render:ig,hydrate:function(t,e,n){return Rp(t,e),"function"==typeof n&&n(),t?t.__c:null},unmountComponentAtNode:function(t){return!!t.__k&&($o(null,t),!0)},createPortal:ng,createElement:Xn,createContext:function(t,e){var n={__c:e="__cC"+Mp++,__:t,Consumer:function(t,e){return t.children(e)},Provider:function(t){var n,r;return this.getChildContext||(n=new Set,(r={})[e]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&n.forEach((function(t){t.__e=!0,Rl(t)}))},this.sub=function(t){n.add(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){n&&n.delete(t),e&&e.call(t)}}),t.children}};return n.Provider.__=n.Consumer.contextType=n},createFactory:function(t){return Xn.bind(null,t)},cloneElement:function(t){return Os(t)?E0.apply(null,arguments):t},createRef:function(){return{current:null}},Fragment:_r,isValidElement:Os,isElement:Os,isFragment:function(t){return Os(t)&&t.type===_r},isMemo:function(t){return!!t&&!!t.displayName&&("string"==typeof t.displayName||t.displayName instanceof String)&&t.displayName.startsWith("Memo(")},findDOMNode:function(t){return t&&(t.base||1===t.nodeType&&t)||null},Component:Zn,PureComponent:xl,memo:function(t,e){function n(t){var n=this.props.ref,r=n==t.ref;return!r&&n&&(n.call?n(null):n.current=null),e?!e(this.props,t)||!r:Vl(this.props,t)}function r(e){return this.shouldComponentUpdate=n,Xn(t,e)}return r.displayName="Memo("+(t.displayName||t.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r},forwardRef:function(t){function e(e){var n=Kp({},e);return delete n.ref,t(n,e.ref||null)}return e.$$typeof=B0,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(t.displayName||t.name)+")",e},flushSync:function(t,e){return t(e)},unstable_batchedUpdates:function(t,e){return t(e)},StrictMode:_r,Suspense:Ys,SuspenseList:wo,lazy:function(t){var e,n,r;function i(i){if(e||(e=t()).then((function(t){n=t.default||t}),(function(t){r=t})),r)throw r;if(!n)throw e;return Xn(n,i)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:H0};function zl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function pf(t,e,n,r,i,o,s){try{var a=t[o](s),l=a.value}catch(t){return void n(t)}a.done?e(l):Promise.resolve(l).then(r,i)}function ll(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function s(t){pf(o,r,i,s,a,"next",t)}function a(t){pf(o,r,i,s,a,"throw",t)}s(void 0)}))}}function $i(t,e,n){return e=Jo(e),function(t,e){if(e&&("object"==mt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Bc()?Reflect.construct(e,n||[],Jo(t).constructor):e.apply(t,n))}function Ji(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Qi(t,e,n){return Object.defineProperty(t,"prototype",{writable:!1}),t}function Gi(t,e,n){return r=function(t){if("object"!=mt(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=mt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"==mt(r)?r:r+"")in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Ke(){return Ke=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ke.apply(null,arguments)}function Jo(t){return(Jo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Xi(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Qo(t,e)}function Bc(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch{}return(Bc=function(){return!!t})()}function gf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ut(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?gf(Object(n),!0).forEach((function(e){Gi(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):gf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function tr(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(e.includes(r))continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.includes(n)||{}.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function Qr(){Qr=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch{c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var o=e&&e.prototype instanceof y?e:y,s=Object.create(o.prototype),a=new A(r||[]);return i(s,"_invoke",{value:x(t,n,a)}),s}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var h="suspendedStart",f="suspendedYield",p="executing",m="completed",b={};function y(){}function g(){}function v(){}var _={};c(_,s,(function(){return this}));var w=Object.getPrototypeOf,S=w&&w(w(T([])));S&&S!==n&&r.call(S,s)&&(_=S);var O=v.prototype=y.prototype=Object.create(_);function I(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function n(i,o,s,a){var l=d(t[i],t,o);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==mt(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){n("next",t,s,a)}),(function(t){n("throw",t,s,a)})):e.resolve(u).then((function(t){c.value=t,s(c)}),(function(t){return n("throw",t,s,a)}))}a(l.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function x(e,n,r){var i=h;return function(o,s){if(i===p)throw Error("Generator is already running");if(i===m){if("throw"===o)throw s;return{value:t,done:!0}}for(r.method=o,r.arg=s;;){var a=r.delegate;if(a){var l=D(a,r);if(l){if(l===b)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===h)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=p;var c=d(e,n,r);if("normal"===c.type){if(i=r.done?m:f,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=m,r.method="throw",r.arg=c.arg)}}}function D(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,D(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var o=d(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,b;var s=o.arg;return s?s.done?(n[e.resultName]=s.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,b):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(mt(e)+" is not iterable")}return g.prototype=v,i(O,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:g,configurable:!0}),g.displayName=c(v,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,c(t,l,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},I($.prototype),c($.prototype,a,(function(){return this})),e.AsyncIterator=$,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var s=new $(u(t,n,r,i),o);return e.isGeneratorFunction(n)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},I(O),c(O,l,"Generator"),c(O,s,(function(){return this})),c(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return a.type="throw",a.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var l=r.call(s,"catchLoc"),c=r.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;k(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),b}},e}function Qo(t,e){return(Qo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function an(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,s,a=[],l=!0,c=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(a.push(r.value),a.length!==e);l=!0);}catch(t){c=!0,i=t}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw i}}return a}}(t,e)||og(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Do(t){return function(t){if(Array.isArray(t))return zl(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||og(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function og(t,e){if(t){if("string"==typeof t)return zl(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?zl(t,e):void 0}}function Wl(t){var e="function"==typeof Map?new Map:void 0;return Wl=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch{return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return function(t,e,n){if(Bc())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var i=new(t.bind.apply(t,r));return n&&Qo(i,n.prototype),i}(t,arguments,Jo(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),Qo(n,t)},Wl(t)}function q0(){return k.createElement("svg",{width:"15",height:"15",className:"DocSearch-Control-Key-Icon"},k.createElement("path",{d:"M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953",strokeWidth:"1.2",stroke:"currentColor",fill:"none",strokeLinecap:"square"}))}function sg(){return k.createElement("svg",{width:"20",height:"20",className:"DocSearch-Search-Icon",viewBox:"0 0 20 20","aria-hidden":"true"},k.createElement("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}var Y0=["translations"],cl="Ctrl",K0=k.forwardRef((function(t,e){var n=t.translations,r=void 0===n?{}:n,i=tr(t,Y0),o=r.buttonText,s=void 0===o?"Search":o,a=r.buttonAriaLabel,l=void 0===a?"Search":a,c=an(ls(null),2),u=c[0],d=c[1];cs((function(){typeof navigator<"u"&&(/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?d("⌘"):d(cl))}),[]);var h=an(u===cl?[cl,"Ctrl",k.createElement(q0,null)]:["Meta","Command",u],3),f=h[0],p=h[1],m=h[2];return k.createElement("button",Ke({type:"button",className:"DocSearch DocSearch-Button","aria-label":"".concat(l," (").concat(p,"+K)")},i,{ref:e}),k.createElement("span",{className:"DocSearch-Button-Container"},k.createElement(sg,null),k.createElement("span",{className:"DocSearch-Button-Placeholder"},s)),k.createElement("span",{className:"DocSearch-Button-Keys"},null!==u&&k.createElement(k.Fragment,null,k.createElement(_f,{reactsToKey:f},m),k.createElement(_f,{reactsToKey:"k"},"K"))))}));function _f(t){var e=t.reactsToKey,n=t.children,r=an(ls(!1),2),i=r[0],o=r[1];return cs((function(){if(e)return window.addEventListener("keydown",t),window.addEventListener("keyup",n),function(){window.removeEventListener("keydown",t),window.removeEventListener("keyup",n)};function t(t){t.key===e&&o(!0)}function n(t){t.key!==e&&"Meta"!==t.key||o(!1)}}),[e]),k.createElement("kbd",{className:i?"DocSearch-Button-Key DocSearch-Button-Key--pressed":"DocSearch-Button-Key"},n)}function ag(t,e){var n=void 0;return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];n&&clearTimeout(n),n=setTimeout((function(){return t.apply(void 0,i)}),e)}}function Go(t){return t.reduce((function(t,e){return t.concat(e)}),[])}var $0=0;function Hl(t){return 0===t.collections.length?0:t.collections.reduce((function(t,e){return t+e.items.length}),0)}function bf(t){return t!==Object(t)}function lg(t,e){if(t===e)return!0;if(bf(t)||bf(e)||"function"==typeof t||"function"==typeof e)return t===e;if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];if(!(i in e)||!lg(t[i],e[i]))return!1}return!0}var Ks=function(){},J0=[{segment:"autocomplete-core",version:"1.17.9"}];function yf(t){var e=t.item,n=t.items,r=void 0===n?[]:n;return{index:e.__autocomplete_indexName,items:[e],positions:[1+r.findIndex((function(t){return t.objectID===e.objectID}))],queryID:e.__autocomplete_queryID,algoliaSource:["autocomplete"]}}function vf(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var Q0=["items"],G0=["items"];function Eo(t){return(Eo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function ks(t){return function(t){if(Array.isArray(t))return ul(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ul(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ul(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ul(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function cg(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function wf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ei(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?wf(Object(n),!0).forEach((function(e){X0(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):wf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function X0(t,e,n){return r=function(t){if("object"!==Eo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Eo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Eo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Ds(t){return t.map((function(t){var e=t.items,n=cg(t,Q0);return Ei(Ei({},n),{},{objectIDs:(null==e?void 0:e.map((function(t){return t.objectID})))||n.objectIDs})}))}function Z0(t){var e=t.items.reduce((function(t,e){var n;return t[e.__autocomplete_indexName]=(null!==(n=t[e.__autocomplete_indexName])&&void 0!==n?n:[]).concat(e),t}),{});return Object.keys(e).map((function(t){return{index:t,items:e[t],algoliaSource:["autocomplete"]}}))}function ao(t){return t.objectID&&t.__autocomplete_indexName&&t.__autocomplete_queryID}function Ao(t){return(Ao="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Ai(t){return function(t){if(Array.isArray(t))return fl(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return fl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?fl(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Sf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Nn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Sf(Object(n),!0).forEach((function(e){t1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Sf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function t1(t,e,n){return r=function(t){if("object"!==Ao(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Ao(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Ao(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}var ug="2.15.0",e1="https://cdn.jsdelivr.net/npm/search-insights@".concat(ug,"/dist/search-insights.min.js"),n1=ag((function(t){var e=t.onItemsChange,n=t.items,r=t.insights,i=t.state;e({insights:r,insightsEvents:Z0({items:n}).map((function(t){return Nn({eventName:"Items Viewed"},t)})),state:i})}),400);function If(t){var e,n,r=Nn({onItemsChange:function(t){var e=t.insights,n=t.insightsEvents,r=t.state;e.viewedObjectIDs.apply(e,Ai(n.map((function(t){return Nn(Nn({},t),{},{algoliaSource:Of(t.algoliaSource,r.context)})}))))},onSelect:function(t){var e=t.insights,n=t.insightsEvents,r=t.state;e.clickedObjectIDsAfterSearch.apply(e,Ai(n.map((function(t){return Nn(Nn({},t),{},{algoliaSource:Of(t.algoliaSource,r.context)})}))))},onActive:Ks,__autocomplete_clickAnalytics:!0},t),i=r.insightsClient,o=r.insightsInitParams,s=r.onItemsChange,a=r.onSelect,l=r.onActive,c=r.__autocomplete_clickAnalytics,u=i;if(i||typeof window<"u"&&(e=window,"string"==typeof(n=e.AlgoliaAnalyticsObject||"aa")&&(u=e[n]),u||(e.AlgoliaAnalyticsObject=n,e[n]||(e[n]=function(){e[n].queue||(e[n].queue=[]);for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];e[n].queue.push(r)}),e[n].version=ug,u=e[n],function(t){var e="[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";try{var n=t.document.createElement("script");n.async=!0,n.src=e1,n.onerror=function(){console.error(e)},document.body.appendChild(n)}catch{console.error(e)}}(e))),!u)return{};o&&u("init",Nn({partial:!0},o));var d=function(t){var e,n,r,i,o=(n=(e=function(t){if(Array.isArray(t))return t}(i=(t.version||"").split(".").map(Number))||function(t){var e=null==t?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,r,i,o,s=[],a=!0,l=!1;try{for(i=(e=e.call(t)).next;!(a=(n=i.call(e)).done)&&(s.push(n.value),2!==s.length);a=!0);}catch(t){l=!0,r=t}finally{try{if(!a&&null!=e.return&&(o=e.return(),Object(o)!==o))return}finally{if(l)throw r}}return s}}(i)||function(t){if(t){if("string"==typeof t)return vf(t,2);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?vf(t,2):void 0}}(i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],r=e[1],n>=3||2===n&&r>=4||1===n&&r>=10);function s(e,n,r){if(o&&void 0!==r){var i=r[0].__autocomplete_algoliaCredentials,s={"X-Algolia-Application-Id":i.appId,"X-Algolia-API-Key":i.apiKey};t.apply(void 0,[e].concat(ks(n),[{headers:s}]))}else t.apply(void 0,[e].concat(ks(n)))}return{init:function(e,n){t("init",{appId:e,apiKey:n})},setAuthenticatedUserToken:function(e){t("setAuthenticatedUserToken",e)},setUserToken:function(e){t("setUserToken",e)},clickedObjectIDsAfterSearch:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("clickedObjectIDsAfterSearch",Ds(e),e[0].items)},clickedObjectIDs:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("clickedObjectIDs",Ds(e),e[0].items)},clickedFilters:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];n.length>0&&t.apply(void 0,["clickedFilters"].concat(n))},convertedObjectIDsAfterSearch:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("convertedObjectIDsAfterSearch",Ds(e),e[0].items)},convertedObjectIDs:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("convertedObjectIDs",Ds(e),e[0].items)},convertedFilters:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];n.length>0&&t.apply(void 0,["convertedFilters"].concat(n))},viewedObjectIDs:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&e.reduce((function(t,e){var n=e.items,r=cg(e,G0);return[].concat(ks(t),ks(function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,n=[],r=0;r<t.objectIDs.length;r+=e)n.push(Ei(Ei({},t),{},{objectIDs:t.objectIDs.slice(r,r+e)}));return n}(Ei(Ei({},r),{},{objectIDs:(null==n?void 0:n.map((function(t){return t.objectID})))||r.objectIDs})).map((function(t){return{items:n,payload:t}}))))}),[]).forEach((function(t){var e=t.items;return s("viewedObjectIDs",[t.payload],e)}))},viewedFilters:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];n.length>0&&t.apply(void 0,["viewedFilters"].concat(n))}}}(u),h={current:[]},f=ag((function(t){var e=t.state;if(e.isOpen){var n=e.collections.reduce((function(t,e){return[].concat(Ai(t),Ai(e.items))}),[]).filter(ao);lg(h.current.map((function(t){return t.objectID})),n.map((function(t){return t.objectID})))||(h.current=n,n.length>0&&n1({onItemsChange:s,items:n,insights:d,state:e}))}}),0);return{name:"aa.algoliaInsightsPlugin",subscribe:function(t){var e=t.setContext,n=t.onSelect,r=t.onActive;function i(t){e({algoliaInsightsPlugin:{__algoliaSearchParameters:Nn(Nn({},c?{clickAnalytics:!0}:{}),t?{userToken:r1(t)}:{}),insights:d}})}u("addAlgoliaAgent","insights-plugin"),i(),u("onUserTokenChange",(function(t){i(t)})),u("getUserToken",null,(function(t,e){i(e)})),n((function(t){var e=t.item,n=t.state,r=t.event,i=t.source;ao(e)&&a({state:n,event:r,insights:d,item:e,insightsEvents:[Nn({eventName:"Item Selected"},yf({item:e,items:i.getItems().filter(ao)}))]})})),r((function(t){var e=t.item,n=t.source,r=t.state,i=t.event;ao(e)&&l({state:r,event:i,insights:d,item:e,insightsEvents:[Nn({eventName:"Item Active"},yf({item:e,items:n.getItems().filter(ao)}))]})}))},onStateChange:function(t){var e=t.state;f({state:e})},__autocomplete_pluginOptions:t}}function Of(){var t,e=arguments.length>1?arguments[1]:void 0;return[].concat(Ai(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]),["autocomplete-internal"],Ai(null!==(t=e.algoliaInsightsPlugin)&&void 0!==t&&t.__automaticInsights?["autocomplete-automatic"]:[]))}function r1(t){return"number"==typeof t?t.toString():t}function $s(t,e){var n=e;return{then:function(e,r){return $s(t.then(Es(e,n,t),Es(r,n,t)),n)},catch:function(e){return $s(t.catch(Es(e,n,t)),n)},finally:function(e){return e&&n.onCancelList.push(e),$s(t.finally(Es(e&&function(){return n.onCancelList=[],e()},n,t)),n)},cancel:function(){n.isCanceled=!0;var t=n.onCancelList;n.onCancelList=[],t.forEach((function(t){t()}))},isCanceled:function(){return!0===n.isCanceled}}}function kf(t){return $s(t,{isCanceled:!1,onCancelList:[]})}function Es(t,e,n){return t?function(n){return e.isCanceled?n:t(n)}:n}function Df(t,e,n,r){if(!n)return null;if(t<0&&(null===e||null!==r&&0===e))return n+t;var i=(null===e?-1:e)+t;return i<=-1||i>=n?null===r?null:0:i}function Ef(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Af(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ef(Object(n),!0).forEach((function(e){i1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ef(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function i1(t,e,n){return r=function(t){if("object"!==To(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==To(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===To(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function To(t){return(To="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Zr(t){var e,n,r=(n=(e=t).collections.map((function(t){return t.items.length})).reduce((function(t,e,n){var r=(t[n-1]||0)+e;return t.push(r),t}),[]).reduce((function(t,n){return n<=e.activeItemId?t+1:t}),0),e.collections[n]);if(!r)return null;var i=r.items[function(t){for(var e=t.state,n=t.collection,r=!1,i=0,o=0;!1===r;){var s=e.collections[i];if(s===n){r=!0;break}o+=s.items.length,i++}return e.activeItemId-o}({state:t,collection:r})],o=r.source;return{item:i,itemInputValue:o.getItemInputValue({item:i,state:t}),itemUrl:o.getItemUrl({item:i,state:t}),source:o}}function _n(t,e,n){return[t,null==n?void 0:n.sourceId,e].filter(Boolean).join("-").replace(/\s/g,"")}var o1=/((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;function Tf(t){return t.nativeEvent||t}function Bo(t){return(Bo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Bf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function s1(t,e,n){return r=function(t){if("object"!==Bo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Bo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Bo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Mo(t){return(Mo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Mf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function As(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Mf(Object(n),!0).forEach((function(e){a1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Mf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function a1(t,e,n){return r=function(t){if("object"!==Mo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Mo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Mo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Po(t){return(Po="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function dl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Pf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ci(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Pf(Object(n),!0).forEach((function(e){l1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Pf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l1(t,e,n){return r=function(t){if("object"!==Po(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Po(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Po(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function No(t){return(No="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Nf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ts(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Nf(Object(n),!0).forEach((function(e){fg(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Nf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function fg(t,e,n){return r=function(t){if("object"!==No(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==No(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===No(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Co(t){return(Co="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Cf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ui(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Cf(Object(n),!0).forEach((function(e){c1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Cf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function c1(t,e,n){return r=function(t){if("object"!==Co(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Co(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Co(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function dg(t){return function(t){if(Array.isArray(t))return hl(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return hl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?hl(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Bs(t){return!!t.execute}function u1(t){var e=t.reduce((function(t,e){if(!Bs(e))return t.push(e),t;var n=e.searchClient,r=e.execute,i=e.requesterId,o=e.requests,s=t.find((function(t){return Bs(e)&&Bs(t)&&t.searchClient===n&&!!i&&t.requesterId===i}));if(s){var a;(a=s.items).push.apply(a,dg(o))}else{var l={execute:r,requesterId:i,items:o,searchClient:n};t.push(l)}return t}),[]).map((function(t){if(!Bs(t))return Promise.resolve(t);var e=t,n=e.execute,r=e.items;return n({searchClient:e.searchClient,requests:r})}));return Promise.all(e).then((function(t){return Go(t)}))}function Fo(t){return(Fo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}var f1=["event","nextState","props","query","refresh","store"];function Ff(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function qr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ff(Object(n),!0).forEach((function(e){d1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ff(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function d1(t,e,n){return r=function(t){if("object"!==Fo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Fo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Fo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}var jf,ml,Ms,lo=null,Lf=(jf=-1,ml=-1,Ms=void 0,function(t){var e=++jf;return Promise.resolve(t).then((function(t){return Ms&&e<ml?Ms:(ml=e,Ms=t,t)}))});function Yr(t){var e=t.event,n=t.nextState,r=void 0===n?{}:n,i=t.props,o=t.query,s=t.refresh,a=t.store,l=function(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(t,f1);lo&&i.environment.clearTimeout(lo);var c=l.setCollections,u=l.setIsOpen,d=l.setQuery,h=l.setActiveItemId,f=l.setStatus,p=l.setContext;if(d(o),h(i.defaultActiveItemId),!o&&!1===i.openOnFocus){var m,b=a.getState().collections.map((function(t){return qr(qr({},t),{},{items:[]})}));f("idle"),c(b),u(null!==(m=r.isOpen)&&void 0!==m?m:i.shouldPanelOpen({state:a.getState()}));var y=kf(Lf(b).then((function(){return Promise.resolve()})));return a.pendingRequests.add(y)}f("loading"),lo=i.environment.setTimeout((function(){f("stalled")}),i.stallThreshold);var g=kf(Lf(i.getSources(qr({query:o,refresh:s,state:a.getState()},l)).then((function(t){return Promise.all(t.map((function(t){return Promise.resolve(t.getItems(qr({query:o,refresh:s,state:a.getState()},l))).then((function(e){return function(t,e,n){if(null!=(i=t)&&i.execute){var r="algolia"===t.requesterId?Object.assign.apply(Object,[{}].concat(dg(Object.keys(n.context).map((function(t){var e;return null===(e=n.context[t])||void 0===e?void 0:e.__algoliaSearchParameters}))))):{};return ui(ui({},t),{},{requests:t.queries.map((function(n){return{query:"algolia"===t.requesterId?ui(ui({},n),{},{params:ui(ui({},r),n.params)}):n,sourceId:e,transformResponse:t.transformResponse}}))})}var i;return{items:t,sourceId:e}}(e,t.sourceId,a.getState())}))}))).then(u1).then((function(e){var n,r,i,o=e.some((function(t){return e=t.items,!Array.isArray(e)&&!(null==e||!e._automaticInsights);var e}));return o&&p({algoliaInsightsPlugin:qr(qr({},(null===(n=a.getState().context)||void 0===n?void 0:n.algoliaInsightsPlugin)||{}),{},{__automaticInsights:o})}),r=e,i=a,t.map((function(t){var e,n=r.filter((function(e){return e.sourceId===t.sourceId})),o=n.map((function(t){return t.items})),s=n[0].transformResponse,a=s?s({results:e=o,hits:e.map((function(t){return t.hits})).filter(Boolean),facetHits:e.map((function(t){var e;return null===(e=t.facetHits)||void 0===e?void 0:e.map((function(t){return{label:t.value,count:t.count,_highlightResult:{label:{value:t.highlighted}}}}))})).filter(Boolean)}):o;return t.onResolve({source:t,results:o,items:a,state:i.getState()}),a.every(Boolean),'The `getItems` function from source "'.concat(t.sourceId,'" must return an array of items but returned ').concat(JSON.stringify(void 0),".\n\nDid you forget to return items?\n\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems"),{source:t,items:a}}))})).then((function(t){return n=(e={collections:t,props:i,state:a.getState()}).props,r=e.state,o=e.collections.reduce((function(t,e){return Ts(Ts({},t),{},fg({},e.source.sourceId,Ts(Ts({},e.source),{},{getItems:function(){return Go(e.items)}})))}),{}),s=n.plugins.reduce((function(t,e){return e.reshape?e.reshape(t):t}),{sourcesBySourceId:o,state:r}).sourcesBySourceId,Go(n.reshape({sourcesBySourceId:s,sources:Object.values(s),state:r})).filter(Boolean).map((function(t){return{source:t,items:t.getItems()}}));var e,n,r,o,s}))})))).then((function(t){var n;f("idle"),c(t);var d=i.shouldPanelOpen({state:a.getState()});u(null!==(n=r.isOpen)&&void 0!==n?n:i.openOnFocus&&!o&&d||d);var h=Zr(a.getState());if(null!==a.getState().activeItemId&&h){var p=h.item,m=h.itemInputValue,b=h.itemUrl,y=h.source;y.onActive(qr({event:e,item:p,itemInputValue:m,itemUrl:b,refresh:s,source:y,state:a.getState()},l))}})).finally((function(){f("idle"),lo&&i.environment.clearTimeout(lo)}));return a.pendingRequests.add(g)}function jo(t){return(jo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}var h1=["event","props","refresh","store"];function Rf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Kr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Rf(Object(n),!0).forEach((function(e){m1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Rf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function m1(t,e,n){return r=function(t){if("object"!==jo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==jo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===jo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Lo(t){return(Lo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}var p1=["props","refresh","store"],g1=["inputElement","formElement","panelElement"],_1=["inputElement"],b1=["inputElement","maxLength"],y1=["source"],v1=["item","source"];function Uf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function xe(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Uf(Object(n),!0).forEach((function(e){w1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Uf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function w1(t,e,n){return r=function(t){if("object"!==Lo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Lo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Lo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function fi(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function Ro(t){return(Ro="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Vf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function S1(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Vf(Object(n),!0).forEach((function(e){hg(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Vf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function hg(t,e,n){return r=function(t){if("object"!==Ro(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Ro(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Ro(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function I1(t){var e,n,r,i,o=t.plugins,s=t.options,a=null===(e=((null===(n=s.__autocomplete_metadata)||void 0===n?void 0:n.userAgents)||[])[0])||void 0===e?void 0:e.segment,l=a?hg({},a,Object.keys((null===(r=s.__autocomplete_metadata)||void 0===r?void 0:r.options)||{})):{};return{plugins:o.map((function(t){return{name:t.name,options:Object.keys(t.__autocomplete_pluginOptions||[])}})),options:S1({"autocomplete-core":Object.keys(s)},l),ua:J0.concat((null===(i=s.__autocomplete_metadata)||void 0===i?void 0:i.userAgents)||[])}}function xf(t){var e,n=t.state;return!1===n.isOpen||null===n.activeItemId?null:(null===(e=Zr(n))||void 0===e?void 0:e.itemInputValue)||null}function Uo(t){return(Uo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function zf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function qt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?zf(Object(n),!0).forEach((function(e){O1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):zf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function O1(t,e,n){return r=function(t){if("object"!==Uo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Uo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Uo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}var k1=function(t,e){switch(e.type){case"setActiveItemId":case"mousemove":return qt(qt({},t),{},{activeItemId:e.payload});case"setQuery":return qt(qt({},t),{},{query:e.payload,completion:null});case"setCollections":return qt(qt({},t),{},{collections:e.payload});case"setIsOpen":return qt(qt({},t),{},{isOpen:e.payload});case"setStatus":return qt(qt({},t),{},{status:e.payload});case"setContext":return qt(qt({},t),{},{context:qt(qt({},t.context),e.payload)});case"ArrowDown":var n=qt(qt({},t),{},{activeItemId:e.payload.hasOwnProperty("nextActiveItemId")?e.payload.nextActiveItemId:Df(1,t.activeItemId,Hl(t),e.props.defaultActiveItemId)});return qt(qt({},n),{},{completion:xf({state:n})});case"ArrowUp":var r=qt(qt({},t),{},{activeItemId:Df(-1,t.activeItemId,Hl(t),e.props.defaultActiveItemId)});return qt(qt({},r),{},{completion:xf({state:r})});case"Escape":return t.isOpen?qt(qt({},t),{},{activeItemId:null,isOpen:!1,completion:null}):qt(qt({},t),{},{activeItemId:null,query:"",status:"idle",collections:[]});case"submit":return qt(qt({},t),{},{activeItemId:null,isOpen:!1,status:"idle"});case"reset":return qt(qt({},t),{},{activeItemId:!0===e.props.openOnFocus?e.props.defaultActiveItemId:null,status:"idle",completion:null,query:""});case"focus":return qt(qt({},t),{},{activeItemId:e.props.defaultActiveItemId,isOpen:(e.props.openOnFocus||!!t.query)&&e.props.shouldPanelOpen({state:t})});case"blur":return e.props.debug?t:qt(qt({},t),{},{isOpen:!1,activeItemId:null});case"mouseleave":return qt(qt({},t),{},{activeItemId:e.props.defaultActiveItemId});default:return"The reducer action ".concat(JSON.stringify(e.type)," is not supported."),t}};function Vo(t){return(Vo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Wf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function $r(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Wf(Object(n),!0).forEach((function(e){D1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Wf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function D1(t,e,n){return r=function(t){if("object"!==Vo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Vo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Vo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function E1(t){var e,n,r,i,o,s=[],a=(e=t,n=s,i=typeof window<"u"?window:{},o=e.plugins||[],ci(ci({debug:!1,openOnFocus:!1,enterKeyHint:void 0,ignoreCompositionEvents:!1,placeholder:"",autoFocus:!1,defaultActiveItemId:null,stallThreshold:300,insights:void 0,environment:i,shouldPanelOpen:function(t){return Hl(t.state)>0},reshape:function(t){return t.sources}},e),{},{id:null!==(r=e.id)&&void 0!==r?r:"autocomplete-".concat($0++),plugins:o,initialState:ci({activeItemId:null,query:"",completion:null,collections:[],isOpen:!1,status:"idle",context:{}},e.initialState),onStateChange:function(t){var n;null===(n=e.onStateChange)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onStateChange)||void 0===n?void 0:n.call(e,t)}))},onSubmit:function(t){var n;null===(n=e.onSubmit)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onSubmit)||void 0===n?void 0:n.call(e,t)}))},onReset:function(t){var n;null===(n=e.onReset)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onReset)||void 0===n?void 0:n.call(e,t)}))},getSources:function(t){return Promise.all([].concat((r=o.map((function(t){return t.getSources})),function(t){if(Array.isArray(t))return dl(t)}(r)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(t,e){if(t){if("string"==typeof t)return dl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?dl(t,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[e.getSources]).filter(Boolean).map((function(e){return n=e,r=t,i=[],Promise.resolve(n(r)).then((function(t){return Promise.all(t.filter((function(t){return!!t})).map((function(t){if(t.sourceId,i.includes(t.sourceId))throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(t.sourceId)," is not unique."));i.push(t.sourceId);var e={getItemInputValue:function(t){return t.state.query},getItemUrl:function(){},onSelect:function(t){(0,t.setIsOpen)(!1)},onActive:Ks,onResolve:Ks};Object.keys(e).forEach((function(t){e[t].__default=!0}));var n=Af(Af({},e),t);return Promise.resolve(n)})))}));var n,r,i}))).then((function(t){return Go(t)})).then((function(t){return t.map((function(t){return ci(ci({},t),{},{onSelect:function(e){t.onSelect(e),n.forEach((function(t){var n;return null===(n=t.onSelect)||void 0===n?void 0:n.call(t,e)}))},onActive:function(e){t.onActive(e),n.forEach((function(t){var n;return null===(n=t.onActive)||void 0===n?void 0:n.call(t,e)}))},onResolve:function(e){t.onResolve(e),n.forEach((function(t){var n;return null===(n=t.onResolve)||void 0===n?void 0:n.call(t,e)}))}})}))}));var r},navigator:ci({navigate:function(t){var e=t.itemUrl;i.location.assign(e)},navigateNewTab:function(t){var e=t.itemUrl,n=i.open(e,"_blank","noopener");null==n||n.focus()},navigateNewWindow:function(t){var e=t.itemUrl;i.open(e,"_blank","noopener")}},e.navigator)})),l=function(t,e){var n,r=e.initialState;return{getState:function(){return r},dispatch:function(n,i){var o=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Bf(Object(n),!0).forEach((function(e){s1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Bf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},r);!function(t){var e,n,r=t.prevState,i=t.state;if(a.onStateChange($r({prevState:r,state:i,refresh:d,navigator:a.navigator},c)),!f()&&null!==(e=i.context)&&void 0!==e&&null!==(n=e.algoliaInsightsPlugin)&&void 0!==n&&n.__automaticInsights&&!1!==a.insights){var o=If({__autocomplete_clickAnalytics:!1});a.plugins.push(o),h([o])}}({state:r=t(r,{type:n,props:e,payload:i}),prevState:o})},pendingRequests:(n=[],{add:function(t){return n.push(t),t.finally((function(){n=n.filter((function(e){return e!==t}))}))},cancelAll:function(){n.forEach((function(t){return t.cancel()}))},isEmpty:function(){return 0===n.length}})}}(k1,a),c=function(t){var e=t.store;return{setActiveItemId:function(t){e.dispatch("setActiveItemId",t)},setQuery:function(t){e.dispatch("setQuery",t)},setCollections:function(t){var n=0,r=t.map((function(t){return As(As({},t),{},{items:Go(t.items).map((function(t){return As(As({},t),{},{__autocomplete_id:n++})}))})}));e.dispatch("setCollections",r)},setIsOpen:function(t){e.dispatch("setIsOpen",t)},setStatus:function(t){e.dispatch("setStatus",t)},setContext:function(t){e.dispatch("setContext",t)}}}({store:l}),u=function(t){var e=t.props,n=t.refresh,r=t.store,i=fi(t,p1);return{getEnvironmentProps:function(t){var n=t.inputElement,i=t.formElement,o=t.panelElement;function s(t){!r.getState().isOpen&&r.pendingRequests.isEmpty()||t.target===n||!1===[i,o].some((function(e){return(n=e)===(r=t.target)||n.contains(r);var n,r}))&&(r.dispatch("blur",null),e.debug||r.pendingRequests.cancelAll())}return xe({onTouchStart:s,onMouseDown:s,onTouchMove:function(t){!1!==r.getState().isOpen&&n===e.environment.document.activeElement&&t.target!==n&&n.blur()}},fi(t,g1))},getRootProps:function(t){return xe({role:"combobox","aria-expanded":r.getState().isOpen,"aria-haspopup":"listbox","aria-controls":r.getState().isOpen?r.getState().collections.map((function(t){var n=t.source;return _n(e.id,"list",n)})).join(" "):void 0,"aria-labelledby":_n(e.id,"label")},t)},getFormProps:function(t){return t.inputElement,xe({action:"",noValidate:!0,role:"search",onSubmit:function(o){var s;o.preventDefault(),e.onSubmit(xe({event:o,refresh:n,state:r.getState()},i)),r.dispatch("submit",null),null===(s=t.inputElement)||void 0===s||s.blur()},onReset:function(o){var s;o.preventDefault(),e.onReset(xe({event:o,refresh:n,state:r.getState()},i)),r.dispatch("reset",null),null===(s=t.inputElement)||void 0===s||s.focus()}},fi(t,_1))},getLabelProps:function(t){return xe({htmlFor:_n(e.id,"input"),id:_n(e.id,"label")},t)},getInputProps:function(t){var o;function s(t){(e.openOnFocus||r.getState().query)&&Yr(xe({event:t,props:e,query:r.getState().completion||r.getState().query,refresh:n,store:r},i)),r.dispatch("focus",null)}var a=t||{};a.inputElement;var l,c=a.maxLength,u=void 0===c?512:c,d=fi(a,b1),h=Zr(r.getState()),f=!(!(l=(null===(o=e.environment.navigator)||void 0===o?void 0:o.userAgent)||"")||!l.match(o1)),p=e.enterKeyHint||(null!=h&&h.itemUrl&&!f?"go":"search");return xe({"aria-autocomplete":"both","aria-activedescendant":r.getState().isOpen&&null!==r.getState().activeItemId?_n(e.id,"item-".concat(r.getState().activeItemId),null==h?void 0:h.source):void 0,"aria-controls":r.getState().isOpen?r.getState().collections.map((function(t){var n=t.source;return _n(e.id,"list",n)})).join(" "):void 0,"aria-labelledby":_n(e.id,"label"),value:r.getState().completion||r.getState().query,id:_n(e.id,"input"),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",enterKeyHint:p,spellCheck:"false",autoFocus:e.autoFocus,placeholder:e.placeholder,maxLength:u,type:"search",onChange:function(t){var o=t.currentTarget.value;e.ignoreCompositionEvents&&Tf(t).isComposing?i.setQuery(o):Yr(xe({event:t,props:e,query:o.slice(0,u),refresh:n,store:r},i))},onCompositionEnd:function(t){Yr(xe({event:t,props:e,query:t.currentTarget.value.slice(0,u),refresh:n,store:r},i))},onKeyDown:function(t){Tf(t).isComposing||function(t){var e=t.event,n=t.props,r=t.refresh,i=t.store,o=function(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(t,h1);if("ArrowUp"===e.key||"ArrowDown"===e.key){var s=function(){var t=Zr(i.getState()),e=n.environment.document.getElementById(_n(n.id,"item-".concat(i.getState().activeItemId),null==t?void 0:t.source));e&&(e.scrollIntoViewIfNeeded?e.scrollIntoViewIfNeeded(!1):e.scrollIntoView(!1))},a=function(){var t=Zr(i.getState());if(null!==i.getState().activeItemId&&t){var n=t.item,s=t.itemInputValue,a=t.itemUrl,l=t.source;l.onActive(Kr({event:e,item:n,itemInputValue:s,itemUrl:a,refresh:r,source:l,state:i.getState()},o))}};e.preventDefault(),!1===i.getState().isOpen&&(n.openOnFocus||i.getState().query)?Yr(Kr({event:e,props:n,query:i.getState().query,refresh:r,store:i},o)).then((function(){i.dispatch(e.key,{nextActiveItemId:n.defaultActiveItemId}),a(),setTimeout(s,0)})):(i.dispatch(e.key,{}),a(),s())}else if("Escape"===e.key)e.preventDefault(),i.dispatch(e.key,null),i.pendingRequests.cancelAll();else if("Tab"===e.key)i.dispatch("blur",null),i.pendingRequests.cancelAll();else if("Enter"===e.key){if(null===i.getState().activeItemId||i.getState().collections.every((function(t){return 0===t.items.length})))return void(n.debug||i.pendingRequests.cancelAll());e.preventDefault();var l=Zr(i.getState()),c=l.item,u=l.itemInputValue,d=l.itemUrl,h=l.source;if(e.metaKey||e.ctrlKey)void 0!==d&&(h.onSelect(Kr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o)),n.navigator.navigateNewTab({itemUrl:d,item:c,state:i.getState()}));else if(e.shiftKey)void 0!==d&&(h.onSelect(Kr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o)),n.navigator.navigateNewWindow({itemUrl:d,item:c,state:i.getState()}));else if(!e.altKey){if(void 0!==d)return h.onSelect(Kr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o)),void n.navigator.navigate({itemUrl:d,item:c,state:i.getState()});Yr(Kr({event:e,nextState:{isOpen:!1},props:n,query:u,refresh:r,store:i},o)).then((function(){h.onSelect(Kr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o))}))}}}(xe({event:t,props:e,refresh:n,store:r},i))},onFocus:s,onBlur:Ks,onClick:function(n){t.inputElement!==e.environment.document.activeElement||r.getState().isOpen||s(n)}},d)},getPanelProps:function(t){return xe({onMouseDown:function(t){t.preventDefault()},onMouseLeave:function(){r.dispatch("mouseleave",null)}},t)},getListProps:function(t){var n=t||{},r=n.source,i=fi(n,y1);return xe({role:"listbox","aria-labelledby":_n(e.id,"label"),id:_n(e.id,"list",r)},i)},getItemProps:function(t){var o=t.item,s=t.source,a=fi(t,v1);return xe({id:_n(e.id,"item-".concat(o.__autocomplete_id),s),role:"option","aria-selected":r.getState().activeItemId===o.__autocomplete_id,onMouseMove:function(t){if(o.__autocomplete_id!==r.getState().activeItemId){r.dispatch("mousemove",o.__autocomplete_id);var e=Zr(r.getState());if(null!==r.getState().activeItemId&&e){var s=e.item,a=e.itemInputValue,l=e.itemUrl,c=e.source;c.onActive(xe({event:t,item:s,itemInputValue:a,itemUrl:l,refresh:n,source:c,state:r.getState()},i))}}},onMouseDown:function(t){t.preventDefault()},onClick:function(t){var a=s.getItemInputValue({item:o,state:r.getState()}),l=s.getItemUrl({item:o,state:r.getState()});(l?Promise.resolve():Yr(xe({event:t,nextState:{isOpen:!1},props:e,query:a,refresh:n,store:r},i))).then((function(){s.onSelect(xe({event:t,item:o,itemInputValue:a,itemUrl:l,refresh:n,source:s,state:r.getState()},i))}))}},a)}}}($r({props:a,refresh:d,store:l,navigator:a.navigator},c));function d(){return Yr($r({event:new Event("input"),nextState:{isOpen:l.getState().isOpen},props:a,navigator:a.navigator,query:l.getState().query,refresh:d,store:l},c))}function h(t){t.forEach((function(t){var e;return null===(e=t.subscribe)||void 0===e?void 0:e.call(t,$r($r({},c),{},{navigator:a.navigator,refresh:d,onSelect:function(t){s.push({onSelect:t})},onActive:function(t){s.push({onActive:t})},onResolve:function(t){s.push({onResolve:t})}}))}))}function f(){return a.plugins.some((function(t){return"aa.algoliaInsightsPlugin"===t.name}))}if(a.insights&&!f()){var p="boolean"==typeof a.insights?{}:a.insights;a.plugins.push(If(p))}return h(a.plugins),function(t){var e,n,r=t.metadata,i=t.environment;if(null!==(e=i.navigator)&&void 0!==e&&null!==(n=e.userAgent)&&void 0!==n&&n.includes("Algolia Crawler")){var o=i.document.createElement("meta"),s=i.document.querySelector("head");o.name="algolia:metadata",setTimeout((function(){o.content=JSON.stringify(r),s.appendChild(o)}),0)}}({metadata:I1({plugins:a.plugins,options:t}),environment:a.environment}),$r($r({refresh:d,navigator:a.navigator},u),c)}function A1(t){var e=t.translations,n=(void 0===e?{}:e).searchByText,r=void 0===n?"Search by":n;return k.createElement("a",{href:"https://www.algolia.com/ref/docsearch/?utm_source=".concat(window.location.hostname,"&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"),target:"_blank",rel:"noopener noreferrer"},k.createElement("span",{className:"DocSearch-Label"},r),k.createElement("svg",{width:"77",height:"19","aria-label":"Algolia",role:"img",id:"Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 2196.2 500"},k.createElement("defs",null,k.createElement("style",null,".cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}")),k.createElement("path",{className:"cls-2",d:"M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("rect",{className:"cls-1",x:"1845.88",y:"104.73",width:"62.58",height:"277.9",rx:"5.9",ry:"5.9"}),k.createElement("path",{className:"cls-2",d:"M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z"}),k.createElement("path",{className:"cls-2",d:"M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("path",{className:"cls-2",d:"M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z"}),k.createElement("path",{className:"cls-2",d:"M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z"}),k.createElement("path",{className:"cls-1",d:"M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z"})))}function Ps(t){return k.createElement("svg",{width:"15",height:"15","aria-label":t.ariaLabel,role:"img"},k.createElement("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.2"},t.children))}function T1(t){var e=t.translations,n=void 0===e?{}:e,r=n.selectText,i=void 0===r?"to select":r,o=n.selectKeyAriaLabel,s=void 0===o?"Enter key":o,a=n.navigateText,l=void 0===a?"to navigate":a,c=n.navigateUpKeyAriaLabel,u=void 0===c?"Arrow up":c,d=n.navigateDownKeyAriaLabel,h=void 0===d?"Arrow down":d,f=n.closeText,p=void 0===f?"to close":f,m=n.closeKeyAriaLabel,b=void 0===m?"Escape key":m,y=n.searchByText,g=void 0===y?"Search by":y;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Logo"},k.createElement(A1,{translations:{searchByText:g}})),k.createElement("ul",{className:"DocSearch-Commands"},k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:s},k.createElement("path",{d:"M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"}))),k.createElement("span",{className:"DocSearch-Label"},i)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:h},k.createElement("path",{d:"M7.5 3.5v8M10.5 8.5l-3 3-3-3"}))),k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:u},k.createElement("path",{d:"M7.5 11.5v-8M10.5 6.5l-3-3-3 3"}))),k.createElement("span",{className:"DocSearch-Label"},l)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Ps,{ariaLabel:b},k.createElement("path",{d:"M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"}))),k.createElement("span",{className:"DocSearch-Label"},p))))}function B1(t){var e=t.hit,n=t.children;return k.createElement("a",{href:e.url},n)}function M1(){return k.createElement("svg",{viewBox:"0 0 38 38",stroke:"currentColor",strokeOpacity:".5"},k.createElement("g",{fill:"none",fillRule:"evenodd"},k.createElement("g",{transform:"translate(1 1)",strokeWidth:"2"},k.createElement("circle",{strokeOpacity:".3",cx:"18",cy:"18",r:"18"}),k.createElement("path",{d:"M36 18c0-9.94-8.06-18-18-18"},k.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})))))}function P1(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0"}),k.createElement("path",{d:"M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13"})))}function ql(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function N1(){return k.createElement("svg",{className:"DocSearch-Hit-Select-Icon",width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M18 3v4c0 2-2 4-4 4H2"}),k.createElement("path",{d:"M8 17l-6-6 6-6"})))}var C1=function(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))};function F1(t){switch(t.type){case"lvl1":return k.createElement(C1,null);case"content":return k.createElement(L1,null);default:return k.createElement(j1,null)}}function j1(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function L1(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 5H3h14zm0 5H3h14zm0 5H3h14z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function Hf(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function R1(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0"}))}function U1(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2"}))}function V1(t){var e=t.translations,n=void 0===e?{}:e,r=n.titleText,i=void 0===r?"Unable to fetch results":r,o=n.helpText,s=void 0===o?"You might want to check your network connection.":o;return k.createElement("div",{className:"DocSearch-ErrorScreen"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(R1,null)),k.createElement("p",{className:"DocSearch-Title"},i),k.createElement("p",{className:"DocSearch-Help"},s))}var x1=["translations"];function z1(t){var e=t.translations,n=void 0===e?{}:e,r=tr(t,x1),i=n.noResultsText,o=void 0===i?"No results for":i,s=n.suggestedQueryText,a=void 0===s?"Try searching for":s,l=n.reportMissingResultsText,c=void 0===l?"Believe this query should return results?":l,u=n.reportMissingResultsLinkText,d=void 0===u?"Let us know.":u,h=r.state.context.searchSuggestions;return k.createElement("div",{className:"DocSearch-NoResults"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(U1,null)),k.createElement("p",{className:"DocSearch-Title"},o,' "',k.createElement("strong",null,r.state.query),'"'),h&&h.length>0&&k.createElement("div",{className:"DocSearch-NoResults-Prefill-List"},k.createElement("p",{className:"DocSearch-Help"},a,":"),k.createElement("ul",null,h.slice(0,3).reduce((function(t,e){return[].concat(Do(t),[k.createElement("li",{key:e},k.createElement("button",{className:"DocSearch-Prefill",key:e,type:"button",onClick:function(){r.setQuery(e.toLowerCase()+" "),r.refresh(),r.inputRef.current.focus()}},e))])}),[]))),r.getMissingResultsUrl&&k.createElement("p",{className:"DocSearch-Help"},"".concat(c," "),k.createElement("a",{href:r.getMissingResultsUrl({query:r.state.query}),target:"_blank",rel:"noopener noreferrer"},d)))}var W1=["hit","attribute","tagName"];function qf(t,e){return e.split(".").reduce((function(t,e){return null!=t&&t[e]?t[e]:null}),t)}function di(t){var e=t.hit,n=t.attribute,r=t.tagName;return Xn(void 0===r?"span":r,Ut(Ut({},tr(t,W1)),{},{dangerouslySetInnerHTML:{__html:qf(e,"_snippetResult.".concat(n,".value"))||qf(e,n)}}))}function Yl(t){return t.collection&&0!==t.collection.items.length?k.createElement("section",{className:"DocSearch-Hits"},k.createElement("div",{className:"DocSearch-Hit-source"},t.title),k.createElement("ul",t.getListProps(),t.collection.items.map((function(e,n){return k.createElement(H1,Ke({key:[t.title,e.objectID].join(":"),item:e,index:n},t))})))):null}function H1(t){var e=t.item,n=t.index,r=t.renderIcon,i=t.renderAction,o=t.getItemProps,s=t.onItemClick,a=t.collection,l=t.hitComponent,c=an(k.useState(!1),2),u=c[0],d=c[1],h=an(k.useState(!1),2),f=h[0],p=h[1],m=k.useRef(null),b=l;return k.createElement("li",Ke({className:["DocSearch-Hit",e.__docsearch_parent&&"DocSearch-Hit--Child",u&&"DocSearch-Hit--deleting",f&&"DocSearch-Hit--favoriting"].filter(Boolean).join(" "),onTransitionEnd:function(){m.current&&m.current()}},o({item:e,source:a.source,onClick:function(t){s(e,t)}})),k.createElement(b,{hit:e},k.createElement("div",{className:"DocSearch-Hit-Container"},r({item:e,index:n}),e.hierarchy[e.type]&&"lvl1"===e.type&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(di,{className:"DocSearch-Hit-title",hit:e,attribute:"hierarchy.lvl1"}),e.content&&k.createElement(di,{className:"DocSearch-Hit-path",hit:e,attribute:"content"})),e.hierarchy[e.type]&&("lvl2"===e.type||"lvl3"===e.type||"lvl4"===e.type||"lvl5"===e.type||"lvl6"===e.type)&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(di,{className:"DocSearch-Hit-title",hit:e,attribute:"hierarchy.".concat(e.type)}),k.createElement(di,{className:"DocSearch-Hit-path",hit:e,attribute:"hierarchy.lvl1"})),"content"===e.type&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(di,{className:"DocSearch-Hit-title",hit:e,attribute:"content"}),k.createElement(di,{className:"DocSearch-Hit-path",hit:e,attribute:"hierarchy.lvl1"})),i({item:e,runDeleteTransition:function(t){d(!0),m.current=t},runFavoriteTransition:function(t){p(!0),m.current=t}}))))}function Yf(t,e,n){return t.reduce((function(t,r){var i=e(r);return t.hasOwnProperty(i)||(t[i]=[]),t[i].length<(n||5)&&t[i].push(r),t}),{})}function Kf(t){return t}function Ns(t){return 1===t.button||t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}function q1(){}var mg=/(<mark>|<\/mark>)/g,Y1=RegExp(mg.source);function pg(t){var e,n,r=t;if(!r.__docsearch_parent&&!t._highlightResult)return t.hierarchy.lvl0;var i=r.__docsearch_parent?null===(e=r.__docsearch_parent)||void 0===e||null===(e=e._highlightResult)||void 0===e||null===(e=e.hierarchy)||void 0===e?void 0:e.lvl0:null===(n=t._highlightResult)||void 0===n||null===(n=n.hierarchy)||void 0===n?void 0:n.lvl0;return i?i.value&&Y1.test(i.value)?i.value.replace(mg,""):i.value:t.hierarchy.lvl0}function K1(t){return k.createElement("div",{className:"DocSearch-Dropdown-Container"},t.state.collections.map((function(e){if(0===e.items.length)return null;var n=pg(e.items[0]);return k.createElement(Yl,Ke({},t,{key:e.source.sourceId,title:n,collection:e,renderIcon:function(t){var n,r=t.item,i=t.index;return k.createElement(k.Fragment,null,r.__docsearch_parent&&k.createElement("svg",{className:"DocSearch-Hit-Tree",viewBox:"0 0 24 54"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},r.__docsearch_parent!==(null===(n=e.items[i+1])||void 0===n?void 0:n.__docsearch_parent)?k.createElement("path",{d:"M8 6v21M20 27H8.3"}):k.createElement("path",{d:"M8 6v42M20 27H8.3"}))),k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(F1,{type:r.type})))},renderAction:function(){return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement(N1,null))}}))})),t.resultsFooterComponent&&k.createElement("section",{className:"DocSearch-HitsFooter"},k.createElement(t.resultsFooterComponent,{state:t.state})))}var $1=["translations"];function J1(t){var e=t.translations,n=void 0===e?{}:e,r=tr(t,$1),i=n.recentSearchesTitle,o=void 0===i?"Recent":i,s=n.noRecentSearchesText,a=void 0===s?"No recent searches":s,l=n.saveRecentSearchButtonTitle,c=void 0===l?"Save this search":l,u=n.removeRecentSearchButtonTitle,d=void 0===u?"Remove this search from history":u,h=n.favoriteSearchesTitle,f=void 0===h?"Favorite":h,p=n.removeFavoriteSearchButtonTitle,m=void 0===p?"Remove this search from favorites":p;return"idle"===r.state.status&&!1===r.hasCollections?r.disableUserPersonalization?null:k.createElement("div",{className:"DocSearch-StartScreen"},k.createElement("p",{className:"DocSearch-Help"},a)):!1===r.hasCollections?null:k.createElement("div",{className:"DocSearch-Dropdown-Container"},k.createElement(Yl,Ke({},r,{title:o,collection:r.state.collections[0],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(P1,null))},renderAction:function(t){var e=t.item,n=t.runFavoriteTransition,i=t.runDeleteTransition;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:c,type:"submit",onClick:function(t){t.preventDefault(),t.stopPropagation(),n((function(){r.favoriteSearches.add(e),r.recentSearches.remove(e),r.refresh()}))}},k.createElement(Hf,null))),k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:d,type:"submit",onClick:function(t){t.preventDefault(),t.stopPropagation(),i((function(){r.recentSearches.remove(e),r.refresh()}))}},k.createElement(ql,null))))}})),k.createElement(Yl,Ke({},r,{title:f,collection:r.state.collections[1],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(Hf,null))},renderAction:function(t){var e=t.item,n=t.runDeleteTransition;return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:m,type:"submit",onClick:function(t){t.preventDefault(),t.stopPropagation(),n((function(){r.favoriteSearches.remove(e),r.refresh()}))}},k.createElement(ql,null)))}})))}var Q1=["translations"],G1=k.memo((function(t){var e=t.translations,n=void 0===e?{}:e,r=tr(t,Q1);if("error"===r.state.status)return k.createElement(V1,{translations:null==n?void 0:n.errorScreen});var i=r.state.collections.some((function(t){return t.items.length>0}));return r.state.query?!1===i?k.createElement(z1,Ke({},r,{translations:null==n?void 0:n.noResultsScreen})):k.createElement(K1,r):k.createElement(J1,Ke({},r,{hasCollections:i,translations:null==n?void 0:n.startScreen}))}),(function(t,e){return"loading"===e.state.status||"stalled"===e.state.status})),X1=["translations"];function Z1(t){var e=t.translations,n=void 0===e?{}:e,r=tr(t,X1),i=n.resetButtonTitle,o=void 0===i?"Clear the query":i,s=n.resetButtonAriaLabel,a=void 0===s?"Clear the query":s,l=n.cancelButtonText,c=void 0===l?"Cancel":l,u=n.cancelButtonAriaLabel,d=void 0===u?"Cancel":u,h=n.searchInputLabel,f=void 0===h?"Search":h,p=r.getFormProps({inputElement:r.inputRef.current}).onReset;return k.useEffect((function(){r.autoFocus&&r.inputRef.current&&r.inputRef.current.focus()}),[r.autoFocus,r.inputRef]),k.useEffect((function(){r.isFromSelection&&r.inputRef.current&&r.inputRef.current.select()}),[r.isFromSelection,r.inputRef]),k.createElement(k.Fragment,null,k.createElement("form",{className:"DocSearch-Form",onSubmit:function(t){t.preventDefault()},onReset:p},k.createElement("label",Ke({className:"DocSearch-MagnifierLabel"},r.getLabelProps()),k.createElement(sg,null),k.createElement("span",{className:"DocSearch-VisuallyHiddenForAccessibility"},f)),k.createElement("div",{className:"DocSearch-LoadingIndicator"},k.createElement(M1,null)),k.createElement("input",Ke({className:"DocSearch-Input",ref:r.inputRef},r.getInputProps({inputElement:r.inputRef.current,autoFocus:r.autoFocus,maxLength:64}))),k.createElement("button",{type:"reset",title:o,className:"DocSearch-Reset","aria-label":a,hidden:!r.state.query},k.createElement(ql,null))),k.createElement("button",{className:"DocSearch-Cancel",type:"reset","aria-label":d,onClick:r.onClose},c))}var tw=["_highlightResult","_snippetResult"];function $f(t){var e,n=t.key,r=t.limit,i=void 0===r?5:r,o=(e=n,!1===function(){var t="__TEST_KEY__";try{return localStorage.setItem(t,""),localStorage.removeItem(t),!0}catch{return!1}}()?{setItem:function(){},getItem:function(){return[]}}:{setItem:function(t){return window.localStorage.setItem(e,JSON.stringify(t))},getItem:function(){var t=window.localStorage.getItem(e);return t?JSON.parse(t):[]}}),s=o.getItem().slice(0,i);return{add:function(t){var e=t;e._highlightResult,e._snippetResult;var n=tr(e,tw),r=s.findIndex((function(t){return t.objectID===n.objectID}));r>-1&&s.splice(r,1),s.unshift(n),s=s.slice(0,i),o.setItem(s)},remove:function(t){s=s.filter((function(e){return e.objectID!==t.objectID})),o.setItem(s)},getAll:function(){return s}}}function ew(t){var e,n="algolia-client-js-".concat(t.key);function r(){return void 0===e&&(e=t.localStorage||window.localStorage),e}function i(){return JSON.parse(r().getItem(n)||"{}")}function o(t){r().setItem(n,JSON.stringify(t))}return{get:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return Promise.resolve().then((function(){var n,r,s;return n=t.timeToLive?1e3*t.timeToLive:null,r=i(),o(s=Object.fromEntries(Object.entries(r).filter((function(t){return void 0!==an(t,2)[1].timestamp})))),n&&o(Object.fromEntries(Object.entries(s).filter((function(t){var e=an(t,2)[1],r=(new Date).getTime();return!(e.timestamp+n<r)})))),i()[JSON.stringify(e)]})).then((function(t){return Promise.all([t?t.value:n(),void 0!==t])})).then((function(t){var e=an(t,2),n=e[0],i=e[1];return Promise.all([n,i||r.miss(n)])})).then((function(t){return an(t,1)[0]}))},set:function(t,e){return Promise.resolve().then((function(){var o=i();return o[JSON.stringify(t)]={timestamp:(new Date).getTime(),value:e},r().setItem(n,JSON.stringify(o)),e}))},delete:function(t){return Promise.resolve().then((function(){var e=i();delete e[JSON.stringify(t)],r().setItem(n,JSON.stringify(e))}))},clear:function(){return Promise.resolve().then((function(){r().removeItem(n)}))}}}function So(t){var e=Do(t.caches),n=e.shift();return void 0===n?{get:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return e().then((function(t){return Promise.all([t,n.miss(t)])})).then((function(t){return an(t,1)[0]}))},set:function(t,e){return Promise.resolve(e)},delete:function(t){return Promise.resolve()},clear:function(){return Promise.resolve()}}:{get:function(t,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return n.get(t,r,i).catch((function(){return So({caches:e}).get(t,r,i)}))},set:function(t,r){return n.set(t,r).catch((function(){return So({caches:e}).set(t,r)}))},delete:function(t){return n.delete(t).catch((function(){return So({caches:e}).delete(t)}))},clear:function(){return n.clear().catch((function(){return So({caches:e}).clear()}))}}}function pl(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{serializable:!0},e={};return{get:function(n,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}},o=JSON.stringify(n);if(o in e)return Promise.resolve(t.serializable?JSON.parse(e[o]):e[o]);var s=r();return s.then((function(t){return i.miss(t)})).then((function(){return s}))},set:function(n,r){return e[JSON.stringify(n)]=t.serializable?JSON.stringify(r):r,Promise.resolve(r)},delete:function(t){return delete e[JSON.stringify(t)],Promise.resolve()},clear:function(){return e={},Promise.resolve()}}}function nw(t){var e,n,r=t.algoliaAgents,i=t.client,o=t.version,s=(e=o,n={value:"Algolia for JavaScript (".concat(e,")"),add:function(t){var e="; ".concat(t.segment).concat(void 0!==t.version?" (".concat(t.version,")"):"");return-1===n.value.indexOf(e)&&(n.value="".concat(n.value).concat(e)),n}},n).add({segment:i,version:o});return r.forEach((function(t){return s.add(t)})),s}var Jf=12e4;function Qf(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"up",n=Date.now();return Ut(Ut({},t),{},{status:e,lastUpdate:n,isUp:function(){return"up"===e||Date.now()-n>Jf},isTimedOut:function(){return"timed out"===e&&Date.now()-n<=Jf}})}var gg=function(){function t(e,n){var r;return Ji(this,t),Gi(r=$i(this,t,[e]),"name","AlgoliaError"),n&&(r.name=n),r}return Xi(t,Wl(Error)),Qi(t)}(),_g=function(){function t(e,n,r){var i;return Ji(this,t),Gi(i=$i(this,t,[e,r]),"stackTrace",void 0),i.stackTrace=n,i}return Xi(t,gg),Qi(t)}(),rw=function(){function t(e){return Ji(this,t),$i(this,t,["Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",e,"RetryError"])}return Xi(t,_g),Qi(t)}(),Kl=function(){function t(e,n,r){var i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ApiError";return Ji(this,t),Gi(i=$i(this,t,[e,r,o]),"status",void 0),i.status=n,i}return Xi(t,_g),Qi(t)}(),iw=function(){function t(e,n){var r;return Ji(this,t),Gi(r=$i(this,t,[e,"DeserializationError"]),"response",void 0),r.response=n,r}return Xi(t,gg),Qi(t)}(),ow=function(){function t(e,n,r,i){var o;return Ji(this,t),Gi(o=$i(this,t,[e,n,i,"DetailedApiError"]),"error",void 0),o.error=r,o}return Xi(t,Kl),Qi(t)}();function sw(t,e,n){var r,i=(r=n,Object.keys(r).filter((function(t){return void 0!==r[t]})).sort().map((function(t){return"".concat(t,"=").concat(encodeURIComponent("[object Array]"===Object.prototype.toString.call(r[t])?r[t].join(","):r[t]).replace(/\+/g,"%20"))})).join("&")),o="".concat(t.protocol,"://").concat(t.url).concat(t.port?":".concat(t.port):"","/").concat("/"===e.charAt(0)?e.substring(1):e);return i.length&&(o+="?".concat(i)),o}function aw(t,e){if("GET"!==t.method&&(void 0!==t.data||void 0!==e.data)){var n=Array.isArray(t.data)?t.data:Ut(Ut({},t.data),e.data);return JSON.stringify(n)}}function lw(t,e,n){var r=Ut(Ut(Ut({Accept:"application/json"},t),e),n),i={};return Object.keys(r).forEach((function(t){var e=r[t];i[t.toLowerCase()]=e})),i}function cw(t){try{return JSON.parse(t.content)}catch(e){throw new iw(e.message,t)}}function uw(t,e){var n=t.content,r=t.status;try{var i=JSON.parse(n);return"error"in i?new ow(i.message,r,i.error,e):new Kl(i.message,r,e)}catch{}return new Kl(n,r,e)}function fw(t){return t.map((function(t){return bg(t)}))}function bg(t){var e=t.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return Ut(Ut({},t),{},{request:Ut(Ut({},t.request),{},{headers:Ut(Ut({},t.request.headers),e)})})}var dw=["appId","apiKey","authMode","algoliaAgents"],hw=["params"],Gf="5.19.0";function mw(t){return[{url:"".concat(t,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(t,".algolia.net"),accept:"write",protocol:"https"}].concat(function(t){for(var e=t,n=t.length-1;n>0;n--){var r=Math.floor(Math.random()*(n+1)),i=t[n];e[n]=t[r],e[r]=i}return e}([{url:"".concat(t,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}]))}var $l="3.8.3",pw=["footer","searchBox"];function gw(t){var e,n,r,i=t.appId,o=t.apiKey,s=t.indexName,a=t.placeholder,l=void 0===a?"Search docs":a,c=t.searchParameters,u=t.maxResultsPerGroup,d=t.onClose,h=void 0===d?q1:d,f=t.transformItems,p=void 0===f?Kf:f,m=t.hitComponent,b=void 0===m?B1:m,y=t.resultsFooterComponent,g=void 0===y?function(){return null}:y,v=t.navigator,_=t.initialScrollY,w=void 0===_?0:_,S=t.transformSearchClient,O=void 0===S?Kf:S,I=t.disableUserPersonalization,$=void 0!==I&&I,x=t.initialQuery,D=void 0===x?"":x,E=t.translations,A=void 0===E?{}:E,T=t.getMissingResultsUrl,N=t.insights,C=void 0!==N&&N,M=A.footer,B=A.searchBox,j=tr(A,pw),P=an(k.useState({query:"",collections:[],completion:null,context:{},isOpen:!1,activeItemId:null,status:"idle"}),2),L=P[0],R=P[1],F=k.useRef(null),U=k.useRef(null),z=k.useRef(null),V=k.useRef(null),q=k.useRef(null),W=k.useRef(10),Y=k.useRef(typeof window<"u"?window.getSelection().toString().slice(0,64):"").current,H=k.useRef(D||Y).current,K=(e=i,n=o,r=O,k.useMemo((function(){var t=function(t,e){if(!t||"string"!=typeof t)throw new Error("`appId` is missing.");if(!e||"string"!=typeof e)throw new Error("`apiKey` is missing.");return n=Ut({appId:t,apiKey:e,timeouts:{connect:1e3,read:2e3,write:3e4},logger:{debug:function(t,e){return Promise.resolve()},info:function(t,e){return Promise.resolve()},error:function(t,e){return Promise.resolve()}},requester:{send:function(t){return new Promise((function(e){var n=new XMLHttpRequest;n.open(t.method,t.url,!0),Object.keys(t.headers).forEach((function(e){return n.setRequestHeader(e,t.headers[e])}));var r,i=function(t,r){return setTimeout((function(){n.abort(),e({status:0,content:r,isTimedOut:!0})}),t)},o=i(t.connectTimeout,"Connection timeout");n.onreadystatechange=function(){n.readyState>n.OPENED&&void 0===r&&(clearTimeout(o),r=i(t.responseTimeout,"Socket timeout"))},n.onerror=function(){0===n.status&&(clearTimeout(o),clearTimeout(r),e({content:n.responseText||"Network request failed",status:n.status,isTimedOut:!1}))},n.onload=function(){clearTimeout(o),clearTimeout(r),e({content:n.responseText,status:n.status,isTimedOut:!1})},n.send(t.data)}))}},algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:pl(),requestsCache:pl({serializable:!1}),hostsCache:So({caches:[ew({key:"".concat(Gf,"-").concat(t)}),pl()]})},void 0),r=n.appId,i=n.apiKey,o=n.authMode,s=n.algoliaAgents,a=tr(n,dw),l=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"WithinHeaders",r={"x-algolia-api-key":e,"x-algolia-application-id":t};return{headers:function(){return"WithinHeaders"===n?r:{}},queryParameters:function(){return"WithinQueryParameters"===n?r:{}}}}(r,i,o),c=function(t){var e=t.hosts,n=t.hostsCache,r=t.baseHeaders,i=t.logger,o=t.baseQueryParameters,s=t.algoliaAgent,a=t.timeouts,l=t.requester,c=t.requestsCache,u=t.responsesCache;function d(t){return h.apply(this,arguments)}function h(){return(h=ll(Qr().mark((function t(e){var r,i,o,s,a;return Qr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Promise.all(e.map((function(t){return n.get(t,(function(){return Promise.resolve(Qf(t))}))})));case 2:return r=t.sent,i=r.filter((function(t){return t.isUp()})),o=r.filter((function(t){return t.isTimedOut()})),s=[].concat(Do(i),Do(o)),a=s.length>0?s:e,t.abrupt("return",{hosts:a,getTimeout:function(t,e){return(0===o.length&&0===t?1:o.length+3+t)*e}});case 8:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function f(t,e){return p.apply(this,arguments)}function p(){return p=ll(Qr().mark((function t(c,u){var h,f,p,m,b,y,g,v,_,w,S,O,I,$=arguments;return Qr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(h=!($.length>2&&void 0!==$[2])||$[2],f=[],p=aw(c,u),m=lw(r,c.headers,u.headers),b="GET"===c.method?Ut(Ut({},c.data),u.data):{},y=Ut(Ut(Ut({},o),c.queryParameters),b),s.value&&(y["x-algolia-agent"]=s.value),u&&u.queryParameters)for(g=0,v=Object.keys(u.queryParameters);g<v.length;g++)_=v[g],u.queryParameters[_]&&"[object Object]"!==Object.prototype.toString.call(u.queryParameters[_])?y[_]=u.queryParameters[_].toString():y[_]=u.queryParameters[_];return w=0,S=function(){var t=ll(Qr().mark((function t(e,r){var o,s,d,b,g,v;return Qr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0!==(o=e.pop())){t.next=3;break}throw new rw(fw(f));case 3:return s=Ut(Ut({},a),u.timeouts),d={data:p,headers:m,method:c.method,url:sw(o,c.path,y),connectTimeout:r(w,s.connect),responseTimeout:r(w,h?s.read:s.write)},b=function(t){var n={request:d,response:t,host:o,triesLeft:e.length};return f.push(n),n},t.next=8,l.send(d);case 8:if(I=(O=g=t.sent).isTimedOut,$=O.status,!I&&((_={isTimedOut:I,status:$}).isTimedOut||~~_.status)&&(2==~~($/100)||4==~~($/100))){t.next=16;break}return v=b(g),g.isTimedOut&&w++,i.info("Retryable failure",bg(v)),t.next=15,n.set(o,Qf(o,g.isTimedOut?"timed out":"down"));case 15:return t.abrupt("return",S(e,r));case 16:if(2!=~~(g.status/100)){t.next=18;break}return t.abrupt("return",cw(g));case 18:throw b(g),uw(g,f);case 20:case"end":return t.stop()}var _,O,I,$}),t)})));return function(e,n){return t.apply(this,arguments)}}(),O=e.filter((function(t){return"readWrite"===t.accept||(h?"read"===t.accept:"write"===t.accept)})),t.next=13,d(O);case 13:return I=t.sent,t.abrupt("return",S(Do(I.hosts).reverse(),I.getTimeout));case 15:case"end":return t.stop()}}),t)}))),p.apply(this,arguments)}return{hostsCache:n,requester:l,timeouts:a,logger:i,algoliaAgent:s,baseHeaders:r,baseQueryParameters:o,hosts:e,request:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.useReadTransporter||"GET"===t.method;if(!n)return f(t,e,n);var i=function(){return f(t,e)};if(!0!==(e.cacheable||t.cacheable))return i();var s={request:t,requestOptions:e,transporter:{queryParameters:o,headers:r}};return u.get(s,(function(){return c.get(s,(function(){return c.set(s,i()).then((function(t){return Promise.all([c.delete(s),t])}),(function(t){return Promise.all([c.delete(s),Promise.reject(t)])})).then((function(t){var e=an(t,2);return e[0],e[1]}))}))}),{miss:function(t){return u.set(s,t)}})},requestsCache:c,responsesCache:u}}(Ut(Ut({hosts:mw(r)},a),{},{algoliaAgent:nw({algoliaAgents:s,client:"Lite",version:Gf}),baseHeaders:Ut(Ut({"content-type":"text/plain"},l.headers()),a.baseHeaders),baseQueryParameters:Ut(Ut({},l.queryParameters()),a.baseQueryParameters)})),{transporter:c,appId:r,apiKey:i,clearCache:function(){return Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then((function(){}))},get _ua(){return c.algoliaAgent.value},addAlgoliaAgent:function(t,e){c.algoliaAgent.add({segment:t,version:e})},setClientApiKey:function(t){var e=t.apiKey;o&&"WithinHeaders"!==o?c.baseQueryParameters["x-algolia-api-key"]=e:c.baseHeaders["x-algolia-api-key"]=e},searchForHits:function(t,e){return this.search(t,e)},searchForFacets:function(t,e){return this.search(t,e)},customPost:function(t,e){var n=t.path,r=t.parameters,i=t.body;if(!n)throw new Error("Parameter `path` is required when calling `customPost`.");var o={method:"POST",path:"/{path}".replace("{path}",n),queryParameters:r||{},headers:{},data:i||{}};return c.request(o,e)},getRecommendations:function(t,e){if(t&&Array.isArray(t)&&(t={requests:t}),!t)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!t.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");var n={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:t,useReadTransporter:!0,cacheable:!0};return c.request(n,e)},search:function(t,e){if(t&&Array.isArray(t)){var n={requests:t.map((function(t){var e=t.params,n=tr(t,hw);return"facet"===n.type?Ut(Ut(Ut({},n),e),{},{type:"facet"}):Ut(Ut(Ut({},n),e),{},{facet:void 0,maxFacetHits:void 0,facetQuery:void 0})}))};t=n}if(!t)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!t.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");var r={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:t,useReadTransporter:!0,cacheable:!0};return c.request(r,e)}};var n,r,i,o,s,a,l,c}(e,n);return t.addAlgoliaAgent("docsearch",$l),!1===/docsearch.js \(.*\)/.test(t.transporter.algoliaAgent.value)&&t.addAlgoliaAgent("docsearch-react",$l),r(t)}),[e,n,r])),G=k.useRef($f({key:"__DOCSEARCH_FAVORITE_SEARCHES__".concat(s),limit:10})).current,Q=k.useRef($f({key:"__DOCSEARCH_RECENT_SEARCHES__".concat(s),limit:0===G.getAll().length?7:4})).current,J=k.useCallback((function(t){if(!$){var e="content"===t.type?t.__docsearch_parent:t;e&&-1===G.getAll().findIndex((function(t){return t.objectID===e.objectID}))&&Q.add(e)}}),[G,Q,$]),Z=k.useCallback((function(t){if(L.context.algoliaInsightsPlugin&&t.__autocomplete_id){var e=t,n={eventName:"Item Selected",index:e.__autocomplete_indexName,items:[e],positions:[t.__autocomplete_id],queryID:e.__autocomplete_queryID};L.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(n)}}),[L.context.algoliaInsightsPlugin]),X=k.useMemo((function(){return E1({id:"docsearch",defaultActiveItemId:0,placeholder:l,openOnFocus:!0,initialState:{query:H,context:{searchSuggestions:[]}},insights:C,navigator:v,onStateChange:function(t){R(t.state)},getSources:function(t){var e=t.query,n=t.state,r=t.setContext,a=t.setStatus;if(!e)return $?[]:[{sourceId:"recentSearches",onSelect:function(t){var e=t.item,n=t.event;J(e),Ns(n)||h()},getItemUrl:function(t){return t.item.url},getItems:function(){return Q.getAll()}},{sourceId:"favoriteSearches",onSelect:function(t){var e=t.item,n=t.event;J(e),Ns(n)||h()},getItemUrl:function(t){return t.item.url},getItems:function(){return G.getAll()}}];var l=!!C;return K.search({requests:[Ut({query:e,indexName:s,attributesToRetrieve:["hierarchy.lvl0","hierarchy.lvl1","hierarchy.lvl2","hierarchy.lvl3","hierarchy.lvl4","hierarchy.lvl5","hierarchy.lvl6","content","type","url"],attributesToSnippet:["hierarchy.lvl1:".concat(W.current),"hierarchy.lvl2:".concat(W.current),"hierarchy.lvl3:".concat(W.current),"hierarchy.lvl4:".concat(W.current),"hierarchy.lvl5:".concat(W.current),"hierarchy.lvl6:".concat(W.current),"content:".concat(W.current)],snippetEllipsisText:"…",highlightPreTag:"<mark>",highlightPostTag:"</mark>",hitsPerPage:20,clickAnalytics:l},c)]}).catch((function(t){throw"RetryError"===t.name&&a("error"),t})).then((function(t){var e=t.results[0],a=e.hits,c=e.nbHits,d=Yf(a,(function(t){return pg(t)}),u);n.context.searchSuggestions.length<Object.keys(d).length&&r({searchSuggestions:Object.keys(d)}),r({nbHits:c});var f={};return l&&(f={__autocomplete_indexName:s,__autocomplete_queryID:e.queryID,__autocomplete_algoliaCredentials:{appId:i,apiKey:o}}),Object.values(d).map((function(t,e){return{sourceId:"hits".concat(e),onSelect:function(t){var e=t.item,n=t.event;J(e),Ns(n)||h()},getItemUrl:function(t){return t.item.url},getItems:function(){return Object.values(Yf(t,(function(t){return t.hierarchy.lvl1}),u)).map(p).map((function(t){return t.map((function(e){var n=null,r=t.find((function(t){return"lvl1"===t.type&&t.hierarchy.lvl1===e.hierarchy.lvl1}));return"lvl1"!==e.type&&r&&(n=r),Ut(Ut({},e),{},{__docsearch_parent:n},f)}))})).flat()}}}))}))}})}),[s,c,u,K,h,Q,G,J,H,l,v,p,$,C,i,o]),tt=X.getEnvironmentProps,et=X.getRootProps,nt=X.refresh;return function(t){var e=t.getEnvironmentProps,n=t.panelElement,r=t.formElement,i=t.inputElement;k.useEffect((function(){if(n&&r&&i){var t=e({panelElement:n,formElement:r,inputElement:i}),o=t.onTouchStart,s=t.onTouchMove;return window.addEventListener("touchstart",o),window.addEventListener("touchmove",s),function(){window.removeEventListener("touchstart",o),window.removeEventListener("touchmove",s)}}}),[e,n,r,i])}({getEnvironmentProps:tt,panelElement:V.current,formElement:z.current,inputElement:q.current}),function(t){var e=t.container;k.useEffect((function(){if(e){var t=e.querySelectorAll("a[href]:not([disabled]), button:not([disabled]), input:not([disabled])"),n=t[0],r=t[t.length-1];return e.addEventListener("keydown",i),function(){e.removeEventListener("keydown",i)}}function i(t){"Tab"===t.key&&(t.shiftKey?document.activeElement===n&&(t.preventDefault(),r.focus()):document.activeElement===r&&(t.preventDefault(),n.focus()))}}),[e])}({container:F.current}),k.useEffect((function(){return document.body.classList.add("DocSearch--active"),function(){var t,e;document.body.classList.remove("DocSearch--active"),null===(t=(e=window).scrollTo)||void 0===t||t.call(e,0,w)}}),[]),k.useLayoutEffect((function(){var t=window.innerWidth-document.body.clientWidth;return document.body.style.marginRight="".concat(t,"px"),function(){document.body.style.marginRight="0px"}}),[]),k.useEffect((function(){window.matchMedia("(max-width: 768px)").matches&&(W.current=5)}),[]),k.useEffect((function(){V.current&&(V.current.scrollTop=0)}),[L.query]),k.useEffect((function(){H.length>0&&(nt(),q.current&&q.current.focus())}),[H,nt]),k.useEffect((function(){function t(){if(U.current){var t=.01*window.innerHeight;U.current.style.setProperty("--docsearch-vh","".concat(t,"px"))}}return t(),window.addEventListener("resize",t),function(){window.removeEventListener("resize",t)}}),[]),k.createElement("div",Ke({ref:F},et({"aria-expanded":!0}),{className:["DocSearch","DocSearch-Container","stalled"===L.status&&"DocSearch-Container--Stalled","error"===L.status&&"DocSearch-Container--Errored"].filter(Boolean).join(" "),role:"button",tabIndex:0,onMouseDown:function(t){t.target===t.currentTarget&&h()}}),k.createElement("div",{className:"DocSearch-Modal",ref:U},k.createElement("header",{className:"DocSearch-SearchBar",ref:z},k.createElement(Z1,Ke({},X,{state:L,autoFocus:0===H.length,inputRef:q,isFromSelection:!!H&&H===Y,translations:B,onClose:h}))),k.createElement("div",{className:"DocSearch-Dropdown",ref:V},k.createElement(G1,Ke({},X,{indexName:s,state:L,hitComponent:b,resultsFooterComponent:g,disableUserPersonalization:$,recentSearches:Q,favoriteSearches:G,inputRef:q,translations:j,getMissingResultsUrl:T,onItemClick:function(t,e){Z(t),J(t),Ns(e)||h()}}))),k.createElement("footer",{className:"DocSearch-Footer"},k.createElement(T1,{translations:M}))))}function _w(t){var e,n,r,i,o,s,a,l,c=k.useRef(null),u=an(k.useState(!1),2),d=u[0],h=u[1],f=an(k.useState((null==t?void 0:t.initialQuery)||void 0),2),p=f[0],m=f[1],b=k.useCallback((function(){h(!0)}),[h]),y=k.useCallback((function(){h(!1),m(null==t?void 0:t.initialQuery)}),[h,t.initialQuery]);return r={isOpen:d,onOpen:b,onClose:y,onInput:k.useCallback((function(t){h(!0),m(t.key)}),[h,m]),searchButtonRef:c},i=r.isOpen,o=r.onOpen,s=r.onClose,a=r.onInput,l=r.searchButtonRef,k.useEffect((function(){function t(t){var e,n,r;if("Escape"===t.code&&i||"k"===(null===(e=t.key)||void 0===e?void 0:e.toLowerCase())&&(t.metaKey||t.ctrlKey)||(r=(n=t.target).tagName,!n.isContentEditable&&"INPUT"!==r&&"SELECT"!==r&&"TEXTAREA"!==r&&"/"===t.key&&!i))return t.preventDefault(),void(i?s():document.body.classList.contains("DocSearch--active")||o());l&&l.current===document.activeElement&&a&&/[a-zA-Z0-9]/.test(String.fromCharCode(t.keyCode))&&a(t)}return window.addEventListener("keydown",t),function(){window.removeEventListener("keydown",t)}}),[i,o,s,a,l]),k.createElement(k.Fragment,null,k.createElement(K0,{ref:c,translations:null==t||null===(e=t.translations)||void 0===e?void 0:e.button,onClick:b}),d&&ng(k.createElement(gw,Ke({},t,{initialScrollY:window.scrollY,initialQuery:p,translations:null==t||null===(n=t.translations)||void 0===n?void 0:n.modal,onClose:y})),document.body))}function bw(t){ig(k.createElement(_w,Cl({},t,{transformSearchClient:function(e){return e.addAlgoliaAgent("docsearch.js",$l),t.transformSearchClient?t.transformSearchClient(e):e}})),function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return"string"==typeof t?e.document.querySelector(t):t}(t.container,t.environment))}function yw(t){let e;return{c(){e=Y("div"),this.h()},l(t){e=q(t,"DIV",{id:!0,class:!0}),$(e).forEach(S),this.h()},h(){N(e,"id","docsearch"),N(e,"class",t[0]+" "+t[1]+" "+t[2]+" "+t[3])},m(t,n){R(t,e,n)},p:Qt,i:Qt,o:Qt,d(t){t&&S(e)}}}function vw(t,e,n){let{algolia:r}=e;return Pr((()=>{bw({container:"#docsearch",appId:null==r?void 0:r.appId,apiKey:null==r?void 0:r.apiKey,indexName:null==r?void 0:r.indexName})})),t.$$set=t=>{"algolia"in t&&n(4,r=t.algolia)},["\n\t**:[&.DocSearch-Button]:bg-base-100\n\t**:[&.DocSearch-Button]:border-base-300\n\t**:[&.DocSearch-Button]:hover:bg-base-200/40\n\t**:[&.DocSearch-Button]:transition-colors\n\t**:[&.DocSearch-Button]:duration-200\n\t**:[&.DocSearch-Button]:rounded-md\n\t**:[&.DocSearch-Button]:flex\n\t**:[&.DocSearch-Button]:gap-16\n\t**:[&.DocSearch-Button]:cursor-pointer\n\t**:[&.DocSearch-Button]:py-1\n\t**:[&.DocSearch-Button]:pl-2\n\t**:[&.DocSearch-Button]:sm:pr-1\n\t**:[&.DocSearch-Button]:pr-20\n\t**:[&.DocSearch-Button]:sm:text-xs\n\t**:[&.DocSearch-Button]:border\n\t**:[&.DocSearch-Button]:font-sans\n\t**:[&.DocSearch-Button]:font-medium\n\t**:[&.DocSearch-Button]:items-center;\n\t","\n\t**:[&DocSearch-Button-Placeholder]:text-base-content-muted\n\t","\n\t**:[&.DocSearch-Search-Icon]:hidden\n\t","\n\t**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:text-base-content-muted\n\t**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:*:text-base-content-muted\n\t",r]}class ww extends ae{constructor(t){super(),le(this,t,vw,yw,ee,{algolia:4})}}function Sw(t){let e;const n=t[3].default,r=Oe(n,t,t[11],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||2048&i)&&ke(r,n,t,t[11],e?Ee(n,t[11],i,null):De(t[11]),null)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function Iw(t){let e,n;const r=[{class:Fi("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",t[1]&&"pl-8",t[0])},t[2]];let i={$$slots:{default:[Sw]},$$scope:{ctx:t}};for(let t=0;t<r.length;t+=1)i=me(i,r[t]);return e=new Tv({props:i}),e.$on("click",t[4]),e.$on("keydown",t[5]),e.$on("focusin",t[6]),e.$on("focusout",t[7]),e.$on("pointerdown",t[8]),e.$on("pointerleave",t[9]),e.$on("pointermove",t[10]),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,[n]){const i=7&n?An(r,[3&n&&{class:Fi("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",t[1]&&"pl-8",t[0])},4&n&&Ql(t[2])]):{};2048&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Ow(t,e,n){const r=["class","inset"];let i=ln(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e,{inset:l}=e;return t.$$set=t=>{e=me(me({},e),ri(t)),n(2,i=ln(e,r)),"class"in t&&n(0,a=t.class),"inset"in t&&n(1,l=t.inset),"$$scope"in t&&n(11,s=t.$$scope)},[a,l,i,o,function(e){lr.call(this,t,e)},function(e){lr.call(this,t,e)},function(e){lr.call(this,t,e)},function(e){lr.call(this,t,e)},function(e){lr.call(this,t,e)},function(e){lr.call(this,t,e)},function(e){lr.call(this,t,e)},s]}class Mc extends ae{constructor(t){super(),le(this,t,Ow,Iw,ee,{class:0,inset:1})}}function kw(t){let e;const n=t[5].default,r=Oe(n,t,t[7],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||128&i)&&ke(r,n,t,t[7],e?Ee(n,t[7],i,null):De(t[7]),null)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function Dw(t){let e,n;const r=[{transition:t[2]},{transitionConfig:t[3]},{sideOffset:t[1]},{class:Fi("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",t[0])},t[4]];let i={$$slots:{default:[kw]},$$scope:{ctx:t}};for(let t=0;t<r.length;t+=1)i=me(i,r[t]);return e=new e0({props:i}),e.$on("keydown",t[6]),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,[n]){const i=31&n?An(r,[4&n&&{transition:t[2]},8&n&&{transitionConfig:t[3]},2&n&&{sideOffset:t[1]},1&n&&{class:Fi("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",t[0])},16&n&&Ql(t[4])]):{};128&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Ew(t,e,n){const r=["class","sideOffset","transition","transitionConfig"];let i=ln(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e,{sideOffset:l=4}=e,{transition:c=qg}=e,{transitionConfig:u}=e;return t.$$set=t=>{e=me(me({},e),ri(t)),n(4,i=ln(e,r)),"class"in t&&n(0,a=t.class),"sideOffset"in t&&n(1,l=t.sideOffset),"transition"in t&&n(2,c=t.transition),"transitionConfig"in t&&n(3,u=t.transitionConfig),"$$scope"in t&&n(7,s=t.$$scope)},[a,l,c,u,i,o,function(e){lr.call(this,t,e)},s]}class Aw extends ae{constructor(t){super(),le(this,t,Ew,Dw,ee,{class:0,sideOffset:1,transition:2,transitionConfig:3})}}function Tw(t){let e,n,r;const i=t[3].default,o=Oe(i,t,t[2],null);let s=[{class:n=Fi("ml-auto text-xs tracking-widest opacity-60",t[0])},t[1]],a={};for(let t=0;t<s.length;t+=1)a=me(a,s[t]);return{c(){e=Y("span"),o&&o.c(),this.h()},l(t){e=q(t,"SPAN",{class:!0});var n=$(e);o&&o.l(n),n.forEach(S),this.h()},h(){$e(e,a)},m(t,n){R(t,e,n),o&&o.m(e,null),r=!0},p(t,[l]){o&&o.p&&(!r||4&l)&&ke(o,i,t,t[2],r?Ee(i,t[2],l,null):De(t[2]),null),$e(e,a=An(s,[(!r||1&l&&n!==(n=Fi("ml-auto text-xs tracking-widest opacity-60",t[0])))&&{class:n},2&l&&t[1]]))},i(t){r||(O(o,t),r=!0)},o(t){D(o,t),r=!1},d(t){t&&S(e),o&&o.d(t)}}}function Bw(t,e,n){const r=["class"];let i=ln(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e;return t.$$set=t=>{e=me(me({},e),ri(t)),n(1,i=ln(e,r)),"class"in t&&n(0,a=t.class),"$$scope"in t&&n(2,s=t.$$scope)},[a,i,s,o]}class yg extends ae{constructor(t){super(),le(this,t,Bw,Tw,ee,{class:0})}}const Mw=Vv,Pw=l0,Nw=jv;function Cw(t){let e,n;return e=new Tn({props:{src:$g,class:"h-6 w-6"}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p:Qt,i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Fw(t){let e,n;return e=new u_({props:{builders:[t[15]],variant:"ghost",size:"sm",class:"px-1","aria-label":"Menu",$$slots:{default:[Cw]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};32768&n&&(r.builders=[t[15]]),65536&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function jw(t){let e;return{c(){e=wt("⌘P")},l(t){e=vt(t,"⌘P")},m(t,n){R(t,e,n)},d(t){t&&S(e)}}}function Lw(t){let e,n,r;return n=new yg({props:{$$slots:{default:[jw]},$$scope:{ctx:t}}}),{c(){e=wt("Print PDF\n\t\t\t\t"),ct(n.$$.fragment)},l(t){e=vt(t,"Print PDF\n\t\t\t\t"),lt(n.$$.fragment,t)},m(t,i){R(t,e,i),at(n,t,i),r=!0},p(t,e){const r={};65536&e&&(r.$$scope={dirty:e,ctx:t}),n.$set(r)},i(t){r||(O(n.$$.fragment,t),r=!0)},o(t){D(n.$$.fragment,t),r=!1},d(t){t&&S(e),st(n,t)}}}function Xf(t){let e,n;return e=new Mc({props:{$$slots:{default:[Rw]},$$scope:{ctx:t}}}),e.$on("click",t[11]),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};65544&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Rw(t){let e,n,r=t[3]?"Hide ":"Show ";return{c(){e=wt(r),n=wt(" Queries")},l(t){e=vt(t,r),n=vt(t," Queries")},m(t,r){R(t,e,r),R(t,n,r)},p(t,n){8&n&&r!==(r=t[3]?"Hide ":"Show ")&&Le(e,r)},d(t){t&&(S(e),S(n))}}}function Uw(t){let e,n;return e=new Mc({props:{$$slots:{default:[xw]},$$scope:{ctx:t}}}),e.$on("click",t[12]),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};65542&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Vw(t){let e,n,r,i,o;return i=new Tn({props:{src:t[1],class:"h-4 w-4 ml-1"}}),{c(){e=Y("span"),n=wt(t[2]),r=rt(),ct(i.$$.fragment),this.h()},l(o){e=q(o,"SPAN",{class:!0});var s=$(e);n=vt(s,t[2]),s.forEach(S),r=nt(o),lt(i.$$.fragment,o),this.h()},h(){N(e,"class","text-xs leading-none")},m(t,s){R(t,e,s),z(e,n),R(t,r,s),at(i,t,s),o=!0},p(t,e){(!o||4&e)&&Le(n,t[2]);const r={};2&e&&(r.src=t[1]),i.$set(r)},i(t){o||(O(i.$$.fragment,t),o=!0)},o(t){D(i.$$.fragment,t),o=!1},d(t){t&&(S(e),S(r)),st(i,t)}}}function xw(t){let e,n,r;return n=new yg({props:{class:"tracking-normal flex flex-row items-center",$$slots:{default:[Vw]},$$scope:{ctx:t}}}),{c(){e=wt("Appearance\n\t\t\t\t\t"),ct(n.$$.fragment)},l(t){e=vt(t,"Appearance\n\t\t\t\t\t"),lt(n.$$.fragment,t)},m(t,i){R(t,e,i),at(n,t,i),r=!0},p(t,e){const r={};65542&e&&(r.$$scope={dirty:e,ctx:t}),n.$set(r)},i(t){r||(O(n.$$.fragment,t),r=!0)},o(t){D(n.$$.fragment,t),r=!1},d(t){t&&S(e),st(n,t)}}}function zw(t){let e,n,r,i,o;e=new Mc({props:{$$slots:{default:[Lw]},$$scope:{ctx:t}}}),e.$on("click",t[4]);let s=!t[0]&&Xf(t),a=t[8].appearance.switcher&&Uw(t);return{c(){ct(e.$$.fragment),n=rt(),s&&s.c(),r=rt(),a&&a.c(),i=ft()},l(t){lt(e.$$.fragment,t),n=nt(t),s&&s.l(t),r=nt(t),a&&a.l(t),i=ft()},m(t,l){at(e,t,l),R(t,n,l),s&&s.m(t,l),R(t,r,l),a&&a.m(t,l),R(t,i,l),o=!0},p(t,n){const i={};65536&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i),t[0]?s&&(jt(),D(s,1,1,(()=>{s=null})),Lt()):s?(s.p(t,n),1&n&&O(s,1)):(s=Xf(t),s.c(),O(s,1),s.m(r.parentNode,r)),t[8].appearance.switcher&&a.p(t,n)},i(t){o||(O(e.$$.fragment,t),O(s),O(a),o=!0)},o(t){D(e.$$.fragment,t),D(s),D(a),o=!1},d(t){t&&(S(n),S(r),S(i)),st(e,t),s&&s.d(t),a&&a.d(t)}}}function Ww(t){let e,n,r,i;e=new Nw({props:{$$slots:{default:[zw]},$$scope:{ctx:t}}});let o=vl;return{c(){ct(e.$$.fragment),n=rt(),r=ft()},l(t){lt(e.$$.fragment,t),n=nt(t),r=ft()},m(t,o){at(e,t,o),R(t,n,o),R(t,r,o),i=!0},p(t,n){const r={};65551&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){i||(O(e.$$.fragment,t),O(o),i=!0)},o(t){D(e.$$.fragment,t),D(o),i=!1},d(t){t&&(S(n),S(r)),st(e,t)}}}function Hw(t){let e,n,r,i;return e=new Pw({props:{asChild:!0,$$slots:{default:[Fw,({builder:t})=>({15:t}),({builder:t})=>t?32768:0]},$$scope:{ctx:t}}}),r=new Aw({props:{class:"w-52 text-xs",$$slots:{default:[Ww]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment),n=rt(),ct(r.$$.fragment)},l(t){lt(e.$$.fragment,t),n=nt(t),lt(r.$$.fragment,t)},m(t,o){at(e,t,o),R(t,n,o),at(r,t,o),i=!0},p(t,n){const i={};98304&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i);const o={};65551&n&&(o.$$scope={dirty:n,ctx:t}),r.$set(o)},i(t){i||(O(e.$$.fragment,t),O(r.$$.fragment,t),i=!0)},o(t){D(e.$$.fragment,t),D(r.$$.fragment,t),i=!1},d(t){t&&S(n),st(e,t),st(r,t)}}}function qw(t){let e,n;return e=new Mw({props:{$$slots:{default:[Hw]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,[n]){const r={};65551&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Yw(t,e,n){let r,i,o,s,a;Ae(t,Kc,(t=>n(3,a=t)));const l=new Event("export-beforeprint"),c=new Event("export-afterprint"),{selectedAppearance:u,activeAppearance:d,cycleAppearance:h,themesConfig:f}=hh();Ae(t,u,(t=>n(10,s=t))),Ae(t,d,(t=>n(9,o=t)));let{neverShowQueries:p}=e;return t.$$set=t=>{"neverShowQueries"in t&&n(0,p=t.neverShowQueries)},t.$$.update=()=>{1024&t.$$.dirty&&n(2,r="system"===s?"System":"light"===s?"Light":"Dark"),512&t.$$.dirty&&n(1,i="light"===o?Yg:Kg)},[p,i,r,a,function(){window.dispatchEvent(l),setTimeout((()=>window.print()),0),setTimeout((()=>window.dispatchEvent(c)),0)},u,d,h,f,o,s,t=>{t.preventDefault(),Kc.update((t=>!t))},t=>{t.preventDefault(),h()}]}class Kw extends ae{constructor(t){super(),le(this,t,Yw,qw,ee,{neverShowQueries:0})}}function $w(t){let e,n,r,i,o,s,a,l,c,u,d;const h=[Gw,Qw],f=[];function p(t,e){return t[0]?0:1}return r=p(t),i=f[r]=h[r](t),l=new Sc({props:{logo:t[2],lightLogo:t[3],darkLogo:t[4],title:t[1]}}),{c(){e=Y("div"),n=Y("button"),i.c(),s=rt(),a=Y("a"),ct(l.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=$(e);n=q(r,"BUTTON",{type:!0,class:!0});var o=$(n);i.l(o),o.forEach(S),s=nt(r),a=q(r,"A",{href:!0,class:!0});var c=$(a);lt(l.$$.fragment,c),c.forEach(S),r.forEach(S),this.h()},h(){N(n,"type","button"),N(n,"class",o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+("hide"===t[9]?"block":"md:hidden")),N(a,"href",xt("/")),N(a,"class","text-sm font-bold text-base-content hidden md:block"),N(e,"class","flex gap-x-4 items-center")},m(i,o){R(i,e,o),z(e,n),f[r].m(n,null),z(e,s),z(e,a),at(l,a,null),c=!0,u||(d=he(n,"click",t[15]),u=!0)},p(t,e){let s=r;r=p(t),r!==s&&(jt(),D(f[s],1,1,(()=>{f[s]=null})),Lt(),i=f[r],i||(i=f[r]=h[r](t),i.c()),O(i,1),i.m(n,null)),(!c||512&e&&o!==(o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+("hide"===t[9]?"block":"md:hidden")))&&N(n,"class",o);const a={};4&e&&(a.logo=t[2]),8&e&&(a.lightLogo=t[3]),16&e&&(a.darkLogo=t[4]),2&e&&(a.title=t[1]),l.$set(a)},i(t){c||(O(i),O(l.$$.fragment,t),c=!0)},o(t){D(i),D(l.$$.fragment,t),c=!1},d(t){t&&S(e),f[r].d(),st(l),u=!1,d()}}}function Jw(t){let e,n,r;return n=new Sc({props:{logo:t[2],lightLogo:t[3],darkLogo:t[4],title:t[1]}}),{c(){e=Y("a"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"href",xt("/")),N(e,"class","block text-sm font-bold text-base-content")},m(t,i){R(t,e,i),at(n,e,null),r=!0},p(t,e){const r={};4&e&&(r.logo=t[2]),8&e&&(r.lightLogo=t[3]),16&e&&(r.darkLogo=t[4]),2&e&&(r.title=t[1]),n.$set(r)},i(t){r||(O(n.$$.fragment,t),r=!0)},o(t){D(n.$$.fragment,t),r=!1},d(t){t&&S(e),st(n)}}}function Qw(t){let e,n,r,i,o="Open sidebar";return r=new Tn({props:{class:"w-5 h-5",src:Jg}}),{c(){e=Y("span"),e.textContent=o,n=rt(),ct(r.$$.fragment),this.h()},l(t){e=q(t,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-73kebv"!==yr(e)&&(e.textContent=o),n=nt(t),lt(r.$$.fragment,t),this.h()},h(){N(e,"class","sr-only")},m(t,o){R(t,e,o),R(t,n,o),at(r,t,o),i=!0},i(t){i||(O(r.$$.fragment,t),i=!0)},o(t){D(r.$$.fragment,t),i=!1},d(t){t&&(S(e),S(n)),st(r,t)}}}function Gw(t){let e,n,r,i,o="Close sidebar";return r=new Tn({props:{class:"w-5 h-5",src:Js}}),{c(){e=Y("span"),e.textContent=o,n=rt(),ct(r.$$.fragment),this.h()},l(t){e=q(t,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-13q18xv"!==yr(e)&&(e.textContent=o),n=nt(t),lt(r.$$.fragment,t),this.h()},h(){N(e,"class","sr-only")},m(t,o){R(t,e,o),R(t,n,o),at(r,t,o),i=!0},i(t){i||(O(r.$$.fragment,t),i=!0)},o(t){D(r.$$.fragment,t),i=!1},d(t){t&&(S(e),S(n)),st(r,t)}}}function Zf(t){let e,n;return e=new ww({props:{algolia:t[10]}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};1024&n&&(r.algolia=t[10]),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function td(t){let e,n,r,i;return n=new Tn({props:{src:S_,class:"w-4 h-4 text-base-content"}}),{c(){e=Y("a"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"href",r=xt(t[11])),N(e,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),N(e,"target","_blank"),N(e,"rel","noreferrer")},m(t,r){R(t,e,r),at(n,e,null),i=!0},p(t,n){(!i||2048&n&&r!==(r=xt(t[11])))&&N(e,"href",r)},i(t){i||(O(n.$$.fragment,t),i=!0)},o(t){D(n.$$.fragment,t),i=!1},d(t){t&&S(e),st(n)}}}function ed(t){let e,n,r,i;return n=new Tn({props:{src:I_,class:"w-4 h-4 text-base-content"}}),{c(){e=Y("a"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"href",r=xt(t[12])),N(e,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),N(e,"target","_blank"),N(e,"rel","noreferrer")},m(t,r){R(t,e,r),at(n,e,null),i=!0},p(t,n){(!i||4096&n&&r!==(r=xt(t[12])))&&N(e,"href",r)},i(t){i||(O(n.$$.fragment,t),i=!0)},o(t){D(n.$$.fragment,t),i=!1},d(t){t&&S(e),st(n)}}}function nd(t){let e,n,r;return n=new Tn({props:{src:O_,fill:"currentColor",class:"w-4 h-4 text-base-content "}}),{c(){e=Y("a"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"href",t[13]),N(e,"class","hover:bg-gray-50 rounded-lg p-2 transition-all duration-200"),N(e,"target","_blank"),N(e,"rel","noreferrer")},m(t,i){R(t,e,i),at(n,e,null),r=!0},p(t,n){(!r||8192&n)&&N(e,"href",t[13])},i(t){r||(O(n.$$.fragment,t),r=!0)},o(t){D(n.$$.fragment,t),r=!1},d(t){t&&S(e),st(n)}}}function rd(t){let e,n,r,i;return n=new Tn({props:{src:k_,class:"w-4 h-4 text-base-content "}}),{c(){e=Y("a"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"href",r=xt(t[14])),N(e,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),N(e,"target","_blank"),N(e,"rel","noreferrer")},m(t,r){R(t,e,r),at(n,e,null),i=!0},p(t,n){(!i||16384&n&&r!==(r=xt(t[14])))&&N(e,"href",r)},i(t){i||(O(n.$$.fragment,t),i=!0)},o(t){D(n.$$.fragment,t),i=!1},d(t){t&&S(e),st(n)}}}function Xw(t){let e,n,r,i,o,s,a,l,c,u,d,h,f,p,m,b;const y=[Jw,$w],g=[];function v(t,e){return t[8]||"never"===t[9]?0:1}r=v(t),i=g[r]=y[r](t);let _=t[10]&&Zf(t),w=t[11]&&td(t),I=t[12]&&ed(t),x=t[13]&&nd(t),E=t[14]&&rd(t);return p=new Kw({props:{neverShowQueries:t[5]}}),{c(){e=Y("header"),n=Y("div"),i.c(),o=rt(),s=Y("div"),_&&_.c(),a=rt(),l=Y("div"),w&&w.c(),c=rt(),I&&I.c(),u=rt(),x&&x.c(),d=rt(),E&&E.c(),h=rt(),f=Y("div"),ct(p.$$.fragment),this.h()},l(t){e=q(t,"HEADER",{class:!0});var r=$(e);n=q(r,"DIV",{class:!0,style:!0});var m=$(n);i.l(m),o=nt(m),s=q(m,"DIV",{class:!0});var b=$(s);_&&_.l(b),a=nt(b),l=q(b,"DIV",{class:!0});var y=$(l);w&&w.l(y),c=nt(y),I&&I.l(y),u=nt(y),x&&x.l(y),d=nt(y),E&&E.l(y),y.forEach(S),h=nt(b),f=q(b,"DIV",{class:!0});var g=$(f);lt(p.$$.fragment,g),g.forEach(S),b.forEach(S),m.forEach(S),r.forEach(S),this.h()},h(){N(l,"class","flex gap-2 items-center"),N(f,"class","relative"),N(s,"class","flex gap-2 text-sm items-center"),N(n,"class",m=(t[6]?"max-w-full ":t[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"),Qs(n,"max-width",t[7]+"px"),N(e,"class","fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden")},m(t,i){R(t,e,i),z(e,n),g[r].m(n,null),z(n,o),z(n,s),_&&_.m(s,null),z(s,a),z(s,l),w&&w.m(l,null),z(l,c),I&&I.m(l,null),z(l,u),x&&x.m(l,null),z(l,d),E&&E.m(l,null),z(s,h),z(s,f),at(p,f,null),b=!0},p(t,[e]){let h=r;r=v(t),r===h?g[r].p(t,e):(jt(),D(g[h],1,1,(()=>{g[h]=null})),Lt(),i=g[r],i?i.p(t,e):(i=g[r]=y[r](t),i.c()),O(i,1),i.m(n,o)),t[10]?_?(_.p(t,e),1024&e&&O(_,1)):(_=Zf(t),_.c(),O(_,1),_.m(s,a)):_&&(jt(),D(_,1,1,(()=>{_=null})),Lt()),t[11]?w?(w.p(t,e),2048&e&&O(w,1)):(w=td(t),w.c(),O(w,1),w.m(l,c)):w&&(jt(),D(w,1,1,(()=>{w=null})),Lt()),t[12]?I?(I.p(t,e),4096&e&&O(I,1)):(I=ed(t),I.c(),O(I,1),I.m(l,u)):I&&(jt(),D(I,1,1,(()=>{I=null})),Lt()),t[13]?x?(x.p(t,e),8192&e&&O(x,1)):(x=nd(t),x.c(),O(x,1),x.m(l,d)):x&&(jt(),D(x,1,1,(()=>{x=null})),Lt()),t[14]?E?(E.p(t,e),16384&e&&O(E,1)):(E=rd(t),E.c(),O(E,1),E.m(l,null)):E&&(jt(),D(E,1,1,(()=>{E=null})),Lt());const f={};32&e&&(f.neverShowQueries=t[5]),p.$set(f),(!b||192&e&&m!==(m=(t[6]?"max-w-full ":t[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"))&&N(n,"class",m),(!b||128&e)&&Qs(n,"max-width",t[7]+"px")},i(t){b||(O(i),O(_),O(w),O(I),O(x),O(E),O(p.$$.fragment,t),b=!0)},o(t){D(i),D(_),D(w),D(I),D(x),D(E),D(p.$$.fragment,t),b=!1},d(t){t&&S(e),g[r].d(),_&&_.d(),w&&w.d(),I&&I.d(),x&&x.d(),E&&E.d(),st(p)}}}function Zw(t,e,n){let{mobileSidebarOpen:r}=e,{title:i}=e,{logo:o}=e,{lightLogo:s}=e,{darkLogo:a}=e,{neverShowQueries:l}=e,{fullWidth:c}=e,{maxWidth:u}=e,{hideSidebar:d}=e,{sidebarFrontMatter:h}=e,{algolia:f}=e,{githubRepo:p}=e,{xProfile:m}=e,{blueskyProfile:b}=e,{slackCommunity:y}=e;return t.$$set=t=>{"mobileSidebarOpen"in t&&n(0,r=t.mobileSidebarOpen),"title"in t&&n(1,i=t.title),"logo"in t&&n(2,o=t.logo),"lightLogo"in t&&n(3,s=t.lightLogo),"darkLogo"in t&&n(4,a=t.darkLogo),"neverShowQueries"in t&&n(5,l=t.neverShowQueries),"fullWidth"in t&&n(6,c=t.fullWidth),"maxWidth"in t&&n(7,u=t.maxWidth),"hideSidebar"in t&&n(8,d=t.hideSidebar),"sidebarFrontMatter"in t&&n(9,h=t.sidebarFrontMatter),"algolia"in t&&n(10,f=t.algolia),"githubRepo"in t&&n(11,p=t.githubRepo),"xProfile"in t&&n(12,m=t.xProfile),"blueskyProfile"in t&&n(13,b=t.blueskyProfile),"slackCommunity"in t&&n(14,y=t.slackCommunity)},[r,i,o,s,a,l,c,u,d,h,f,p,m,b,y,()=>{n(0,r=!r)}]}class t2 extends ae{constructor(t){super(),le(this,t,Zw,Xw,ee,{mobileSidebarOpen:0,title:1,logo:2,lightLogo:3,darkLogo:4,neverShowQueries:5,fullWidth:6,maxWidth:7,hideSidebar:8,sidebarFrontMatter:9,algolia:10,githubRepo:11,xProfile:12,blueskyProfile:13,slackCommunity:14})}}function e2(t){return Pr((()=>{})),[]}class n2 extends ae{constructor(t){super(),le(this,t,e2,null,ee,{})}}function r2(t){let e,n,r='<span class="sr-only">Loading...</span> <div class="h-8 rounded-full bg-base-200 w-48 mb-8"></div> <div class="flex gap-3"><div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[65%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[100%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[75%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div>';return{c(){e=Y("div"),e.innerHTML=r,this.h()},l(t){e=q(t,"DIV",{role:!0,class:!0,"data-svelte-h":!0}),"svelte-1u7962h"!==yr(e)&&(e.innerHTML=r),this.h()},h(){N(e,"role","status"),N(e,"class","animate-pulse")},m(t,n){R(t,e,n)},p:Qt,i(t){t&&(n||On((()=>{n=Hi(e,xo,{}),n.start()})))},o:Qt,d(t){t&&S(e)}}}class i2 extends ae{constructor(t){super(),le(this,t,null,r2,ee,{})}}function o2(t){let e,n;const r=t[1].default,i=Oe(r,t,t[0],null);return{c(){e=Y("span"),i&&i.c(),this.h()},l(t){e=q(t,"SPAN",{class:!0});var n=$(e);i&&i.l(n),n.forEach(S),this.h()},h(){N(e,"class","rounded-sm px-0.5 py-[1px] bg-positive/10 border border-positive/20 text-positive text-base sm:text-xs")},m(t,r){R(t,e,r),i&&i.m(e,null),n=!0},p(t,[e]){i&&i.p&&(!n||1&e)&&ke(i,r,t,t[0],n?Ee(r,t[0],e,null):De(t[0]),null)},i(t){n||(O(i,t),n=!0)},o(t){D(i,t),n=!1},d(t){t&&S(e),i&&i.d(t)}}}function s2(t,e,n){let{$$slots:r={},$$scope:i}=e;return t.$$set=t=>{"$$scope"in t&&n(0,i=t.$$scope)},[i,r]}class Bn extends ae{constructor(t){super(),le(this,t,s2,o2,ee,{})}}function id(t,e,n){const r=t.slice();return r[18]=e[n],r}function od(t,e,n){const r=t.slice();return r[21]=e[n],r}function sd(t,e,n){const r=t.slice();return r[24]=e[n],r}function ad(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[24].href.toUpperCase()+"/";return e[27]=n,e}function a2(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[21].href.toUpperCase()+"/";return e[27]=n,e}function ld(t,e,n){const r=t.slice();return r[18]=e[n],r}function cd(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[18].href.toUpperCase()+"/";return e[27]=n,e}function ud(t,e,n){const r=t.slice();return r[18]=e[n],r}function fd(t,e,n){const r=t.slice();return r[21]=e[n],r}function dd(t,e,n){const r=t.slice();return r[24]=e[n],r}function hd(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[24].href.toUpperCase()+"/";return e[27]=n,e}function l2(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[21].href.toUpperCase()+"/";return e[27]=n,e}function md(t,e,n){const r=t.slice();return r[18]=e[n],r}function pd(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[18].href.toUpperCase()+"/";return e[27]=n,e}function gd(t){let e,n,r,i,o,s,a,l,c,u,d,h,f,p,m,b,y,g,v,_,w,I,x,E,k,A,T="Close sidebar";l=new Sc({props:{logo:t[2],title:t[1]}}),p=new Tn({props:{src:Js,class:"w-5 h-5"}});let C=ue(t[11]),M=[];for(let e=0;e<C.length;e+=1)M[e]=_d(md(t,C,e));const B=t=>D(M[t],1,1,(()=>{M[t]=null}));let j=ue(t[11]),P=[];for(let e=0;e<j.length;e+=1)P[e]=wd(ud(t,j,e));const L=t=>D(P[t],1,1,(()=>{P[t]=null}));return{c(){e=Y("div"),r=rt(),i=Y("div"),o=Y("div"),s=Y("div"),a=Y("a"),ct(l.$$.fragment),c=rt(),u=Y("span"),d=Y("button"),h=Y("span"),h.textContent=T,f=rt(),ct(p.$$.fragment),m=rt(),b=Y("div"),y=Y("div"),g=Y("a"),v=wt(t[3]),_=rt();for(let t=0;t<M.length;t+=1)M[t].c();w=rt();for(let t=0;t<P.length;t+=1)P[t].c();this.h()},l(n){e=q(n,"DIV",{class:!0,role:!0,tabindex:!0}),$(e).forEach(S),r=nt(n),i=q(n,"DIV",{class:!0});var O=$(i);o=q(O,"DIV",{class:!0});var I=$(o);s=q(I,"DIV",{class:!0});var x=$(s);a=q(x,"A",{href:!0,class:!0});var D=$(a);lt(l.$$.fragment,D),D.forEach(S),c=nt(x),u=q(x,"SPAN",{role:!0,tabindex:!0});var E=$(u);d=q(E,"BUTTON",{type:!0,class:!0});var k=$(d);h=q(k,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-13q18xv"!==yr(h)&&(h.textContent=T),f=nt(k),lt(p.$$.fragment,k),k.forEach(S),E.forEach(S),x.forEach(S),m=nt(I),b=q(I,"DIV",{class:!0,id:!0});var A=$(b);y=q(A,"DIV",{class:!0});var N=$(y);g=q(N,"A",{class:!0,href:!0});var C=$(g);v=vt(C,t[3]),C.forEach(S),_=nt(N);for(let t=0;t<M.length;t+=1)M[t].l(N);N.forEach(S),w=nt(A);for(let t=0;t<P.length;t+=1)P[t].l(A);A.forEach(S),I.forEach(S),O.forEach(S),this.h()},h(){N(e,"class","fixed inset-0 bg-base-100/80 z-50 backdrop-blur-sm"),N(e,"role","button"),N(e,"tabindex","-1"),N(a,"href",xt("/")),N(a,"class","block mt-1 text-sm font-bold"),N(h,"class","sr-only"),N(d,"type","button"),N(d,"class","hover:bg-base-200 rounded-lg p-1 transition-all duration-500"),N(u,"role","button"),N(u,"tabindex","-1"),N(s,"class","py-3 px-8 mb-3 flex items-start justify-between"),N(g,"class","sticky top-0 bg-base-100 shadow shadow-base-100 text-base-heading font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100"),N(g,"href",xt("/")),N(y,"class","flex flex-col pb-6"),N(b,"class","flex-1 px-8 sm:pb-0 pb-4 overflow-auto text-base sm:text-sm pretty-scrollbar"),N(b,"id","mobileScrollable"),N(o,"class","flex flex-col h-full pb-4"),N(i,"class","bg-base-100 border-r border-base-200 shadow-lg fixed inset-0 z-50 flex sm:w-72 h-screen w-screen flex-col overflow-hidden select-none")},m(n,S){R(n,e,S),R(n,r,S),R(n,i,S),z(i,o),z(o,s),z(s,a),at(l,a,null),z(s,c),z(s,u),z(u,d),z(d,h),z(d,f),at(p,d,null),z(o,m),z(o,b),z(b,y),z(y,g),z(g,v),z(y,_);for(let t=0;t<M.length;t+=1)M[t]&&M[t].m(y,null);z(b,w);for(let t=0;t<P.length;t+=1)P[t]&&P[t].m(b,null);E=!0,k||(A=[he(e,"click",t[13]),he(e,"keypress",t[14]),he(d,"click",t[15]),he(u,"click",t[16]),he(u,"keypress",t[17])],k=!0)},p(t,e){const n={};if(4&e[0]&&(n.logo=t[2]),2&e[0]&&(n.title=t[1]),l.$set(n),(!E||8&e[0])&&Le(v,t[3]),2304&e[0]){let n;for(C=ue(t[11]),n=0;n<C.length;n+=1){const r=md(t,C,n);M[n]?(M[n].p(r,e),O(M[n],1)):(M[n]=_d(r),M[n].c(),O(M[n],1),M[n].m(y,null))}for(jt(),n=C.length;n<M.length;n+=1)B(n);Lt()}if(2432&e[0]){let n;for(j=ue(t[11]),n=0;n<j.length;n+=1){const r=ud(t,j,n);P[n]?(P[n].p(r,e),O(P[n],1)):(P[n]=wd(r),P[n].c(),O(P[n],1),P[n].m(b,null))}for(jt(),n=j.length;n<P.length;n+=1)L(n);Lt()}},i(t){if(!E){t&&On((()=>{E&&(n||(n=In(e,xo,{duration:100},!0)),n.run(1))})),O(l.$$.fragment,t),O(p.$$.fragment,t);for(let t=0;t<C.length;t+=1)O(M[t]);for(let t=0;t<j.length;t+=1)O(P[t]);t&&On((()=>{E&&(x&&x.end(1),I=Hi(i,ei,{x:-50,duration:300}),I.start())})),E=!0}},o(t){t&&(n||(n=In(e,xo,{duration:100},!1)),n.run(0)),D(l.$$.fragment,t),D(p.$$.fragment,t),M=M.filter(Boolean);for(let t=0;t<M.length;t+=1)D(M[t]);P=P.filter(Boolean);for(let t=0;t<P.length;t+=1)D(P[t]);I&&I.invalidate(),t&&(x=es(i,ei,{x:-100,duration:200})),E=!1},d(t){t&&(S(e),S(r),S(i)),t&&n&&n.end(),st(l),st(p),kn(M,t),kn(P,t),t&&x&&x.end(),k=!1,wr(A)}}}function c2(t){var e,n;let r,i,o,s,a,l,c=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",u=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&u2(t);return{c(){r=Y("a"),i=wt(c),o=rt(),u&&u.c(),s=rt(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=$(r);i=vt(e,c),o=nt(e),u&&u.l(e),s=nt(e),e.forEach(S),this.h()},h(){N(r,"class",a="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(r,"href",xt(t[18].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),u&&u.m(r,null),z(r,s),l=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&u.p(t,e),(!l||256&e[0]&&a!==(a="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(r,"class",a)},i(t){l||(O(u),l=!0)},o(t){D(u),l=!1},d(t){t&&S(r),u&&u.d()}}}function u2(t){let e,n;return e=new Bn({props:{$$slots:{default:[f2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function f2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function _d(t){var e,n;let r,i,o=0===t[18].children.length&&t[18].href&&(!1!==(null==(e=t[18].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[18].frontMatter)?void 0:n.sidebar_link))&&c2(pd(t));return{c(){o&&o.c(),r=ft()},l(t){o&&o.l(t),r=ft()},m(t,e){o&&o.m(t,e),R(t,r,e),i=!0},p(t,e){var n,r;0===t[18].children.length&&t[18].href&&(!1!==(null==(n=t[18].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[18].frontMatter)?void 0:r.sidebar_link))&&o.p(pd(t),e)},i(t){i||(O(o),i=!0)},o(t){D(o),i=!1},d(t){t&&S(r),o&&o.d(t)}}}function d2(t){let e,n,r,i,o,s;const a=[m2,h2],l=[];var c,u,d;n=!(c=t)[18].href||!1===(null==(u=c[18].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[18].frontMatter)?void 0:d.sidebar_link)?1:0,r=l[n]=a[n](t);let h=ue(t[18].children),f=[];for(let e=0;e<h.length;e+=1)f[e]=vd(fd(t,h,e));const p=t=>D(f[t],1,1,(()=>{f[t]=null}));return{c(){e=Y("div"),r.c(),i=rt();for(let t=0;t<f.length;t+=1)f[t].c();o=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var n=$(e);r.l(n),i=nt(n);for(let t=0;t<f.length;t+=1)f[t].l(n);o=nt(n),n.forEach(S),this.h()},h(){N(e,"class","flex flex-col pb-6")},m(t,r){R(t,e,r),l[n].m(e,null),z(e,i);for(let t=0;t<f.length;t+=1)f[t]&&f[t].m(e,null);z(e,o),s=!0},p(t,n){if(r.p(t,n),2432&n[0]){let r;for(h=ue(t[18].children),r=0;r<h.length;r+=1){const i=fd(t,h,r);f[r]?(f[r].p(i,n),O(f[r],1)):(f[r]=vd(i),f[r].c(),O(f[r],1),f[r].m(e,o))}for(jt(),r=h.length;r<f.length;r+=1)p(r);Lt()}},i(t){if(!s){O(r);for(let t=0;t<h.length;t+=1)O(f[t]);s=!0}},o(t){D(r),f=f.filter(Boolean);for(let t=0;t<f.length;t+=1)D(f[t]);s=!1},d(t){t&&S(e),l[n].d(),kn(f,t)}}}function h2(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&p2(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0,href:!0});var e=$(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(S),this.h()},h(){N(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),N(r,"href",xt(t[18].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(O(l),s=!0)},o(t){D(l),s=!1},d(t){t&&S(r),l&&l.d()}}}function m2(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&_2(t);return{c(){r=Y("a"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=$(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(S),this.h()},h(){N(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),N(r,"href",xt(t[18].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(O(l),s=!0)},o(t){D(l),s=!1},d(t){t&&S(r),l&&l.d()}}}function p2(t){let e,n;return e=new Bn({props:{$$slots:{default:[g2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function g2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function _2(t){let e,n;return e=new Bn({props:{$$slots:{default:[b2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function b2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function y2(t){var e,n;let r,i,o,s,a=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",l=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&w2(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0});var e=$(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(S),this.h()},h(){N(r,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(t,e){R(t,r,e),z(r,i),z(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(O(l),s=!0)},o(t){D(l),s=!1},d(t){t&&S(r),l&&l.d()}}}function v2(t){var e,n;let r,i,o,s,a,l=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",c=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&I2(t);return{c(){r=Y("a"),i=wt(l),o=rt(),c&&c.c(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=$(r);i=vt(e,l),o=nt(e),c&&c.l(e),e.forEach(S),this.h()},h(){N(r,"class",s="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(r,"href",xt(t[21].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),c&&c.m(r,null),a=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&c.p(t,e),(!a||256&e[0]&&s!==(s="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(r,"class",s)},i(t){a||(O(c),a=!0)},o(t){D(c),a=!1},d(t){t&&S(r),c&&c.d()}}}function w2(t){let e,n;return e=new Bn({props:{$$slots:{default:[S2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function S2(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function I2(t){let e,n;return e=new Bn({props:{$$slots:{default:[O2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function O2(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function bd(t){let e,n,r=ue(t[21].children),i=[];for(let e=0;e<r.length;e+=1)i[e]=yd(dd(t,r,e));const o=t=>D(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=ft()},l(t){for(let e=0;e<i.length;e+=1)i[e].l(t);e=ft()},m(t,r){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,r);R(t,e,r),n=!0},p(t,n){if(2304&n[0]){let s;for(r=ue(t[21].children),s=0;s<r.length;s+=1){const o=dd(t,r,s);i[s]?(i[s].p(o,n),O(i[s],1)):(i[s]=yd(o),i[s].c(),O(i[s],1),i[s].m(e.parentNode,e))}for(jt(),s=r.length;s<i.length;s+=1)o(s);Lt()}},i(t){if(!n){for(let t=0;t<r.length;t+=1)O(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let t=0;t<i.length;t+=1)D(i[t]);n=!1},d(t){t&&S(e),kn(i,t)}}}function k2(t){var e,n;let r,i,o,s,a,l=((null==(e=t[24].frontMatter)?void 0:e.title)??t[24].label)+"",c=(null==(n=t[24].frontMatter)?void 0:n.sidebar_badge)&&D2(t);return{c(){r=Y("a"),i=wt(l),o=rt(),c&&c.c(),this.h()},l(t){r=q(t,"A",{href:!0,class:!0});var e=$(r);i=vt(e,l),o=nt(e),c&&c.l(e),e.forEach(S),this.h()},h(){N(r,"href",xt(t[24].href)),N(r,"class",s="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(t[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content"))},m(t,e){R(t,r,e),z(r,i),z(r,o),c&&c.m(r,null),a=!0},p(t,e){var n;null!=(n=t[24].frontMatter)&&n.sidebar_badge&&c.p(t,e),(!a||256&e[0]&&s!==(s="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(t[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content")))&&N(r,"class",s)},i(t){a||(O(c),a=!0)},o(t){D(c),a=!1},d(t){t&&S(r),c&&c.d()}}}function D2(t){let e,n;return e=new Bn({props:{$$slots:{default:[E2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function E2(t){let e,n=t[24].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function yd(t){var e,n;let r,i,o=t[24].href&&(!1!==(null==(e=t[24].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[24].frontMatter)?void 0:n.sidebar_link))&&k2(hd(t));return{c(){o&&o.c(),r=ft()},l(t){o&&o.l(t),r=ft()},m(t,e){o&&o.m(t,e),R(t,r,e),i=!0},p(t,e){var n,r;t[24].href&&(!1!==(null==(n=t[24].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[24].frontMatter)?void 0:r.sidebar_link))&&o.p(hd(t),e)},i(t){i||(O(o),i=!0)},o(t){D(o),i=!1},d(t){t&&S(r),o&&o.d(t)}}}function vd(t){let e,n,r,i,o;const s=[v2,y2],a=[];function l(t,e){return 0===e?l2(t):t}var c,u,d;e=!(c=t)[21].href||!1===(null==(u=c[21].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[21].frontMatter)?void 0:d.sidebar_link)?1:0,n=a[e]=s[e](l(t,e));let h=t[21].children.length>0&&t[7]>2&&bd(t);return{c(){n.c(),r=rt(),h&&h.c(),i=ft()},l(t){n.l(t),r=nt(t),h&&h.l(t),i=ft()},m(t,n){a[e].m(t,n),R(t,r,n),h&&h.m(t,n),R(t,i,n),o=!0},p(t,r){n.p(l(t,e),r),t[21].children.length>0&&t[7]>2?h?(h.p(t,r),128&r[0]&&O(h,1)):(h=bd(t),h.c(),O(h,1),h.m(i.parentNode,i)):h&&(jt(),D(h,1,1,(()=>{h=null})),Lt())},i(t){o||(O(n),O(h),o=!0)},o(t){D(n),D(h),o=!1},d(t){t&&(S(r),S(i)),a[e].d(t),h&&h.d(t)}}}function wd(t){let e,n,r=t[18].children.length>0&&d2(t);return{c(){r&&r.c(),e=ft()},l(t){r&&r.l(t),e=ft()},m(t,i){r&&r.m(t,i),R(t,e,i),n=!0},p(t,e){t[18].children.length>0&&r.p(t,e)},i(t){n||(O(r),n=!0)},o(t){D(r),n=!1},d(t){t&&S(e),r&&r.d(t)}}}function Sd(t){let e,n,r,i,o,s,a,l=ue(t[11]),c=[];for(let e=0;e<l.length;e+=1)c[e]=Id(ld(t,l,e));const u=t=>D(c[t],1,1,(()=>{c[t]=null}));let d=ue(t[11]),h=[];for(let e=0;e<d.length;e+=1)h[e]=Ad(id(t,d,e));const f=t=>D(h[t],1,1,(()=>{h[t]=null}));return{c(){e=Y("div"),n=Y("div"),r=Y("a"),i=wt(t[3]),o=rt();for(let t=0;t<c.length;t+=1)c[t].c();s=rt();for(let t=0;t<h.length;t+=1)h[t].c();this.h()},l(a){e=q(a,"DIV",{class:!0});var l=$(e);n=q(l,"DIV",{class:!0});var u=$(n);r=q(u,"A",{class:!0,href:!0});var d=$(r);i=vt(d,t[3]),d.forEach(S),o=nt(u);for(let t=0;t<c.length;t+=1)c[t].l(u);u.forEach(S),s=nt(l);for(let t=0;t<h.length;t+=1)h[t].l(l);l.forEach(S),this.h()},h(){N(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading"),N(r,"href",xt("/")),N(n,"class","flex flex-col pb-6"),N(e,"class","hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"),Nr(e,"top-8",t[5])},m(t,l){R(t,e,l),z(e,n),z(n,r),z(r,i),z(n,o);for(let t=0;t<c.length;t+=1)c[t]&&c[t].m(n,null);z(e,s);for(let t=0;t<h.length;t+=1)h[t]&&h[t].m(e,null);a=!0},p(t,r){if((!a||8&r[0])&&Le(i,t[3]),2304&r[0]){let e;for(l=ue(t[11]),e=0;e<l.length;e+=1){const i=ld(t,l,e);c[e]?(c[e].p(i,r),O(c[e],1)):(c[e]=Id(i),c[e].c(),O(c[e],1),c[e].m(n,null))}for(jt(),e=l.length;e<c.length;e+=1)u(e);Lt()}if(2432&r[0]){let n;for(d=ue(t[11]),n=0;n<d.length;n+=1){const i=id(t,d,n);h[n]?(h[n].p(i,r),O(h[n],1)):(h[n]=Ad(i),h[n].c(),O(h[n],1),h[n].m(e,null))}for(jt(),n=d.length;n<h.length;n+=1)f(n);Lt()}(!a||32&r[0])&&Nr(e,"top-8",t[5])},i(t){if(!a){for(let t=0;t<l.length;t+=1)O(c[t]);for(let t=0;t<d.length;t+=1)O(h[t]);a=!0}},o(t){c=c.filter(Boolean);for(let t=0;t<c.length;t+=1)D(c[t]);h=h.filter(Boolean);for(let t=0;t<h.length;t+=1)D(h[t]);a=!1},d(t){t&&S(e),kn(c,t),kn(h,t)}}}function A2(t){var e,n;let r,i,o,s,a,l,c=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",u=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&T2(t);return{c(){r=Y("a"),i=wt(c),o=rt(),u&&u.c(),s=rt(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=$(r);i=vt(e,c),o=nt(e),u&&u.l(e),s=nt(e),e.forEach(S),this.h()},h(){N(r,"class",a="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(r,"href",xt(t[18].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),u&&u.m(r,null),z(r,s),l=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&u.p(t,e),(!l||256&e[0]&&a!==(a="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(r,"class",a)},i(t){l||(O(u),l=!0)},o(t){D(u),l=!1},d(t){t&&S(r),u&&u.d()}}}function T2(t){let e,n;return e=new Bn({props:{$$slots:{default:[B2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function B2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function Id(t){var e,n;let r,i,o=0===t[18].children.length&&t[18].href&&(!1!==(null==(e=t[18].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[18].frontMatter)?void 0:n.sidebar_link))&&A2(cd(t));return{c(){o&&o.c(),r=ft()},l(t){o&&o.l(t),r=ft()},m(t,e){o&&o.m(t,e),R(t,r,e),i=!0},p(t,e){var n,r;0===t[18].children.length&&t[18].href&&(!1!==(null==(n=t[18].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[18].frontMatter)?void 0:r.sidebar_link))&&o.p(cd(t),e)},i(t){i||(O(o),i=!0)},o(t){D(o),i=!1},d(t){t&&S(r),o&&o.d(t)}}}function M2(t){let e,n,r,i,o,s;const a=[N2,P2],l=[];var c,u,d;n=!(c=t)[18].href||!1===(null==(u=c[18].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[18].frontMatter)?void 0:d.sidebar_link)?1:0,r=l[n]=a[n](t);let h=ue(t[18].children),f=[];for(let e=0;e<h.length;e+=1)f[e]=Ed(od(t,h,e));const p=t=>D(f[t],1,1,(()=>{f[t]=null}));return{c(){e=Y("div"),r.c(),i=rt();for(let t=0;t<f.length;t+=1)f[t].c();o=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var n=$(e);r.l(n),i=nt(n);for(let t=0;t<f.length;t+=1)f[t].l(n);o=nt(n),n.forEach(S),this.h()},h(){N(e,"class","flex flex-col pb-6")},m(t,r){R(t,e,r),l[n].m(e,null),z(e,i);for(let t=0;t<f.length;t+=1)f[t]&&f[t].m(e,null);z(e,o),s=!0},p(t,n){if(r.p(t,n),2432&n[0]){let r;for(h=ue(t[18].children),r=0;r<h.length;r+=1){const i=od(t,h,r);f[r]?(f[r].p(i,n),O(f[r],1)):(f[r]=Ed(i),f[r].c(),O(f[r],1),f[r].m(e,o))}for(jt(),r=h.length;r<f.length;r+=1)p(r);Lt()}},i(t){if(!s){O(r);for(let t=0;t<h.length;t+=1)O(f[t]);s=!0}},o(t){D(r),f=f.filter(Boolean);for(let t=0;t<f.length;t+=1)D(f[t]);s=!1},d(t){t&&S(e),l[n].d(),kn(f,t)}}}function P2(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&C2(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0,href:!0});var e=$(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(S),this.h()},h(){N(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize text-base-heading z-10"),N(r,"href",xt(t[18].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(O(l),s=!0)},o(t){D(l),s=!1},d(t){t&&S(r),l&&l.d()}}}function N2(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&j2(t);return{c(){r=Y("a"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=$(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(S),this.h()},h(){N(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group block capitalize hover:underline text-base-heading z-10"),N(r,"href",xt(t[18].href))},m(t,e){R(t,r,e),z(r,i),z(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(O(l),s=!0)},o(t){D(l),s=!1},d(t){t&&S(r),l&&l.d()}}}function C2(t){let e,n;return e=new Bn({props:{$$slots:{default:[F2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function F2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function j2(t){let e,n;return e=new Bn({props:{$$slots:{default:[L2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function L2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function R2(t){var e,n;let r,i,o,s,a=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",l=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&V2(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0});var e=$(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(S),this.h()},h(){N(r,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(t,e){R(t,r,e),z(r,i),z(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(O(l),s=!0)},o(t){D(l),s=!1},d(t){t&&S(r),l&&l.d()}}}function U2(t){var e,n;let r,i,o,s,a,l=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",c=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&z2(t);return{c(){r=Y("a"),i=wt(l),o=rt(),c&&c.c(),this.h()},l(t){r=q(t,"A",{href:!0,class:!0});var e=$(r);i=vt(e,l),o=nt(e),c&&c.l(e),e.forEach(S),this.h()},h(){N(r,"href",xt(t[21].href)),N(r,"class",s="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content"))},m(t,e){R(t,r,e),z(r,i),z(r,o),c&&c.m(r,null),a=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&c.p(t,e),(!a||256&e[0]&&s!==(s="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(r,"class",s)},i(t){a||(O(c),a=!0)},o(t){D(c),a=!1},d(t){t&&S(r),c&&c.d()}}}function V2(t){let e,n;return e=new Bn({props:{$$slots:{default:[x2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function x2(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function z2(t){let e,n;return e=new Bn({props:{$$slots:{default:[W2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function W2(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function Od(t){let e,n,r=ue(t[21].children),i=[];for(let e=0;e<r.length;e+=1)i[e]=Dd(sd(t,r,e));const o=t=>D(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=ft()},l(t){for(let e=0;e<i.length;e+=1)i[e].l(t);e=ft()},m(t,r){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,r);R(t,e,r),n=!0},p(t,n){if(2304&n[0]){let s;for(r=ue(t[21].children),s=0;s<r.length;s+=1){const o=sd(t,r,s);i[s]?(i[s].p(o,n),O(i[s],1)):(i[s]=Dd(o),i[s].c(),O(i[s],1),i[s].m(e.parentNode,e))}for(jt(),s=r.length;s<i.length;s+=1)o(s);Lt()}},i(t){if(!n){for(let t=0;t<r.length;t+=1)O(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let t=0;t<i.length;t+=1)D(i[t]);n=!1},d(t){t&&S(e),kn(i,t)}}}function H2(t){var e,n;let r,i,o,s,a,l,c,u=((null==(e=t[24].frontMatter)?void 0:e.title)??t[24].label)+"",d=(null==(n=t[24].frontMatter)?void 0:n.sidebar_badge)&&q2(t),h=t[27]&&kd(t);return{c(){r=Y("div"),i=Y("a"),o=wt(u),s=rt(),d&&d.c(),l=rt(),h&&h.c(),this.h()},l(t){r=q(t,"DIV",{class:!0});var e=$(r);i=q(e,"A",{href:!0,class:!0});var n=$(i);o=vt(n,u),s=nt(n),d&&d.l(n),n.forEach(S),l=nt(e),h&&h.l(e),e.forEach(S),this.h()},h(){N(i,"href",xt(t[24].href)),N(i,"class",a="group inline-block w-full capitalize transition-all duration-200 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),N(r,"class","relative py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 border-l ml-[1px] transition-all duration-200 hover:border-base-content")},m(t,e){R(t,r,e),z(r,i),z(i,o),z(i,s),d&&d.m(i,null),z(r,l),h&&h.m(r,null),c=!0},p(t,e){var n;null!=(n=t[24].frontMatter)&&n.sidebar_badge&&d.p(t,e),(!c||256&e[0]&&a!==(a="group inline-block w-full capitalize transition-all duration-200 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&N(i,"class",a),t[27]?h?256&e[0]&&O(h,1):(h=kd(t),h.c(),O(h,1),h.m(r,null)):h&&(jt(),D(h,1,1,(()=>{h=null})),Lt())},i(t){c||(O(d),O(h),c=!0)},o(t){D(d),D(h),c=!1},d(t){t&&S(r),d&&d.d(),h&&h.d()}}}function q2(t){let e,n;return e=new Bn({props:{$$slots:{default:[Y2]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function Y2(t){let e,n=t[24].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){R(t,e,n)},p:Qt,d(t){t&&S(e)}}}function kd(t){let e,n,r,i;return{c(){e=Y("div"),this.h()},l(t){e=q(t,"DIV",{class:!0}),$(e).forEach(S),this.h()},h(){N(e,"class","absolute top-0 -left-[1px] w-[1px] h-full bg-primary")},m(t,n){R(t,e,n),i=!0},i(o){i||(o&&On((()=>{i&&(r&&r.end(1),n=Hi(e,t[9],{key:"trigger"}),n.start())})),i=!0)},o(o){n&&n.invalidate(),o&&(r=es(e,t[10],{key:"trigger"})),i=!1},d(t){t&&S(e),t&&r&&r.end()}}}function Dd(t){var e,n;let r,i,o=t[24].href&&(!1!==(null==(e=t[24].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[24].frontMatter)?void 0:n.sidebar_link))&&H2(ad(t));return{c(){o&&o.c(),r=ft()},l(t){o&&o.l(t),r=ft()},m(t,e){o&&o.m(t,e),R(t,r,e),i=!0},p(t,e){var n,r;t[24].href&&(!1!==(null==(n=t[24].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[24].frontMatter)?void 0:r.sidebar_link))&&o.p(ad(t),e)},i(t){i||(O(o),i=!0)},o(t){D(o),i=!1},d(t){t&&S(r),o&&o.d(t)}}}function Ed(t){let e,n,r,i,o;const s=[U2,R2],a=[];function l(t,e){return 0===e?a2(t):t}var c,u,d;e=!(c=t)[21].href||!1===(null==(u=c[21].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[21].frontMatter)?void 0:d.sidebar_link)?1:0,n=a[e]=s[e](l(t,e));let h=t[21].children.length>0&&t[7]>2&&Od(t);return{c(){n.c(),r=rt(),h&&h.c(),i=ft()},l(t){n.l(t),r=nt(t),h&&h.l(t),i=ft()},m(t,n){a[e].m(t,n),R(t,r,n),h&&h.m(t,n),R(t,i,n),o=!0},p(t,r){n.p(l(t,e),r),t[21].children.length>0&&t[7]>2?h?(h.p(t,r),128&r[0]&&O(h,1)):(h=Od(t),h.c(),O(h,1),h.m(i.parentNode,i)):h&&(jt(),D(h,1,1,(()=>{h=null})),Lt())},i(t){o||(O(n),O(h),o=!0)},o(t){D(n),D(h),o=!1},d(t){t&&(S(r),S(i)),a[e].d(t),h&&h.d(t)}}}function Ad(t){let e,n,r=t[18].children.length>0&&M2(t);return{c(){r&&r.c(),e=ft()},l(t){r&&r.l(t),e=ft()},m(t,i){r&&r.m(t,i),R(t,e,i),n=!0},p(t,e){t[18].children.length>0&&r.p(t,e)},i(t){n||(O(r),n=!0)},o(t){D(r),n=!1},d(t){t&&S(e),r&&r.d(t)}}}function Td(t){let e,n='<a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a>';return{c(){e=Y("div"),e.innerHTML=n,this.h()},l(t){e=q(t,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-fworv4"!==yr(e)&&(e.innerHTML=n),this.h()},h(){N(e,"class","fixed bottom-0 text-xs py-2")},m(t,n){R(t,e,n)},d(t){t&&S(e)}}}function K2(t){let e,n,r,i,o,s=t[0]&&gd(t),a=!t[0]&&Sd(t),l=t[4]&&Td();return{c(){s&&s.c(),e=rt(),n=Y("aside"),a&&a.c(),r=rt(),l&&l.c(),this.h()},l(t){s&&s.l(t),e=nt(t),n=q(t,"ASIDE",{class:!0});var i=$(n);a&&a.l(i),r=nt(i),l&&l.l(i),i.forEach(S),this.h()},h(){N(n,"class",i="w-48 flex-none "+("hide"===t[6]?"hidden":"hidden md:flex"))},m(t,i){s&&s.m(t,i),R(t,e,i),R(t,n,i),a&&a.m(n,null),z(n,r),l&&l.m(n,null),o=!0},p(t,c){t[0]?s?(s.p(t,c),1&c[0]&&O(s,1)):(s=gd(t),s.c(),O(s,1),s.m(e.parentNode,e)):s&&(jt(),D(s,1,1,(()=>{s=null})),Lt()),t[0]?a&&(jt(),D(a,1,1,(()=>{a=null})),Lt()):a?(a.p(t,c),1&c[0]&&O(a,1)):(a=Sd(t),a.c(),O(a,1),a.m(n,r)),t[4]?l||(l=Td(),l.c(),l.m(n,null)):l&&(l.d(1),l=null),(!o||64&c[0]&&i!==(i="w-48 flex-none "+("hide"===t[6]?"hidden":"hidden md:flex")))&&N(n,"class",i)},i(t){o||(O(s),O(a),o=!0)},o(t){D(s),D(a),o=!1},d(t){t&&(S(e),S(n)),s&&s.d(t),a&&a.d(),l&&l.d()}}}function vg(t){Object.keys(t.children).forEach((function(e){var n;const r=t.children[e];vg(r),(!r.label&&!r.href||0===r.children.length&&!1===(null==(n=r.frontMatter)?void 0:n.sidebar_link))&&delete t.children[e]}))}function wg(t){return t.children=Object.values(t.children).sort(((t,e)=>{var n,r,i,o;return isNaN(null==(n=t.frontMatter)?void 0:n.sidebar_position)||isNaN(null==(r=e.frontMatter)?void 0:r.sidebar_position)?isNaN(null==(i=t.frontMatter)?void 0:i.sidebar_position)?isNaN(null==(o=e.frontMatter)?void 0:o.sidebar_position)?t.label.localeCompare(e.label):1:-1:t.frontMatter.sidebar_position-e.frontMatter.sidebar_position||t.label.localeCompare(e.label)})),t.children.forEach(wg),t}function $2(t,e,n){let r;Ae(t,Ma,(t=>n(8,r=t)));let{fileTree:i}=e,{title:o}=e,{logo:s}=e,{homePageName:a}=e,{builtWithEvidence:l}=e,{hideHeader:c=!1}=e,{sidebarFrontMatter:u}=e,{sidebarDepth:d=3}=e;const[h,f]=Qg({duration:200,easing:Gg});i=structuredClone(i),vg(i),i=wg(i);let p=null==i?void 0:i.children,{mobileSidebarOpen:m=!1}=e;return h_((()=>{{let t=document.querySelector("#mobileScrollable");m?Zg(t):Xg(t)}})),t.$$set=t=>{"fileTree"in t&&n(12,i=t.fileTree),"title"in t&&n(1,o=t.title),"logo"in t&&n(2,s=t.logo),"homePageName"in t&&n(3,a=t.homePageName),"builtWithEvidence"in t&&n(4,l=t.builtWithEvidence),"hideHeader"in t&&n(5,c=t.hideHeader),"sidebarFrontMatter"in t&&n(6,u=t.sidebarFrontMatter),"sidebarDepth"in t&&n(7,d=t.sidebarDepth),"mobileSidebarOpen"in t&&n(0,m=t.mobileSidebarOpen)},[m,o,s,a,l,c,u,d,r,h,f,p,i,()=>n(0,m=!1),()=>n(0,m=!1),()=>{n(0,m=!1)},()=>n(0,m=!1),()=>n(0,m=!1)]}class J2 extends ae{constructor(t){super(),le(this,t,$2,K2,ee,{fileTree:12,title:1,logo:2,homePageName:3,builtWithEvidence:4,hideHeader:5,sidebarFrontMatter:6,sidebarDepth:7,mobileSidebarOpen:0},null,[-1,-1])}}function Bd(t,e,n){const r=t.slice();return r[5]=e[n],r}function Md(t){let e,n,r,i="On this page",o=ue(t[0]),s=[];for(let e=0;e<o.length;e+=1)s[e]=Pd(Bd(t,o,e));return{c(){e=Y("span"),e.textContent=i,n=rt();for(let t=0;t<s.length;t+=1)s[t].c();r=ft(),this.h()},l(t){e=q(t,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-14mun4z"!==yr(e)&&(e.textContent=i),n=nt(t);for(let e=0;e<s.length;e+=1)s[e].l(t);r=ft(),this.h()},h(){N(e,"class","block text-xs sticky top-0 mb-2 bg-base-100 shadow-base-100 font-medium")},m(t,i){R(t,e,i),R(t,n,i);for(let e=0;e<s.length;e+=1)s[e]&&s[e].m(t,i);R(t,r,i)},p(t,e){if(3&e){let n;for(o=ue(t[0]),n=0;n<o.length;n+=1){const i=Bd(t,o,n);s[n]?s[n].p(i,e):(s[n]=Pd(i),s[n].c(),s[n].m(r.parentNode,r))}for(;n<s.length;n+=1)s[n].d(1);s.length=o.length}},d(t){t&&(S(e),S(n),S(r)),kn(s,t)}}}function Pd(t){let e,n,r,i,o,s=t[5].innerText+"";return{c(){e=Y("a"),n=wt(s),r=rt(),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var i=$(e);n=vt(i,s),r=nt(i),i.forEach(S),this.h()},h(){N(e,"href",i="#"+t[5].id),N(e,"class",o=t[1][t[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")},m(t,i){R(t,e,i),z(e,n),z(e,r)},p(t,r){1&r&&s!==(s=t[5].innerText+"")&&Le(n,s),1&r&&i!==(i="#"+t[5].id)&&N(e,"href",i),1&r&&o!==(o=t[1][t[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")&&N(e,"class",o)},d(t){t&&S(e)}}}function Q2(t){let e,n=t[0]&&t[0].length>1&&Md(t);return{c(){n&&n.c(),e=ft()},l(t){n&&n.l(t),e=ft()},m(t,r){n&&n.m(t,r),R(t,e,r)},p(t,[r]){t[0]&&t[0].length>1?n?n.p(t,r):(n=Md(t),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:Qt,o:Qt,d(t){t&&S(e),n&&n.d(t)}}}function G2(t,e,n){let r,i=[];function o(){n(0,i=Array.from(document.querySelectorAll("h1.markdown, h2.markdown, h3.markdown")))}return Pr((()=>{o(),r=new MutationObserver((()=>{o()})),i.forEach((t=>{r.observe(t,{subtree:!0,characterData:!0,childList:!0})}))})),m_((()=>{null==r||r.disconnect()})),[i,{h1:"mt-3 font-semibold block bg-base-100 shadow shadow-base-100",h2:"pl-0 text-base-content-muted",h3:"pl-4 text-base-content-muted"}]}class X2 extends ae{constructor(t){super(),le(this,t,G2,Q2,ee,{})}}function Nd(t){let e,n,r;return n=new X2({}),{c(){e=Y("div"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"class","fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"),Nr(e,"top-8",t[0])},m(t,i){R(t,e,i),at(n,e,null),r=!0},p(t,n){(!r||1&n)&&Nr(e,"top-8",t[0])},i(t){r||(O(n.$$.fragment,t),r=!0)},o(t){D(n.$$.fragment,t),r=!1},d(t){t&&S(e),st(n)}}}function Z2(t){let e,n,r=!t[1]&&t[2].data.isUserPage&&Nd(t);return{c(){e=Y("aside"),r&&r.c(),this.h()},l(t){e=q(t,"ASIDE",{class:!0});var n=$(e);r&&r.l(n),n.forEach(S),this.h()},h(){N(e,"class","hidden lg:block w-48")},m(t,i){R(t,e,i),r&&r.m(e,null),n=!0},p(t,[n]){!t[1]&&t[2].data.isUserPage?r?(r.p(t,n),6&n&&O(r,1)):(r=Nd(t),r.c(),O(r,1),r.m(e,null)):r&&(jt(),D(r,1,1,(()=>{r=null})),Lt())},i(t){n||(O(r),n=!0)},o(t){D(r),n=!1},d(t){t&&S(e),r&&r.d()}}}function tS(t,e,n){let r,i;Ae(t,yh,(t=>n(1,r=t))),Ae(t,Ma,(t=>n(2,i=t)));let{hideHeader:o=!1}=e;return t.$$set=t=>{"hideHeader"in t&&n(0,o=t.hideHeader)},[o,r,i]}class eS extends ae{constructor(t){super(),le(this,t,tS,Z2,ee,{hideHeader:0})}}function Cd(t,e,n){const r=t.slice();return r[3]=e[n],r[5]=n,r}function nS(t){let e,n,r,i,o=t[3].title+"";return{c(){e=Y("a"),n=wt(o),r=rt(),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var i=$(e);n=vt(i,o),r=nt(i),i.forEach(S),this.h()},h(){N(e,"href",i=xt(t[3].href)),N(e,"class","hover:underline")},m(t,i){R(t,e,i),z(e,n),z(e,r)},p(t,r){1&r&&o!==(o=t[3].title+"")&&Le(n,o),1&r&&i!==(i=xt(t[3].href))&&N(e,"href",i)},i:Qt,o:Qt,d(t){t&&S(e)}}}function rS(t){let e,n,r,i;function o(t,e){return t[3].href?oS:iS}e=new Tn({props:{src:t_,size:"12px",theme:"solid"}});let s=o(t),a=s(t);return{c(){ct(e.$$.fragment),n=rt(),a.c(),r=ft()},l(t){lt(e.$$.fragment,t),n=nt(t),a.l(t),r=ft()},m(t,o){at(e,t,o),R(t,n,o),a.m(t,o),R(t,r,o),i=!0},p(t,e){s===(s=o(t))&&a?a.p(t,e):(a.d(1),a=s(t),a&&(a.c(),a.m(r.parentNode,r)))},i(t){i||(O(e.$$.fragment,t),i=!0)},o(t){D(e.$$.fragment,t),i=!1},d(t){t&&(S(n),S(r)),st(e,t),a.d(t)}}}function iS(t){let e,n,r=t[3].title+"";return{c(){e=Y("span"),n=wt(r),this.h()},l(t){e=q(t,"SPAN",{class:!0});var i=$(e);n=vt(i,r),i.forEach(S),this.h()},h(){N(e,"class","cursor-default")},m(t,r){R(t,e,r),z(e,n)},p(t,e){1&e&&r!==(r=t[3].title+"")&&Le(n,r)},d(t){t&&S(e)}}}function oS(t){let e,n,r,i=t[3].title+"";return{c(){e=Y("a"),n=wt(i),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var r=$(e);n=vt(r,i),r.forEach(S),this.h()},h(){N(e,"href",r=xt(t[3].href)),N(e,"class","hover:underline")},m(t,r){R(t,e,r),z(e,n)},p(t,o){1&o&&i!==(i=t[3].title+"")&&Le(n,i),1&o&&r!==(r=xt(t[3].href))&&N(e,"href",r)},d(t){t&&S(e)}}}function Fd(t){let e,n,r,i;const o=[rS,nS],s=[];return e=t[5]>0?0:1,n=s[e]=o[e](t),{c(){n.c(),r=ft()},l(t){n.l(t),r=ft()},m(t,n){s[e].m(t,n),R(t,r,n),i=!0},p(t,e){n.p(t,e)},i(t){i||(O(n),i=!0)},o(t){D(n),i=!1},d(t){t&&S(r),s[e].d(t)}}}function sS(t){let e,n,r,i=ue(t[0]),o=[];for(let e=0;e<i.length;e+=1)o[e]=Fd(Cd(t,i,e));const s=t=>D(o[t],1,1,(()=>{o[t]=null}));return{c(){e=Y("div"),n=Y("div");for(let t=0;t<o.length;t+=1)o[t].c();this.h()},l(t){e=q(t,"DIV",{class:!0});var r=$(e);n=q(r,"DIV",{class:!0});var i=$(n);for(let t=0;t<o.length;t+=1)o[t].l(i);i.forEach(S),r.forEach(S),this.h()},h(){N(n,"class","inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"),N(e,"class","flex items-start mt-0 whitespace-nowrap overflow-auto")},m(t,i){R(t,e,i),z(e,n);for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(n,null);r=!0},p(t,[e]){if(1&e){let r;for(i=ue(t[0]),r=0;r<i.length;r+=1){const s=Cd(t,i,r);o[r]?(o[r].p(s,e),O(o[r],1)):(o[r]=Fd(s),o[r].c(),O(o[r],1),o[r].m(n,null))}for(jt(),r=i.length;r<o.length;r+=1)s(r);Lt()}},i(t){if(!r){for(let t=0;t<i.length;t+=1)O(o[t]);r=!0}},o(t){o=o.filter(Boolean);for(let t=0;t<o.length;t+=1)D(o[t]);r=!1},d(t){t&&S(e),kn(o,t)}}}function aS(t,e){if("/"===t)return e;const n=t.replace(mh.deployment.basePath,"").split("/").slice(1);let r=e;for(let t of n)if(r=r.children[t]?r.children[t]:Object.values(r.children).find((t=>t.isTemplated)),!r)return null;return r}function lS(t,e){const n=[{href:"/",title:"Home"}];t.forEach(((e,r)=>{""!=e&&`/${e}`!==mh.deployment.basePath&&n.push({href:"/"+t.slice(0,r+1).join("/"),title:decodeURIComponent(e.replace(/_/g," ").replace(/-/g," "))})})),n.length>3&&n.splice(1,n.length-3,{href:n.slice(-3)[0].href,title:"..."});for(const t of n)if("/"===t.href)t.href=xt("/"),t.title="Home";else{const n=aS(t.href,e);n&&n.isPage?t.title=n.title??t.title:t.href=null}return n}function cS(t,e,n){let r,i;Ae(t,Ma,(t=>n(2,i=t)));let{fileTree:o}=e;return t.$$set=t=>{"fileTree"in t&&n(1,o=t.fileTree)},t.$$.update=()=>{6&t.$$.dirty&&n(0,r=lS(i.url.pathname.split("/").slice(1),o))},[r,o,i]}class uS extends ae{constructor(t){super(),le(this,t,cS,sS,ee,{fileTree:1})}}function jd(t){let e,n,r,i,o,s,a,l,c,u,d,h="Error",f='<a href="https://docs.evidence.dev" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">docs</a> <a href="https://evidencedev.slack.com/join/shared_invite/zt-uda6wp6a-hP6Qyz0LUOddwpXW5qG03Q#/shared-invite/email" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">slack</a> <a href="mailto:<EMAIL>" class="hover:text-base-content-muted transition-colors duration-200">email</a>';return{c(){e=Y("div"),n=Y("div"),r=Y("h1"),r.textContent=h,i=rt(),o=Y("p"),s=wt(t[1]),a=rt(),l=Y("div"),l.innerHTML=f,this.h()},l(c){e=q(c,"DIV",{class:!0});var u=$(e);n=q(u,"DIV",{class:!0});var d=$(n);r=q(d,"H1",{class:!0,"data-svelte-h":!0}),"svelte-1wczc15"!==yr(r)&&(r.textContent=h),i=nt(d),o=q(d,"P",{class:!0});var p=$(o);s=vt(p,t[1]),p.forEach(S),a=nt(d),l=q(d,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-vfh8n7"!==yr(l)&&(l.innerHTML=f),d.forEach(S),u.forEach(S),this.h()},h(){N(r,"class","text-2xl font-bold tracking-wide border-b pb-4 border-base-300"),N(o,"class","text-xl mt-6 leading-relaxed select-text"),N(l,"class","absolute bottom-0 flex items-end gap-4 text-lg mb-6"),N(n,"class","relative min-w-full h-screen bg-gradient-to-b from-base-200 to-base-300 rounded-lg border-t-8 border-negative shadow-xl p-8"),N(e,"class","fixed flex flex-col z-50 h-screen w-screen bg-base-100/50 justify-center items-center py-20 px-10 sm:px-20 select-none backdrop-blur-sm")},m(t,c){R(t,e,c),z(e,n),z(n,r),z(n,i),z(n,o),z(o,s),z(n,a),z(n,l),d=!0},p(t,e){(!d||2&e)&&Le(s,t[1])},i(t){d||(t&&On((()=>{d&&(c||(c=In(n,ei,{y:100,duration:300},!0)),c.run(1))})),t&&On((()=>{d&&(u||(u=In(e,xo,{duration:100},!0)),u.run(1))})),d=!0)},o(t){t&&(c||(c=In(n,ei,{y:100,duration:300},!1)),c.run(0)),t&&(u||(u=In(e,xo,{duration:100},!1)),u.run(0)),d=!1},d(t){t&&S(e),t&&c&&c.end(),t&&u&&u.end()}}}function fS(t){let e,n=t[0]&&jd(t);return{c(){n&&n.c(),e=ft()},l(t){n&&n.l(t),e=ft()},m(t,r){n&&n.m(t,r),R(t,e,r)},p(t,[r]){t[0]?n?(n.p(t,r),1&r&&O(n,1)):(n=jd(t),n.c(),O(n,1),n.m(e.parentNode,e)):n&&(jt(),D(n,1,1,(()=>{n=null})),Lt())},i(t){O(n)},o(t){D(n)},d(t){t&&S(e),n&&n.d(t)}}}function dS(t,e,n){return[!1,void 0]}class hS extends ae{constructor(t){super(),le(this,t,dS,fS,ee,{})}}function mS(t){let e,n,r=JSON.stringify(t[0],null,2)+"";return{c(){e=Y("pre"),n=wt(r),this.h()},l(t){e=q(t,"PRE",{class:!0});var i=$(e);n=vt(i,r),i.forEach(S),this.h()},h(){N(e,"class","text-xs px-2 py-2 bg-base-200 my-2")},m(t,r){R(t,e,r),z(e,n)},p(t,[e]){1&e&&r!==(r=JSON.stringify(t[0],null,2)+"")&&Le(n,r)},i:Qt,o:Qt,d(t){t&&S(e)}}}function pS(t,e,n){let r;const i=ph();return Ae(t,i,(t=>n(0,r=t))),[r,i]}class gS extends ae{constructor(t){super(),le(this,t,pS,mS,ee,{})}}function _S(t){const e=[{type:"unchanged",content:"{"}];function n(t,e){return e.reduce(((t,e)=>null==t?void 0:t[e]),t)}return function r(i,o){const s=Object.keys(i);s.forEach(((a,l)=>{const c=n(t.added,o)??{},u=n(t.deleted,o)??{},d=n(t.updated,o)??{};let h="unchanged";a in c&&(h="added"),a in u&&(h="deleted"),a in d&&(h="updated");const f=(t,e=!1)=>{const n=`"${a}": `;let r=`${"  ".repeat(o.length+1)}${e?"":n}${t}`;return l<s.length-1&&(r+=","),r};if("object"==typeof i[a])return e.push({type:"updated"===h?"unchanged":h,content:f("{")}),r(i[a],o.concat(a)),void e.push({type:"updated"===h?"unchanged":h,content:f("}",!0)});{const r="deleted"===h?n(t.before,o)[a]:i[a];return void e.push({type:h,content:f(JSON.stringify(r))})}}))}(e_(t.before,t.after),[]),e.push({type:"unchanged",content:"}"}),e}const bS={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},yS=(t,e,n)=>{let r;const i=bS[t];return r="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),null!=n&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function gl(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const vS={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},wS={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},SS={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},IS={date:gl({formats:vS,defaultWidth:"full"}),time:gl({formats:wS,defaultWidth:"full"}),dateTime:gl({formats:SS,defaultWidth:"full"})},OS={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},kS=(t,e,n,r)=>OS[t];function co(t){return(e,n)=>{let r;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth,i=null!=n&&n.width?String(n.width):e;r=t.formattingValues[i]||t.formattingValues[e]}else{const e=t.defaultWidth,i=null!=n&&n.width?String(n.width):t.defaultWidth;r=t.values[i]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}const DS={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},ES={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},AS={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},TS={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},BS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},MS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},PS=(t,e)=>{const n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},NS={ordinalNumber:PS,era:co({values:DS,defaultWidth:"wide"}),quarter:co({values:ES,defaultWidth:"wide",argumentCallback:t=>t-1}),month:co({values:AS,defaultWidth:"wide"}),day:co({values:TS,defaultWidth:"wide"}),dayPeriod:co({values:BS,defaultWidth:"wide",formattingValues:MS,defaultFormattingWidth:"wide"})};function uo(t){return(e,n={})=>{const r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;const s=o[0],a=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(a)?FS(a,(t=>t.test(s))):CS(a,(t=>t.test(s)));let c;return c=t.valueCallback?t.valueCallback(l):l,c=n.valueCallback?n.valueCallback(c):c,{value:c,rest:e.slice(s.length)}}}function CS(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}function FS(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}function jS(t){return(e,n={})=>{const r=e.match(t.matchPattern);if(!r)return null;const i=r[0],o=e.match(t.parsePattern);if(!o)return null;let s=t.valueCallback?t.valueCallback(o[0]):o[0];return s=n.valueCallback?n.valueCallback(s):s,{value:s,rest:e.slice(i.length)}}}const LS=/^(\d+)(th|st|nd|rd)?/i,RS=/\d+/i,US={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},VS={any:[/^b/i,/^(a|c)/i]},xS={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},zS={any:[/1/i,/2/i,/3/i,/4/i]},WS={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},HS={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},qS={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},YS={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},KS={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},$S={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},JS={ordinalNumber:jS({matchPattern:LS,parsePattern:RS,valueCallback:t=>parseInt(t,10)}),era:uo({matchPatterns:US,defaultMatchWidth:"wide",parsePatterns:VS,defaultParseWidth:"any"}),quarter:uo({matchPatterns:xS,defaultMatchWidth:"wide",parsePatterns:zS,defaultParseWidth:"any",valueCallback:t=>t+1}),month:uo({matchPatterns:WS,defaultMatchWidth:"wide",parsePatterns:HS,defaultParseWidth:"any"}),day:uo({matchPatterns:qS,defaultMatchWidth:"wide",parsePatterns:YS,defaultParseWidth:"any"}),dayPeriod:uo({matchPatterns:KS,defaultMatchWidth:"any",parsePatterns:$S,defaultParseWidth:"any"})},Sg={code:"en-US",formatDistance:yS,formatLong:IS,formatRelative:kS,localize:NS,match:JS,options:{weekStartsOn:0,firstWeekContainsDate:1}};let QS={};function fs(){return QS}function GS(t){return e=>{const n=(t?Math[t]:Math.trunc)(e);return 0===n?0:n}}function ze(t){const e=Object.prototype.toString.call(t);return t instanceof Date||"object"==typeof t&&"[object Date]"===e?new t.constructor(+t):"number"==typeof t||"[object Number]"===e||"string"==typeof t||"[object String]"===e?new Date(t):new Date(NaN)}function Aa(t){const e=ze(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function XS(t,e){const n=ze(t),r=ze(e),i=n.getTime()-r.getTime();return i<0?-1:i>0?1:i}const Ig=6048e5,ZS=864e5,Ld=6e4,Rd=525600,Ud=43200,Vd=1440;function tI(t,e,n){const r=fs(),i=(null==n?void 0:n.locale)??r.locale??Sg,o=XS(t,e);if(isNaN(o))throw new RangeError("Invalid time value");const s=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:o});let a,l;o>0?(a=ze(e),l=ze(t)):(a=ze(t),l=ze(e));const c=GS((null==n?void 0:n.roundingMethod)??"round"),u=l.getTime()-a.getTime(),d=u/Ld,h=(u-(Aa(l)-Aa(a)))/Ld,f=null==n?void 0:n.unit;let p;if(p=f||(d<1?"second":d<60?"minute":d<Vd?"hour":h<Ud?"day":h<Rd?"month":"year"),"second"===p){const t=c(u/1e3);return i.formatDistance("xSeconds",t,s)}if("minute"===p){const t=c(d);return i.formatDistance("xMinutes",t,s)}if("hour"===p){const t=c(d/60);return i.formatDistance("xHours",t,s)}if("day"===p){const t=c(h/Vd);return i.formatDistance("xDays",t,s)}if("month"===p){const t=c(h/Ud);return 12===t&&"month"!==f?i.formatDistance("xYears",1,s):i.formatDistance("xMonths",t,s)}{const t=c(h/Rd);return i.formatDistance("xYears",t,s)}}function Lr(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}function eI(t){return Lr(t,Date.now())}function xd(t,e){return tI(t,eI(t),e)}function zd(t){const e=ze(t);return e.setHours(0,0,0,0),e}function nI(t,e){const n=zd(t),r=zd(e),i=+n-Aa(n),o=+r-Aa(r);return Math.round((i-o)/ZS)}function rI(t){const e=ze(t),n=Lr(t,0);return n.setFullYear(e.getFullYear(),0,1),n.setHours(0,0,0,0),n}function iI(t){const e=ze(t);return nI(e,rI(e))+1}function Xo(t,e){var n,r,i,o;const s=fs(),a=(null==e?void 0:e.weekStartsOn)??(null==(r=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:r.weekStartsOn)??s.weekStartsOn??(null==(o=null==(i=s.locale)?void 0:i.options)?void 0:o.weekStartsOn)??0,l=ze(t),c=l.getDay(),u=(c<a?7:0)+c-a;return l.setDate(l.getDate()-u),l.setHours(0,0,0,0),l}function Ta(t){return Xo(t,{weekStartsOn:1})}function Og(t){const e=ze(t),n=e.getFullYear(),r=Lr(t,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const i=Ta(r),o=Lr(t,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const s=Ta(o);return e.getTime()>=i.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}function oI(t){const e=Og(t),n=Lr(t,0);return n.setFullYear(e,0,4),n.setHours(0,0,0,0),Ta(n)}function sI(t){const e=ze(t),n=+Ta(e)-+oI(e);return Math.round(n/Ig)+1}function kg(t,e){var n,r,i,o;const s=ze(t),a=s.getFullYear(),l=fs(),c=(null==e?void 0:e.firstWeekContainsDate)??(null==(r=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:r.firstWeekContainsDate)??l.firstWeekContainsDate??(null==(o=null==(i=l.locale)?void 0:i.options)?void 0:o.firstWeekContainsDate)??1,u=Lr(t,0);u.setFullYear(a+1,0,c),u.setHours(0,0,0,0);const d=Xo(u,e),h=Lr(t,0);h.setFullYear(a,0,c),h.setHours(0,0,0,0);const f=Xo(h,e);return s.getTime()>=d.getTime()?a+1:s.getTime()>=f.getTime()?a:a-1}function aI(t,e){var n,r,i,o;const s=fs(),a=(null==e?void 0:e.firstWeekContainsDate)??(null==(r=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:r.firstWeekContainsDate)??s.firstWeekContainsDate??(null==(o=null==(i=s.locale)?void 0:i.options)?void 0:o.firstWeekContainsDate)??1,l=kg(t,e),c=Lr(t,0);return c.setFullYear(l,0,a),c.setHours(0,0,0,0),Xo(c,e)}function lI(t,e){const n=ze(t),r=+Xo(n,e)-+aI(n,e);return Math.round(r/Ig)+1}function oe(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const Ar={y(t,e){const n=t.getFullYear(),r=n>0?n:1-n;return oe("yy"===e?r%100:r,e.length)},M(t,e){const n=t.getMonth();return"M"===e?String(n+1):oe(n+1,2)},d:(t,e)=>oe(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>oe(t.getHours()%12||12,e.length),H:(t,e)=>oe(t.getHours(),e.length),m:(t,e)=>oe(t.getMinutes(),e.length),s:(t,e)=>oe(t.getSeconds(),e.length),S(t,e){const n=e.length,r=t.getMilliseconds();return oe(Math.trunc(r*Math.pow(10,n-3)),e.length)}},hi={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Wd={G:function(t,e,n){const r=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){const e=t.getFullYear(),r=e>0?e:1-e;return n.ordinalNumber(r,{unit:"year"})}return Ar.y(t,e)},Y:function(t,e,n,r){const i=kg(t,r),o=i>0?i:1-i;return"YY"===e?oe(o%100,2):"Yo"===e?n.ordinalNumber(o,{unit:"year"}):oe(o,e.length)},R:function(t,e){return oe(Og(t),e.length)},u:function(t,e){return oe(t.getFullYear(),e.length)},Q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return oe(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return oe(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){const r=t.getMonth();switch(e){case"M":case"MM":return Ar.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){const r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return oe(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){const i=lI(t,r);return"wo"===e?n.ordinalNumber(i,{unit:"week"}):oe(i,e.length)},I:function(t,e,n){const r=sI(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):oe(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):Ar.d(t,e)},D:function(t,e,n){const r=iI(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):oe(r,e.length)},E:function(t,e,n){const r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){const i=t.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return oe(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){const i=t.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return oe(o,e.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(i,{width:"narrow",context:"standalone"});case"cccccc":return n.day(i,{width:"short",context:"standalone"});default:return n.day(i,{width:"wide",context:"standalone"})}},i:function(t,e,n){const r=t.getDay(),i=0===r?7:r;switch(e){case"i":return String(i);case"ii":return oe(i,e.length);case"io":return n.ordinalNumber(i,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){const r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){const r=t.getHours();let i;switch(i=12===r?hi.noon:0===r?hi.midnight:r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(t,e,n){const r=t.getHours();let i;switch(i=r>=17?hi.evening:r>=12?hi.afternoon:r>=4?hi.morning:hi.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return Ar.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):Ar.H(t,e)},K:function(t,e,n){const r=t.getHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):oe(r,e.length)},k:function(t,e,n){let r=t.getHours();return 0===r&&(r=24),"ko"===e?n.ordinalNumber(r,{unit:"hour"}):oe(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):Ar.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):Ar.s(t,e)},S:function(t,e){return Ar.S(t,e)},X:function(t,e,n){const r=t.getTimezoneOffset();if(0===r)return"Z";switch(e){case"X":return qd(r);case"XXXX":case"XX":return Gr(r);default:return Gr(r,":")}},x:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"x":return qd(r);case"xxxx":case"xx":return Gr(r);default:return Gr(r,":")}},O:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+Hd(r,":");default:return"GMT"+Gr(r,":")}},z:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+Hd(r,":");default:return"GMT"+Gr(r,":")}},t:function(t,e,n){return oe(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return oe(t.getTime(),e.length)}};function Hd(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),i=Math.trunc(r/60),o=r%60;return 0===o?n+String(i):n+String(i)+e+oe(o,2)}function qd(t,e){return t%60==0?(t>0?"-":"+")+oe(Math.abs(t)/60,2):Gr(t,e)}function Gr(t,e=""){const n=t>0?"-":"+",r=Math.abs(t);return n+oe(Math.trunc(r/60),2)+e+oe(r%60,2)}const Yd=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},Dg=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},cI=(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],r=n[1],i=n[2];if(!i)return Yd(t,e);let o;switch(r){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;default:o=e.dateTime({width:"full"})}return o.replace("{{date}}",Yd(r,e)).replace("{{time}}",Dg(i,e))},uI={p:Dg,P:cI},fI=/^D+$/,dI=/^Y+$/,hI=["D","DD","YY","YYYY"];function mI(t){return fI.test(t)}function pI(t){return dI.test(t)}function gI(t,e,n){const r=_I(t,e,n);if(console.warn(r),hI.includes(t))throw new RangeError(r)}function _I(t,e,n){const r="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function bI(t){return t instanceof Date||"object"==typeof t&&"[object Date]"===Object.prototype.toString.call(t)}function yI(t){if(!bI(t)&&"number"!=typeof t)return!1;const e=ze(t);return!isNaN(Number(e))}const vI=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,wI=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,SI=/^'([^]*?)'?$/,II=/''/g,OI=/[a-zA-Z]/;function Kd(t,e,n){var r,i,o,s;const a=fs(),l=a.locale??Sg,c=a.firstWeekContainsDate??(null==(i=null==(r=a.locale)?void 0:r.options)?void 0:i.firstWeekContainsDate)??1,u=a.weekStartsOn??(null==(s=null==(o=a.locale)?void 0:o.options)?void 0:s.weekStartsOn)??0,d=ze(t);if(!yI(d))throw new RangeError("Invalid time value");let h=e.match(wI).map((t=>{const e=t[0];return"p"===e||"P"===e?(0,uI[e])(t,l.formatLong):t})).join("").match(vI).map((t=>{if("''"===t)return{isToken:!1,value:"'"};const e=t[0];if("'"===e)return{isToken:!1,value:kI(t)};if(Wd[e])return{isToken:!0,value:t};if(e.match(OI))throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}}));l.localize.preprocessor&&(h=l.localize.preprocessor(d,h));const f={firstWeekContainsDate:c,weekStartsOn:u,locale:l};return h.map((n=>{if(!n.isToken)return n.value;const r=n.value;return(pI(r)||mI(r))&&gI(r,e,String(t)),(0,Wd[r[0]])(d,r,l.localize,f)})).join("")}function kI(t){const e=t.match(SI);return e?e[1].replace(II,"'"):t}function $d(t,e,n){const r=t.slice();return r[6]=e[n],r}function Jd(t){let e,n,r,i,o,s,a,l,c,u,d=t[4](t[6].type)+"",h=t[6].content+"";return{c(){e=Y("div"),n=Y("span"),r=wt(d),o=rt(),s=Y("div"),a=Y("pre"),l=wt(h),u=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var i=$(e);n=q(i,"SPAN",{class:!0});var c=$(n);r=vt(c,d),c.forEach(S),o=nt(i),s=q(i,"DIV",{class:!0});var f=$(s);a=q(f,"PRE",{class:!0});var p=$(a);l=vt(p,h),p.forEach(S),f.forEach(S),u=nt(i),i.forEach(S),this.h()},h(){N(n,"class",i=t[3][t[6].type]+" px-1 select-none"),N(a,"class","whitespace-pre-wrap"),N(s,"class",c=t[3][t[6].type]+" selection:bg-black/15"),N(e,"class","group contents")},m(t,i){R(t,e,i),z(e,n),z(n,r),z(e,o),z(e,s),z(s,a),z(a,l),z(e,u)},p(t,e){2&e&&d!==(d=t[4](t[6].type)+"")&&Le(r,d),2&e&&i!==(i=t[3][t[6].type]+" px-1 select-none")&&N(n,"class",i),2&e&&h!==(h=t[6].content+"")&&Le(l,h),2&e&&c!==(c=t[3][t[6].type]+" selection:bg-black/15")&&N(s,"class",c)},d(t){t&&S(e)}}}function DI(t){let e,n,r,i,o,s,a,l,c,u=Kd(t[0].asof,"HH:mm:ss")+"",d=ue(t[1]),h=[];for(let e=0;e<d.length;e+=1)h[e]=Jd($d(t,d,e));return{c(){e=Y("section"),n=Y("span"),r=wt("About "),i=wt(t[2]),o=wt(" ("),s=wt(u),a=wt(")"),l=rt(),c=Y("div");for(let t=0;t<h.length;t+=1)h[t].c();this.h()},l(d){e=q(d,"SECTION",{});var f=$(e);n=q(f,"SPAN",{});var p=$(n);r=vt(p,"About "),i=vt(p,t[2]),o=vt(p," ("),s=vt(p,u),a=vt(p,")"),p.forEach(S),l=nt(f),c=q(f,"DIV",{class:!0});var m=$(c);for(let t=0;t<h.length;t+=1)h[t].l(m);m.forEach(S),f.forEach(S),this.h()},h(){N(c,"class","font-mono text-xs grid grid-cols-[auto,1fr] text-[0.7rem] bg-base-200 p-2 select-text")},m(t,u){R(t,e,u),z(e,n),z(n,r),z(n,i),z(n,o),z(n,s),z(n,a),z(e,l),z(e,c);for(let t=0;t<h.length;t+=1)h[t]&&h[t].m(c,null)},p(t,[e]){if(4&e&&Le(i,t[2]),1&e&&u!==(u=Kd(t[0].asof,"HH:mm:ss")+"")&&Le(s,u),26&e){let n;for(d=ue(t[1]),n=0;n<d.length;n+=1){const r=$d(t,d,n);h[n]?h[n].p(r,e):(h[n]=Jd(r),h[n].c(),h[n].m(c,null))}for(;n<h.length;n+=1)h[n].d(1);h.length=d.length}},i:Qt,o:Qt,d(t){t&&S(e),kn(h,t)}}}function EI(t,e,n){let r,i,{diffData:o={added:{},deleted:{},updated:{},before:{},after:{},asof:new Date}}=e;const s=g_(null,(t=>{t(xd(o.asof,{addSuffix:!0,includeSeconds:!0}));const e=setInterval((()=>{t(xd(o.asof,{addSuffix:!0,includeSeconds:!0}))}),5e3);return()=>{clearInterval(e)}}));return Ae(t,s,(t=>n(2,i=t))),t.$$set=t=>{"diffData"in t&&n(0,o=t.diffData)},t.$$.update=()=>{1&t.$$.dirty&&n(1,r=_S(o))},[o,r,i,{added:"bg-positive/25",deleted:"bg-negative/25",updated:"bg-warning/25",unchanged:""},t=>{switch(t){case"added":return"+";case"deleted":return"-";case"updated":return"~";case"unchanged":return" ";default:return"?"}},s]}class AI extends ae{constructor(t){super(),le(this,t,EI,DI,ee,{diffData:0})}}function Qd(t,e,n){const r=t.slice();return r[2]=e[n],r}function Gd(t){let e,n,r,i;return n=new AI({props:{diffData:t[2]}}),{c(){e=Y("div"),ct(n.$$.fragment),r=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var i=$(e);lt(n.$$.fragment,i),r=nt(i),i.forEach(S),this.h()},h(){N(e,"class","my-4")},m(t,o){R(t,e,o),at(n,e,null),z(e,r),i=!0},p(t,e){const r={};2&e&&(r.diffData=t[2]),n.$set(r)},i(t){i||(O(n.$$.fragment,t),i=!0)},o(t){D(n.$$.fragment,t),i=!1},d(t){t&&S(e),st(n)}}}function TI(t){let e,n,r=ue(t[1].reverse()),i=[];for(let e=0;e<r.length;e+=1)i[e]=Gd(Qd(t,r,e));const o=t=>D(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=ft()},l(t){for(let e=0;e<i.length;e+=1)i[e].l(t);e=ft()},m(t,r){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,r);R(t,e,r),n=!0},p(t,[n]){if(2&n){let s;for(r=ue(t[1].reverse()),s=0;s<r.length;s+=1){const o=Qd(t,r,s);i[s]?(i[s].p(o,n),O(i[s],1)):(i[s]=Gd(o),i[s].c(),O(i[s],1),i[s].m(e.parentNode,e))}for(jt(),s=r.length;s<i.length;s+=1)o(s);Lt()}},i(t){if(!n){for(let t=0;t<r.length;t+=1)O(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let t=0;t<i.length;t+=1)D(i[t]);n=!1},d(t){t&&S(e),kn(i,t)}}}function BI(t,e,n){let r,i=Qt,o=()=>(i(),i=p_(s,(t=>n(1,r=t))),s);t.$$.on_destroy.push((()=>i()));let{history:s}=e;return o(),t.$$set=t=>{"history"in t&&o(n(0,s=t.history))},[s,r]}class MI extends ae{constructor(t){super(),le(this,t,BI,TI,ee,{history:0})}}function Xd(t,e,n){const r=t.slice();return r[12]=e[n][0],r[13]=e[n][1],r}function Zd(t){let e,n,r,i,o,s,a,l,c,u,d,h="Evidence Dev Tools";return r=new Tn({props:{src:t[0]?Js:yl,class:"w-4 h-4"}}),a=new E_({props:{$$slots:{default:[FI]},$$scope:{ctx:t}}}),{c(){e=Y("div"),n=Y("button"),ct(r.$$.fragment),i=rt(),o=Y("header"),o.textContent=h,s=rt(),ct(a.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var l=$(e);n=q(l,"BUTTON",{class:!0});var c=$(n);lt(r.$$.fragment,c),c.forEach(S),i=nt(l),o=q(l,"HEADER",{class:!0,"data-svelte-h":!0}),"svelte-ekyf9x"!==yr(o)&&(o.textContent=h),s=nt(l),lt(a.$$.fragment,l),l.forEach(S),this.h()},h(){N(n,"class","absolute right-4 top-4 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-30"),N(o,"class","text-xl font-bold mb-4"),N(e,"class","h-[calc(100vh-3rem)] w-96 bg-base-100 fixed overflow-auto right-0 top-12 px-4 py-4 z-10")},m(l,h){R(l,e,h),z(e,n),at(r,n,null),z(e,i),z(e,o),z(e,s),at(a,e,null),c=!0,u||(d=he(n,"click",t[7]),u=!0)},p(t,e){const n={};1&e&&(n.src=t[0]?Js:yl),r.$set(n);const i={};2054&e&&(i.$$scope={dirty:e,ctx:t}),a.$set(i)},i(t){c||(O(r.$$.fragment,t),O(a.$$.fragment,t),t&&On((()=>{c&&(l||(l=In(e,ei,{x:384,duration:250,delay:0},!0)),l.run(1))})),c=!0)},o(t){D(r.$$.fragment,t),D(a.$$.fragment,t),t&&(l||(l=In(e,ei,{x:384,duration:250,delay:0},!1)),l.run(0)),c=!1},d(t){t&&S(e),st(r),st(a),t&&l&&l.end(),u=!1,d()}}}function th(t,e){let n,r,i,o,s,a,l,c,u,d=e[13].id+"",h=e[13].hash+"";function f(){return e[8](e[13])}return{key:t,first:null,c(){n=Y("button"),r=Y("p"),i=wt(d),o=rt(),s=Y("p"),a=wt(h),l=rt(),this.h()},l(t){n=q(t,"BUTTON",{class:!0});var e=$(n);r=q(e,"P",{class:!0});var c=$(r);i=vt(c,d),c.forEach(S),o=nt(e),s=q(e,"P",{class:!0});var u=$(s);a=vt(u,h),u.forEach(S),l=nt(e),e.forEach(S),this.h()},h(){var t;N(r,"class","w-full text-left truncate"),N(s,"class","w-full text-right"),N(n,"class","flex justify-between w-full odd:bg-base-200/40 hover:bg-base-200"),Nr(n,"bg-negative",e[13].error),Nr(n,"bg-warning",null==(t=e[13].opts)?void 0:t.noResolve),this.first=n},m(t,e){R(t,n,e),z(n,r),z(r,i),z(n,o),z(n,s),z(s,a),z(n,l),c||(u=he(n,"click",f),c=!0)},p(t,r){var o;e=t,4&r&&d!==(d=e[13].id+"")&&Le(i,d),4&r&&h!==(h=e[13].hash+"")&&Le(a,h),4&r&&Nr(n,"bg-negative",e[13].error),4&r&&Nr(n,"bg-warning",null==(o=e[13].opts)?void 0:o.noResolve)},d(t){t&&S(n),c=!1,u()}}}function eh(t){let e,n;return e=new s_({props:{query:t[1]}}),e.$on("close",t[9]),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};2&n&&(r.query=t[1]),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function PI(t){let e,n,r,i,o=[],s=new Map,a=$c.isQuery(t[1]),l=ue(t[2].entries());const c=t=>t[12];for(let e=0;e<l.length;e+=1){let n=Xd(t,l,e),r=c(n);s.set(r,o[e]=th(r,n))}let u=a&&eh(t);return{c(){e=Y("section");for(let t=0;t<o.length;t+=1)o[t].c();n=rt(),u&&u.c(),r=ft(),this.h()},l(t){e=q(t,"SECTION",{class:!0});var i=$(e);for(let t=0;t<o.length;t+=1)o[t].l(i);i.forEach(S),n=nt(t),u&&u.l(t),r=ft(),this.h()},h(){N(e,"class","")},m(t,s){R(t,e,s);for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(e,null);R(t,n,s),u&&u.m(t,s),R(t,r,s),i=!0},p(t,n){6&n&&(l=ue(t[2].entries()),o=dh(o,n,c,1,t,l,s,e,o_,th,null,Xd)),2&n&&(a=$c.isQuery(t[1])),a?u?(u.p(t,n),2&n&&O(u,1)):(u=eh(t),u.c(),O(u,1),u.m(r.parentNode,r)):u&&(jt(),D(u,1,1,(()=>{u=null})),Lt())},i(t){i||(O(u),i=!0)},o(t){D(u),i=!1},d(t){t&&(S(e),S(n),S(r));for(let t=0;t<o.length;t+=1)o[t].d();u&&u.d(t)}}}function NI(t){let e,n;return e=new gS({props:{history:t[4]}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p:Qt,i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function CI(t){let e,n;return e=new MI({props:{history:t[4]}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p:Qt,i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function FI(t){let e,n,r,i,o,s;return e=new Ja({props:{title:"Inspect Queries",compact:!0,$$slots:{default:[PI]},$$scope:{ctx:t}}}),r=new Ja({props:{title:"Inspect Inputs",compact:!0,$$slots:{default:[NI]},$$scope:{ctx:t}}}),o=new Ja({props:{title:"View Input History",compact:!0,$$slots:{default:[CI]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment),n=rt(),ct(r.$$.fragment),i=rt(),ct(o.$$.fragment)},l(t){lt(e.$$.fragment,t),n=nt(t),lt(r.$$.fragment,t),i=nt(t),lt(o.$$.fragment,t)},m(t,a){at(e,t,a),R(t,n,a),at(r,t,a),R(t,i,a),at(o,t,a),s=!0},p(t,n){const i={};2054&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i);const s={};2048&n&&(s.$$scope={dirty:n,ctx:t}),r.$set(s);const a={};2048&n&&(a.$$scope={dirty:n,ctx:t}),o.$set(a)},i(t){s||(O(e.$$.fragment,t),O(r.$$.fragment,t),O(o.$$.fragment,t),s=!0)},o(t){D(e.$$.fragment,t),D(r.$$.fragment,t),D(o.$$.fragment,t),s=!1},d(t){t&&(S(n),S(i)),st(e,t),st(r,t),st(o,t)}}}function jI(t){let e,n,r,i,o,s;return n=new Tn({props:{src:yl,class:"w-4 h-4"}}),{c(){e=Y("button"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"BUTTON",{class:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"class","fixed right-4 top-16 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-0")},m(r,a){R(r,e,a),at(n,e,null),i=!0,o||(s=he(e,"click",t[10]),o=!0)},p:Qt,i(t){i||(O(n.$$.fragment,t),t&&On((()=>{i&&(r||(r=In(e,bl,{axis:"x"},!0)),r.run(1))})),i=!0)},o(t){D(n.$$.fragment,t),t&&(r||(r=In(e,bl,{axis:"x"},!1)),r.run(0)),i=!1},d(t){t&&S(e),st(n),t&&r&&r.end(),o=!1,s()}}}function LI(t){let e,n,r,i=n_(),o=t[0]&&Zd(t),s=i&&jI(t);const a=t[6].default,l=Oe(a,t,t[11],null);return{c(){o&&o.c(),e=rt(),s&&s.c(),n=rt(),l&&l.c()},l(t){o&&o.l(t),e=nt(t),s&&s.l(t),n=nt(t),l&&l.l(t)},m(t,i){o&&o.m(t,i),R(t,e,i),s&&s.m(t,i),R(t,n,i),l&&l.m(t,i),r=!0},p(t,[n]){t[0]?o?(o.p(t,n),1&n&&O(o,1)):(o=Zd(t),o.c(),O(o,1),o.m(e.parentNode,e)):o&&(jt(),D(o,1,1,(()=>{o=null})),Lt()),i&&s.p(t,n),l&&l.p&&(!r||2048&n)&&ke(l,a,t,t[11],r?Ee(a,t[11],n,null):De(t[11]),null)},i(t){r||(O(o),O(s),O(l,t),r=!0)},o(t){D(o),D(s),D(l,t),r=!1},d(t){t&&(S(e),S(n)),o&&o.d(t),s&&s.d(t),l&&l.d(t)}}}function RI(t,e,n){let r,i;Ae(t,r_,(t=>n(2,i=t)));let{$$slots:o={},$$scope:s}=e;i_(qe({}));let a,l=!1;__((()=>{})),Pr((()=>{const t=t=>{"Escape"===t.key&&(n(0,l=!1),t.stopPropagation()),"e"===t.key.toLowerCase()&&t.shiftKey&&(t.ctrlKey||t.metaKey)&&(n(0,l=!0),t.stopPropagation())};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)}));const c=ph();Ae(t,c,(t=>n(5,r=t)));const u=new u0;return t.$$set=t=>{"$$scope"in t&&n(11,s=t.$$scope)},t.$$.update=()=>{32&t.$$.dirty&&u.push(r)},[l,a,i,c,u,r,o,()=>n(0,l=!l),t=>n(1,a=t),()=>n(1,a=null),()=>n(0,l=!l),s]}class UI extends ae{constructor(t){super(),le(this,t,RI,LI,ee,{})}}function VI(t){let e;const n=t[0].default,r=Oe(n,t,t[1],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||2&i)&&ke(r,n,t,t[1],e?Ee(n,t[1],i,null):De(t[1]),null)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function xI(t){let e,n;return e=new UI({props:{$$slots:{default:[zI]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,n){const r={};2&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function zI(t){let e;const n=t[0].default,r=Oe(n,t,t[1],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||2&i)&&ke(r,n,t,t[1],e?Ee(n,t[1],i,null):De(t[1]),null)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function WI(t){let e,n,r,i;const o=[xI,VI],s=[];return e=1,n=s[1]=o[1](t),{c(){n.c(),r=ft()},l(t){n.l(t),r=ft()},m(t,e){s[1].m(t,e),R(t,r,e),i=!0},p(t,[e]){n.p(t,e)},i(t){i||(O(n),i=!0)},o(t){D(n),i=!1},d(t){t&&S(r),s[1].d(t)}}}function HI(t,e,n){let{$$slots:r={},$$scope:i}=e;return t.$$set=t=>{"$$scope"in t&&n(1,i=t.$$scope)},[r,i]}class qI extends ae{constructor(t){super(),le(this,t,HI,WI,ee,{})}}const YI=t=>({}),nh=t=>({});function rh(t){let e,n,r;function i(e){t[41](e)}let o={title:t[0],logo:t[1],lightLogo:t[2],darkLogo:t[3],neverShowQueries:t[4],fullWidth:t[21],maxWidth:t[20],hideSidebar:t[5],githubRepo:t[8],slackCommunity:t[11],xProfile:t[9],blueskyProfile:t[10],algolia:t[7],sidebarFrontMatter:t[14]};return void 0!==t[17]&&(o.mobileSidebarOpen=t[17]),e=new t2({props:o}),Kn.push((()=>bh(e,"mobileSidebarOpen",i))),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,n){at(e,t,n),r=!0},p(t,r){const i={};1&r[0]&&(i.title=t[0]),2&r[0]&&(i.logo=t[1]),4&r[0]&&(i.lightLogo=t[2]),8&r[0]&&(i.darkLogo=t[3]),16&r[0]&&(i.neverShowQueries=t[4]),2097152&r[0]&&(i.fullWidth=t[21]),1048576&r[0]&&(i.maxWidth=t[20]),32&r[0]&&(i.hideSidebar=t[5]),256&r[0]&&(i.githubRepo=t[8]),2048&r[0]&&(i.slackCommunity=t[11]),512&r[0]&&(i.xProfile=t[9]),1024&r[0]&&(i.blueskyProfile=t[10]),128&r[0]&&(i.algolia=t[7]),16384&r[0]&&(i.sidebarFrontMatter=t[14]),!n&&131072&r[0]&&(n=!0,i.mobileSidebarOpen=t[17],_h((()=>n=!1))),e.$set(i)},i(t){r||(O(e.$$.fragment,t),r=!0)},o(t){D(e.$$.fragment,t),r=!1},d(t){st(e,t)}}}function ih(t){let e,n,r,i;function o(e){t[42](e)}let s={fileTree:t[24],title:t[0],logo:t[1],homePageName:t[12],builtWithEvidence:t[6],hideHeader:t[19],sidebarFrontMatter:t[14],sidebarDepth:t[13]};return void 0!==t[17]&&(s.mobileSidebarOpen=t[17]),n=new J2({props:s}),Kn.push((()=>bh(n,"mobileSidebarOpen",o))),{c(){e=Y("div"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"class","print:hidden")},m(t,r){R(t,e,r),at(n,e,null),i=!0},p(t,e){const i={};1&e[0]&&(i.title=t[0]),2&e[0]&&(i.logo=t[1]),4096&e[0]&&(i.homePageName=t[12]),64&e[0]&&(i.builtWithEvidence=t[6]),524288&e[0]&&(i.hideHeader=t[19]),16384&e[0]&&(i.sidebarFrontMatter=t[14]),8192&e[0]&&(i.sidebarDepth=t[13]),!r&&131072&e[0]&&(r=!0,i.mobileSidebarOpen=t[17],_h((()=>r=!1))),n.$set(i)},i(t){i||(O(n.$$.fragment,t),i=!0)},o(t){D(n.$$.fragment,t),i=!1},d(t){t&&S(e),st(n)}}}function oh(t){let e,n,r="/settings"!==t[15].route.id&&sh(t);return{c(){e=Y("div"),r&&r.c(),this.h()},l(t){e=q(t,"DIV",{class:!0});var n=$(e);r&&r.l(n),n.forEach(S),this.h()},h(){N(e,"class","print:hidden")},m(t,i){R(t,e,i),r&&r.m(e,null),n=!0},p(t,n){"/settings"!==t[15].route.id?r?(r.p(t,n),32768&n[0]&&O(r,1)):(r=sh(t),r.c(),O(r,1),r.m(e,null)):r&&(jt(),D(r,1,1,(()=>{r=null})),Lt())},i(t){n||(O(r),n=!0)},o(t){D(r),n=!1},d(t){t&&S(e),r&&r.d()}}}function sh(t){let e,n;return e=new uS({props:{fileTree:t[24]}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p:Qt,i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function KI(t){let e,n;return e=new i2({}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p:Qt,i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function $I(t){let e,n;const r=t[40].content,i=Oe(r,t,t[43],nh);return{c(){e=Y("article"),i&&i.c(),this.h()},l(t){e=q(t,"ARTICLE",{id:!0,class:!0});var n=$(e);i&&i.l(n),n.forEach(S),this.h()},h(){N(e,"id","evidence-main-article"),N(e,"class","select-text markdown pb-10")},m(t,r){R(t,e,r),i&&i.m(e,null),n=!0},p(t,e){i&&i.p&&(!n||4096&e[1])&&ke(i,r,t,t[43],n?Ee(r,t[43],e,YI):De(t[43]),nh)},i(t){n||(O(i,t),n=!0)},o(t){D(i,t),n=!1},d(t){t&&S(e),i&&i.d(t)}}}function ah(t){let e,n,r;return n=new eS({props:{hideHeader:t[19]}}),{c(){e=Y("div"),ct(n.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=$(e);lt(n.$$.fragment,r),r.forEach(S),this.h()},h(){N(e,"class","print:hidden")},m(t,i){R(t,e,i),at(n,e,null),r=!0},p(t,e){const r={};524288&e[0]&&(r.hideHeader=t[19]),n.$set(r)},i(t){r||(O(n.$$.fragment,t),r=!0)},o(t){D(n.$$.fragment,t),r=!1},d(t){t&&S(e),st(n)}}}function lh(t){let e,n;return e=new n2({}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function JI(t){let e,n,r,i,o,s,a,l,c,u,d,h,f,p,m,b,y=!t[16]&&vl;n=new hS({});let g=!t[19]&&rh(t),v=!t[5]&&"never"!==t[14]&&"/settings"!==t[15].route.id&&ih(t),_=!t[22]&&"/settings"!==t[15].route.id&&oh(t);const w=[$I,KI],I=[];function x(t,e){return t[16]?1:0}c=x(t),u=I[c]=w[c](t);let E=!t[18]&&"/settings"!==t[15].route.id&&ah(t),k=y&&lh();return{c(){e=Y("div"),ct(n.$$.fragment),r=rt(),g&&g.c(),i=rt(),o=Y("div"),v&&v.c(),s=rt(),a=Y("main"),_&&_.c(),l=rt(),u.c(),h=rt(),E&&E.c(),p=rt(),k&&k.c(),m=ft(),this.h()},l(t){e=q(t,"DIV",{"data-sveltekit-preload-data":!0,class:!0});var c=$(e);lt(n.$$.fragment,c),r=nt(c),g&&g.l(c),i=nt(c),o=q(c,"DIV",{class:!0,style:!0});var d=$(o);v&&v.l(d),s=nt(d),a=q(d,"MAIN",{class:!0});var f=$(a);_&&_.l(f),l=nt(f),u.l(f),f.forEach(S),h=nt(d),E&&E.l(d),d.forEach(S),c.forEach(S),p=nt(t),k&&k.l(t),m=ft(),this.h()},h(){N(a,"class",d=("/settings"===t[15].route.id?"w-full mt-16 sm:mt-20 ":(t[5]||["hide","never"].includes(t[14])?"":"md:pl-8 ")+(t[18]?"":"md:pr-8 ")+(t[19]?t[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":t[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"),N(o,"class",f=(t[21]?"max-w-full ":t[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"),Qs(o,"max-width",t[20]+"px"),N(e,"data-sveltekit-preload-data",t[23]),N(e,"class","antialiased")},m(t,u){R(t,e,u),at(n,e,null),z(e,r),g&&g.m(e,null),z(e,i),z(e,o),v&&v.m(o,null),z(o,s),z(o,a),_&&_.m(a,null),z(a,l),I[c].m(a,null),z(o,h),E&&E.m(o,null),R(t,p,u),k&&k.m(t,u),R(t,m,u),b=!0},p(t,n){t[19]?g&&(jt(),D(g,1,1,(()=>{g=null})),Lt()):g?(g.p(t,n),524288&n[0]&&O(g,1)):(g=rh(t),g.c(),O(g,1),g.m(e,i)),t[5]||"never"===t[14]||"/settings"===t[15].route.id?v&&(jt(),D(v,1,1,(()=>{v=null})),Lt()):v?(v.p(t,n),49184&n[0]&&O(v,1)):(v=ih(t),v.c(),O(v,1),v.m(o,s)),t[22]||"/settings"===t[15].route.id?_&&(jt(),D(_,1,1,(()=>{_=null})),Lt()):_?(_.p(t,n),4227072&n[0]&&O(_,1)):(_=oh(t),_.c(),O(_,1),_.m(a,l));let r=c;c=x(t),c===r?I[c].p(t,n):(jt(),D(I[r],1,1,(()=>{I[r]=null})),Lt(),u=I[c],u?u.p(t,n):(u=I[c]=w[c](t),u.c()),O(u,1),u.m(a,null)),(!b||5029920&n[0]&&d!==(d=("/settings"===t[15].route.id?"w-full mt-16 sm:mt-20 ":(t[5]||["hide","never"].includes(t[14])?"":"md:pl-8 ")+(t[18]?"":"md:pr-8 ")+(t[19]?t[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":t[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"))&&N(a,"class",d),t[18]||"/settings"===t[15].route.id?E&&(jt(),D(E,1,1,(()=>{E=null})),Lt()):E?(E.p(t,n),294912&n[0]&&O(E,1)):(E=ah(t),E.c(),O(E,1),E.m(o,null)),(!b||3145728&n[0]&&f!==(f=(t[21]?"max-w-full ":t[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"))&&N(o,"class",f),(!b||1048576&n[0])&&Qs(o,"max-width",t[20]+"px"),98304&n[0]&&(y=!t[16]&&vl),y?k?98304&n[0]&&O(k,1):(k=lh(),k.c(),O(k,1),k.m(m.parentNode,m)):k&&(jt(),D(k,1,1,(()=>{k=null})),Lt())},i(t){b||(O(n.$$.fragment,t),O(g),O(v),O(_),O(u),O(E),O(k),b=!0)},o(t){D(n.$$.fragment,t),D(g),D(v),D(_),D(u),D(E),D(k),b=!1},d(t){t&&(S(e),S(p),S(m)),st(n),g&&g.d(),v&&v.d(),_&&_.d(),I[c].d(),E&&E.d(),k&&k.d(t)}}}function QI(t){let e,n,r,i,o;const s=t[40].default,a=Oe(s,t,t[43],null);return n=new b0({}),i=new qI({props:{$$slots:{default:[JI]},$$scope:{ctx:t}}}),{c(){a&&a.c(),e=rt(),ct(n.$$.fragment),r=rt(),ct(i.$$.fragment)},l(t){a&&a.l(t),e=nt(t),lt(n.$$.fragment,t),r=nt(t),lt(i.$$.fragment,t)},m(t,s){a&&a.m(t,s),R(t,e,s),at(n,t,s),R(t,r,s),at(i,t,s),o=!0},p(t,e){a&&a.p&&(!o||4096&e[1])&&ke(a,s,t,t[43],o?Ee(s,t[43],e,null):De(t[43]),null);const n={};8388607&e[0]|4096&e[1]&&(n.$$scope={dirty:e,ctx:t}),i.$set(n)},i(t){o||(O(a,t),O(n.$$.fragment,t),O(i.$$.fragment,t),o=!0)},o(t){D(a,t),D(n.$$.fragment,t),D(i.$$.fragment,t),o=!1},d(t){t&&(S(e),S(r)),a&&a.d(t),st(n,t),st(i,t)}}}function GI(t){const e=new Map;return function t(n,r=""){const i=n.href||r;n.isPage&&e.set(decodeURI(i),n),n.children&&Object.entries(n.children).forEach((([e,n])=>{t(n,`${i}/${e}`)}))}(t),e}function XI(t,e,n){let r,i,o,s,a,l,c,u,d,h,f,p,m,b,y,g,v;Ae(t,Ma,(t=>n(15,g=t))),Ae(t,yh,(t=>n(16,v=t)));let{$$slots:_={},$$scope:w}=e;{const t=document.getElementById("__evidence_project_splash");null==t||t.remove()}let{data:S}=e,{title:O}=e,{logo:I}=e,{lightLogo:$}=e,{darkLogo:x}=e,{neverShowQueries:D=!1}=e,{fullWidth:E=!1}=e,{hideSidebar:k=!1}=e,{builtWithEvidence:A=!0}=e,{algolia:T}=e,{githubRepo:N}=e,{xProfile:C}=e,{blueskyProfile:M}=e,{slackCommunity:B}=e,{maxWidth:j}=e,{homePageName:P="Home"}=e,{hideBreadcrumbs:L=!1}=e,{hideHeader:R=!1}=e,{hideTOC:F=!1}=e,{sidebarDepth:U=3}=e,z=!1,V=null==S?void 0:S.pagesManifest;Pr((async()=>{if(!("serviceWorker"in navigator))return;const t=await navigator.serviceWorker.register(xt("/fix-tprotocol-service-worker.js"),{scope:xt("/"),type:"classic"});console.debug("[fix-tprotocol-service-worker] Service Worker registered",{registration:t})}));const{syncThemeAttribute:q,cycleAppearance:W,selectedAppearance:Y,setAppearance:H,activeAppearance:K}=hh();return Ae(t,Y,(t=>n(44,b=t))),Ae(t,K,(t=>n(45,y=t))),Pr((()=>{const t=t=>{"l"===t.key.toLowerCase()&&t.shiftKey&&(t.ctrlKey||t.metaKey)&&W()};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)})),Pr((()=>q(document.querySelector("html")))),Pr((()=>{let t;const e=()=>{t=y,"dark"===b&&H("light")},n=()=>{"dark"===t&&H("dark")};return window.addEventListener("beforeprint",e),window.addEventListener("afterprint",n),()=>{window.removeEventListener("beforeprint",e),window.removeEventListener("afterprint",n)}})),t.$$set=t=>{"data"in t&&n(27,S=t.data),"title"in t&&n(0,O=t.title),"logo"in t&&n(1,I=t.logo),"lightLogo"in t&&n(2,$=t.lightLogo),"darkLogo"in t&&n(3,x=t.darkLogo),"neverShowQueries"in t&&n(4,D=t.neverShowQueries),"fullWidth"in t&&n(28,E=t.fullWidth),"hideSidebar"in t&&n(5,k=t.hideSidebar),"builtWithEvidence"in t&&n(6,A=t.builtWithEvidence),"algolia"in t&&n(7,T=t.algolia),"githubRepo"in t&&n(8,N=t.githubRepo),"xProfile"in t&&n(9,C=t.xProfile),"blueskyProfile"in t&&n(10,M=t.blueskyProfile),"slackCommunity"in t&&n(11,B=t.slackCommunity),"maxWidth"in t&&n(29,j=t.maxWidth),"homePageName"in t&&n(12,P=t.homePageName),"hideBreadcrumbs"in t&&n(30,L=t.hideBreadcrumbs),"hideHeader"in t&&n(31,R=t.hideHeader),"hideTOC"in t&&n(32,F=t.hideTOC),"sidebarDepth"in t&&n(13,U=t.sidebarDepth),"$$scope"in t&&n(43,w=t.$$scope)},t.$$.update=()=>{var e;65536&t.$$.dirty[0]&&v&&n(17,z=!1),32768&t.$$.dirty[0]|256&t.$$.dirty[1]&&n(34,i=null==(e=r.get(g.route.id))?void 0:e.frontMatter),8&t.$$.dirty[1]&&n(14,o=null==i?void 0:i.sidebar),16384&t.$$.dirty[0]&&(["show","hide","never"].includes(o)||n(14,o=void 0)),8&t.$$.dirty[1]&&n(38,s=null==i?void 0:i.hide_breadcrumbs),1073741824&t.$$.dirty[0]|128&t.$$.dirty[1]&&n(22,a=s??L),8&t.$$.dirty[1]&&n(37,l=null==i?void 0:i.full_width),268435456&t.$$.dirty[0]|64&t.$$.dirty[1]&&n(21,c=l??E),8&t.$$.dirty[1]&&n(36,u=null==i?void 0:i.max_width),536870912&t.$$.dirty[0]|32&t.$$.dirty[1]&&n(20,d=u??j),8&t.$$.dirty[1]&&n(35,h=null==i?void 0:i.hide_header),17&t.$$.dirty[1]&&n(19,f=h??R),8&t.$$.dirty[1]&&n(33,p=null==i?void 0:i.hide_toc),6&t.$$.dirty[1]&&n(18,m=p??F)},n(39,r=GI(V)),[O,I,$,x,D,k,A,T,N,C,M,B,P,U,o,g,v,z,m,f,d,c,a,"hover",V,Y,K,S,E,j,L,R,F,p,i,h,u,l,s,r,_,function(t){z=t,n(17,z),n(16,v)},function(t){z=t,n(17,z),n(16,v)},w]}class ZI extends ae{constructor(t){super(),le(this,t,XI,QI,ee,{data:27,title:0,logo:1,lightLogo:2,darkLogo:3,neverShowQueries:4,fullWidth:28,hideSidebar:5,builtWithEvidence:6,algolia:7,githubRepo:8,xProfile:9,blueskyProfile:10,slackCommunity:11,maxWidth:29,homePageName:12,hideBreadcrumbs:30,hideHeader:31,hideTOC:32,sidebarDepth:13},null,[-1,-1])}}const tO=t=>({}),ch=t=>({slot:"content"});function eO(t){let e;const n=t[1].default,r=Oe(n,t,t[2],ch);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||4&i)&&ke(r,n,t,t[2],e?Ee(n,t[2],i,tO):De(t[2]),ch)},i(t){e||(O(r,t),e=!0)},o(t){D(r,t),e=!1},d(t){r&&r.d(t)}}}function nO(t){let e,n;return e=new ZI({props:{data:t[0],$$slots:{content:[eO]},$$scope:{ctx:t}}}),{c(){ct(e.$$.fragment)},l(t){lt(e.$$.fragment,t)},m(t,r){at(e,t,r),n=!0},p(t,[n]){const r={};1&n&&(r.data=t[0]),4&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(O(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){st(e,t)}}}function rO(t,e,n){let{$$slots:r={},$$scope:i}=e,{data:o}=e;return t.$$set=t=>{"data"in t&&n(0,o=t.data),"$$scope"in t&&n(2,i=t.$$scope)},[o,r,i]}class _O extends ae{constructor(t){super(),le(this,t,rO,nO,ee,{data:0})}}export{_O as component,gO as universal};