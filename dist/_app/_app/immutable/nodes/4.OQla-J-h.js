import{L as Il,s as ke,d as m,i as E,r as fe,P as Ee,t as je,Q as re,R as nt,c as we,u as Le,g as Ie,a as De,v as me,a6 as Ve,A as ot,h as D,j as V,m as N,D as ft,a4 as Nl,a5 as Ml,p as kt,z as zt,l as qe,J as pt,b as T,B as gl,a8 as js,e as P,x as ce,k as q,y as de,n as j,a9 as Rl,aa as qt,o as $l,ab as Ul,w as Xe,S as cn,N as Q,ac as Cr,ad as qi,a7 as Er,C as gt,ae as jt,af as dl,ag as ml,ah as ji,ai as vr,aj as Tr,ak as Sr,q as We,E as Xt,al as pr,I as Ar,am as Or,an as wr}from"../chunks/scheduler.CXt6djuF.js";import{S as ve,i as Te,t as v,a as y,g as ge,c as be,f as Ct,h as Ws,j as Vl,k as Hl,d as te,m as le,b as ie,e as ne}from"../chunks/index.DP2zcclO.js";import{t as dn,B as Lr,G as Ir,w as Dr,F as Nr,_ as Mr,$ as Rr,v as wl,a0 as Bi,a1 as Pr,N as Ui,l as mn,H as zr,K as hn,L as ei,a2 as Fr,n as ti,O as li,a3 as Br,a4 as _n,a5 as Ur,a6 as Vr,p as Hr,S as Xs,R as Ys,U as Ks,x as et,a7 as Gr,V as qr,X as Ge,a8 as jr,a9 as Gl,aa as Wr,ab as fi,ac as Qs,ad as ri,ae as Xr,af as Yr,ag as Kr,ah as Qr,ai as Zr,aj as Jr,y as lt,A as Nt,I as ui,ak as xr,al as Zs,am as $r,an as gn,h as at,u as Js,o as xs,ao as eo,ap as to,aq as lo,ar as io,Y as no,as as vt,at as so,Z as $s,au as ro,g as Yt,e as At,av as Ki,aw as er,ax as si,ay as oo,az as ct,P as bn,aA as ao,aB as yn,aC as Pl,aD as fo,aE as uo,aF as Qi,aG as Zi,aH as tr,aI as co,f as mo,aJ as ho,aK as Ht,aL as _o,aM as go,aN as Vi,aO as Wi,aP as Xi,aQ as kn,aR as bo,aS as It,aT as ii,aU as yo,aV as lr,aW as ir,aX as nr,aY as ko,aZ as zl,a_ as Co,a$ as Eo,b0 as vo,b1 as To,b2 as So,b3 as po,b4 as Cn,b5 as En,b6 as Ao}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.nOdVbBr4.js";import{w as oi,d as Wt}from"../chunks/entry.Dyop2LGn.js";import{h as vn,c as Oo,B as wo,a as ni,p as Lo}from"../chunks/button.BBPrIXH5.js";import{p as Ji}from"../chunks/stores.CV4JZkJh.js";import{r as Io}from"../chunks/scroll.DWlsEOxT.js";import{c as Do}from"../chunks/checkRequiredProps.o_C_V3S5.js";const sr=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global,Yi=(e,t={serializeStrings:!0})=>null==e?"null":"string"==typeof e?!1!==t.serializeStrings?`'${e.replaceAll("'","''")}'`:e:"number"==typeof e||"bigint"==typeof e||"boolean"==typeof e?String(e):e instanceof Date?`'${e.toISOString()}'::TIMESTAMP_MS`:Array.isArray(e)?`[${e.map((e=>Yi(e,t))).join(", ")}]`:JSON.stringify(e),No={positioning:{placement:"bottom"},arrowSize:8,defaultOpen:!1,disableFocusTrap:!1,closeOnEscape:!0,preventScroll:!1,onOpenChange:void 0,closeOnOutsideClick:!0,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},{name:Ll}=zr("popover"),Mo=["trigger","content"];function Ro(e){const t={...No,...e},n=dn(Lr(t,"open","ids")),{positioning:s,arrowSize:l,disableFocusTrap:i,preventScroll:o,closeOnEscape:a,closeOnOutsideClick:r,portal:c,forceVisible:d,openFocus:u,closeFocus:p,onOutsideClick:m}=n,f=t.open??oi(t.defaultOpen),h=Ir(f,null==t?void 0:t.onOpenChange),y=Dr.writable(null),$=dn({...Nr(Mo),...t.ids});function g(){h.set(!1);const e=document.getElementById($.trigger.get());vn({prop:p.get(),defaultEl:e})}Mr((()=>{y.set(document.getElementById($.trigger.get()))}));const v=Rr({open:h,activeTrigger:y,forceVisible:d}),b=wl(Ll("content"),{stores:[v,c,$.content],returned:([e,t,n])=>({hidden:!e||!mn||void 0,tabindex:-1,style:Ui({display:e?void 0:"none"}),id:n,"data-state":e?"open":"closed","data-portal":Pr(t)}),action:e=>{let t=ti;const n=Bi([v,y,s,i,a,r,c],(([n,s,l,i,o,a,r])=>{t(),n&&s&&Il().then((()=>{t(),t=Br(e,{anchorElement:s,open:h,options:{floating:l,focusTrap:i?null:{returnFocusOnDeactivate:!1,clickOutsideDeactivates:a,allowOutsideClick:!0,escapeDeactivates:o},modal:{shouldCloseOnInteractOutside:x,onClose:g,open:n,closeOnInteractOutside:a},escapeKeydown:o?{handler:()=>{g()}}:null,portal:_n(e,r)}}).destroy}))}));return{destroy(){n(),t()}}}});function E(e){h.update((e=>!e)),e&&e!==y.get()&&y.set(e)}function x(e){var t;if(null==(t=m.get())||t(e),e.defaultPrevented)return!1;const n=e.target,s=document.getElementById($.trigger.get());return!(s&&Ur(n)&&(n===s||s.contains(n)))}const T=wl(Ll("trigger"),{stores:[v,$.content,$.trigger],returned:([e,t,n])=>({role:"button","aria-haspopup":"dialog","aria-expanded":e?"true":"false","data-state":Tn(e),"aria-controls":t,id:n}),action:e=>({destroy:hn(ei(e,"click",(()=>{E(e)})),ei(e,"keydown",(t=>{t.key!==li.ENTER&&t.key!==li.SPACE||(t.preventDefault(),E(e))})))})}),w=wl(Ll("overlay"),{stores:[v],returned:([e])=>({hidden:!e||void 0,tabindex:-1,style:Ui({display:e?void 0:"none"}),"aria-hidden":"true","data-state":Tn(e)}),action:e=>{let t=ti,n=ti,s=ti;if(a.get()){const n=Fr(e,{handler:()=>{g()}});n&&n.destroy&&(t=n.destroy)}return n=Bi([c],(([t])=>{if(s(),null===t)return;const n=_n(e,t);null!==n&&(s=Vr(e,n).destroy)})),{destroy(){t(),n(),s()}}}}),D=wl(Ll("arrow"),{stores:l,returned:e=>({"data-arrow":!0,style:Ui({position:"absolute",width:`var(--arrow-size, ${e}px)`,height:`var(--arrow-size, ${e}px)`})})}),C=wl(Ll("close"),{returned:()=>({type:"button"}),action:e=>({destroy:hn(ei(e,"click",(e=>{e.defaultPrevented||g()})),ei(e,"keydown",(e=>{e.defaultPrevented||e.key!==li.ENTER&&e.key!==li.SPACE||(e.preventDefault(),E())})))})});return Bi([h,y,o],(([e,t,n])=>{if(!mn)return;const s=[];if(e){t||Il().then((()=>{const e=document.getElementById($.trigger.get());Hr(e)&&y.set(e)})),n&&s.push(Io());const e=t??document.getElementById($.trigger.get());vn({prop:u.get(),defaultEl:e})}return()=>{s.forEach((e=>e()))}})),{ids:$,elements:{trigger:T,content:b,arrow:D,close:C,overlay:w},states:{open:h},options:n}}function Tn(e){return e?"open":"closed"}function Po(){return{NAME:"separator",PARTS:["root"]}}function zo(e){const{NAME:t,PARTS:n}=Po(),s=Xs(t,n),l={...Oo(Ys(e)),getAttrs:s};return{...l,updateOption:Ks(l.options)}}const Fo=e=>({builder:4&e}),Sn=e=>({builder:e[2]});function Bo(e){let t,n,s,l=[e[2],e[4]],i={};for(let e=0;e<l.length;e+=1)i=re(i,l[e]);return{c(){t=N("div"),this.h()},l(e){t=D(e,"DIV",{}),V(t).forEach(m),this.h()},h(){Ve(t,i)},m(l,i){E(l,t,i),e[10](t),n||(s=ot(e[2].action(t)),n=!0)},p(e,n){Ve(t,i=et(l,[4&n&&e[2],16&n&&e[4]]))},i:me,o:me,d(l){l&&m(t),e[10](null),n=!1,s()}}}function Uo(e){let t;const n=e[9].default,s=we(n,e,e[8],Sn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||260&l)&&Le(s,n,e,e[8],t?De(n,e[8],l,Fo):Ie(e[8]),Sn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Vo(e){let t,n,s,l;const i=[Uo,Bo],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function Ho(e,t,n){let s;const l=["orientation","decorative","asChild","el"];let i,o=Ee(t,l),{$$slots:a={},$$scope:r}=t,{orientation:c="horizontal"}=t,{decorative:d=!0}=t,{asChild:u=!1}=t,{el:p}=t;const{elements:{root:m},updateOption:f,getAttrs:h}=zo({orientation:c,decorative:d});je(e,m,(e=>n(7,i=e)));const y=h("root");return e.$$set=e=>{t=re(re({},t),nt(e)),n(4,o=Ee(t,l)),"orientation"in e&&n(5,c=e.orientation),"decorative"in e&&n(6,d=e.decorative),"asChild"in e&&n(1,u=e.asChild),"el"in e&&n(0,p=e.el),"$$scope"in e&&n(8,r=e.$$scope)},e.$$.update=()=>{32&e.$$.dirty&&f("orientation",c),64&e.$$.dirty&&f("decorative",d),128&e.$$.dirty&&n(2,s=i),4&e.$$.dirty&&Object.assign(s,y)},[p,u,s,m,o,c,d,i,r,a,function(e){ft[e?"unshift":"push"]((()=>{p=e,n(0,p)}))}]}let Go=class extends ve{constructor(e){super(),Te(this,e,Ho,Vo,ke,{orientation:5,decorative:6,asChild:1,el:0})}};function rr(){return{NAME:"popover",PARTS:["arrow","close","content","trigger"]}}function qo(e){const{NAME:t,PARTS:n}=rr(),s=Xs(t,n),l={...Ro({positioning:{placement:"bottom",gutter:0},...Ys(e),forceVisible:!0}),getAttrs:s};return Nl(t,l),{...l,updateOption:Ks(l.options)}}function xi(){const{NAME:e}=rr();return Ml(e)}function jo(e){const t={side:"bottom",align:"center",...e},{options:{positioning:n}}=xi();Gr(n)(t)}const Wo=e=>({ids:1&e}),pn=e=>({ids:e[0]});function Xo(e){let t;const n=e[13].default,s=we(n,e,e[12],pn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,[l]){s&&s.p&&(!t||4097&l)&&Le(s,n,e,e[12],t?De(n,e[12],l,Wo):Ie(e[12]),pn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Yo(e,t,n){let s,{$$slots:l={},$$scope:i}=t,{disableFocusTrap:o}=t,{closeOnEscape:a}=t,{closeOnOutsideClick:r}=t,{preventScroll:c}=t,{portal:d}=t,{open:u}=t,{onOpenChange:p}=t,{openFocus:m}=t,{closeFocus:f}=t,{onOutsideClick:h}=t;const{updateOption:y,states:{open:$},ids:g}=qo({disableFocusTrap:o,closeOnEscape:a,closeOnOutsideClick:r,preventScroll:c,portal:d,defaultOpen:u,openFocus:m,closeFocus:f,onOutsideClick:h,onOpenChange:({next:e})=>(u!==e&&(null==p||p(e),n(2,u=e)),e),positioning:{gutter:0,offset:{mainAxis:1}}}),v=Wt([g.content,g.trigger],(([e,t])=>({content:e,trigger:t})));return je(e,v,(e=>n(0,s=e))),e.$$set=e=>{"disableFocusTrap"in e&&n(3,o=e.disableFocusTrap),"closeOnEscape"in e&&n(4,a=e.closeOnEscape),"closeOnOutsideClick"in e&&n(5,r=e.closeOnOutsideClick),"preventScroll"in e&&n(6,c=e.preventScroll),"portal"in e&&n(7,d=e.portal),"open"in e&&n(2,u=e.open),"onOpenChange"in e&&n(8,p=e.onOpenChange),"openFocus"in e&&n(9,m=e.openFocus),"closeFocus"in e&&n(10,f=e.closeFocus),"onOutsideClick"in e&&n(11,h=e.onOutsideClick),"$$scope"in e&&n(12,i=e.$$scope)},e.$$.update=()=>{4&e.$$.dirty&&void 0!==u&&$.set(u),8&e.$$.dirty&&y("disableFocusTrap",o),16&e.$$.dirty&&y("closeOnEscape",a),32&e.$$.dirty&&y("closeOnOutsideClick",r),64&e.$$.dirty&&y("preventScroll",c),128&e.$$.dirty&&y("portal",d),512&e.$$.dirty&&y("openFocus",m),1024&e.$$.dirty&&y("closeFocus",f),2048&e.$$.dirty&&y("onOutsideClick",h)},[s,v,u,o,a,r,c,d,p,m,f,h,i,l]}class Ko extends ve{constructor(e){super(),Te(this,e,Yo,Xo,ke,{disableFocusTrap:3,closeOnEscape:4,closeOnOutsideClick:5,preventScroll:6,portal:7,open:2,onOpenChange:8,openFocus:9,closeFocus:10,onOutsideClick:11})}}const Qo=e=>({builder:256&e[0]}),An=e=>({builder:e[8]}),Zo=e=>({builder:256&e[0]}),On=e=>({builder:e[8]}),Jo=e=>({builder:256&e[0]}),wn=e=>({builder:e[8]}),xo=e=>({builder:256&e[0]}),Ln=e=>({builder:e[8]}),$o=e=>({builder:256&e[0]}),In=e=>({builder:e[8]}),ea=e=>({builder:256&e[0]}),Dn=e=>({builder:e[8]});function ta(e){let t,n,s,l;const i=e[27].default,o=we(i,e,e[26],An);let a=[e[8],e[12]],r={};for(let e=0;e<a.length;e+=1)r=re(r,a[e]);return{c(){t=N("div"),o&&o.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);o&&o.l(n),n.forEach(m),this.h()},h(){Ve(t,r)},m(i,a){E(i,t,a),o&&o.m(t,null),e[32](t),n=!0,s||(l=ot(e[8].action(t)),s=!0)},p(e,s){o&&o.p&&(!n||67109120&s[0])&&Le(o,i,e,e[26],n?De(i,e[26],s,Qo):Ie(e[26]),An),Ve(t,r=et(a,[256&s[0]&&e[8],4096&s[0]&&e[12]]))},i(e){n||(y(o,e),n=!0)},o(e){v(o,e),n=!1},d(n){n&&m(t),o&&o.d(n),e[32](null),s=!1,l()}}}function la(e){let t,n,s,l,i;const o=e[27].default,a=we(o,e,e[26],On);let r=[e[8],e[12]],c={};for(let e=0;e<r.length;e+=1)c=re(c,r[e]);return{c(){t=N("div"),a&&a.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);a&&a.l(n),n.forEach(m),this.h()},h(){Ve(t,c)},m(n,o){E(n,t,o),a&&a.m(t,null),e[31](t),s=!0,l||(i=ot(e[8].action(t)),l=!0)},p(n,l){e=n,a&&a.p&&(!s||67109120&l[0])&&Le(a,o,e,e[26],s?De(o,e[26],l,Zo):Ie(e[26]),On),Ve(t,c=et(r,[256&l[0]&&e[8],4096&l[0]&&e[12]]))},i(e){s||(y(a,e),n&&n.end(1),s=!0)},o(l){v(a,l),l&&(n=Ws(t,e[5],e[6])),s=!1},d(s){s&&m(t),a&&a.d(s),e[31](null),s&&n&&n.end(),l=!1,i()}}}function ia(e){let t,n,s,l,i;const o=e[27].default,a=we(o,e,e[26],wn);let r=[e[8],e[12]],c={};for(let e=0;e<r.length;e+=1)c=re(c,r[e]);return{c(){t=N("div"),a&&a.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);a&&a.l(n),n.forEach(m),this.h()},h(){Ve(t,c)},m(n,o){E(n,t,o),a&&a.m(t,null),e[30](t),s=!0,l||(i=ot(e[8].action(t)),l=!0)},p(n,l){e=n,a&&a.p&&(!s||67109120&l[0])&&Le(a,o,e,e[26],s?De(o,e[26],l,Jo):Ie(e[26]),wn),Ve(t,c=et(r,[256&l[0]&&e[8],4096&l[0]&&e[12]]))},i(l){s||(y(a,l),l&&(n||kt((()=>{n=Vl(t,e[3],e[4]),n.start()}))),s=!0)},o(e){v(a,e),s=!1},d(n){n&&m(t),a&&a.d(n),e[30](null),l=!1,i()}}}function na(e){let t,n,s,l,i,o;const a=e[27].default,r=we(a,e,e[26],Ln);let c=[e[8],e[12]],d={};for(let e=0;e<c.length;e+=1)d=re(d,c[e]);return{c(){t=N("div"),r&&r.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);r&&r.l(n),n.forEach(m),this.h()},h(){Ve(t,d)},m(n,s){E(n,t,s),r&&r.m(t,null),e[29](t),l=!0,i||(o=ot(e[8].action(t)),i=!0)},p(n,s){e=n,r&&r.p&&(!l||67109120&s[0])&&Le(r,a,e,e[26],l?De(a,e[26],s,xo):Ie(e[26]),Ln),Ve(t,d=et(c,[256&s[0]&&e[8],4096&s[0]&&e[12]]))},i(i){l||(y(r,i),i&&kt((()=>{l&&(s&&s.end(1),n=Vl(t,e[3],e[4]),n.start())})),l=!0)},o(i){v(r,i),n&&n.invalidate(),i&&(s=Ws(t,e[5],e[6])),l=!1},d(n){n&&m(t),r&&r.d(n),e[29](null),n&&s&&s.end(),i=!1,o()}}}function sa(e){let t,n,s,l,i;const o=e[27].default,a=we(o,e,e[26],In);let r=[e[8],e[12]],c={};for(let e=0;e<r.length;e+=1)c=re(c,r[e]);return{c(){t=N("div"),a&&a.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);a&&a.l(n),n.forEach(m),this.h()},h(){Ve(t,c)},m(n,o){E(n,t,o),a&&a.m(t,null),e[28](t),s=!0,l||(i=ot(e[8].action(t)),l=!0)},p(n,l){e=n,a&&a.p&&(!s||67109120&l[0])&&Le(a,o,e,e[26],s?De(o,e[26],l,$o):Ie(e[26]),In),Ve(t,c=et(r,[256&l[0]&&e[8],4096&l[0]&&e[12]]))},i(l){s||(y(a,l),l&&kt((()=>{s&&(n||(n=Ct(t,e[1],e[2],!0)),n.run(1))})),s=!0)},o(l){v(a,l),l&&(n||(n=Ct(t,e[1],e[2],!1)),n.run(0)),s=!1},d(s){s&&m(t),a&&a.d(s),e[28](null),s&&n&&n.end(),l=!1,i()}}}function ra(e){let t;const n=e[27].default,s=we(n,e,e[26],Dn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||67109120&l[0])&&Le(s,n,e,e[26],t?De(n,e[26],l,ea):Ie(e[26]),Dn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function oa(e){let t,n,s,l;const i=[ra,sa,na,ia,la,ta],o=[];function a(e,t){return e[7]&&e[9]?0:e[1]&&e[9]?1:e[3]&&e[5]&&e[9]?2:e[3]&&e[9]?3:e[5]&&e[9]?4:e[9]?5:-1}return~(t=a(e))&&(n=o[t]=i[t](e)),{c(){n&&n.c(),s=fe()},l(e){n&&n.l(e),s=fe()},m(e,n){~t&&o[t].m(e,n),E(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?~t&&o[t].p(e,l):(n&&(ge(),v(o[r],1,1,(()=>{o[r]=null})),be()),~t?(n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s)):n=null)},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),~t&&o[t].d(e)}}}function aa(e,t,n){let s;const l=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let i,o,a=Ee(t,l),{$$slots:r={},$$scope:c}=t,{transition:d}=t,{transitionConfig:u}=t,{inTransition:p}=t,{inTransitionConfig:m}=t,{outTransition:f}=t,{outTransitionConfig:h}=t,{asChild:y=!1}=t,{id:$}=t,{side:g="bottom"}=t,{align:v="center"}=t,{sideOffset:b=0}=t,{alignOffset:E=0}=t,{collisionPadding:x=8}=t,{avoidCollisions:T=!0}=t,{collisionBoundary:w}=t,{sameWidth:D=!1}=t,{fitViewport:C=!1}=t,{strategy:L="absolute"}=t,{overlap:N=!1}=t,{el:O}=t;const{elements:{content:A},states:{open:I},ids:S,getAttrs:k}=xi();je(e,A,(e=>n(25,o=e))),je(e,I,(e=>n(9,i=e)));const P=k("content");return e.$$set=e=>{t=re(re({},t),nt(e)),n(12,a=Ee(t,l)),"transition"in e&&n(1,d=e.transition),"transitionConfig"in e&&n(2,u=e.transitionConfig),"inTransition"in e&&n(3,p=e.inTransition),"inTransitionConfig"in e&&n(4,m=e.inTransitionConfig),"outTransition"in e&&n(5,f=e.outTransition),"outTransitionConfig"in e&&n(6,h=e.outTransitionConfig),"asChild"in e&&n(7,y=e.asChild),"id"in e&&n(13,$=e.id),"side"in e&&n(14,g=e.side),"align"in e&&n(15,v=e.align),"sideOffset"in e&&n(16,b=e.sideOffset),"alignOffset"in e&&n(17,E=e.alignOffset),"collisionPadding"in e&&n(18,x=e.collisionPadding),"avoidCollisions"in e&&n(19,T=e.avoidCollisions),"collisionBoundary"in e&&n(20,w=e.collisionBoundary),"sameWidth"in e&&n(21,D=e.sameWidth),"fitViewport"in e&&n(22,C=e.fitViewport),"strategy"in e&&n(23,L=e.strategy),"overlap"in e&&n(24,N=e.overlap),"el"in e&&n(0,O=e.el),"$$scope"in e&&n(26,c=e.$$scope)},e.$$.update=()=>{8192&e.$$.dirty[0]&&$&&S.content.set($),33554432&e.$$.dirty[0]&&n(8,s=o),256&e.$$.dirty[0]&&Object.assign(s,P),33538560&e.$$.dirty[0]&&i&&jo({side:g,align:v,sideOffset:b,alignOffset:E,collisionPadding:x,avoidCollisions:T,collisionBoundary:w,sameWidth:D,fitViewport:C,strategy:L,overlap:N})},[O,d,u,p,m,f,h,y,s,i,A,I,a,$,g,v,b,E,x,T,w,D,C,L,N,o,c,r,function(e){ft[e?"unshift":"push"]((()=>{O=e,n(0,O)}))},function(e){ft[e?"unshift":"push"]((()=>{O=e,n(0,O)}))},function(e){ft[e?"unshift":"push"]((()=>{O=e,n(0,O)}))},function(e){ft[e?"unshift":"push"]((()=>{O=e,n(0,O)}))},function(e){ft[e?"unshift":"push"]((()=>{O=e,n(0,O)}))}]}let fa=class extends ve{constructor(e){super(),Te(this,e,aa,oa,ke,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:13,side:14,align:15,sideOffset:16,alignOffset:17,collisionPadding:18,avoidCollisions:19,collisionBoundary:20,sameWidth:21,fitViewport:22,strategy:23,overlap:24,el:0},null,[-1,-1])}};const ua=e=>({builder:4&e}),Nn=e=>({builder:e[2]}),ca=e=>({builder:4&e}),Mn=e=>({builder:e[2]});function da(e){let t,n,s,l;const i=e[12].default,o=we(i,e,e[11],Nn);let a=[e[2],{type:"button"},e[6]],r={};for(let e=0;e<a.length;e+=1)r=re(r,a[e]);return{c(){t=N("button"),o&&o.c(),this.h()},l(e){t=D(e,"BUTTON",{type:!0});var n=V(t);o&&o.l(n),n.forEach(m),this.h()},h(){Ve(t,r)},m(i,a){E(i,t,a),o&&o.m(t,null),t.autofocus&&t.focus(),e[13](t),n=!0,s||(l=[ot(e[2].action(t)),qe(t,"m-click",e[5]),qe(t,"m-keydown",e[5])],s=!0)},p(e,s){o&&o.p&&(!n||2052&s)&&Le(o,i,e,e[11],n?De(i,e[11],s,ua):Ie(e[11]),Nn),Ve(t,r=et(a,[4&s&&e[2],{type:"button"},64&s&&e[6]]))},i(e){n||(y(o,e),n=!0)},o(e){v(o,e),n=!1},d(n){n&&m(t),o&&o.d(n),e[13](null),s=!1,zt(l)}}}function ma(e){let t;const n=e[12].default,s=we(n,e,e[11],Mn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||2052&l)&&Le(s,n,e,e[11],t?De(n,e[11],l,ca):Ie(e[11]),Mn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function ha(e){let t,n,s,l;const i=[ma,da],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function _a(e,t,n){let s,l;const i=["asChild","id","el"];let o,a,r=Ee(t,i),{$$slots:c={},$$scope:d}=t,{asChild:u=!1}=t,{id:p}=t,{el:m}=t;const{elements:{trigger:f},states:{open:h},ids:y,getAttrs:$}=xi();je(e,f,(e=>n(9,o=e))),je(e,h,(e=>n(10,a=e)));const g=qr(),v=$("trigger");return e.$$set=e=>{t=re(re({},t),nt(e)),n(6,r=Ee(t,i)),"asChild"in e&&n(1,u=e.asChild),"id"in e&&n(7,p=e.id),"el"in e&&n(0,m=e.el),"$$scope"in e&&n(11,d=e.$$scope)},e.$$.update=()=>{128&e.$$.dirty&&p&&y.trigger.set(p),1024&e.$$.dirty&&n(8,s={...v,"aria-controls":a?y.content:void 0}),512&e.$$.dirty&&n(2,l=o),260&e.$$.dirty&&Object.assign(l,s)},[m,u,l,f,h,g,r,p,s,o,a,d,c,function(e){ft[e?"unshift":"push"]((()=>{m=e,n(0,m)}))}]}class ga extends ve{constructor(e){super(),Te(this,e,_a,ha,ke,{asChild:1,id:7,el:0})}}function ba(e){let t,n;const s=e[2].default,l=we(s,e,e[1],null);return{c(){t=N("div"),l&&l.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var n=V(t);l&&l.l(n),n.forEach(m),this.h()},h(){T(t,"class","contents"),pt(t,"print:hidden",e[0])},m(e,s){E(e,t,s),l&&l.m(t,null),n=!0},p(e,[i]){l&&l.p&&(!n||2&i)&&Le(l,s,e,e[1],n?De(s,e[1],i,null):Ie(e[1]),null),(!n||1&i)&&pt(t,"print:hidden",e[0])},i(e){n||(y(l,e),n=!0)},o(e){v(l,e),n=!1},d(e){e&&m(t),l&&l.d(e)}}}function ya(e,t,n){let{$$slots:s={},$$scope:l}=t,{enabled:i=!0}=t;return e.$$set=e=>{"enabled"in e&&n(0,i=e.enabled),"$$scope"in e&&n(1,l=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(0,i=Ge(i))},[i,l,s]}class ka extends ve{constructor(e){super(),Te(this,e,ya,ba,ke,{enabled:0})}}const or=Symbol("EVIDENCE_DROPDOWN_CTX");let Ca=0;function Ea(e,t,n){let{value:s}=t,{valueLabel:l=s}=t,{idx:i=-1}=t,{__auto:o=!1}=t;o||(i=Ca++);const a=Ml(or);return gl((()=>a.registerOption({value:s,label:l,idx:i,__auto:o}))),e.$$set=e=>{"value"in e&&n(1,s=e.value),"valueLabel"in e&&n(2,l=e.valueLabel),"idx"in e&&n(0,i=e.idx),"__auto"in e&&n(3,o=e.__auto)},[i,s,l,o]}class hl extends ve{constructor(e){super(),Te(this,e,Ea,null,ke,{value:1,valueLabel:2,idx:0,__auto:3})}}function va(e){return Object.keys(e).reduce(((t,n)=>void 0===e[n]?t:t+`${n}:${e[n]};`),"")}const Ta={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function ai(e,t,n,s){const l=Array.isArray(t)?t:[t];return l.forEach((t=>e.addEventListener(t,n,s))),()=>{l.forEach((t=>e.removeEventListener(t,n,s)))}}function ar(...e){return(...t)=>{for(const n of e)"function"==typeof n&&n(...t)}}const Sa=e=>4&e,pa=e=>({}),Rn=e=>({...e[2]}),Aa=e=>4&e,Oa=e=>({}),Pn=e=>({...e[2]});function wa(e){let t,n,s,l,i,o,a,r=(e[0]??"")+"",c=[e[6]],d={};for(let e=0;e<c.length;e+=1)d=re(d,c[e]);const u=e[18].default,p=we(u,e,e[17],Rn);let f=[e[5],e[7]],h={};for(let e=0;e<f.length;e+=1)h=re(h,f[e]);return{c(){t=N("div"),n=N("label"),s=de(r),l=j(),p&&p.c(),this.h()},l(e){t=D(e,"DIV",{});var i=V(t);n=D(i,"LABEL",{});var o=V(n);s=ce(o,r),o.forEach(m),l=q(i),p&&p.l(i),i.forEach(m),this.h()},h(){Ve(n,d),Ve(t,h)},m(r,c){E(r,t,c),P(t,n),P(n,s),P(t,l),p&&p.m(t,null),i=!0,o||(a=ot(e[4].call(null,t)),o=!0)},p(e,n){(!i||1&n)&&r!==(r=(e[0]??"")+"")&&js(s,r,d.contenteditable),p&&p.p&&(!i||131076&n)&&Le(p,u,e,e[17],Sa(n)||!i?Ie(e[17]):De(u,e[17],n,pa),Rn),Ve(t,h=et(f,[e[5],128&n&&e[7]]))},i(e){i||(y(p,e),i=!0)},o(e){v(p,e),i=!1},d(e){e&&m(t),p&&p.d(e),o=!1,a()}}}function La(e){let t;const n=e[18].default,s=we(n,e,e[17],Pn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||131076&l)&&Le(s,n,e,e[17],Aa(l)||!t?Ie(e[17]):De(n,e[17],l,Oa),Pn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Ia(e){let t,n,s,l;const i=[La,wa],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function Da(e,t,n){let s;const l=["label","shouldFilter","filter","value","onValueChange","loop","onKeydown","state","ids","asChild"];let i,o=Ee(t,l),{$$slots:a={},$$scope:r}=t,{label:c}=t,{shouldFilter:d=!0}=t,{filter:u}=t,{value:p}=t,{onValueChange:m}=t,{loop:f}=t,{onKeydown:h}=t,{state:y}=t,{ids:$}=t,{asChild:g=!1}=t;const{commandEl:v,handleRootKeydown:b,ids:E,state:x}=jr({label:c,shouldFilter:d,filter:u,value:p,onValueChange:e=>{e!==p&&(n(8,p=e),null==m||m(e))},loop:f,state:y,ids:$});function T(e){return v.set(e),{destroy:ar(ai(e,"keydown",C))}}je(e,x,(e=>n(16,i=e)));const w={role:"application",id:E.root,"data-cmdk-root":""},D={"data-cmdk-label":"",for:E.input,id:E.label,style:va(Ta)};function C(e){null==h||h(e),!e.defaultPrevented&&b(e)}const L={action:T,attrs:w};return e.$$set=e=>{t=re(re({},t),nt(e)),n(7,o=Ee(t,l)),"label"in e&&n(0,c=e.label),"shouldFilter"in e&&n(9,d=e.shouldFilter),"filter"in e&&n(10,u=e.filter),"value"in e&&n(8,p=e.value),"onValueChange"in e&&n(11,m=e.onValueChange),"loop"in e&&n(12,f=e.loop),"onKeydown"in e&&n(13,h=e.onKeydown),"state"in e&&n(14,y=e.state),"ids"in e&&n(15,$=e.ids),"asChild"in e&&n(1,g=e.asChild),"$$scope"in e&&n(17,r=e.$$scope)},e.$$.update=()=>{var t;256&e.$$.dirty&&(t=p)&&t!==i.value&&Rl(x,i.value=t,i),65536&e.$$.dirty&&n(2,s={root:L,label:{attrs:D},stateStore:x,state:i})},[c,g,s,x,T,w,D,o,p,d,u,m,f,h,y,$,i,r,a]}let Na=class extends ve{constructor(e){super(),Te(this,e,Da,Ia,ke,{label:0,shouldFilter:9,filter:10,value:8,onValueChange:11,loop:12,onKeydown:13,state:14,ids:15,asChild:1})}};const Ma=e=>({}),zn=e=>({attrs:e[4]});function Fn(e){let t,n,s,l;const i=[Pa,Ra],o=[];function a(e,t){return e[0]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function Ra(e){let t,n;const s=e[8].default,l=we(s,e,e[7],null);let i=[e[4],e[5]],o={};for(let e=0;e<i.length;e+=1)o=re(o,i[e]);return{c(){t=N("div"),l&&l.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);l&&l.l(n),n.forEach(m),this.h()},h(){Ve(t,o)},m(e,s){E(e,t,s),l&&l.m(t,null),n=!0},p(e,a){l&&l.p&&(!n||128&a)&&Le(l,s,e,e[7],n?De(s,e[7],a,null):Ie(e[7]),null),Ve(t,o=et(i,[e[4],32&a&&e[5]]))},i(e){n||(y(l,e),n=!0)},o(e){v(l,e),n=!1},d(e){e&&m(t),l&&l.d(e)}}}function Pa(e){let t;const n=e[8].default,s=we(n,e,e[7],zn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||128&l)&&Le(s,n,e,e[7],t?De(n,e[7],l,Ma):Ie(e[7]),zn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function za(e){let t,n,s=!e[1]&&e[2]&&Fn(e);return{c(){s&&s.c(),t=fe()},l(e){s&&s.l(e),t=fe()},m(e,l){s&&s.m(e,l),E(e,t,l),n=!0},p(e,[n]){!e[1]&&e[2]?s?(s.p(e,n),6&n&&y(s,1)):(s=Fn(e),s.c(),y(s,1),s.m(t.parentNode,t)):s&&(ge(),v(s,1,1,(()=>{s=null})),be())},i(e){n||(y(s),n=!0)},o(e){v(s),n=!1},d(e){e&&m(t),s&&s.d(e)}}}function Fa(e,t,n){let s;const l=["asChild"];let i,o=Ee(t,l),{$$slots:a={},$$scope:r}=t,{asChild:c=!1}=t,d=!0;gl((()=>{n(1,d=!1)}));const u=Gl();return je(e,u,(e=>n(6,i=e))),e.$$set=e=>{t=re(re({},t),nt(e)),n(5,o=Ee(t,l)),"asChild"in e&&n(0,c=e.asChild),"$$scope"in e&&n(7,r=e.$$scope)},e.$$.update=()=>{64&e.$$.dirty&&n(2,s=0===i.filtered.count)},[c,d,s,u,{"data-cmdk-empty":"",role:"presentation"},o,i,r,a]}class Ba extends ve{constructor(e){super(),Te(this,e,Fa,za,ke,{asChild:0})}}const Ua=e=>({container:32&e,group:16&e}),Bn=e=>({container:e[5],group:e[4],heading:{attrs:e[8]}}),Va=e=>({container:32&e,group:16&e}),Un=e=>({container:e[5],group:e[4],heading:{attrs:e[8]}});function Ha(e){let t,n,s,l,i,o,a=e[0]&&Vn(e);const r=e[14].default,c=we(r,e,e[13],Bn);let d=[e[2]],u={};for(let e=0;e<d.length;e+=1)u=re(u,d[e]);let p=[e[3],e[9]],f={};for(let e=0;e<p.length;e+=1)f=re(f,p[e]);return{c(){t=N("div"),a&&a.c(),n=j(),s=N("div"),c&&c.c(),this.h()},l(e){t=D(e,"DIV",{});var l=V(t);a&&a.l(l),n=q(l),s=D(l,"DIV",{});var i=V(s);c&&c.l(i),i.forEach(m),l.forEach(m),this.h()},h(){Ve(s,u),Ve(t,f)},m(r,d){E(r,t,d),a&&a.m(t,null),P(t,n),P(t,s),c&&c.m(s,null),l=!0,i||(o=ot(e[7].call(null,t)),i=!0)},p(e,i){e[0]?a?a.p(e,i):(a=Vn(e),a.c(),a.m(t,n)):a&&(a.d(1),a=null),c&&c.p&&(!l||8240&i)&&Le(c,r,e,e[13],l?De(r,e[13],i,Ua):Ie(e[13]),Bn),Ve(s,u=et(d,[4&i&&e[2]])),Ve(t,f=et(p,[8&i&&e[3],512&i&&e[9]]))},i(e){l||(y(c,e),l=!0)},o(e){v(c,e),l=!1},d(e){e&&m(t),a&&a.d(),c&&c.d(e),i=!1,o()}}}function Ga(e){let t;const n=e[14].default,s=we(n,e,e[13],Un);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8240&l)&&Le(s,n,e,e[13],t?De(n,e[13],l,Va):Ie(e[13]),Un)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Vn(e){let t,n,s=[e[8]],l={};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return{c(){t=N("div"),n=de(e[0]),this.h()},l(s){t=D(s,"DIV",{});var l=V(t);n=ce(l,e[0]),l.forEach(m),this.h()},h(){Ve(t,l)},m(e,s){E(e,t,s),P(t,n)},p(e,t){1&t&&js(n,e[0],l.contenteditable)},d(e){e&&m(t)}}}function qa(e){let t,n,s,l;const i=[Ga,Ha],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function ja(e,t,n){let s,l,i,o;const a=["heading","value","alwaysRender","asChild"];let r,c=Ee(t,a),{$$slots:d={},$$scope:u}=t,{heading:p}=t,{value:m=""}=t,{alwaysRender:f=!1}=t,{asChild:h=!1}=t;const{id:y}=Wr(f),$=fi(),g=Gl(),v=Qs(),b=Wt(g,(e=>!(!f&&!1!==$.filter()&&e.search)||e.filtered.groups.has(y)));function E(e){if(m)return $.value(y,m),void e.setAttribute(ri,m);p?n(10,m=p.trim().toLowerCase()):e.textContent&&n(10,m=e.textContent.trim().toLowerCase()),$.value(y,m),e.setAttribute(ri,m)}je(e,b,(e=>n(12,r=e))),gl((()=>$.group(y)));const x={"data-cmdk-group-heading":"","aria-hidden":!0,id:v};return e.$$set=e=>{t=re(re({},t),nt(e)),n(9,c=Ee(t,a)),"heading"in e&&n(0,p=e.heading),"value"in e&&n(10,m=e.value),"alwaysRender"in e&&n(11,f=e.alwaysRender),"asChild"in e&&n(1,h=e.asChild),"$$scope"in e&&n(13,u=e.$$scope)},e.$$.update=()=>{5120&e.$$.dirty&&n(3,s={"data-cmdk-group":"",role:"presentation",hidden:!r||void 0,"data-value":m}),1&e.$$.dirty&&n(2,l={"data-cmdk-group-items":"",role:"group","aria-labelledby":p?v:void 0}),8&e.$$.dirty&&n(5,i={action:E,attrs:s}),4&e.$$.dirty&&n(4,o={attrs:l})},[p,h,l,s,o,i,b,E,x,c,m,f,r,u,d]}class Wa extends ve{constructor(e){super(),Te(this,e,ja,qa,ke,{heading:0,value:10,alwaysRender:11,asChild:1})}}function Xa(e){return new Promise((t=>setTimeout(t,e)))}const Ya=e=>({attrs:8&e}),Hn=e=>({action:e[6],attrs:e[3]});function Ka(e){let t,n,s,l=[e[3],e[7]],i={};for(let e=0;e<l.length;e+=1)i=re(i,l[e]);return{c(){t=N("input"),this.h()},l(e){t=D(e,"INPUT",{}),this.h()},h(){Ve(t,i)},m(l,i){E(l,t,i),t.autofocus&&t.focus(),e[16](t),qt(t,e[0]),n||(s=[qe(t,"input",e[17]),ot(e[6].call(null,t)),qe(t,"input",e[12]),qe(t,"focus",e[13]),qe(t,"blur",e[14]),qe(t,"change",e[15])],n=!0)},p(e,n){Ve(t,i=et(l,[8&n&&e[3],128&n&&e[7]])),1&n&&t.value!==e[0]&&qt(t,e[0])},i:me,o:me,d(l){l&&m(t),e[16](null),n=!1,zt(s)}}}function Qa(e){let t;const n=e[11].default,s=we(n,e,e[10],Hn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||1032&l)&&Le(s,n,e,e[10],t?De(n,e[10],l,Ya):Ie(e[10]),Hn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Za(e){let t,n,s,l;const i=[Qa,Ka],o=[];function a(e,t){return e[2]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function Ja(e,t,n){const s=["autofocus","value","asChild","el"];let l,i,o=Ee(t,s),{$$slots:a={},$$scope:r}=t;const{ids:c,commandEl:d}=fi(),u=Gl(),p=Wt(u,(e=>e.search));je(e,p,(e=>n(18,i=e)));const m=Wt(u,(e=>e.value));let{autofocus:f}=t,{value:h=i}=t,{asChild:y=!1}=t,{el:$}=t;const g=Wt([m,d],(([e,t])=>{if(!Yr)return;const n=null==t?void 0:t.querySelector(`${Kr}[${ri}="${e}"]`);return null==n?void 0:n.getAttribute("id")}));let v;return je(e,g,(e=>n(9,l=e))),e.$$set=e=>{t=re(re({},t),nt(e)),n(7,o=Ee(t,s)),"autofocus"in e&&n(8,f=e.autofocus),"value"in e&&n(0,h=e.value),"asChild"in e&&n(2,y=e.asChild),"el"in e&&n(1,$=e.el),"$$scope"in e&&n(10,r=e.$$scope)},e.$$.update=()=>{var t;1&e.$$.dirty&&(t=h,u.updateState("search",t)),512&e.$$.dirty&&n(3,v={type:"text","data-cmdk-input":"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.list,"aria-labelledby":c.label,"aria-activedescendant":l??void 0,id:c.input})},[h,$,y,v,p,g,function(e){return f&&Xa(10).then((()=>e.focus())),{destroy:ai(e,"change",(e=>{Xr(e.target)&&u.updateState("search",e.target.value)}))}},o,f,l,r,a,function(t){$l.call(this,e,t)},function(t){$l.call(this,e,t)},function(t){$l.call(this,e,t)},function(t){$l.call(this,e,t)},function(e){ft[e?"unshift":"push"]((()=>{$=e,n(1,$)}))},function(){h=this.value,n(0,h)}]}class xa extends ve{constructor(e){super(),Te(this,e,Ja,Za,ke,{autofocus:8,value:0,asChild:2,el:1})}}const $a=e=>({attrs:4&e}),Gn=e=>({action:e[6],attrs:e[2]}),ef=e=>({attrs:4&e}),qn=e=>({action:e[6],attrs:e[2]});function jn(e){let t,n,s,l;const i=[lf,tf],o=[];function a(e,t){return e[0]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function tf(e){let t,n,s,l;const i=e[15].default,o=we(i,e,e[14],Gn);let a=[e[2],e[7]],r={};for(let e=0;e<a.length;e+=1)r=re(r,a[e]);return{c(){t=N("div"),o&&o.c(),this.h()},l(e){t=D(e,"DIV",{});var n=V(t);o&&o.l(n),n.forEach(m),this.h()},h(){Ve(t,r)},m(i,a){E(i,t,a),o&&o.m(t,null),n=!0,s||(l=ot(e[6].call(null,t)),s=!0)},p(e,s){o&&o.p&&(!n||16388&s)&&Le(o,i,e,e[14],n?De(i,e[14],s,$a):Ie(e[14]),Gn),Ve(t,r=et(a,[4&s&&e[2],128&s&&e[7]]))},i(e){n||(y(o,e),n=!0)},o(e){v(o,e),n=!1},d(e){e&&m(t),o&&o.d(e),s=!1,l()}}}function lf(e){let t;const n=e[15].default,s=we(n,e,e[14],qn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||16388&l)&&Le(s,n,e,e[14],t?De(n,e[14],l,ef):Ie(e[14]),qn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function nf(e){let t,n,s=(e[3]||e[1])&&jn(e);return{c(){s&&s.c(),t=fe()},l(e){s&&s.l(e),t=fe()},m(e,l){s&&s.m(e,l),E(e,t,l),n=!0},p(e,[n]){e[3]||e[1]?s?(s.p(e,n),10&n&&y(s,1)):(s=jn(e),s.c(),y(s,1),s.m(t.parentNode,t)):s&&(ge(),v(s,1,1,(()=>{s=null})),be())},i(e){n||(y(s),n=!0)},o(e){v(s),n=!1},d(e){e&&m(t),s&&s.d(e)}}}function sf(e,t,n){let s;const l=["disabled","value","onSelect","alwaysRender","asChild","id"];let i,o,a=Ee(t,l),{$$slots:r={},$$scope:c}=t,{disabled:d=!1}=t,{value:u=""}=t,{onSelect:p}=t,{alwaysRender:m=!1}=t,{asChild:f=!1}=t,{id:h=Qs()}=t;const y=Qr(),$=fi(),g=Gl(),v=m??(null==y?void 0:y.alwaysRender),b=Wt(g,(e=>{if(v||!1===$.filter()||!e.search)return!0;const t=e.filtered.items.get(h);return!Zr(t)&&t>0}));je(e,b,(e=>n(3,o=e)));let E=!0;gl((()=>(n(1,E=!1),$.item(h,null==y?void 0:y.id))));const x=Wt(g,(e=>e.value===u));function T(){g.updateState("value",u,!0)}return je(e,x,(e=>n(13,i=e))),e.$$set=e=>{t=re(re({},t),nt(e)),n(7,a=Ee(t,l)),"disabled"in e&&n(9,d=e.disabled),"value"in e&&n(8,u=e.value),"onSelect"in e&&n(10,p=e.onSelect),"alwaysRender"in e&&n(11,m=e.alwaysRender),"asChild"in e&&n(0,f=e.asChild),"id"in e&&n(12,h=e.id),"$$scope"in e&&n(14,c=e.$$scope)},e.$$.update=()=>{13056&e.$$.dirty&&n(2,s={"aria-disabled":!!d||void 0,"aria-selected":!!i||void 0,"data-disabled":!!d||void 0,"data-selected":!!i||void 0,"data-cmdk-item":"","data-value":u,role:"option",id:h})},[f,E,s,o,b,x,function(e){!u&&e.textContent&&n(8,u=e.textContent.trim().toLowerCase()),$.value(h,u),e.setAttribute(ri,u);const t=ar(ai(e,"pointermove",(()=>{d||T()})),ai(e,"click",(()=>{d||(T(),null==p||p(u))})));return{destroy(){t()}}},a,u,d,p,m,h,i,c,r]}class rf extends ve{constructor(e){super(),Te(this,e,sf,nf,ke,{disabled:9,value:8,onSelect:10,alwaysRender:11,asChild:0,id:12})}}const of=e=>({}),Wn=e=>({list:e[7],sizer:e[8]});function af(e){let t,n,s,l,i,o=""===e[2].search,a=Xn(e),r=[e[6]],c={};for(let e=0;e<r.length;e+=1)c=re(c,r[e]);let d=[e[5],e[9]],u={};for(let e=0;e<d.length;e+=1)u=re(u,d[e]);return{c(){t=N("div"),n=N("div"),a.c(),this.h()},l(e){t=D(e,"DIV",{});var s=V(t);n=D(s,"DIV",{});var l=V(n);a.l(l),l.forEach(m),s.forEach(m),this.h()},h(){Ve(n,c),Ve(t,u)},m(o,r){E(o,t,r),P(t,n),a.m(n,null),e[12](t),s=!0,l||(i=ot(e[4].call(null,n)),l=!0)},p(e,s){4&s&&ke(o,o=""===e[2].search)?(ge(),v(a,1,1,me),be(),a=Xn(e),a.c(),y(a,1),a.m(n,null)):a.p(e,s),Ve(t,u=et(d,[e[5],512&s&&e[9]]))},i(e){s||(y(a),s=!0)},o(e){v(a),s=!1},d(n){n&&m(t),a.d(n),e[12](null),l=!1,i()}}}function ff(e){let t,n,s=""===e[2].search,l=Yn(e);return{c(){l.c(),t=fe()},l(e){l.l(e),t=fe()},m(e,s){l.m(e,s),E(e,t,s),n=!0},p(e,n){4&n&&ke(s,s=""===e[2].search)?(ge(),v(l,1,1,me),be(),l=Yn(e),l.c(),y(l,1),l.m(t.parentNode,t)):l.p(e,n)},i(e){n||(y(l),n=!0)},o(e){v(l),n=!1},d(e){e&&m(t),l.d(e)}}}function Xn(e){let t;const n=e[11].default,s=we(n,e,e[10],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||1024&l)&&Le(s,n,e,e[10],t?De(n,e[10],l,null):Ie(e[10]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Yn(e){let t;const n=e[11].default,s=we(n,e,e[10],Wn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||1024&l)&&Le(s,n,e,e[10],t?De(n,e[10],l,of):Ie(e[10]),Wn)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function uf(e){let t,n,s,l;const i=[ff,af],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function cf(e,t,n){const s=["el","asChild"];let l,i=Ee(t,s),{$$slots:o={},$$scope:a}=t;const{ids:r}=fi(),c=Gl();je(e,c,(e=>n(2,l=e)));let{el:d}=t,{asChild:u=!1}=t;function p(e){let t;const n=e.closest("[data-cmdk-list]");if(!Jr(n))return;const s=new ResizeObserver((()=>{t=requestAnimationFrame((()=>{const t=e.offsetHeight;n.style.setProperty("--cmdk-list-height",t.toFixed(1)+"px")}))}));return s.observe(e),{destroy(){cancelAnimationFrame(t),s.unobserve(e)}}}const m={"data-cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:r.list,"aria-labelledby":r.input},f={"data-cmdk-list-sizer":""},h={attrs:m},y={attrs:f,action:p};return e.$$set=e=>{t=re(re({},t),nt(e)),n(9,i=Ee(t,s)),"el"in e&&n(0,d=e.el),"asChild"in e&&n(1,u=e.asChild),"$$scope"in e&&n(10,a=e.$$scope)},[d,u,l,c,p,m,f,h,y,i,a,o,function(e){ft[e?"unshift":"push"]((()=>{d=e,n(0,d)}))}]}class df extends ve{constructor(e){super(),Te(this,e,cf,uf,ke,{el:0,asChild:1})}}function mf(e){let t;const n=e[3].default,s=we(n,e,e[5],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||32&l)&&Le(s,n,e,e[5],t?De(n,e[5],l,null):Ie(e[5]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function hf(e){let t,n,s;const l=[{class:lt("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",e[1])},e[2]];function i(t){e[4](t)}let o={$$slots:{default:[mf]},$$scope:{ctx:e}};for(let e=0;e<l.length;e+=1)o=re(o,l[e]);return void 0!==e[0]&&(o.value=e[0]),t=new Na({props:o}),ft.push((()=>Hl(t,"value",i))),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,n){le(t,e,n),s=!0},p(e,[s]){const i=6&s?et(l,[2&s&&{class:lt("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",e[1])},4&s&&Nt(e[2])]):{};32&s&&(i.$$scope={dirty:s,ctx:e}),!n&&1&s&&(n=!0,i.value=e[0],Ul((()=>n=!1))),t.$set(i)},i(e){s||(y(t.$$.fragment,e),s=!0)},o(e){v(t.$$.fragment,e),s=!1},d(e){te(t,e)}}}function _f(e,t,n){const s=["value","class"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{value:a}=t,{class:r}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(2,l=Ee(t,s)),"value"in e&&n(0,a=e.value),"class"in e&&n(1,r=e.class),"$$scope"in e&&n(5,o=e.$$scope)},[a,r,l,i,function(e){a=e,n(0,a)},o]}class gf extends ve{constructor(e){super(),Te(this,e,_f,hf,ke,{value:0,class:1})}}function bf(e){let t;const n=e[2].default,s=we(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&Le(s,n,e,e[3],t?De(n,e[3],l,null):Ie(e[3]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function yf(e){let t,n;const s=[{class:lt("py-6 text-center text-sm",e[0])},e[1]];let l={$$slots:{default:[bf]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new Ba({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const l=3&n?et(s,[1&n&&{class:lt("py-6 text-center text-sm",e[0])},2&n&&Nt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function kf(e,t,n){const s=["class"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(1,l=Ee(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class Cf extends ve{constructor(e){super(),Te(this,e,kf,yf,ke,{class:0})}}function Ef(e){let t;const n=e[2].default,s=we(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&Le(s,n,e,e[3],t?De(n,e[3],l,null):Ie(e[3]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function vf(e){let t,n;const s=[{class:lt("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",e[0])},e[1]];let l={$$slots:{default:[Ef]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new Wa({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const l=3&n?et(s,[1&n&&{class:lt("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",e[0])},2&n&&Nt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Tf(e,t,n){const s=["class"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(1,l=Ee(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class Sf extends ve{constructor(e){super(),Te(this,e,Tf,vf,ke,{class:0})}}function pf(e){let t;const n=e[2].default,s=we(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&Le(s,n,e,e[3],t?De(n,e[3],l,null):Ie(e[3]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Af(e){let t,n;const s=[{class:lt("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e[0])},e[1]];let l={$$slots:{default:[pf]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new rf({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const l=3&n?et(s,[1&n&&{class:lt("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e[0])},2&n&&Nt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Of(e,t,n){const s=["class"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(1,l=Ee(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class $i extends ve{constructor(e){super(),Te(this,e,Of,Af,ke,{class:0})}}function wf(e){let t,n,s,l,i,o;n=new ui({props:{src:xr,class:"mr-2 h-4 w-4 shrink-0 text-base-content-muted"}});const a=[{class:lt("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",e[1])},e[2]];function r(t){e[3](t)}let c={};for(let e=0;e<a.length;e+=1)c=re(c,a[e]);return void 0!==e[0]&&(c.value=e[0]),l=new xa({props:c}),ft.push((()=>Hl(l,"value",r))),{c(){t=N("div"),ne(n.$$.fragment),s=j(),ne(l.$$.fragment),this.h()},l(e){t=D(e,"DIV",{class:!0,"data-cmdk-input-wrapper":!0});var i=V(t);ie(n.$$.fragment,i),s=q(i),ie(l.$$.fragment,i),i.forEach(m),this.h()},h(){T(t,"class","flex items-center border-b border-base-300 px-3"),T(t,"data-cmdk-input-wrapper","")},m(e,i){E(e,t,i),le(n,t,null),P(t,s),le(l,t,null),o=!0},p(e,[t]){const n=6&t?et(a,[2&t&&{class:lt("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",e[1])},4&t&&Nt(e[2])]):{};!i&&1&t&&(i=!0,n.value=e[0],Ul((()=>i=!1))),l.$set(n)},i(e){o||(y(n.$$.fragment,e),y(l.$$.fragment,e),o=!0)},o(e){v(n.$$.fragment,e),v(l.$$.fragment,e),o=!1},d(e){e&&m(t),te(n),te(l)}}}function Lf(e,t,n){const s=["class","value"];let l=Ee(t,s),{class:i}=t,{value:o=""}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(2,l=Ee(t,s)),"class"in e&&n(1,i=e.class),"value"in e&&n(0,o=e.value)},[o,i,l,function(e){o=e,n(0,o)}]}class If extends ve{constructor(e){super(),Te(this,e,Lf,wf,ke,{class:1,value:0})}}function Df(e){let t;const n=e[2].default,s=we(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&Le(s,n,e,e[3],t?De(n,e[3],l,null):Ie(e[3]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Nf(e){let t,n;const s=[{class:lt("max-h-[300px] overflow-y-auto overflow-x-hidden",e[0])},e[1]];let l={$$slots:{default:[Df]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new df({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const l=3&n?et(s,[1&n&&{class:lt("max-h-[300px] overflow-y-auto overflow-x-hidden",e[0])},2&n&&Nt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Mf(e,t,n){const s=["class"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(1,l=Ee(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class Rf extends ve{constructor(e){super(),Te(this,e,Mf,Nf,ke,{class:0})}}function Pf(e){let t,n,s;return n=new ui({props:{src:Zs,class:lt("h-4 w-4",e[2]?"":"text-transparent")}}),{c(){t=N("div"),ne(n.$$.fragment),this.h()},l(e){t=D(e,"DIV",{class:!0});var s=V(t);ie(n.$$.fragment,s),s.forEach(m),this.h()},h(){T(t,"class","mr-2 flex h-4 w-4 items-center justify-center")},m(e,l){E(e,t,l),le(n,t,null),s=!0},p(e,t){const s={};4&t&&(s.class=lt("h-4 w-4",e[2]?"":"text-transparent")),n.$set(s)},i(e){s||(y(n.$$.fragment,e),s=!0)},o(e){v(n.$$.fragment,e),s=!1},d(e){e&&m(t),te(n)}}}function zf(e){let t,n,s,l;return n=new ui({props:{src:Zs,class:lt("h-4 w-4")}}),{c(){t=N("div"),ne(n.$$.fragment),this.h()},l(e){t=D(e,"DIV",{class:!0});var s=V(t);ie(n.$$.fragment,s),s.forEach(m),this.h()},h(){T(t,"class",s=lt("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",e[2]?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible"))},m(e,s){E(e,t,s),le(n,t,null),l=!0},p(e,n){(!l||4&n&&s!==(s=lt("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",e[2]?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible")))&&T(t,"class",s)},i(e){l||(y(n.$$.fragment,e),l=!0)},o(e){v(n.$$.fragment,e),l=!1},d(e){e&&m(t),te(n)}}}function Ff(e){let t,n,s,l,i,o;const a=[zf,Pf],r=[];function c(e,t){return e[4]?0:1}return t=c(e),n=r[t]=a[t](e),{c(){n.c(),s=j(),l=N("span"),i=de(e[1]),this.h()},l(t){n.l(t),s=q(t),l=D(t,"SPAN",{class:!0});var o=V(l);i=ce(o,e[1]),o.forEach(m),this.h()},h(){T(l,"class","line-clamp-4")},m(e,n){r[t].m(e,n),E(e,s,n),E(e,l,n),P(l,i),o=!0},p(e,l){let d=t;t=c(e),t===d?r[t].p(e,l):(ge(),v(r[d],1,1,(()=>{r[d]=null})),be(),n=r[t],n?n.p(e,l):(n=r[t]=a[t](e),n.c()),y(n,1),n.m(s.parentNode,s)),(!o||2&l)&&Xe(i,e[1])},i(e){o||(y(n),o=!0)},o(e){v(n),o=!1},d(e){e&&(m(s),m(l)),r[t].d(e)}}}function Bf(e){let t,n;return t=new $i({props:{value:String(e[1]),onSelect:e[5],$$slots:{default:[Ff]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const s={};2&n&&(s.value=String(e[1])),11&n&&(s.onSelect=e[5]),86&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Uf(e,t,n){let{value:s}=t,{valueLabel:l=s}=t,{active:i=!1}=t,{handleSelect:o}=t,{multiple:a}=t;return e.$$set=e=>{"value"in e&&n(0,s=e.value),"valueLabel"in e&&n(1,l=e.valueLabel),"active"in e&&n(2,i=e.active),"handleSelect"in e&&n(3,o=e.handleSelect),"multiple"in e&&n(4,a=e.multiple)},[s,l,i,o,a,()=>o({value:s,label:l})]}class fr extends ve{constructor(e){super(),Te(this,e,Uf,Bf,ke,{value:0,valueLabel:1,active:2,handleSelect:3,multiple:4})}}function Vf(e){let t;const n=e[6].default,s=we(n,e,e[7],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||128&l)&&Le(s,n,e,e[7],t?De(n,e[7],l,null):Ie(e[7]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Hf(e){let t,n;const s=[{transition:e[1]},{transitionConfig:e[2]},{align:e[3]},{sideOffset:e[4]},e[5],{class:lt("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",e[0])}];let l={$$slots:{default:[Vf]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new fa({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const l=63&n?et(s,[2&n&&{transition:e[1]},4&n&&{transitionConfig:e[2]},8&n&&{align:e[3]},16&n&&{sideOffset:e[4]},32&n&&Nt(e[5]),1&n&&{class:lt("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",e[0])}]):{};128&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Gf(e,t,n){const s=["class","transition","transitionConfig","align","sideOffset"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t,{transition:r=$r}=t,{transitionConfig:c}=t,{align:d="center"}=t,{sideOffset:u=4}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(5,l=Ee(t,s)),"class"in e&&n(0,a=e.class),"transition"in e&&n(1,r=e.transition),"transitionConfig"in e&&n(2,c=e.transitionConfig),"align"in e&&n(3,d=e.align),"sideOffset"in e&&n(4,u=e.sideOffset),"$$scope"in e&&n(7,o=e.$$scope)},[a,r,c,d,u,l,i,o]}class qf extends ve{constructor(e){super(),Te(this,e,Gf,Hf,ke,{class:0,transition:1,transitionConfig:2,align:3,sideOffset:4})}}const jf=Ko,Wf=ga;function Xf(e){let t,n;const s=[{class:lt("shrink-0 bg-base-300","horizontal"===e[1]?"h-[1px] w-full":"h-full w-[1px]",e[0])},{orientation:e[1]},{decorative:e[2]},e[3]];let l={};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new Go({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const l=15&n?et(s,[3&n&&{class:lt("shrink-0 bg-base-300","horizontal"===e[1]?"h-[1px] w-full":"h-full w-[1px]",e[0])},2&n&&{orientation:e[1]},4&n&&{decorative:e[2]},8&n&&Nt(e[3])]):{};t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Yf(e,t,n){const s=["class","orientation","decorative"];let l=Ee(t,s),{class:i}=t,{orientation:o="horizontal"}=t,{decorative:a}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(3,l=Ee(t,s)),"class"in e&&n(0,i=e.class),"orientation"in e&&n(1,o=e.orientation),"decorative"in e&&n(2,a=e.decorative)},[i,o,a,l]}class ur extends ve{constructor(e){super(),Te(this,e,Yf,Xf,ke,{class:0,orientation:1,decorative:2})}}function Hi(e){let t,n,s;const l=e[5].default,i=we(l,e,e[4],null);let o=[{href:e[1]},{class:n=lt(gn({variant:e[2],className:e[0]}))},e[3]],a={};for(let e=0;e<o.length;e+=1)a=re(a,o[e]);return{c(){t=N(e[1]?"a":"span"),i&&i.c(),this.h()},l(n){t=D(n,((e[1]?"a":"span")||"null").toUpperCase(),{href:!0,class:!0});var s=V(t);i&&i.l(s),s.forEach(m),this.h()},h(){cn(e[1]?"a":"span")(t,a)},m(e,n){E(e,t,n),i&&i.m(t,null),s=!0},p(e,r){i&&i.p&&(!s||16&r)&&Le(i,l,e,e[4],s?De(l,e[4],r,null):Ie(e[4]),null),cn(e[1]?"a":"span")(t,a=et(o,[(!s||2&r)&&{href:e[1]},(!s||5&r&&n!==(n=lt(gn({variant:e[2],className:e[0]}))))&&{class:n},8&r&&e[3]]))},i(e){s||(y(i,e),s=!0)},o(e){v(i,e),s=!1},d(e){e&&m(t),i&&i.d(e)}}}function Kf(e){let t,n,s=e[1]?"a":"span",l=(e[1]?"a":"span")&&Hi(e);return{c(){l&&l.c(),t=fe()},l(e){l&&l.l(e),t=fe()},m(e,s){l&&l.m(e,s),E(e,t,s),n=!0},p(e,[n]){e[1],s?ke(s,e[1]?"a":"span")?(l.d(1),l=Hi(e),s=e[1]?"a":"span",l.c(),l.m(t.parentNode,t)):l.p(e,n):(l=Hi(e),s=e[1]?"a":"span",l.c(),l.m(t.parentNode,t))},i(e){n||(y(l,e),n=!0)},o(e){v(l,e),n=!1},d(e){e&&m(t),l&&l.d(e)}}}function Qf(e,t,n){const s=["class","href","variant"];let l=Ee(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t,{href:r}=t,{variant:c="default"}=t;return e.$$set=e=>{t=re(re({},t),nt(e)),n(3,l=Ee(t,s)),"class"in e&&n(0,a=e.class),"href"in e&&n(1,r=e.href),"variant"in e&&n(2,c=e.variant),"$$scope"in e&&n(4,o=e.$$scope)},[a,r,c,l,o,i]}class en extends ve{constructor(e){super(),Te(this,e,Qf,Kf,ke,{class:0,href:1,variant:2})}}function Kn(e){return t=>t.map((t=>{var n;const s={},l=Object.keys(t);for(const i of l)s[null!=(n=e[i])?n:i]=t[i];return s}))}function Zf(e,t){if(0===e.length||0===t.length)return{};const n=Object.keys(e[0]),s=Object.keys(t[0]),l={};for(const e of n)s.includes(e)&&(l[e]=e);return l}function Jf(e,t,n){for(const s in n)if(e[n[s]]!==t[s])return!1;return!0}function xf(e,t){return t=>{if(!e.length)return t;const n=Zf(t,e),s=Object.keys(e[0]);return t.flatMap((t=>{const l=e.filter((e=>Jf(t,e,n)));if(l.length)return l.map((e=>({...t,...e})));const i=Object.fromEntries(s.filter((e=>null==t[e])).map((e=>[e,void 0])));return{...t,...i}}))}}function Qn(e){return t=>{const n=t.map((e=>({...e})));for(const s in e){const l=e[s],i="function"==typeof l?l(n):l,o=null!=i&&i[Symbol.iterator]&&"string"!=typeof i?i:t.map((()=>i));let a=-1;for(const e of n)e[s]=o[++a]}return n}}function $f(e){return t=>{const n=tu(e),s=[];for(const e in n){const l=n[e];let i;i="function"==typeof l?l(t):Array.isArray(l)?l:Array.from(new Set(t.map((t=>t[e])))),s.push(i.map((t=>({[e]:t}))))}return eu(s)}}function eu(e){const t=[];return function e(t,n,s){if(!s.length&&null!=n)return void t.push(n);const l=s[0],i=s.slice(1);for(const s of l)e(t,{...n,...s},i)}(t,null,e),t}function tu(e){if(Array.isArray(e)){const t={};for(const n of e)t[n]=n;return t}return"object"==typeof e?e:{[e]:e}}function lu(e){return t=>{const n=[];for(const s of t){const t={...s};for(const n in e)null==t[n]&&(t[n]=e[n]);n.push(t)}return n}}function Zn(e,t){return n=>{const s=$f(e)(n),l=xf(n)(s);return t?lu(t)(l):l}}function Jn(e,t,n){return null==e||null==t?void 0:0===t&&0===e?0:n||0!==t?e/t:void 0}function xn(e,t,n){const s="function"==typeof e?e:t=>t[e],l=e=>e[t],{predicate:i,allowDivideByZero:o}={};return null==i?(e,t,n)=>{const i=l(e);return Jn(s(e,t,n),i,o)}:(e,t,n)=>{if(!i(e,t,n))return;const a=l(e);return Jn(s(e,t,n),a,o)}}function $n(e,t,n){const s=e.slice();return s[22]=t[n],s}const iu=e=>({item:16&e}),es=e=>({item:e[22].data});function nu(e){let t;return{c(){t=de("Missing template")},l(e){t=ce(e,"Missing template")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function ts(e,t){let n,s,l;const i=t[14].default,o=we(i,t,t[13],es),a=o||nu();return{key:e,first:null,c(){n=N("div"),a&&a.c(),s=j(),this.h()},l(e){n=D(e,"DIV",{class:!0});var t=V(n);a&&a.l(t),s=q(t),t.forEach(m),this.h()},h(){T(n,"class","row svelte-1youqmj"),this.first=n},m(e,t){E(e,n,t),a&&a.m(n,null),P(n,s),l=!0},p(e,n){t=e,o&&o.p&&(!l||8208&n)&&Le(o,i,t,t[13],l?De(i,t[13],n,iu):Ie(t[13]),es)},i(e){l||(y(a,e),l=!0)},o(e){v(a,e),l=!1},d(e){e&&m(n),a&&a.d(e)}}}function su(e){let t,n,s,l,i,o,a=[],r=new Map,c=at(e[4]);const d=e=>e[22].index;for(let t=0;t<c.length;t+=1){let n=$n(e,c,t),s=d(n);r.set(s,a[t]=ts(s,n))}return{c(){t=N("div"),n=N("div");for(let e=0;e<a.length;e+=1)a[e].c();this.h()},l(e){t=D(e,"DIV",{style:!0,class:!0});var s=V(t);n=D(s,"DIV",{class:!0,style:!0});var l=V(n);for(let e=0;e<a.length;e+=1)a[e].l(l);l.forEach(m),s.forEach(m),this.h()},h(){T(n,"class","contents svelte-1youqmj"),Q(n,"padding-top",e[5]+"px"),Q(n,"padding-bottom",e[6]+"px"),Q(t,"height",e[0]),T(t,"class","viewport svelte-1youqmj"),kt((()=>e[17].call(t)))},m(r,c){E(r,t,c),P(t,n);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(n,null);e[15](n),e[16](t),s=Cr(t,e[17].bind(t)),l=!0,i||(o=qe(t,"scroll",e[7]),i=!0)},p(e,[s]){8208&s&&(c=at(e[4]),ge(),a=Js(a,s,d,1,e,c,r,n,xs,ts,null,$n),be()),(!l||32&s)&&Q(n,"padding-top",e[5]+"px"),(!l||64&s)&&Q(n,"padding-bottom",e[6]+"px"),(!l||1&s)&&Q(t,"height",e[0])},i(e){if(!l){for(let e=0;e<c.length;e+=1)y(a[e]);l=!0}},o(e){for(let e=0;e<a.length;e+=1)v(a[e]);l=!1},d(n){n&&m(t);for(let e=0;e<a.length;e+=1)a[e].d();e[15](null),e[16](null),s(),i=!1,o()}}}function ru(e,t,n){let s,l,i,o,a,r,{$$slots:c={},$$scope:d}=t,{items:u}=t,{height:p="100%"}=t,{itemHeight:m}=t,{start:f=0}=t,{end:h=0}=t,y=[],$=0,g=0,v=0;return gl((()=>(s=i.getElementsByClassName("row"),n(12,a=!0),()=>n(12,a=!1)))),e.$$set=e=>{"items"in e&&n(10,u=e.items),"height"in e&&n(0,p=e.height),"itemHeight"in e&&n(11,m=e.itemHeight),"start"in e&&n(8,f=e.start),"end"in e&&n(9,h=e.end),"$$scope"in e&&n(13,d=e.$$scope)},e.$$.update=()=>{1792&e.$$.dirty&&n(4,o=u.slice(f,h).map(((e,t)=>({index:t+f,data:e})))),7170&e.$$.dirty&&a&&async function(e,t,i){const{scrollTop:o}=l;if(await Il(),!a)return;let c=g-o,d=f;for(;c<t&&d<e.length;){let e=s[d-f];if(!e){if(n(9,h=d+1),await Il(),!a)return;e=s[d-f]}c+=y[d]=i||(null==e?void 0:e.offsetHeight)||Number.MAX_SAFE_INTEGER,d+=1}n(9,h=d);const u=e.length-h;r=(g+c)/h,n(6,v=u*r),y.length=e.length}(u,$,m)},[p,$,l,i,o,g,v,async function(){var e,t;const{scrollTop:i}=l,o=f;for(let t=0;t<s.length;t+=1)y[f+t]=m||(null==(e=s[t])?void 0:e.offsetHeight)||Number.MAX_SAFE_INTEGER;let a=0,c=0;for(;a<u.length;){const e=y[a]||r;if(c+e>i){n(8,f=a),n(5,g=c);break}c+=e,a+=1}for(;a<u.length&&(c+=y[a]||r,a+=1,!(c>i+$)););n(9,h=a);const d=u.length-h;for(r=c/h;a<u.length;)y[a++]=r;if(n(6,v=d*r),f<o){await Il();let e=0,n=0;for(let l=f;l<o;l+=1)s[l-f]&&(e+=y[l],n+=m||(null==(t=s[l-f])?void 0:t.offsetHeight)||Number.MAX_SAFE_INTEGER);const a=n-e;l.scrollTo(0,i+a)}},f,h,u,m,a,d,c,function(e){ft[e?"unshift":"push"]((()=>{i=e,n(3,i)}))},function(e){ft[e?"unshift":"push"]((()=>{l=e,n(2,l)}))},function(){$=this.offsetHeight,n(1,$)}]}class ou extends ve{constructor(e){super(),Te(this,e,ru,su,ke,{items:10,height:0,itemHeight:11,start:8,end:9})}}const{Boolean:cr}=sr;function ls(e,t,n){const s=e.slice();return s[58]=t[n],s[60]=n,s}function is(e,t,n){const s=e.slice();return s[58]=t[n],s}function ns(e,t,n){const s=e.slice();return s[58]=t[n],s}function ss(e,t){let n,s,l;return s=new hl({props:{value:t[58][t[6]]??t[58].value,valueLabel:t[58][t[7]]??t[58].label,idx:hs(t[58]),__auto:!0}}),{key:e,first:null,c(){n=fe(),ne(s.$$.fragment),this.h()},l(e){n=fe(),ie(s.$$.fragment,e),this.h()},h(){this.first=n},m(e,t){E(e,n,t),le(s,e,t),l=!0},p(e,n){t=e;const l={};4160&n[0]&&(l.value=t[58][t[6]]??t[58].value),4224&n[0]&&(l.valueLabel=t[58][t[7]]??t[58].label),4096&n[0]&&(l.idx=hs(t[58])),s.$set(l)},i(e){l||(y(s.$$.fragment,e),l=!0)},o(e){v(s.$$.fragment,e),l=!1},d(e){e&&m(n),te(s,e)}}}function au(e){let t,n,s;function l(t){e[40](t)}let i={$$slots:{default:[Iu]},$$scope:{ctx:e}};return void 0!==e[8]&&(i.open=e[8]),t=new jf({props:i}),ft.push((()=>Hl(t,"open",l))),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,n){le(t,e,n),s=!0},p(e,s){const l={};49981&s[0]|1024&s[1]&&(l.$$scope={dirty:s,ctx:e}),!n&&256&s[0]&&(n=!0,l.open=e[8],Ul((()=>n=!1))),t.$set(l)},i(e){s||(y(t.$$.fragment,e),s=!0)},o(e){v(t.$$.fragment,e),s=!1},d(e){te(t,e)}}}function fu(e){let t,n;return t=new no({props:{inputType:"Dropdown",error:e[10],height:"32",width:"140"}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};1024&n[0]&&(s.error=e[10]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function uu(e){let t,n,s,l,i=(e[3]??vt(e[4]))+"",o=e[5]&&rs(e);return{c(){t=de(i),n=j(),o&&o.c(),s=fe()},l(e){t=ce(e,i),n=q(e),o&&o.l(e),s=fe()},m(e,i){E(e,t,i),E(e,n,i),o&&o.m(e,i),E(e,s,i),l=!0},p(e,n){(!l||24&n[0])&&i!==(i=(e[3]??vt(e[4]))+"")&&Xe(t,i),e[5]?o?(o.p(e,n),32&n[0]&&y(o,1)):(o=rs(e),o.c(),y(o,1),o.m(s.parentNode,s)):o&&(ge(),v(o,1,1,(()=>{o=null})),be())},i(e){l||(y(o),l=!0)},o(e){v(o),l=!1},d(e){e&&(m(t),m(n),m(s)),o&&o.d(e)}}}function cu(e){let t,n=e[14][0].label+"";return{c(){t=de(n)},l(e){t=ce(e,n)},m(e,n){E(e,t,n)},p(e,s){16384&s[0]&&n!==(n=e[14][0].label+"")&&Xe(t,n)},i:me,o:me,d(e){e&&m(t)}}}function du(e){let t,n,s,l,i,o=e[5]&&os(e),a=e[14].length>0&&as(e);return{c(){t=de(e[3]),n=j(),o&&o.c(),s=j(),a&&a.c(),l=fe()},l(i){t=ce(i,e[3]),n=q(i),o&&o.l(i),s=q(i),a&&a.l(i),l=fe()},m(e,r){E(e,t,r),E(e,n,r),o&&o.m(e,r),E(e,s,r),a&&a.m(e,r),E(e,l,r),i=!0},p(e,n){(!i||8&n[0])&&Xe(t,e[3]),e[5]?o?(o.p(e,n),32&n[0]&&y(o,1)):(o=os(e),o.c(),y(o,1),o.m(s.parentNode,s)):o&&(ge(),v(o,1,1,(()=>{o=null})),be()),e[14].length>0?a?(a.p(e,n),16384&n[0]&&y(a,1)):(a=as(e),a.c(),y(a,1),a.m(l.parentNode,l)):a&&(ge(),v(a,1,1,(()=>{a=null})),be())},i(e){i||(y(o),y(a),i=!0)},o(e){v(o),v(a),i=!1},d(e){e&&(m(t),m(n),m(s),m(l)),o&&o.d(e),a&&a.d(e)}}}function rs(e){let t,n;return t=new $s({props:{description:e[5],className:"pl-1"}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};32&n[0]&&(s.description=e[5]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function os(e){let t,n;return t=new $s({props:{description:e[5],className:"pl-1"}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};32&n[0]&&(s.description=e[5]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function as(e){let t,n,s,l,i=e[14][0].label+"";return t=new ur({props:{orientation:"vertical",class:"mx-2 h-4"}}),{c(){ne(t.$$.fragment),n=j(),s=de(i)},l(e){ie(t.$$.fragment,e),n=q(e),s=ce(e,i)},m(e,i){le(t,e,i),E(e,n,i),E(e,s,i),l=!0},p(e,t){(!l||16384&t[0])&&i!==(i=e[14][0].label+"")&&Xe(s,i)},i(e){l||(y(t.$$.fragment,e),l=!0)},o(e){v(t.$$.fragment,e),l=!1},d(e){e&&(m(n),m(s)),te(t,e)}}}function fs(e){let t,n,s,l,i,o,a,r;t=new ur({props:{orientation:"vertical",class:"mx-2 h-4"}}),s=new en({props:{variant:"default",class:"rounded-xs px-1 font-normal sm:hidden",$$slots:{default:[mu]},$$scope:{ctx:e}}});const c=[_u,hu],d=[];function u(e,t){return e[14].length>3?0:1}return o=u(e),a=d[o]=c[o](e),{c(){ne(t.$$.fragment),n=j(),ne(s.$$.fragment),l=j(),i=N("div"),a.c(),this.h()},l(e){ie(t.$$.fragment,e),n=q(e),ie(s.$$.fragment,e),l=q(e),i=D(e,"DIV",{class:!0});var o=V(i);a.l(o),o.forEach(m),this.h()},h(){T(i,"class","hidden space-x-1 sm:flex")},m(e,a){le(t,e,a),E(e,n,a),le(s,e,a),E(e,l,a),E(e,i,a),d[o].m(i,null),r=!0},p(e,t){const n={};16384&t[0]|1024&t[1]&&(n.$$scope={dirty:t,ctx:e}),s.$set(n);let l=o;o=u(e),o===l?d[o].p(e,t):(ge(),v(d[l],1,1,(()=>{d[l]=null})),be(),a=d[o],a?a.p(e,t):(a=d[o]=c[o](e),a.c()),y(a,1),a.m(i,null))},i(e){r||(y(t.$$.fragment,e),y(s.$$.fragment,e),y(a),r=!0)},o(e){v(t.$$.fragment,e),v(s.$$.fragment,e),v(a),r=!1},d(e){e&&(m(n),m(l),m(i)),te(t,e),te(s,e),d[o].d()}}}function mu(e){let t,n=e[14].length+"";return{c(){t=de(n)},l(e){t=ce(e,n)},m(e,n){E(e,t,n)},p(e,s){16384&s[0]&&n!==(n=e[14].length+"")&&Xe(t,n)},d(e){e&&m(t)}}}function hu(e){let t,n,s=at(e[14]),l=[];for(let t=0;t<s.length;t+=1)l[t]=us(is(e,s,t));const i=e=>v(l[e],1,1,(()=>{l[e]=null}));return{c(){for(let e=0;e<l.length;e+=1)l[e].c();t=fe()},l(e){for(let t=0;t<l.length;t+=1)l[t].l(e);t=fe()},m(e,s){for(let t=0;t<l.length;t+=1)l[t]&&l[t].m(e,s);E(e,t,s),n=!0},p(e,n){if(16384&n[0]){let o;for(s=at(e[14]),o=0;o<s.length;o+=1){const i=is(e,s,o);l[o]?(l[o].p(i,n),y(l[o],1)):(l[o]=us(i),l[o].c(),y(l[o],1),l[o].m(t.parentNode,t))}for(ge(),o=s.length;o<l.length;o+=1)i(o);be()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)y(l[e]);n=!0}},o(e){l=l.filter(cr);for(let e=0;e<l.length;e+=1)v(l[e]);n=!1},d(e){e&&m(t),jt(l,e)}}}function _u(e){let t,n;return t=new en({props:{variant:"default",class:"rounded-xs px-1 font-normal",$$slots:{default:[bu]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};16384&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function gu(e){let t,n,s=e[58].label+"";return{c(){t=de(s),n=j()},l(e){t=ce(e,s),n=q(e)},m(e,s){E(e,t,s),E(e,n,s)},p(e,n){16384&n[0]&&s!==(s=e[58].label+"")&&Xe(t,s)},d(e){e&&(m(t),m(n))}}}function us(e){let t,n;return t=new en({props:{variant:"default",class:"rounded-xs px-1 font-normal",$$slots:{default:[gu]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};16384&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function bu(e){let t,n,s=e[14].length+"";return{c(){t=de(s),n=de(" Selected")},l(e){t=ce(e,s),n=ce(e," Selected")},m(e,s){E(e,t,s),E(e,n,s)},p(e,n){16384&n[0]&&s!==(s=e[14].length+"")&&Xe(t,s)},d(e){e&&(m(t),m(n))}}}function yu(e){let t,n,s,l,i,o,a;const r=[du,cu,uu],c=[];function d(e,t){return e[3]&&!e[0]?0:e[14].length>0&&!e[0]?1:2}t=d(e),n=c[t]=r[t](e),l=new ui({props:{src:so,class:"ml-2 h-4 w-4"}});let u=e[14].length>0&&e[0]&&fs(e);return{c(){n.c(),s=j(),ne(l.$$.fragment),i=j(),u&&u.c(),o=fe()},l(e){n.l(e),s=q(e),ie(l.$$.fragment,e),i=q(e),u&&u.l(e),o=fe()},m(e,n){c[t].m(e,n),E(e,s,n),le(l,e,n),E(e,i,n),u&&u.m(e,n),E(e,o,n),a=!0},p(e,l){let i=t;t=d(e),t===i?c[t].p(e,l):(ge(),v(c[i],1,1,(()=>{c[i]=null})),be(),n=c[t],n?n.p(e,l):(n=c[t]=r[t](e),n.c()),y(n,1),n.m(s.parentNode,s)),e[14].length>0&&e[0]?u?(u.p(e,l),16385&l[0]&&y(u,1)):(u=fs(e),u.c(),y(u,1),u.m(o.parentNode,o)):u&&(ge(),v(u,1,1,(()=>{u=null})),be())},i(e){a||(y(n),y(l.$$.fragment,e),y(u),a=!0)},o(e){v(n),v(l.$$.fragment,e),v(u),a=!1},d(e){e&&(m(s),m(i),m(o)),c[t].d(e),te(l,e),u&&u.d(e)}}}function ku(e){let t,n;return t=new wo({props:{builders:[e[61]],variant:"outline",role:"combobox",size:"sm",class:"min-w-5 h-8 border border-base-300","aria-label":e[3]??vt(e[4]),$$slots:{default:[yu]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};1073741824&n[1]&&(s.builders=[e[61]]),24&n[0]&&(s["aria-label"]=e[3]??vt(e[4])),16441&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Cu(e){let t;return{c(){t=de("No results found.")},l(e){t=ce(e,"No results found.")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function Eu(e){let t,n;return t=new ou({props:{height:32*dr+"px",items:e[15],$$slots:{default:[Tu,({item:e})=>({58:e}),({item:e})=>[0,e?134217728:0]]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};32768&n[0]&&(s.items=e[15]),16641&n[0]|134218752&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function vu(e){let t,n,s=at(e[15]),l=[];for(let t=0;t<s.length;t+=1)l[t]=cs(ls(e,s,t));const i=e=>v(l[e],1,1,(()=>{l[e]=null}));return{c(){for(let e=0;e<l.length;e+=1)l[e].c();t=fe()},l(e){for(let t=0;t<l.length;t+=1)l[t].l(e);t=fe()},m(e,s){for(let t=0;t<l.length;t+=1)l[t]&&l[t].m(e,s);E(e,t,s),n=!0},p(e,n){if(4243713&n[0]){let o;for(s=at(e[15]),o=0;o<s.length;o+=1){const i=ls(e,s,o);l[o]?(l[o].p(i,n),y(l[o],1)):(l[o]=cs(i),l[o].c(),y(l[o],1),l[o].m(t.parentNode,t))}for(ge(),o=s.length;o<l.length;o+=1)i(o);be()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)y(l[e]);n=!0}},o(e){l=l.filter(cr);for(let e=0;e<l.length;e+=1)v(l[e]);n=!1},d(e){e&&m(t),jt(l,e)}}}function Tu(e){var t,n;let s,l;function i(...t){return e[39](e[58],...t)}return s=new fr({props:{value:null==(t=e[58])?void 0:t.value,valueLabel:null==(n=e[58])?void 0:n.label,handleSelect:e[38],multiple:e[0],active:e[14].some(i)}}),{c(){ne(s.$$.fragment)},l(e){ie(s.$$.fragment,e)},m(e,t){le(s,e,t),l=!0},p(t,n){var l,o;e=t;const a={};134217728&n[1]&&(a.value=null==(l=e[58])?void 0:l.value),134217728&n[1]&&(a.valueLabel=null==(o=e[58])?void 0:o.label),257&n[0]&&(a.handleSelect=e[38]),1&n[0]&&(a.multiple=e[0]),16384&n[0]|134217728&n[1]&&(a.active=e[14].some(i)),s.$set(a)},i(e){l||(y(s.$$.fragment,e),l=!0)},o(e){v(s.$$.fragment,e),l=!1},d(e){te(s,e)}}}function cs(e){let t,n;function s(...t){return e[37](e[58],...t)}return t=new fr({props:{id:e[60],value:e[58].value,valueLabel:e[58].label,handleSelect:e[36],multiple:e[0],active:e[14].some(s)}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(n,l){e=n;const i={};32768&l[0]&&(i.value=e[58].value),32768&l[0]&&(i.valueLabel=e[58].label),257&l[0]&&(i.handleSelect=e[36]),1&l[0]&&(i.multiple=e[0]),49152&l[0]&&(i.active=e[14].some(s)),t.$set(i)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Su(e){let t,n,s,l;const i=[vu,Eu],o=[];function a(e,t){return e[15].length<=dr?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function ds(e){let t,n,s,l,i,o=!e[2]&&ms(e);return l=new $i({props:{disabled:0===e[14].length,class:"justify-center text-center",onSelect:e[21],$$slots:{default:[Au]},$$scope:{ctx:e}}}),{c(){o&&o.c(),t=j(),n=N("div"),s=j(),ne(l.$$.fragment),this.h()},l(e){o&&o.l(e),t=q(e),n=D(e,"DIV",{class:!0}),V(n).forEach(m),s=q(e),ie(l.$$.fragment,e),this.h()},h(){T(n,"class","-mx-1 h-px bg-base-300")},m(e,a){o&&o.m(e,a),E(e,t,a),E(e,n,a),E(e,s,a),le(l,e,a),i=!0},p(e,n){e[2]?o&&(ge(),v(o,1,1,(()=>{o=null})),be()):o?(o.p(e,n),4&n[0]&&y(o,1)):(o=ms(e),o.c(),y(o,1),o.m(t.parentNode,t));const s={};16384&n[0]&&(s.disabled=0===e[14].length),1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),l.$set(s)},i(e){i||(y(o),y(l.$$.fragment,e),i=!0)},o(e){v(o),v(l.$$.fragment,e),i=!1},d(e){e&&(m(t),m(n),m(s)),o&&o.d(e),te(l,e)}}}function ms(e){let t,n,s,l;return s=new $i({props:{class:"justify-center text-center",onSelect:e[20],$$slots:{default:[pu]},$$scope:{ctx:e}}}),{c(){t=N("div"),n=j(),ne(s.$$.fragment),this.h()},l(e){t=D(e,"DIV",{class:!0}),V(t).forEach(m),n=q(e),ie(s.$$.fragment,e),this.h()},h(){T(t,"class","-mx-1 h-px bg-base-300")},m(e,i){E(e,t,i),E(e,n,i),le(s,e,i),l=!0},p(e,t){const n={};1024&t[1]&&(n.$$scope={dirty:t,ctx:e}),s.$set(n)},i(e){l||(y(s.$$.fragment,e),l=!0)},o(e){v(s.$$.fragment,e),l=!1},d(e){e&&(m(t),m(n)),te(s,e)}}}function pu(e){let t;return{c(){t=de("Select all")},l(e){t=ce(e,"Select all")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function Au(e){let t;return{c(){t=de("Clear selection")},l(e){t=ce(e,"Clear selection")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function Ou(e){let t,n,s,l,i,o;t=new Cf({props:{$$slots:{default:[Cu]},$$scope:{ctx:e}}}),s=new Sf({props:{$$slots:{default:[Su]},$$scope:{ctx:e}}});let a=e[0]&&ds(e);return{c(){ne(t.$$.fragment),n=j(),ne(s.$$.fragment),l=j(),a&&a.c(),i=fe()},l(e){ie(t.$$.fragment,e),n=q(e),ie(s.$$.fragment,e),l=q(e),a&&a.l(e),i=fe()},m(e,r){le(t,e,r),E(e,n,r),le(s,e,r),E(e,l,r),a&&a.m(e,r),E(e,i,r),o=!0},p(e,n){const l={};1024&n[1]&&(l.$$scope={dirty:n,ctx:e}),t.$set(l);const o={};49409&n[0]|1024&n[1]&&(o.$$scope={dirty:n,ctx:e}),s.$set(o),e[0]?a?(a.p(e,n),1&n[0]&&y(a,1)):(a=ds(e),a.c(),y(a,1),a.m(i.parentNode,i)):a&&(ge(),v(a,1,1,(()=>{a=null})),be())},i(e){o||(y(t.$$.fragment,e),y(s.$$.fragment,e),y(a),o=!0)},o(e){v(t.$$.fragment,e),v(s.$$.fragment,e),v(a),o=!1},d(e){e&&(m(n),m(l),m(i)),te(t,e),te(s,e),a&&a.d(e)}}}function wu(e){let t,n,s,l,i;function o(t){e[35](t)}let a={placeholder:e[3]};return void 0!==e[9]&&(a.value=e[9]),t=new If({props:a}),ft.push((()=>Hl(t,"value",o))),l=new Rf({props:{$$slots:{default:[Ou]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment),s=j(),ne(l.$$.fragment)},l(e){ie(t.$$.fragment,e),s=q(e),ie(l.$$.fragment,e)},m(e,n){le(t,e,n),E(e,s,n),le(l,e,n),i=!0},p(e,s){const i={};8&s[0]&&(i.placeholder=e[3]),!n&&512&s[0]&&(n=!0,i.value=e[9],Ul((()=>n=!1))),t.$set(i);const o={};49413&s[0]|1024&s[1]&&(o.$$scope={dirty:s,ctx:e}),l.$set(o)},i(e){i||(y(t.$$.fragment,e),y(l.$$.fragment,e),i=!0)},o(e){v(t.$$.fragment,e),v(l.$$.fragment,e),i=!1},d(e){e&&m(s),te(t,e),te(l,e)}}}function Lu(e){let t,n;return t=new gf({props:{shouldFilter:!1,$$slots:{default:[wu]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};49933&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Iu(e){let t,n,s,l;return t=new Wf({props:{asChild:!0,$$slots:{default:[ku,({builder:e})=>({61:e}),({builder:e})=>[0,e?1073741824:0]]},$$scope:{ctx:e}}}),s=new qf({props:{class:"w-[200px] p-0",align:"start",side:"bottom",$$slots:{default:[Lu]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment),n=j(),ne(s.$$.fragment)},l(e){ie(t.$$.fragment,e),n=q(e),ie(s.$$.fragment,e)},m(e,i){le(t,e,i),E(e,n,i),le(s,e,i),l=!0},p(e,n){const l={};16441&n[0]|1073742848&n[1]&&(l.$$scope={dirty:n,ctx:e}),t.$set(l);const i={};49933&n[0]|1024&n[1]&&(i.$$scope={dirty:n,ctx:e}),s.$set(i)},i(e){l||(y(t.$$.fragment,e),y(s.$$.fragment,e),l=!0)},o(e){v(t.$$.fragment,e),v(s.$$.fragment,e),l=!1},d(e){e&&m(n),te(t,e),te(s,e)}}}function Du(e){let t,n,s,l;const i=[fu,au],o=[];function a(e,t){return e[10].length>0?0:1}return n=a(e),s=o[n]=i[n](e),{c(){t=N("div"),s.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var n=V(t);s.l(n),n.forEach(m),this.h()},h(){T(t,"class","mt-2 mb-4 ml-0 mr-2 inline-block")},m(e,s){E(e,t,s),o[n].m(t,null),l=!0},p(e,l){let r=n;n=a(e),n===r?o[n].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),s=o[n],s?s.p(e,l):(s=o[n]=i[n](e),s.c()),y(s,1),s.m(t,null))},i(e){l||(y(s),l=!0)},o(e){v(s),l=!1},d(e){e&&m(t),o[n].d()}}}function Nu(e){let t,n,s,l,i=[],o=new Map;const a=e[34].default,r=we(a,e,e[41],null);let c=at(e[12]);const d=e=>{var t,n;return`${null==(t=e[58].label)?void 0:t.toString()} ${null==(n=e[58].value)?void 0:n.toString()}`};for(let t=0;t<c.length;t+=1){let n=ns(e,c,t),s=d(n);o.set(s,i[t]=ss(s,n))}return s=new ka({props:{enabled:e[1],$$slots:{default:[Du]},$$scope:{ctx:e}}}),{c(){r&&r.c(),t=j();for(let e=0;e<i.length;e+=1)i[e].c();n=j(),ne(s.$$.fragment)},l(e){r&&r.l(e),t=q(e);for(let t=0;t<i.length;t+=1)i[t].l(e);n=q(e),ie(s.$$.fragment,e)},m(e,o){r&&r.m(e,o),E(e,t,o);for(let t=0;t<i.length;t+=1)i[t]&&i[t].m(e,o);E(e,n,o),le(s,e,o),l=!0},p(e,t){r&&r.p&&(!l||1024&t[1])&&Le(r,a,e,e[41],l?De(a,e[41],t,null):Ie(e[41]),null),4288&t[0]&&(c=at(e[12]),ge(),i=Js(i,t,d,1,e,c,o,n.parentNode,xs,ss,n,ns),be());const u={};2&t[0]&&(u.enabled=e[1]),51005&t[0]|1024&t[1]&&(u.$$scope={dirty:t,ctx:e}),s.$set(u)},i(e){if(!l){y(r,e);for(let e=0;e<c.length;e+=1)y(i[e]);y(s.$$.fragment,e),l=!0}},o(e){v(r,e);for(let e=0;e<i.length;e+=1)v(i[e]);v(s.$$.fragment,e),l=!1},d(e){e&&(m(t),m(n)),r&&r.d(e);for(let t=0;t<i.length;t+=1)i[t].d(e);te(s,e)}}}const dr=5;function hs(e){return"similarity"in e?-1*e.similarity:e.ordinal??0}function Mu(e,t,n){var s;let l,i,o,a,r,c,d,u,p=me,m=me,f=()=>(m(),m=gt(Z,(e=>n(32,o=e))),Z);je(e,Ji,(e=>n(45,d=e))),e.$$.on_destroy.push((()=>p())),e.$$.on_destroy.push((()=>m()));let{$$slots:h={},$$scope:y}=t;const $=Er(h),g=eo();je(e,g,(e=>n(44,r=e)));let{title:v}=t,{name:b}=t,{multiple:E=!1}=t,{hideDuringPrint:x=!0}=t,{disableSelectAll:T=!1}=t,{defaultValue:w=[]}=t,{noDefault:D=!1}=t,{selectAllByDefault:C=!1}=t,{description:L}=t,{value:N="value",data:O,label:A=N,order:I,where:S}=t;const{results:k,update:P}=to({value:N,data:O,label:A,order:I,where:S},`Dropdown-${b}`,null==(s=null==d?void 0:d.data)?void 0:s.data[`Dropdown-${b}_data`]);je(e,k,(e=>n(33,c=e)));let V=!!O;const M=b in r&&"rawValues"in r[b]&&Array.isArray(r[b].rawValues)?r[b].rawValues:[],R=lo({multiselect:E,defaultValues:Array.isArray(w)?w:[w],initialOptions:M,noDefault:D,selectAllByDefault:Ge(C)}),{addOptions:q,removeOptions:j,options:z,selectedOptions:_,selectAll:F,deselectAll:U,toggleSelected:G,pauseSorting:B,resumeSorting:H,forceSort:Q,destroy:X}=R;je(e,z,(e=>n(15,u=e))),je(e,_,(e=>n(14,a=e))),qi(X);const W=e=>{JSON.stringify(e)!==JSON.stringify(r[b])&&Rl(g,r[b]=e,r)};let Y=[],K=a.length>0;qi(_.subscribe((e=>{if(K||(K=e.length>0),e&&K){const t=e;E?W({label:t.map((e=>e.label)).join(", "),value:t.length?`(${t.map((e=>Yi(e.value)))})`:"(select null where 0)",rawValues:t}):t.length?t.length&&W({label:t[0].label,value:Yi(t[0].value,{serializeStrings:!1}),rawValues:t}):W({label:"",value:null,rawValues:[]})}}))),Nl(or,{registerOption:e=>(q(e),()=>{j(e)})});let J,Z,ee="",te=0;const ne=io((()=>{if(te++,ee&&V){const e=te,t=l.search(ee,"label");t.hash!==(null==Z?void 0:Z.hash)&&ro((()=>{e===te&&(f(n(13,Z=t)),Q())}),t.fetch())}else f(n(13,Z=l??O))}));let se=[];N||(O?se.push('Missing required prop: "value".'):$.default||se.push('Dropdown requires either "value" and "data" props or <DropdownOption />.')),O&&"object"!=typeof O&&("string"==typeof O?se.push(`'${O}' is not a recognized query result. Data should be provided in the format: data = {'${O.replace("data.","")}'}`):se.push(`'${O}' is not a recognized query result. Data should be an object. e.g data = {QueryName}`));try{Do({name:b})}catch(e){se.push(e.message)}let le=!1;return e.$$set=e=>{"title"in e&&n(3,v=e.title),"name"in e&&n(4,b=e.name),"multiple"in e&&n(0,E=e.multiple),"hideDuringPrint"in e&&n(1,x=e.hideDuringPrint),"disableSelectAll"in e&&n(2,T=e.disableSelectAll),"defaultValue"in e&&n(25,w=e.defaultValue),"noDefault"in e&&n(23,D=e.noDefault),"selectAllByDefault"in e&&n(24,C=e.selectAllByDefault),"description"in e&&n(5,L=e.description),"value"in e&&n(6,N=e.value),"data"in e&&n(26,O=e.data),"label"in e&&n(7,A=e.label),"order"in e&&n(27,I=e.order),"where"in e&&n(28,S=e.where),"$$scope"in e&&n(41,y=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty[0]&&n(0,E=Ge(E)),2&e.$$.dirty[0]&&n(1,x=Ge(x)),4&e.$$.dirty[0]&&n(2,T=Ge(T)),8388608&e.$$.dirty[0]&&n(23,D=Ge(D)),16777216&e.$$.dirty[0]&&n(24,C=Ge(C)),469762240&e.$$.dirty[0]&&P({value:N,data:O,label:A,order:I,where:S}),4&e.$$.dirty[1]&&n(29,({hasQuery:V,query:l}=c),V,(n(11,l),p(),p=gt(l,(e=>n(31,i=e))),l)),2048&e.$$.dirty[0]&&l&&l.fetch(),67111424&e.$$.dirty[0]&&ne(),256&e.$$.dirty[0]&&(J?B():H()),2&e.$$.dirty[1]&&null!=o&&o.dataLoaded&&n(12,Y=o),1610613760&e.$$.dirty[0]|1&e.$$.dirty[1]&&null!=i&&i.error&&V&&!le&&(n(10,se=[...se,i.error]),n(30,le=!0))},[E,x,T,v,b,L,N,A,J,ee,se,l,Y,Z,a,u,g,k,z,_,F,U,G,D,C,w,O,I,S,V,le,i,o,c,h,function(e){ee=e,n(9,ee)},({value:e,label:t})=>{G({value:e,label:t}),E||n(8,J=!1)},(e,t)=>t.value===e.value&&t.label===e.label,({value:e,label:t})=>{G({value:e,label:t}),E||n(8,J=!1)},(e,t)=>t.value===e.value&&t.label===e.label,function(e){J=e,n(8,J)},y]}class _s extends ve{constructor(e){super(),Te(this,e,Mu,Nu,ke,{title:3,name:4,multiple:0,hideDuringPrint:1,disableSelectAll:2,defaultValue:25,noDefault:23,selectAllByDefault:24,description:5,value:6,data:26,label:7,order:27,where:28},null,[-1,-1,-1])}}function Ru(e){let t,n,s;return{c(){t=N("span"),n=ml("svg"),s=ml("path"),this.h()},l(e){t=D(e,"SPAN",{"aria-expanded":!0,class:!0});var l=V(t);n=dl(l,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var i=V(n);s=dl(i,"path",{fill:!0,"fill-rule":!0,d:!0}),V(s).forEach(m),i.forEach(m),l.forEach(m),this.h()},h(){T(s,"fill",e[3]),T(s,"fill-rule","evenodd"),T(s,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),T(n,"viewBox","0 0 16 16"),T(n,"width",e[1]),T(n,"height",e[1]),T(n,"class","svelte-lqleyo"),T(t,"aria-expanded",e[0]),T(t,"class","svelte-lqleyo")},m(e,l){E(e,t,l),P(t,n),P(n,s)},p(e,[l]){8&l&&T(s,"fill",e[3]),2&l&&T(n,"width",e[1]),2&l&&T(n,"height",e[1]),1&l&&T(t,"aria-expanded",e[0])},i:me,o:me,d(e){e&&m(t)}}}function Pu(e,t,n){let s,l,i=me;e.$$.on_destroy.push((()=>i()));const{resolveColor:o}=Yt();let{toggled:a=!1}=t,{color:r="base-content"}=t,{size:c=10}=t;return e.$$set=e=>{"toggled"in e&&n(0,a=e.toggled),"color"in e&&n(4,r=e.color),"size"in e&&n(1,c=e.size)},e.$$.update=()=>{16&e.$$.dirty&&(n(2,s=o(r)),i(),i=gt(s,(e=>n(3,l=e))))},[a,c,s,l,r]}class mr extends ve{constructor(e){super(),Te(this,e,Pu,Ru,ke,{toggled:0,color:4,size:1})}}function zu(e){let t,n,s,l,i,o;const a=e[5].default,r=we(a,e,e[4],null);return{c(){t=N("div"),n=N("span"),s=de(e[2]),l=j(),i=N("div"),r&&r.c(),this.h()},l(o){t=D(o,"DIV",{class:!0});var a=V(t);n=D(a,"SPAN",{class:!0});var c=V(n);s=ce(c,e[2]),c.forEach(m),l=q(a),i=D(a,"DIV",{class:!0});var d=V(i);r&&r.l(d),d.forEach(m),a.forEach(m),this.h()},h(){T(n,"class","text-sm font-semibold inline-flex"),T(i,"class","pt-1 mb-6 text-sm"),T(t,"class","mb-4 mt-2 text-base-content-muted")},m(e,a){E(e,t,a),P(t,n),P(n,s),P(t,l),P(t,i),r&&r.m(i,null),o=!0},p(e,t){(!o||4&t)&&Xe(s,e[2]),r&&r.p&&(!o||16&t)&&Le(r,a,e,e[4],o?De(a,e[4],t,null):Ie(e[4]),null)},i(e){o||(y(r,e),o=!0)},o(e){v(r,e),o=!1},d(e){e&&m(t),r&&r.d(e)}}}function Fu(e){let t,n,s,l,i,o,a,r,c,d,u,p=e[0]&&gs(e);return{c(){t=N("div"),n=N("button"),s=N("span"),i=j(),o=N("span"),a=de(e[2]),r=j(),p&&p.c(),this.h()},l(l){t=D(l,"DIV",{class:!0});var c=V(t);n=D(c,"BUTTON",{class:!0});var d=V(n);s=D(d,"SPAN",{class:!0}),V(s).forEach(m),i=q(d),o=D(d,"SPAN",{});var u=V(o);a=ce(u,e[2]),u.forEach(m),d.forEach(m),r=q(c),p&&p.l(c),c.forEach(m),this.h()},h(){T(s,"class",l=ji(e[0]?"marker rotate-marker":"marker")+" svelte-v9l93j"),T(n,"class","text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"),T(t,"class","mb-4 mt-2")},m(l,m){E(l,t,m),P(t,n),P(n,s),P(n,i),P(n,o),P(o,a),P(t,r),p&&p.m(t,null),c=!0,d||(u=qe(n,"click",e[10]),d=!0)},p(e,n){(!c||1&n&&l!==(l=ji(e[0]?"marker rotate-marker":"marker")+" svelte-v9l93j"))&&T(s,"class",l),(!c||4&n)&&Xe(a,e[2]),e[0]?p?(p.p(e,n),1&n&&y(p,1)):(p=gs(e),p.c(),y(p,1),p.m(t,null)):p&&(ge(),v(p,1,1,(()=>{p=null})),be())},i(e){c||(y(p),c=!0)},o(e){v(p),c=!1},d(e){e&&m(t),p&&p.d(),d=!1,u()}}}function gs(e){let t,n,s;const l=e[5].default,i=we(l,e,e[4],null);return{c(){t=N("div"),i&&i.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var n=V(t);i&&i.l(n),n.forEach(m),this.h()},h(){T(t,"class","pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm")},m(e,n){E(e,t,n),i&&i.m(t,null),s=!0},p(e,t){i&&i.p&&(!s||16&t)&&Le(i,l,e,e[4],s?De(l,e[4],t,null):Ie(e[4]),null)},i(e){s||(y(i,e),e&&kt((()=>{s&&(n||(n=Ct(t,At,{},!0)),n.run(1))})),s=!0)},o(e){v(i,e),e&&(n||(n=Ct(t,At,{},!1)),n.run(0)),s=!1},d(e){e&&m(t),i&&i.d(e),e&&n&&n.end()}}}function Bu(e){let t,n,s,l,i,o;const a=[Fu,zu],r=[];function c(e,t){return e[3]&&e[1]?1:0}return t=c(e),n=r[t]=a[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(n,a){r[t].m(n,a),E(n,s,a),l=!0,i||(o=[qe(window,"beforeprint",e[6]),qe(window,"afterprint",e[7]),qe(window,"export-beforeprint",e[8]),qe(window,"export-afterprint",e[9])],i=!0)},p(e,[l]){let i=t;t=c(e),t===i?r[t].p(e,l):(ge(),v(r[i],1,1,(()=>{r[i]=null})),be(),n=r[t],n?n.p(e,l):(n=r[t]=a[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),r[t].d(e),i=!1,zt(o)}}}function Uu(e,t,n){let{$$slots:s={},$$scope:l}=t,{title:i="Details"}=t,{open:o=!1}=t,{printShowAll:a=!0}=t,r=!1;return e.$$set=e=>{"title"in e&&n(2,i=e.title),"open"in e&&n(0,o=e.open),"printShowAll"in e&&n(1,a=e.printShowAll),"$$scope"in e&&n(4,l=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(0,o=Ge(o)),2&e.$$.dirty&&n(1,a=Ge(a))},[o,a,i,r,l,s,()=>n(3,r=!0),()=>n(3,r=!1),()=>n(3,r=!0),()=>n(3,r=!1),()=>n(0,o=!o)]}class hr extends ve{constructor(e){super(),Te(this,e,Uu,Bu,ke,{title:2,open:0,printShowAll:1})}}function bs(e,t,n){const s=e.slice();return s[12]=t[n],s[14]=n,s}function ys(e,t,n){const s=e.slice();return s[15]=t[n],s[17]=n,s}function ks(e,t,n){const s=e.slice();return s[15]=t[n],s}function Cs(e,t,n){const s=e.slice();return s[15]=t[n],s}function Es(e){let t,n,s,l,i,o=e[15].id+"";return{c(){t=N("th"),n=de(o),this.h()},l(e){t=D(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var s=V(t);n=ce(s,o),s.forEach(m),this.h()},h(){var n,o;T(t,"class",s="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y"),Q(t,"width",e[6]+"%"),T(t,"evidencetype",l=(null==(n=e[15].evidenceColumnType)?void 0:n.evidenceType)||"unavailable"),T(t,"evidencetypefidelity",i=(null==(o=e[15].evidenceColumnType)?void 0:o.typeFidelity)||"unavailable")},m(e,s){E(e,t,s),P(t,n)},p(e,a){var r,c;8&a&&o!==(o=e[15].id+"")&&Xe(n,o),8&a&&s!==(s="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y")&&T(t,"class",s),64&a&&Q(t,"width",e[6]+"%"),8&a&&l!==(l=(null==(r=e[15].evidenceColumnType)?void 0:r.evidenceType)||"unavailable")&&T(t,"evidencetype",l),8&a&&i!==(i=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")&&T(t,"evidencetypefidelity",i)},d(e){e&&m(t)}}}function vs(e){let t,n,s,l,i,o=e[15].type+"";return{c(){t=N("th"),n=de(o),this.h()},l(e){t=D(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var s=V(t);n=ce(s,o),s.forEach(m),this.h()},h(){var n,o;T(t,"class",s=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),Q(t,"width",e[6]+"%"),T(t,"evidencetype",l=(null==(n=e[15].evidenceColumnType)?void 0:n.evidenceType)||"unavailable"),T(t,"evidencetypefidelity",i=(null==(o=e[15].evidenceColumnType)?void 0:o.typeFidelity)||"unavailable")},m(e,s){E(e,t,s),P(t,n)},p(e,a){var r,c;8&a&&o!==(o=e[15].type+"")&&Xe(n,o),8&a&&s!==(s=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&T(t,"class",s),64&a&&Q(t,"width",e[6]+"%"),8&a&&l!==(l=(null==(r=e[15].evidenceColumnType)?void 0:r.evidenceType)||"unavailable")&&T(t,"evidencetype",l),8&a&&i!==(i=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")&&T(t,"evidencetypefidelity",i)},d(e){e&&m(t)}}}function Vu(e){let t,n=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=de(n)},l(e){t=ce(e,n)},m(e,n){E(e,t,n)},p(e,s){4&s&&n!==(n=(e[2]+e[14]+1).toLocaleString()+"")&&Xe(t,n)},d(e){e&&m(t)}}}function Hu(e){let t,n=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=de(n)},l(e){t=ce(e,n)},m(e,n){E(e,t,n)},p(e,s){4&s&&n!==(n=(e[2]+e[14]+1).toLocaleString()+"")&&Xe(t,n)},d(e){e&&m(t)}}}function Gu(e){let t,n,s=(e[12][e[15].id]||"Ø")+"";return{c(){t=N("td"),n=de(s),this.h()},l(e){t=D(e,"TD",{class:!0,style:!0});var l=V(t);n=ce(l,s),l.forEach(m),this.h()},h(){T(t,"class","other svelte-ghf30y"),Q(t,"width",e[6]+"%")},m(e,s){E(e,t,s),P(t,n)},p(e,l){40&l&&s!==(s=(e[12][e[15].id]||"Ø")+"")&&Xe(n,s),64&l&&Q(t,"width",e[6]+"%")},d(e){e&&m(t)}}}function qu(e){let t,n,s,l,i=(e[12][e[15].id]??"Ø")+"";return{c(){t=N("td"),n=N("div"),s=de(i),this.h()},l(e){t=D(e,"TD",{class:!0,style:!0,title:!0});var l=V(t);n=D(l,"DIV",{class:!0});var o=V(n);s=ce(o,i),o.forEach(m),l.forEach(m),this.h()},h(){T(n,"class","svelte-ghf30y"),T(t,"class","boolean svelte-ghf30y"),Q(t,"width",e[6]+"%"),T(t,"title",l=e[12][e[15].id])},m(e,l){E(e,t,l),P(t,n),P(n,s)},p(e,n){40&n&&i!==(i=(e[12][e[15].id]??"Ø")+"")&&Xe(s,i),64&n&&Q(t,"width",e[6]+"%"),40&n&&l!==(l=e[12][e[15].id])&&T(t,"title",l)},d(e){e&&m(t)}}}function ju(e){let t,n,s,l,i=(e[12][e[15].id]||"Ø")+"";return{c(){t=N("td"),n=N("div"),s=de(i),this.h()},l(e){t=D(e,"TD",{class:!0,style:!0,title:!0});var l=V(t);n=D(l,"DIV",{class:!0});var o=V(n);s=ce(o,i),o.forEach(m),l.forEach(m),this.h()},h(){T(n,"class","svelte-ghf30y"),T(t,"class","string svelte-ghf30y"),Q(t,"width",e[6]+"%"),T(t,"title",l=e[12][e[15].id])},m(e,l){E(e,t,l),P(t,n),P(n,s)},p(e,n){40&n&&i!==(i=(e[12][e[15].id]||"Ø")+"")&&Xe(s,i),64&n&&Q(t,"width",e[6]+"%"),40&n&&l!==(l=e[12][e[15].id])&&T(t,"title",l)},d(e){e&&m(t)}}}function Wu(e){let t,n,s,l,i=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=N("td"),n=N("div"),s=de(i),this.h()},l(e){t=D(e,"TD",{class:!0,style:!0,title:!0});var l=V(t);n=D(l,"DIV",{class:!0});var o=V(n);s=ce(o,i),o.forEach(m),l.forEach(m),this.h()},h(){T(n,"class","svelte-ghf30y"),T(t,"class","string svelte-ghf30y"),Q(t,"width",e[6]+"%"),T(t,"title",l=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))},m(e,l){E(e,t,l),P(t,n),P(n,s)},p(e,n){40&n&&i!==(i=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Xe(s,i),64&n&&Q(t,"width",e[6]+"%"),40&n&&l!==(l=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))&&T(t,"title",l)},d(e){e&&m(t)}}}function Xu(e){let t,n,s=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=N("td"),n=de(s),this.h()},l(e){t=D(e,"TD",{class:!0,style:!0});var l=V(t);n=ce(l,s),l.forEach(m),this.h()},h(){T(t,"class","number svelte-ghf30y"),Q(t,"width",e[6]+"%")},m(e,s){E(e,t,s),P(t,n)},p(e,l){40&l&&s!==(s=ct(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Xe(n,s),64&l&&Q(t,"width",e[6]+"%")},d(e){e&&m(t)}}}function Yu(e){let t,n,s;return{c(){t=N("td"),n=de("Ø"),this.h()},l(e){t=D(e,"TD",{class:!0,style:!0});var s=V(t);n=ce(s,"Ø"),s.forEach(m),this.h()},h(){T(t,"class",s="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y"),Q(t,"width",e[6]+"%")},m(e,s){E(e,t,s),P(t,n)},p(e,n){8&n&&s!==(s="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y")&&T(t,"class",s),64&n&&Q(t,"width",e[6]+"%")},d(e){e&&m(t)}}}function Ts(e){let t;function n(e,t){return null==e[12][e[15].id]?Yu:"number"===e[3][e[17]].type?Xu:"date"===e[3][e[17]].type?Wu:"string"===e[3][e[17]].type?ju:"boolean"===e[3][e[17]].type?qu:Gu}let s=n(e),l=s(e);return{c(){l.c(),t=fe()},l(e){l.l(e),t=fe()},m(e,n){l.m(e,n),E(e,t,n)},p(e,i){s===(s=n(e))&&l?l.p(e,i):(l.d(1),l=s(e),l&&(l.c(),l.m(t.parentNode,t)))},d(e){e&&m(t),l.d(e)}}}function Ss(e){let t,n,s,l,i=(0===e[14]?Hu:Vu)(e),o=at(e[3]),a=[];for(let t=0;t<o.length;t+=1)a[t]=Ts(ys(e,o,t));return{c(){t=N("tr"),n=N("td"),i.c(),s=j();for(let e=0;e<a.length;e+=1)a[e].c();l=j(),this.h()},l(e){t=D(e,"TR",{});var o=V(t);n=D(o,"TD",{class:!0,style:!0});var r=V(n);i.l(r),r.forEach(m),s=q(o);for(let e=0;e<a.length;e+=1)a[e].l(o);l=q(o),o.forEach(m),this.h()},h(){T(n,"class","index text-base-content-muted svelte-ghf30y"),Q(n,"width","10%")},m(e,o){E(e,t,o),P(t,n),i.m(n,null),P(t,s);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(t,null);P(t,l)},p(e,n){if(i.p(e,n),104&n){let s;for(o=at(e[3]),s=0;s<o.length;s+=1){const i=ys(e,o,s);a[s]?a[s].p(i,n):(a[s]=Ts(i),a[s].c(),a[s].m(t,l))}for(;s<a.length;s+=1)a[s].d(1);a.length=o.length}},d(e){e&&m(t),i.d(),jt(a,e)}}}function ps(e){let t,n,s,l,i,o,a,r,c,d=(e[2]+Gt).toLocaleString()+"",u=(e[4]+Gt).toLocaleString()+"";return{c(){t=N("div"),n=N("input"),s=j(),l=N("span"),i=de(d),o=de(" of "),a=de(u),this.h()},l(e){t=D(e,"DIV",{class:!0});var r=V(t);n=D(r,"INPUT",{type:!0,max:!0,step:!0,class:!0}),s=q(r),l=D(r,"SPAN",{class:!0});var c=V(l);i=ce(c,d),o=ce(c," of "),a=ce(c,u),c.forEach(m),r.forEach(m),this.h()},h(){T(n,"type","range"),T(n,"max",e[4]),T(n,"step","1"),T(n,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),T(l,"class","text-xs svelte-ghf30y"),T(t,"class","pagination svelte-ghf30y")},m(d,u){E(d,t,u),P(t,n),qt(n,e[2]),P(t,s),P(t,l),P(l,i),P(l,o),P(l,a),r||(c=[qe(n,"change",e[9]),qe(n,"input",e[9]),qe(n,"input",e[7])],r=!0)},p(e,t){16&t&&T(n,"max",e[4]),4&t&&qt(n,e[2]),4&t&&d!==(d=(e[2]+Gt).toLocaleString()+"")&&Xe(i,d),16&t&&u!==(u=(e[4]+Gt).toLocaleString()+"")&&Xe(a,u)},d(e){e&&m(t),r=!1,zt(c)}}}function Ku(e){let t,n,s,l,i,o,a,r,c,d,u,p,f,h,$,g,b,x,w,C,L,O,A,I,S,k,M=at(e[3]),R=[];for(let t=0;t<M.length;t+=1)R[t]=Es(Cs(e,M,t));let z=at(e[3]),_=[];for(let t=0;t<z.length;t+=1)_[t]=vs(ks(e,z,t));let F=at(e[5]),U=[];for(let t=0;t<F.length;t+=1)U[t]=Ss(bs(e,F,t));let G=e[4]>0&&ps(e);return O=new Ki({props:{class:"download-button",data:e[1],queryID:e[0],display:!0}}),{c(){t=N("div"),n=N("div"),s=N("table"),l=N("thead"),i=N("tr"),o=N("th"),a=j();for(let e=0;e<R.length;e+=1)R[e].c();r=j(),c=N("tr"),d=j(),u=N("tr"),p=N("th"),f=j();for(let e=0;e<_.length;e+=1)_[e].c();h=j(),$=N("tr"),g=j(),b=N("tbody");for(let e=0;e<U.length;e+=1)U[e].c();w=j(),G&&G.c(),C=j(),L=N("div"),ne(O.$$.fragment),this.h()},l(e){t=D(e,"DIV",{class:!0});var y=V(t);n=D(y,"DIV",{class:!0});var v=V(n);s=D(v,"TABLE",{class:!0});var E=V(s);l=D(E,"THEAD",{});var x=V(l);i=D(x,"TR",{});var T=V(i);o=D(T,"TH",{class:!0,style:!0}),V(o).forEach(m),a=q(T);for(let e=0;e<R.length;e+=1)R[e].l(T);r=q(T),T.forEach(m),c=D(x,"TR",{}),V(c).forEach(m),d=q(x),u=D(x,"TR",{class:!0});var N=V(u);p=D(N,"TH",{class:!0,style:!0}),V(p).forEach(m),f=q(N);for(let e=0;e<_.length;e+=1)_[e].l(N);h=q(N),N.forEach(m),$=D(x,"TR",{}),V($).forEach(m),x.forEach(m),g=q(E),b=D(E,"TBODY",{});var A=V(b);for(let e=0;e<U.length;e+=1)U[e].l(A);A.forEach(m),E.forEach(m),v.forEach(m),w=q(y),G&&G.l(y),C=q(y),L=D(y,"DIV",{class:!0});var I=V(L);ie(O.$$.fragment,I),I.forEach(m),y.forEach(m),this.h()},h(){T(o,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),Q(o,"width","10%"),T(p,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),Q(p,"width","10%"),T(u,"class","type-indicator svelte-ghf30y"),T(s,"class","text-xs svelte-ghf30y"),T(n,"class","scrollbox pretty-scrollbar svelte-ghf30y"),T(L,"class","footer svelte-ghf30y"),T(t,"class","results-pane py-1 svelte-ghf30y")},m(m,y){E(m,t,y),P(t,n),P(n,s),P(s,l),P(l,i),P(i,o),P(i,a);for(let e=0;e<R.length;e+=1)R[e]&&R[e].m(i,null);P(i,r),P(l,c),P(l,d),P(l,u),P(u,p),P(u,f);for(let e=0;e<_.length;e+=1)_[e]&&_[e].m(u,null);P(u,h),P(l,$),P(s,g),P(s,b);for(let e=0;e<U.length;e+=1)U[e]&&U[e].m(b,null);P(t,w),G&&G.m(t,null),P(t,C),P(t,L),le(O,L,null),I=!0,S||(k=qe(b,"wheel",e[8]),S=!0)},p(e,[n]){if(72&n){let t;for(M=at(e[3]),t=0;t<M.length;t+=1){const s=Cs(e,M,t);R[t]?R[t].p(s,n):(R[t]=Es(s),R[t].c(),R[t].m(i,r))}for(;t<R.length;t+=1)R[t].d(1);R.length=M.length}if(72&n){let t;for(z=at(e[3]),t=0;t<z.length;t+=1){const s=ks(e,z,t);_[t]?_[t].p(s,n):(_[t]=vs(s),_[t].c(),_[t].m(u,h))}for(;t<_.length;t+=1)_[t].d(1);_.length=z.length}if(108&n){let t;for(F=at(e[5]),t=0;t<F.length;t+=1){const s=bs(e,F,t);U[t]?U[t].p(s,n):(U[t]=Ss(s),U[t].c(),U[t].m(b,null))}for(;t<U.length;t+=1)U[t].d(1);U.length=F.length}e[4]>0?G?G.p(e,n):(G=ps(e),G.c(),G.m(t,C)):G&&(G.d(1),G=null);const s={};2&n&&(s.data=e[1]),1&n&&(s.queryID=e[0]),O.$set(s)},i(e){I||(e&&(x||kt((()=>{x=Vl(s,er,{}),x.start()}))),y(O.$$.fragment,e),e&&kt((()=>{I&&(A||(A=Ct(t,At,{},!0)),A.run(1))})),I=!0)},o(e){v(O.$$.fragment,e),e&&(A||(A=Ct(t,At,{},!1)),A.run(0)),I=!1},d(e){e&&m(t),jt(R,e),jt(_,e),jt(U,e),G&&G.d(),te(O),e&&A&&A.end(),S=!1,k()}}}let Gt=5;function Qu(e,t,n){let s,l,i,o,a,{queryID:r}=t,{data:c}=t,d=0;function u(){a=c.slice(d,d+Gt),n(5,o=a)}const p=oo((e=>{n(2,d=Math.min(Math.max(0,d+Math.floor(e.deltaY/Math.abs(e.deltaY))),i)),u()}),60);return e.$$set=e=>{"queryID"in e&&n(0,r=e.queryID),"data"in e&&n(1,c=e.data)},e.$$.update=()=>{2&e.$$.dirty&&n(3,s=si(c,"array")),8&e.$$.dirty&&n(6,l=90/(s.length+1)),2&e.$$.dirty&&n(4,i=Math.max(c.length-Gt,0)),6&e.$$.dirty&&n(5,o=c.slice(d,d+Gt))},[r,c,d,s,i,o,l,u,function(e){if(Math.abs(e.deltaX)>=Math.abs(e.deltaY))return;const t=e.deltaY<0&&0===d,n=e.deltaY>0&&d===i;t||n||(e.preventDefault(),p(e))},function(){d=vr(this.value),n(2,d)}]}class Zu extends ve{constructor(e){super(),Te(this,e,Qu,Ku,ke,{queryID:0,data:1})}}const As={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function Ju(e){let t,n,s,l,i,o=bn.highlight(e[0],As)+"";return{c(){t=N("pre"),n=de("  "),s=N("code"),l=new Sr(!1),i=de("\n"),this.h()},l(e){t=D(e,"PRE",{class:!0});var o=V(t);n=ce(o,"  "),s=D(o,"CODE",{class:!0});var a=V(s);l=Tr(a,!1),a.forEach(m),i=ce(o,"\n"),o.forEach(m),this.h()},h(){l.a=null,T(s,"class","language-sql svelte-re3fhx"),T(t,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(e,a){E(e,t,a),P(t,n),P(t,s),l.m(o,s),P(t,i)},p(e,[t]){1&t&&o!==(o=bn.highlight(e[0],As)+"")&&l.p(o)},i:me,o:me,d(e){e&&m(t)}}}function xu(e,t,n){let{code:s=""}=t;return e.$$set=e=>{"code"in e&&n(0,s=e.code)},[s]}class _r extends ve{constructor(e){super(),Te(this,e,xu,Ju,ke,{code:0})}}function $u(e){let t,n,s,l,i,o="Compiled",a="Written";return{c(){t=N("button"),t.textContent=o,n=j(),s=N("button"),s.textContent=a,this.h()},l(e){t=D(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-1vzm9jy"!==We(t)&&(t.textContent=o),n=q(e),s=D(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-qu81ez"!==We(s)&&(s.textContent=a),this.h()},h(){T(t,"class","off svelte-ska6l4"),T(s,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(o,a){E(o,t,a),E(o,n,a),E(o,s,a),l||(i=qe(t,"click",e[1]),l=!0)},p:me,d(e){e&&(m(t),m(n),m(s)),l=!1,i()}}}function ec(e){let t,n,s,l,i,o="Compiled",a="Written";return{c(){t=N("button"),t.textContent=o,n=j(),s=N("button"),s.textContent=a,this.h()},l(e){t=D(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-wrfleh"!==We(t)&&(t.textContent=o),n=q(e),s=D(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-v36xno"!==We(s)&&(s.textContent=a),this.h()},h(){T(t,"class","text-info bg-info/10 border border-info svelte-ska6l4"),T(s,"class","off svelte-ska6l4")},m(o,a){E(o,t,a),E(o,n,a),E(o,s,a),l||(i=qe(s,"click",e[1]),l=!0)},p:me,d(e){e&&(m(t),m(n),m(s)),l=!1,i()}}}function tc(e){let t,n,s;function l(e,t){return e[0]?ec:$u}let i=l(e),o=i(e);return{c(){t=N("div"),o.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var n=V(t);o.l(n),n.forEach(m),this.h()},h(){T(t,"class","toggle svelte-ska6l4")},m(e,n){E(e,t,n),o.m(t,null),s=!0},p(e,[n]){i===(i=l(e))&&o?o.p(e,n):(o.d(1),o=i(e),o&&(o.c(),o.m(t,null)))},i(e){s||(e&&kt((()=>{s&&(n||(n=Ct(t,At,{},!0)),n.run(1))})),s=!0)},o(e){e&&(n||(n=Ct(t,At,{},!1)),n.run(0)),s=!1},d(e){e&&m(t),o.d(),e&&n&&n.end()}}}function lc(e,t,n){let{showCompiled:s}=t;return e.$$set=e=>{"showCompiled"in e&&n(0,s=e.showCompiled)},[s,function(){n(0,s=!s)}]}class ic extends ve{constructor(e){super(),Te(this,e,lc,tc,ke,{showCompiled:0})}}function Os(e){let t,n,s,l,i,o,a,r,c,d,u,p,f,h,$,g,b;l=new mr({props:{toggled:e[10]}});let x=e[10]&&e[4]&&ws(e),w=e[10]&&Ls(e);const C=[fc,ac,oc,rc],L=[];function O(e,t){return e[6]?0:e[8]?1:e[2].loading?2:3}u=O(e),p=L[u]=C[u](e);let A=e[8]>0&&!e[6]&&e[9]&&Is(e);return{c(){t=N("div"),n=N("div"),s=N("button"),ne(l.$$.fragment),i=j(),o=de(e[0]),a=j(),x&&x.c(),r=j(),w&&w.c(),c=j(),d=N("button"),p.c(),f=j(),A&&A.c(),this.h()},l(u){t=D(u,"DIV",{class:!0});var h=V(t);n=D(h,"DIV",{class:!0});var y=V(n);s=D(y,"BUTTON",{type:!0,"aria-label":!0,class:!0});var $=V(s);ie(l.$$.fragment,$),i=q($),o=ce($,e[0]),$.forEach(m),a=q(y),x&&x.l(y),r=q(y),w&&w.l(y),y.forEach(m),c=q(h),d=D(h,"BUTTON",{type:!0,"aria-label":!0,class:!0});var g=V(d);p.l(g),g.forEach(m),f=q(h),A&&A.l(h),h.forEach(m),this.h()},h(){T(s,"type","button"),T(s,"aria-label","show-sql"),T(s,"class","title svelte-1ursthx"),T(n,"class","container-a svelte-1ursthx"),T(d,"type","button"),T(d,"aria-label","view-query"),T(d,"class",ji("status-bar")+" svelte-1ursthx"),pt(d,"error",e[6]),pt(d,"success",!e[6]),pt(d,"open",e[9]),pt(d,"closed",!e[9]),T(t,"class","scrollbox my-3 svelte-1ursthx")},m(p,m){E(p,t,m),P(t,n),P(n,s),le(l,s,null),P(s,i),P(s,o),P(n,a),x&&x.m(n,null),P(n,r),w&&w.m(n,null),P(t,c),P(t,d),L[u].m(d,null),P(t,f),A&&A.m(t,null),$=!0,g||(b=[qe(s,"click",e[15]),qe(d,"click",e[16])],g=!0)},p(e,s){const i={};1024&s&&(i.toggled=e[10]),l.$set(i),(!$||1&s)&&Xe(o,e[0]),e[10]&&e[4]?x?(x.p(e,s),1040&s&&y(x,1)):(x=ws(e),x.c(),y(x,1),x.m(n,r)):x&&(ge(),v(x,1,1,(()=>{x=null})),be()),e[10]?w?(w.p(e,s),1024&s&&y(w,1)):(w=Ls(e),w.c(),y(w,1),w.m(n,null)):w&&(ge(),v(w,1,1,(()=>{w=null})),be());let a=u;u=O(e),u===a?L[u].p(e,s):(ge(),v(L[a],1,1,(()=>{L[a]=null})),be(),p=L[u],p?p.p(e,s):(p=L[u]=C[u](e),p.c()),y(p,1),p.m(d,null)),(!$||64&s)&&pt(d,"error",e[6]),(!$||64&s)&&pt(d,"success",!e[6]),(!$||512&s)&&pt(d,"open",e[9]),(!$||512&s)&&pt(d,"closed",!e[9]),e[8]>0&&!e[6]&&e[9]?A?(A.p(e,s),832&s&&y(A,1)):(A=Is(e),A.c(),y(A,1),A.m(t,null)):A&&(ge(),v(A,1,1,(()=>{A=null})),be())},i(e){$||(y(l.$$.fragment,e),y(x),y(w),y(p),y(A),e&&kt((()=>{$&&(h||(h=Ct(t,At,{},!0)),h.run(1))})),$=!0)},o(e){v(l.$$.fragment,e),v(x),v(w),v(p),v(A),e&&(h||(h=Ct(t,At,{},!1)),h.run(0)),$=!1},d(e){e&&m(t),te(l),x&&x.d(),w&&w.d(),L[u].d(),A&&A.d(),e&&h&&h.end(),g=!1,zt(b)}}}function ws(e){let t,n,s;function l(t){e[20](t)}let i={};return void 0!==e[5]&&(i.showCompiled=e[5]),t=new ic({props:i}),ft.push((()=>Hl(t,"showCompiled",l))),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,n){le(t,e,n),s=!0},p(e,s){const l={};!n&&32&s&&(n=!0,l.showCompiled=e[5],Ul((()=>n=!1))),t.$set(l)},i(e){s||(y(t.$$.fragment,e),s=!0)},o(e){v(t.$$.fragment,e),s=!1},d(e){te(t,e)}}}function Ls(e){let t,n,s,l,i;const o=[sc,nc],a=[];function r(e,t){return e[5]?0:1}return n=r(e),s=a[n]=o[n](e),{c(){t=N("div"),s.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var n=V(t);s.l(n),n.forEach(m),this.h()},h(){T(t,"class","code-container svelte-1ursthx")},m(e,s){E(e,t,s),a[n].m(t,null),i=!0},p(e,l){let i=n;n=r(e),n===i?a[n].p(e,l):(ge(),v(a[i],1,1,(()=>{a[i]=null})),be(),s=a[n],s?s.p(e,l):(s=a[n]=o[n](e),s.c()),y(s,1),s.m(t,null))},i(e){i||(y(s),e&&kt((()=>{i&&(l||(l=Ct(t,At,{},!0)),l.run(1))})),i=!0)},o(e){v(s),e&&(l||(l=Ct(t,At,{},!1)),l.run(0)),i=!1},d(e){e&&m(t),a[n].d(),e&&l&&l.end()}}}function nc(e){let t,n;return t=new _r({props:{code:e[3]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};8&n&&(s.code=e[3]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function sc(e){let t,n;return t=new _r({props:{code:e[1].originalText}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};2&n&&(s.code=e[1].originalText),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function rc(e){let t;return{c(){t=de("ran successfully but no data was returned")},l(e){t=ce(e,"ran successfully but no data was returned")},m(e,n){E(e,t,n)},p:me,i:me,o:me,d(e){e&&m(t)}}}function oc(e){let t;return{c(){t=de("loading...")},l(e){t=ce(e,"loading...")},m(e,n){E(e,t,n)},p:me,i:me,o:me,d(e){e&&m(t)}}}function ac(e){let t,n,s,l,i,o,a,r,c,d,u=e[8].toLocaleString()+"",p=e[8]>1?"records":"record",f=e[7].toLocaleString()+"",h=e[7]>1?"properties":"property";return t=new mr({props:{toggled:e[9],color:e[12].colors.info}}),{c(){ne(t.$$.fragment),n=j(),s=de(u),l=j(),i=de(p),o=de(" with "),a=de(f),r=j(),c=de(h)},l(e){ie(t.$$.fragment,e),n=q(e),s=ce(e,u),l=q(e),i=ce(e,p),o=ce(e," with "),a=ce(e,f),r=q(e),c=ce(e,h)},m(e,u){le(t,e,u),E(e,n,u),E(e,s,u),E(e,l,u),E(e,i,u),E(e,o,u),E(e,a,u),E(e,r,u),E(e,c,u),d=!0},p(e,n){const l={};512&n&&(l.toggled=e[9]),4096&n&&(l.color=e[12].colors.info),t.$set(l),(!d||256&n)&&u!==(u=e[8].toLocaleString()+"")&&Xe(s,u),(!d||256&n)&&p!==(p=e[8]>1?"records":"record")&&Xe(i,p),(!d||128&n)&&f!==(f=e[7].toLocaleString()+"")&&Xe(a,f),(!d||128&n)&&h!==(h=e[7]>1?"properties":"property")&&Xe(c,h)},i(e){d||(y(t.$$.fragment,e),d=!0)},o(e){v(t.$$.fragment,e),d=!1},d(e){e&&(m(n),m(s),m(l),m(i),m(o),m(a),m(r),m(c)),te(t,e)}}}function fc(e){let t,n=e[6].message+"";return{c(){t=de(n)},l(e){t=ce(e,n)},m(e,n){E(e,t,n)},p(e,s){64&s&&n!==(n=e[6].message+"")&&Xe(t,n)},i:me,o:me,d(e){e&&m(t)}}}function Is(e){let t,n;return t=new Zu({props:{data:e[1],queryID:e[0]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};2&n&&(s.data=e[1]),1&n&&(s.queryID=e[0]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function uc(e){let t,n,s,l=e[11]&&Os(e);return{c(){t=N("div"),l&&l.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var n=V(t);l&&l.l(n),n.forEach(m),this.h()},h(){T(t,"class","over-container svelte-1ursthx")},m(e,n){E(e,t,n),l&&l.m(t,null),s=!0},p(e,[n]){e[11]?l?(l.p(e,n),2048&n&&y(l,1)):(l=Os(e),l.c(),y(l,1),l.m(t,null)):l&&(ge(),v(l,1,1,(()=>{l=null})),be())},i(e){s||(y(l),e&&(n||kt((()=>{n=Vl(t,er,{}),n.start()}))),s=!0)},o(e){v(l),s=!1},d(e){e&&m(t),l&&l.d()}}}function cc(e,t,n){let s,l,i,o,a,r,c,d,u,p=me,m=()=>(p(),p=gt(h,(e=>n(2,o=e))),h);je(e,Ji,(e=>n(19,c=e))),je(e,ao,(e=>n(11,d=e))),e.$$.on_destroy.push((()=>p()));let{queryID:f}=t,{queryResult:h}=t;m();let y=yn("showSQL_".concat(f),!1);je(e,y,(e=>n(10,r=e)));let $=yn(`showResults_${f}`);je(e,$,(e=>n(9,a=e)));let g,v,b,E=!0;const{theme:x}=Yt();return je(e,x,(e=>n(12,u=e))),e.$$set=e=>{"queryID"in e&&n(0,f=e.queryID),"queryResult"in e&&m(n(1,h=e.queryResult))},e.$$.update=()=>{if(524288&e.$$.dirty&&n(18,s=c.data.evidencemeta.queries),4&e.$$.dirty&&n(6,b=o?o.error:new Error("queryResult is undefined")),4&e.$$.dirty&&n(8,l=(null==o?void 0:o.length)??0),4&e.$$.dirty&&n(7,i=o.columns.length??(null==o?void 0:o._evidenceColumnTypes.length)??0),262145&e.$$.dirty){let e=null==s?void 0:s.find((e=>e.id===f));e&&(n(3,g=e.inputQueryString),n(4,v=e.compiled&&void 0===e.compileError))}},[f,h,o,g,v,E,b,i,l,a,r,d,u,y,$,function(){Rl(y,r=!r,r)},function(){!b&&o.length>0&&Rl($,a=!a,a)},x,s,c,function(e){E=e,n(5,E)}]}class gr extends ve{constructor(e){super(),Te(this,e,cc,uc,ke,{queryID:0,queryResult:1})}}const _l=Symbol.for("__evidence-chart-window-debug__"),dc=(e,t)=>{window[_l]||(window[_l]={}),window[_l][e]=t},mc=e=>{window[_l]||(window[_l]={}),delete window[_l][e]},cl=500,hc=(e,t)=>{var n;const s=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&3*e.clientWidth*e.clientHeight*3>16777215;let l;Pl("light",Zi),Pl("dark",tr);const i=()=>{l=Qi(e,t.theme,{renderer:s?"svg":t.renderer??"canvas"})};i(),dc(l.id,l),t.connectGroup&&(l.group=t.connectGroup,fo(t.connectGroup));const o=()=>{if(t.seriesColors){const e=l.getOption();if(!e)return;const n={...e};for(const s of Object.keys(t.seriesColors)){const l=e.series.findIndex((e=>e.name===s));-1!==l&&(n.series[l]={...n.series[l],itemStyle:{...n.series[l].itemStyle,color:t.seriesColors[s]}})}l.setOption(n)}},a=()=>{t.echartsOptions&&l.setOption({...t.echartsOptions})},r=()=>{let e=[];if(t.seriesOptions){const n=t.config.series.reduce(((e,{evidenceSeriesType:t},n)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(n),e)),[]);for(let s=0;s<t.config.series.length;s++)n.includes(s)?e.push({}):e.push({...t.seriesOptions});l.setOption({series:e})}};l.setOption({...t.config,animationDuration:cl,animationDurationUpdate:cl}),o(),a(),r();const c=t.dispatch;l.on("click",(function(e){c("click",e)}));const d=e.parentElement,u=uo((()=>{l.resize({animation:{duration:cl}}),m()}),100);let p;window.ResizeObserver&&d?(p=new ResizeObserver(u),p.observe(d)):window.addEventListener("resize",u);const m=()=>{if(t.showAllXAxisLabels){const n=l.getOption();if(!n)return;const s=new Set(n.series.flatMap((e=>{var t;return null==(t=e.data)?void 0:t.map((e=>e[0]))}))),i=.8,o=(null==e?void 0:e.clientWidth)??0;if(!t.swapXY){const e={xAxis:{axisLabel:{interval:0,overflow:t.xAxisLabelOverflow,width:o*i/s.size}}};l.setOption(e)}}};return u(),window[n=Symbol.for("chart renders")]??(window[n]=0),window[Symbol.for("chart renders")]++,{update(e){window[Symbol.for("chart renders")]++,(e=>{e.theme!==t.theme&&(l.dispose(),t=e,i()),t=e,l.setOption({...t.config,animationDuration:cl,animationDurationUpdate:cl},!0),o(),a(),r(),l.resize({animation:{duration:cl}}),m()})(e)},destroy(){p?p.unobserve(d):window.removeEventListener("resize",u),l.dispose(),mc(l.id)}}},_c=(e,t)=>{Pl("light",Zi),Pl("dark",tr),console.log("echartsCanvasDownloadAction",t.theme);const n=Qi(e,t.theme,{renderer:"canvas"});t.config.animation=!1,n.setOption(t.config),t.echartsOptions&&n.setOption({...t.echartsOptions}),(()=>{if(t.seriesColors){const e=n.getOption();if(!e)return;const s={...e};for(const n of Object.keys(t.seriesColors)){const l=e.series.findIndex((e=>e.name===n));-1!==l&&(s.series[l]={...s.series[l],itemStyle:{...s.series[l].itemStyle,color:t.seriesColors[n]}})}n.setOption(s)}})(),(()=>{let e=[];if(t.seriesOptions){const s=t.config.series.reduce(((e,{evidenceSeriesType:t},n)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(n),e)),[]);for(let n=0;n<t.config.series.length;n++)s.includes(n)?e.push({}):e.push({...t.seriesOptions});n.setOption({series:e})}})();let s=n.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:t.backgroundColor,excludeComponents:["toolbox"]});const l=new Date,i=new Date(l.getTime()-6e4*l.getTimezoneOffset()).toISOString().slice(0,19).replaceAll(":","-");return co(s,(t.evidenceChartTitle??t.queryID??"evidence-chart")+`_${i}.png`),n.dispose(),{destroy(){n.dispose()}}},Fl=(e,t)=>{Pl("evidence-light",Zi);const{config:n,ratio:s,echartsOptions:l,seriesOptions:i,seriesColors:o,isMap:a,extraHeight:r,width:c}=t;let d={renderer:"canvas"};a&&(d.height=.5*c+r,e&&e.parentNode&&(e.style.height=d.height+"px",e.parentNode.style.height=d.height+"px"));const u=Qi(e,"evidence-light",d);n.animation=!1,u.setOption(n),l&&u.setOption(l),l&&u.setOption({...l}),(()=>{if(o){const e=u.getOption();if(!e)return;const t={...e};for(const n of Object.keys(o)){const s=e.series.findIndex((e=>e.name===n));-1!==s&&(t.series[s]={...t.series[s],itemStyle:{...t.series[s].itemStyle,color:o[n]}})}u.setOption(t)}})(),(()=>{let e=[];if(i){const t=n.series.reduce(((e,{evidenceSeriesType:t},n)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(n),e)),[]);for(let s=0;s<n.series.length;s++)t.includes(s)?e.push({}):e.push({...i});u.setOption({series:e})}})();let p=u.getConnectedDataURL({type:"jpeg",pixelRatio:s,backgroundColor:"#fff",excludeComponents:["toolbox"]});e.innerHTML=`<img src=${p} width="100%" style="\n        position: absolute; \n        top: 0;\n        user-select: all;\n        -webkit-user-select: all;\n        -moz-user-select: all;\n        -ms-user-select: all;\n    " />`,t.config.animation=!0};function gc(e){let t;function n(e,t){return e[9]?kc:yc}let s=n(e),l=s(e);return{c(){l.c(),t=fe()},l(e){l.l(e),t=fe()},m(e,n){l.m(e,n),E(e,t,n)},p(e,i){s===(s=n(e))&&l?l.p(e,i):(l.d(1),l=s(e),l&&(l.c(),l.m(t.parentNode,t)))},d(e){e&&m(t),l.d(e)}}}function bc(e){let t,n,s,l;return{c(){t=N("div"),this.h()},l(e){t=D(e,"DIV",{class:!0,style:!0}),V(t).forEach(m),this.h()},h(){T(t,"class","chart"),Q(t,"height",e[1]),Q(t,"width",e[2]),Q(t,"margin-left","0"),Q(t,"margin-top","15px"),Q(t,"margin-bottom","10px"),Q(t,"overflow","visible"),Q(t,"break-inside","avoid")},m(i,o){E(i,t,o),s||(l=ot(n=Fl.call(null,t,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})),s=!0)},p(e,s){2&s&&Q(t,"height",e[1]),4&s&&Q(t,"width",e[2]),n&&Xt(n.update)&&8289&s&&n.update.call(null,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})},d(e){e&&m(t),s=!1,l()}}}function yc(e){let t,n,s,l,i,o,a;return{c(){t=N("div"),s=j(),l=N("div"),this.h()},l(e){t=D(e,"DIV",{class:!0,style:!0}),V(t).forEach(m),s=q(e),l=D(e,"DIV",{class:!0,style:!0}),V(l).forEach(m),this.h()},h(){T(t,"class","chart md:hidden"),Q(t,"height",e[1]),Q(t,"width","650px"),Q(t,"margin-left","0"),Q(t,"margin-top","15px"),Q(t,"margin-bottom","10px"),Q(t,"overflow","visible"),Q(t,"break-inside","avoid"),T(l,"class","chart hidden md:block"),Q(l,"height",e[1]),Q(l,"width","841px"),Q(l,"margin-left","0"),Q(l,"margin-top","15px"),Q(l,"margin-bottom","10px"),Q(l,"overflow","visible"),Q(l,"break-inside","avoid")},m(r,c){E(r,t,c),E(r,s,c),E(r,l,c),o||(a=[ot(n=Fl.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650})),ot(i=Fl.call(null,l,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841}))],o=!0)},p(e,s){2&s&&Q(t,"height",e[1]),n&&Xt(n.update)&&8673&s&&n.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650}),2&s&&Q(l,"height",e[1]),i&&Xt(i.update)&&8673&s&&i.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841})},d(e){e&&(m(t),m(s),m(l)),o=!1,zt(a)}}}function kc(e){let t,n,s,l,i,o,a;return{c(){t=N("div"),s=j(),l=N("div"),this.h()},l(e){t=D(e,"DIV",{class:!0,style:!0}),V(t).forEach(m),s=q(e),l=D(e,"DIV",{class:!0,style:!0}),V(l).forEach(m),this.h()},h(){T(t,"class","chart md:hidden"),Q(t,"height",e[1]),Q(t,"width",e[11]+"px"),Q(t,"margin-left","0"),Q(t,"margin-top","15px"),Q(t,"margin-bottom","10px"),Q(t,"overflow","visible"),Q(t,"break-inside","avoid"),T(l,"class","chart hidden md:block"),Q(l,"height",e[1]),Q(l,"width",e[10]+"px"),Q(l,"margin-left","0"),Q(l,"margin-top","15px"),Q(l,"margin-bottom","10px"),Q(l,"overflow","visible"),Q(l,"break-inside","avoid")},m(r,c){E(r,t,c),E(r,s,c),E(r,l,c),o||(a=[ot(n=Fl.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]})),ot(i=Fl.call(null,l,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]}))],o=!0)},p(e,s){2&s&&Q(t,"height",e[1]),2048&s&&Q(t,"width",e[11]+"px"),n&&Xt(n.update)&&10721&s&&n.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]}),2&s&&Q(l,"height",e[1]),1024&s&&Q(l,"width",e[10]+"px"),i&&Xt(i.update)&&9697&s&&i.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]})},d(e){e&&(m(t),m(s),m(l)),o=!1,zt(a)}}}function Cc(e){let t;function n(e,t){return e[3]?bc:e[4]?gc:void 0}let s=n(e),l=s&&s(e);return{c(){l&&l.c(),t=fe()},l(e){l&&l.l(e),t=fe()},m(e,n){l&&l.m(e,n),E(e,t,n)},p(e,[i]){s===(s=n(e))&&l?l.p(e,i):(l&&l.d(1),l=s&&s(e),l&&(l.c(),l.m(t.parentNode,t)))},i:me,o:me,d(e){e&&m(t),l&&l.d(e)}}}function Ec(e,t,n){let s,l,i,o,a,r,c=me;e.$$.on_destroy.push((()=>c()));const{resolveColorsObject:d}=Yt();let u,p,{config:m}=t,{height:f="291px"}=t,{width:h="100%"}=t,{copying:y=!1}=t,{printing:$=!1}=t,{echartsOptions:g}=t,{seriesOptions:v}=t,{seriesColors:b}=t,{isMap:E=!1}=t,{extraHeight:x}=t,T=!1;const w=Ml("gridConfig");return w&&(T=!0,({cols:u,gapWidth:p}=w)),e.$$set=e=>{"config"in e&&n(0,m=e.config),"height"in e&&n(1,f=e.height),"width"in e&&n(2,h=e.width),"copying"in e&&n(3,y=e.copying),"printing"in e&&n(4,$=e.printing),"echartsOptions"in e&&n(5,g=e.echartsOptions),"seriesOptions"in e&&n(6,v=e.seriesOptions),"seriesColors"in e&&n(14,b=e.seriesColors),"isMap"in e&&n(7,E=e.isMap),"extraHeight"in e&&n(8,x=e.extraHeight)},e.$$.update=()=>{16384&e.$$.dirty&&(n(12,s=d(b)),c(),c=gt(s,(e=>n(13,r=e)))),32768&e.$$.dirty&&n(18,l=Math.min(Number(u),2)),327680&e.$$.dirty&&n(11,i=(650-Number(p)*(l-1))/l),32768&e.$$.dirty&&n(17,o=Math.min(Number(u),3)),196608&e.$$.dirty&&n(10,a=(841-Number(p)*(o-1))/o)},[m,f,h,y,$,g,v,E,x,T,a,i,s,r,b,u,p,o,l]}class vc extends ve{constructor(e){super(),Te(this,e,Ec,Cc,ke,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function Tc(e){let t,n,s,l,i,o="Loading...";return{c(){t=N("div"),n=N("span"),n.textContent=o,s=j(),l=N("div"),this.h()},l(e){t=D(e,"DIV",{role:!0,class:!0});var i=V(t);n=D(i,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-1wtojot"!==We(n)&&(n.textContent=o),s=q(i),l=D(i,"DIV",{class:!0,style:!0}),V(l).forEach(m),i.forEach(m),this.h()},h(){T(n,"class","sr-only"),T(l,"class","bg-base-100 rounded-md max-w-[100%]"),Q(l,"height",e[0]),Q(l,"margin-top","15px"),Q(l,"margin-bottom","31px"),T(t,"role","status"),T(t,"class","animate-pulse")},m(e,i){E(e,t,i),P(t,n),P(t,s),P(t,l)},p(e,[t]){1&t&&Q(l,"height",e[0])},i(e){e&&(i||kt((()=>{i=Vl(t,mo,{}),i.start()})))},o:me,d(e){e&&m(t)}}}function Sc(e,t,n){let{height:s="231px"}=t;return e.$$set=e=>{"height"in e&&n(0,s=e.height)},[s]}class pc extends ve{constructor(e){super(),Te(this,e,Sc,Tc,ke,{height:0})}}function Ds(e){let t,n,s,l;const i=[Oc,Ac],o=[];return t=1,n=o[1]=i[1](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,t){o[1].m(e,t),E(e,s,t),l=!0},p(e,t){n.p(e,t)},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[1].d(e)}}}function Ac(e){let t,n,s,l;return{c(){t=N("div"),this.h()},l(e){t=D(e,"DIV",{class:!0,style:!0}),V(t).forEach(m),this.h()},h(){T(t,"class","chart svelte-db4qxn"),Q(t,"height",e[3]),Q(t,"width",e[4]),Q(t,"overflow","visible"),Q(t,"display",e[15]?"none":"inherit")},m(i,o){E(i,t,o),s||(l=ot(n=hc.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})),s=!0)},p(e,s){8&s[0]&&Q(t,"height",e[3]),16&s[0]&&Q(t,"width",e[4]),32768&s[0]&&Q(t,"display",e[15]?"none":"inherit"),n&&Xt(n.update)&&35141185&s[0]&&n.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})},i:me,o:me,d(e){e&&m(t),s=!1,l()}}}function Oc(e){let t,n;return t=new pc({props:{height:e[3]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};8&n[0]&&(s.height=e[3]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Ns(e){let t,n,s,l=e[8]&&Ms(e),i=e[5]&&e[7]&&Rs(e);return{c(){t=N("div"),l&&l.c(),n=j(),i&&i.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var s=V(t);l&&l.l(s),n=q(s),i&&i.l(s),s.forEach(m),this.h()},h(){T(t,"class","chart-footer svelte-db4qxn")},m(e,o){E(e,t,o),l&&l.m(t,null),P(t,n),i&&i.m(t,null),s=!0},p(e,s){e[8]?l?(l.p(e,s),256&s[0]&&y(l,1)):(l=Ms(e),l.c(),y(l,1),l.m(t,n)):l&&(ge(),v(l,1,1,(()=>{l=null})),be()),e[5]&&e[7]?i?(i.p(e,s),160&s[0]&&y(i,1)):(i=Rs(e),i.c(),y(i,1),i.m(t,null)):i&&(ge(),v(i,1,1,(()=>{i=null})),be())},i(e){s||(y(l),y(i),s=!0)},o(e){v(l),v(i),s=!1},d(e){e&&m(t),l&&l.d(),i&&i.d()}}}function Ms(e){let t,n;return t=new Ki({props:{text:"Save Image",class:"download-button",downloadData:e[32],display:e[17],queryID:e[1],$$slots:{default:[wc]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};16384&n[0]&&(s.downloadData=e[32]),131072&n[0]&&(s.display=e[17]),2&n[0]&&(s.queryID=e[1]),32&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function wc(e){let t,n,s,l;return{c(){t=ml("svg"),n=ml("rect"),s=ml("circle"),l=ml("path"),this.h()},l(e){t=dl(e,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var i=V(t);n=dl(i,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),V(n).forEach(m),s=dl(i,"circle",{cx:!0,cy:!0,r:!0}),V(s).forEach(m),l=dl(i,"path",{d:!0}),V(l).forEach(m),i.forEach(m),this.h()},h(){T(n,"x","3"),T(n,"y","3"),T(n,"width","18"),T(n,"height","18"),T(n,"rx","2"),T(s,"cx","8.5"),T(s,"cy","8.5"),T(s,"r","1.5"),T(l,"d","M20.4 14.5L16 10 4 20"),T(t,"xmlns","http://www.w3.org/2000/svg"),T(t,"width","12"),T(t,"height","12"),T(t,"viewBox","0 0 24 24"),T(t,"fill","none"),T(t,"stroke","#000"),T(t,"stroke-width","2"),T(t,"stroke-linecap","round"),T(t,"stroke-linejoin","round")},m(e,i){E(e,t,i),P(t,n),P(t,s),P(t,l)},p:me,d(e){e&&m(t)}}}function Rs(e){let t,n;return t=new Ki({props:{text:"Download Data",data:e[5],queryID:e[1],class:"download-button",display:e[17]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};32&n[0]&&(s.data=e[5]),2&n[0]&&(s.queryID=e[1]),131072&n[0]&&(s.display=e[17]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Ps(e){let t,n;return t=new ho({props:{source:JSON.stringify(e[0],void 0,3),copyToClipboard:!0,$$slots:{default:[Lc]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};1&n[0]&&(s.source=JSON.stringify(e[0],void 0,3)),1&n[0]|32&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Lc(e){let t,n=JSON.stringify(e[0],void 0,3)+"";return{c(){t=de(n)},l(e){t=ce(e,n)},m(e,n){E(e,t,n)},p(e,s){1&s[0]&&n!==(n=JSON.stringify(e[0],void 0,3)+"")&&Xe(t,n)},d(e){e&&m(t)}}}function zs(e){let t,n,s,l;return{c(){t=N("div"),this.h()},l(e){t=D(e,"DIV",{class:!0,style:!0}),V(t).forEach(m),this.h()},h(){T(t,"class","chart svelte-db4qxn"),Q(t,"display","none"),Q(t,"visibility","visible"),Q(t,"height",e[3]),Q(t,"width","666px"),Q(t,"margin-left","0"),Q(t,"margin-top","15px"),Q(t,"margin-bottom","15px"),Q(t,"overflow","visible")},m(i,o){E(i,t,o),s||(l=ot(n=_c.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})),s=!0)},p(e,s){8&s[0]&&Q(t,"height",e[3]),n&&Xt(n.update)&&37225991&s[0]&&n.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})},d(e){e&&m(t),s=!1,l()}}}function Ic(e){let t,n,s,l,i,o,a,r,c,d,u=!e[16]&&Ds(e);s=new vc({props:{config:e[0],height:e[3],width:e[4],copying:e[15],printing:e[16],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[18]}});let p=(e[7]||e[8])&&Ns(e),f=e[11]&&!e[16]&&Ps(e),h=e[14]&&zs(e);return{c(){t=N("div"),u&&u.c(),n=j(),ne(s.$$.fragment),l=j(),p&&p.c(),i=j(),f&&f.c(),o=j(),h&&h.c(),a=fe(),this.h()},l(e){t=D(e,"DIV",{role:!0,class:!0});var r=V(t);u&&u.l(r),n=q(r),ie(s.$$.fragment,r),l=q(r),p&&p.l(r),i=q(r),f&&f.l(r),r.forEach(m),o=q(e),h&&h.l(e),a=fe(),this.h()},h(){T(t,"role","none"),T(t,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m(m,y){E(m,t,y),u&&u.m(t,null),P(t,n),le(s,t,null),P(t,l),p&&p.m(t,null),P(t,i),f&&f.m(t,null),E(m,o,y),h&&h.m(m,y),E(m,a,y),r=!0,c||(d=[qe(window,"copy",e[27]),qe(window,"beforeprint",e[28]),qe(window,"afterprint",e[29]),qe(window,"export-beforeprint",e[30]),qe(window,"export-afterprint",e[31]),qe(t,"mouseenter",e[33]),qe(t,"mouseleave",e[34])],c=!0)},p(e,l){e[16]?u&&(ge(),v(u,1,1,(()=>{u=null})),be()):u?(u.p(e,l),65536&l[0]&&y(u,1)):(u=Ds(e),u.c(),y(u,1),u.m(t,n));const o={};1&l[0]&&(o.config=e[0]),8&l[0]&&(o.height=e[3]),16&l[0]&&(o.width=e[4]),32768&l[0]&&(o.copying=e[15]),65536&l[0]&&(o.printing=e[16]),512&l[0]&&(o.echartsOptions=e[9]),1024&l[0]&&(o.seriesOptions=e[10]),262144&l[0]&&(o.seriesColors=e[18]),s.$set(o),e[7]||e[8]?p?(p.p(e,l),384&l[0]&&y(p,1)):(p=Ns(e),p.c(),y(p,1),p.m(t,i)):p&&(ge(),v(p,1,1,(()=>{p=null})),be()),e[11]&&!e[16]?f?(f.p(e,l),67584&l[0]&&y(f,1)):(f=Ps(e),f.c(),y(f,1),f.m(t,null)):f&&(ge(),v(f,1,1,(()=>{f=null})),be()),e[14]?h?h.p(e,l):(h=zs(e),h.c(),h.m(a.parentNode,a)):h&&(h.d(1),h=null)},i(e){r||(y(u),y(s.$$.fragment,e),y(p),y(f),r=!0)},o(e){v(u),v(s.$$.fragment,e),v(p),v(f),r=!1},d(e){e&&(m(t),m(o),m(a)),u&&u.d(),te(s),p&&p.d(),f&&f.d(),h&&h.d(e),c=!1,zt(d)}}}function Dc(e,t,n){let s;const l=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let i,o,a,r=Ee(t,l),c=me;e.$$.on_destroy.push((()=>c()));const{activeAppearance:d,theme:u,resolveColorsObject:p}=Yt();je(e,d,(e=>n(20,o=e))),je(e,u,(e=>n(21,a=e)));let{config:m}=t,{queryID:f}=t,{evidenceChartTitle:h}=t,{height:y="291px"}=t,{width:$="100%"}=t,{data:g}=t,{renderer:v}=t,{downloadableData:b}=t,{downloadableImage:E}=t,{echartsOptions:x}=t,{seriesOptions:T}=t,{printEchartsConfig:w}=t,{seriesColors:D}=t,{connectGroup:C}=t,{xAxisLabelOverflow:L}=t;const N=pr();let O=!1,A=!1,I=!1,S=!1;return e.$$set=e=>{t=re(re({},t),nt(e)),n(25,r=Ee(t,l)),"config"in e&&n(0,m=e.config),"queryID"in e&&n(1,f=e.queryID),"evidenceChartTitle"in e&&n(2,h=e.evidenceChartTitle),"height"in e&&n(3,y=e.height),"width"in e&&n(4,$=e.width),"data"in e&&n(5,g=e.data),"renderer"in e&&n(6,v=e.renderer),"downloadableData"in e&&n(7,b=e.downloadableData),"downloadableImage"in e&&n(8,E=e.downloadableImage),"echartsOptions"in e&&n(9,x=e.echartsOptions),"seriesOptions"in e&&n(10,T=e.seriesOptions),"printEchartsConfig"in e&&n(11,w=e.printEchartsConfig),"seriesColors"in e&&n(26,D=e.seriesColors),"connectGroup"in e&&n(12,C=e.connectGroup),"xAxisLabelOverflow"in e&&n(13,L=e.xAxisLabelOverflow)},e.$$.update=()=>{67108864&e.$$.dirty[0]&&(n(18,s=p(D)),c(),c=gt(s,(e=>n(19,i=e))))},[m,f,h,y,$,g,v,b,E,x,T,w,C,L,O,A,I,S,s,i,o,a,d,u,N,r,D,()=>{n(15,A=!0),Ar(),setTimeout((()=>{n(15,A=!1)}),0)},()=>n(16,I=!0),()=>n(16,I=!1),()=>n(16,I=!0),()=>n(16,I=!1),()=>{n(14,O=!0),setTimeout((()=>{n(14,O=!1)}),0)},()=>n(17,S=!0),()=>n(17,S=!1)]}class Nc extends ve{constructor(e){super(),Te(this,e,Dc,Ic,ke,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function Bl(e,t){const n=new Set(e.map((e=>e[t])));return Array.from(n)}function Mc(e,t){return Ht(e,_o({count:go(t)}))[0].count}function Rc(e,t,n){let s;if("object"!=typeof n)s=Ht(e,Wi(t,Qn({xTotal:Xi(n)})),Vi({percentOfX:xn(n,"xTotal")}),Kn({percentOfX:n+"_pct"}));else{s=Ht(e,Vi({valueSum:0}));for(let e=0;e<s.length;e++){s[e].valueSum=0;for(let t=0;t<n.length;t++)s[e].valueSum=s[e].valueSum+s[e][n[t]]}s=Ht(s,Wi(t,Qn({xTotal:Xi("valueSum")})));for(let e=0;e<n.length;e++)s=Ht(s,Vi({percentOfX:xn(n[e],"xTotal")}),Kn({percentOfX:n[e]+"_pct"}))}return s}function Dl(e,t,n){return[...e].sort(((e,s)=>(e[t]<s[t]?-1:1)*(n?1:-1)))}function br(e,t,n){return e%(t+n)<t?0:1}function Pc(e){let t,n;return t=new nr({props:{error:e[14],title:e[8]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};16384&n[0]&&(s.error=e[14]),256&n[0]&&(s.title=e[8]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function zc(e){let t,n,s;const l=e[136].default,i=we(l,e,e[135],null);return n=new Nc({props:{config:e[20],height:e[15],width:e[13],data:e[0],queryID:e[6],evidenceChartTitle:e[7],showAllXAxisLabels:e[1],swapXY:e[3],echartsOptions:e[9],seriesOptions:e[10],printEchartsConfig:e[2],renderer:e[11],downloadableData:e[4],downloadableImage:e[5],connectGroup:e[12],xAxisLabelOverflow:e[23],seriesColors:e[16]}}),{c(){i&&i.c(),t=j(),ne(n.$$.fragment)},l(e){i&&i.l(e),t=q(e),ie(n.$$.fragment,e)},m(e,l){i&&i.m(e,l),E(e,t,l),le(n,e,l),s=!0},p(e,t){i&&i.p&&(!s||2048&t[4])&&Le(i,l,e,e[135],s?De(l,e[135],t,null):Ie(e[135]),null);const o={};1048576&t[0]&&(o.config=e[20]),32768&t[0]&&(o.height=e[15]),8192&t[0]&&(o.width=e[13]),1&t[0]&&(o.data=e[0]),64&t[0]&&(o.queryID=e[6]),128&t[0]&&(o.evidenceChartTitle=e[7]),2&t[0]&&(o.showAllXAxisLabels=e[1]),8&t[0]&&(o.swapXY=e[3]),512&t[0]&&(o.echartsOptions=e[9]),1024&t[0]&&(o.seriesOptions=e[10]),4&t[0]&&(o.printEchartsConfig=e[2]),2048&t[0]&&(o.renderer=e[11]),16&t[0]&&(o.downloadableData=e[4]),32&t[0]&&(o.downloadableImage=e[5]),4096&t[0]&&(o.connectGroup=e[12]),65536&t[0]&&(o.seriesColors=e[16]),n.$set(o)},i(e){s||(y(i,e),y(n.$$.fragment,e),s=!0)},o(e){v(i,e),v(n.$$.fragment,e),s=!1},d(e){e&&m(t),i&&i.d(e),te(n,e)}}}function Fc(e){let t,n,s,l;const i=[zc,Pc],o=[];function a(e,t){return e[14]?1:0}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){o[t].m(e,n),E(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(ge(),v(o[r],1,1,(()=>{o[r]=null})),be(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),y(n,1),n.m(s.parentNode,s))},i(e){l||(y(n),l=!0)},o(e){v(n),l=!1},d(e){e&&m(s),o[t].d(e)}}}function Bc(e,t,n){let s,l,i,o,a,r,c,d,u,p=me,m=me,f=me;e.$$.on_destroy.push((()=>p())),e.$$.on_destroy.push((()=>m())),e.$$.on_destroy.push((()=>f()));let{$$slots:h={},$$scope:y}=t,$=oi({}),g=oi({});je(e,g,(e=>n(20,u=e)));const{theme:v,resolveColor:b,resolveColorsObject:E,resolveColorPalette:x}=Yt();je(e,v,(e=>n(132,r=e)));let T,{data:w}=t,{queryID:D}=t,{x:C}=t,{y:L}=t,{y2:N}=t,{series:O}=t,{size:A}=t,{tooltipTitle:I}=t,{showAllXAxisLabels:S}=t,{printEchartsConfig:k=!1}=t,P=!!L,V=!!C,{swapXY:M=!1}=t,{title:R}=t,{subtitle:q}=t,{chartType:j="Chart"}=t,{bubble:z=!1}=t,{hist:_=!1}=t,{boxplot:F=!1}=t,{xType:U}=t,{xAxisTitle:G="false"}=t,{xBaseline:B=!0}=t,{xTickMarks:H=!1}=t,{xGridlines:Q=!1}=t,{xAxisLabels:X=!0}=t,{sort:W=!0}=t,{xFmt:Y}=t,{xMin:K}=t,{xMax:J}=t,{yLog:Z=!1}=t,{yType:ee=(!0===Z?"log":"value")}=t,{yLogBase:te=10}=t,{yAxisTitle:ne="false"}=t,{yBaseline:se=!1}=t,{yTickMarks:le=!1}=t,{yGridlines:ie=!0}=t,{yAxisLabels:oe=!0}=t,{yMin:ae}=t,{yMax:re}=t,{yScale:ce=!1}=t,{yFmt:de}=t,{yAxisColor:ue="true"}=t,{y2AxisTitle:pe="false"}=t,{y2Baseline:fe=!1}=t,{y2TickMarks:he=!1}=t,{y2Gridlines:ye=!0}=t,{y2AxisLabels:$e=!0}=t,{y2Min:ge}=t,{y2Max:ve}=t,{y2Scale:be=!1}=t,{y2Fmt:Ee}=t,{y2AxisColor:xe="true"}=t,{sizeFmt:Te}=t,{colorPalette:we="default"}=t,{legend:De}=t,{echartsOptions:Ce}=t,{seriesOptions:Le}=t,{seriesColors:Ne}=t,{stackType:Oe}=t,{stacked100:Ae=!1}=t,{chartAreaHeight:Ie}=t,{renderer:Se}=t,{downloadableData:ke=!0}=t,{downloadableImage:Pe=!0}=t,{connectGroup:Ve}=t,{leftPadding:Me}=t,{rightPadding:Re}=t,{xLabelWrap:qe=!1}=t;const ze=qe?"break":"truncate";let _e,Fe,Ue,Be,He,Qe,Xe,We,Ye,Ke,Je,Ze,et,tt,nt,st,lt,it,ot,at,rt,dt,ut,pt,mt,ft,ht,yt,$t,bt,Et,xt,Tt,wt,Dt,Ct,Lt,Nt,Ot,At,St,kt,Pt,Vt,Mt,Rt,qt,jt,zt=[],_t=[],Ft=[],Ut=!0,Gt=[],Bt=[];return e.$$set=e=>{"data"in e&&n(0,w=e.data),"queryID"in e&&n(6,D=e.queryID),"x"in e&&n(24,C=e.x),"y"in e&&n(25,L=e.y),"y2"in e&&n(49,N=e.y2),"series"in e&&n(50,O=e.series),"size"in e&&n(51,A=e.size),"tooltipTitle"in e&&n(52,I=e.tooltipTitle),"showAllXAxisLabels"in e&&n(1,S=e.showAllXAxisLabels),"printEchartsConfig"in e&&n(2,k=e.printEchartsConfig),"swapXY"in e&&n(3,M=e.swapXY),"title"in e&&n(7,R=e.title),"subtitle"in e&&n(53,q=e.subtitle),"chartType"in e&&n(8,j=e.chartType),"bubble"in e&&n(54,z=e.bubble),"hist"in e&&n(55,_=e.hist),"boxplot"in e&&n(56,F=e.boxplot),"xType"in e&&n(26,U=e.xType),"xAxisTitle"in e&&n(27,G=e.xAxisTitle),"xBaseline"in e&&n(28,B=e.xBaseline),"xTickMarks"in e&&n(29,H=e.xTickMarks),"xGridlines"in e&&n(30,Q=e.xGridlines),"xAxisLabels"in e&&n(31,X=e.xAxisLabels),"sort"in e&&n(32,W=e.sort),"xFmt"in e&&n(57,Y=e.xFmt),"xMin"in e&&n(58,K=e.xMin),"xMax"in e&&n(59,J=e.xMax),"yLog"in e&&n(33,Z=e.yLog),"yType"in e&&n(60,ee=e.yType),"yLogBase"in e&&n(61,te=e.yLogBase),"yAxisTitle"in e&&n(34,ne=e.yAxisTitle),"yBaseline"in e&&n(35,se=e.yBaseline),"yTickMarks"in e&&n(36,le=e.yTickMarks),"yGridlines"in e&&n(37,ie=e.yGridlines),"yAxisLabels"in e&&n(38,oe=e.yAxisLabels),"yMin"in e&&n(62,ae=e.yMin),"yMax"in e&&n(63,re=e.yMax),"yScale"in e&&n(39,ce=e.yScale),"yFmt"in e&&n(64,de=e.yFmt),"yAxisColor"in e&&n(65,ue=e.yAxisColor),"y2AxisTitle"in e&&n(40,pe=e.y2AxisTitle),"y2Baseline"in e&&n(41,fe=e.y2Baseline),"y2TickMarks"in e&&n(42,he=e.y2TickMarks),"y2Gridlines"in e&&n(43,ye=e.y2Gridlines),"y2AxisLabels"in e&&n(44,$e=e.y2AxisLabels),"y2Min"in e&&n(66,ge=e.y2Min),"y2Max"in e&&n(67,ve=e.y2Max),"y2Scale"in e&&n(45,be=e.y2Scale),"y2Fmt"in e&&n(68,Ee=e.y2Fmt),"y2AxisColor"in e&&n(69,xe=e.y2AxisColor),"sizeFmt"in e&&n(70,Te=e.sizeFmt),"colorPalette"in e&&n(71,we=e.colorPalette),"legend"in e&&n(46,De=e.legend),"echartsOptions"in e&&n(9,Ce=e.echartsOptions),"seriesOptions"in e&&n(10,Le=e.seriesOptions),"seriesColors"in e&&n(72,Ne=e.seriesColors),"stackType"in e&&n(73,Oe=e.stackType),"stacked100"in e&&n(74,Ae=e.stacked100),"chartAreaHeight"in e&&n(47,Ie=e.chartAreaHeight),"renderer"in e&&n(11,Se=e.renderer),"downloadableData"in e&&n(4,ke=e.downloadableData),"downloadableImage"in e&&n(5,Pe=e.downloadableImage),"connectGroup"in e&&n(12,Ve=e.connectGroup),"leftPadding"in e&&n(75,Me=e.leftPadding),"rightPadding"in e&&n(76,Re=e.rightPadding),"xLabelWrap"in e&&n(48,qe=e.xLabelWrap),"$$scope"in e&&n(135,y=e.$$scope)},e.$$.update=()=>{var t,u,h,y,v,D;if(4&e.$$.dirty[0]&&n(2,k=Ge(k)),8&e.$$.dirty[0]&&n(3,M=Ge(M)),268435456&e.$$.dirty[0]&&n(28,B=Ge(B)),536870912&e.$$.dirty[0]&&n(29,H=Ge(H)),1073741824&e.$$.dirty[0]&&n(30,Q=Ge(Q)),1&e.$$.dirty[1]&&n(31,X=Ge(X)),2&e.$$.dirty[1]&&n(32,W=Ge(W)),4&e.$$.dirty[1]&&n(33,Z=Ge(Z)),16&e.$$.dirty[1]&&n(35,se=Ge(se)),32&e.$$.dirty[1]&&n(36,le=Ge(le)),64&e.$$.dirty[1]&&n(37,ie=Ge(ie)),128&e.$$.dirty[1]&&n(38,oe=Ge(oe)),256&e.$$.dirty[1]&&n(39,ce=Ge(ce)),8&e.$$.dirty[2]&&(n(19,s=b(ue)),f(),f=gt(s,(e=>n(134,d=e)))),1024&e.$$.dirty[1]&&n(41,fe=Ge(fe)),2048&e.$$.dirty[1]&&n(42,he=Ge(he)),4096&e.$$.dirty[1]&&n(43,ye=Ge(ye)),8192&e.$$.dirty[1]&&n(44,$e=Ge($e)),16384&e.$$.dirty[1]&&n(45,be=Ge(be)),128&e.$$.dirty[2]&&(n(18,l=b(xe)),m(),m=gt(l,(e=>n(133,c=e)))),512&e.$$.dirty[2]&&(n(17,i=x(we)),p(),p=gt(i,(e=>n(131,a=e)))),1024&e.$$.dirty[2]&&n(16,o=E(Ne)),16&e.$$.dirty[0]&&n(4,ke=Ge(ke)),32&e.$$.dirty[0]&&n(5,Pe=Ge(Pe)),131072&e.$$.dirty[1]&&n(48,qe=Ge(qe)),2130731403&e.$$.dirty[0]|2147352575&e.$$.dirty[1]|2147481975&e.$$.dirty[2]|2147483647&e.$$.dirty[3]|2047&e.$$.dirty[4])try{if(n(14,Rt=void 0),n(124,Ft=[]),n(83,_t=[]),n(126,Gt=[]),n(127,Bt=[]),n(85,Be=[]),n(77,P=!!L),n(78,V=!!C),kn(w),n(80,_e=si(w)),n(81,Fe=Object.keys(_e)),V||n(24,C=Fe[0]),!P){n(82,zt=Fe.filter((function(e){return![C,O,A].includes(e)})));for(let e=0;e<zt.length;e++)n(85,Be=zt[e]),n(84,Ue=_e[Be].type),"number"===Ue&&_t.push(Be);n(25,L=_t.length>1?_t:_t[0])}n(79,T=z?{x:C,y:L,size:A}:_?{x:C}:F?{}:{x:C,y:L});for(let e in T)null==T[e]&&Ft.push(e);if(1===Ft.length)throw Error((new Intl.ListFormat).format(Ft)+" is required");if(Ft.length>1)throw Error((new Intl.ListFormat).format(Ft)+" are required");if(!0===Ae&&L.includes("_pct")&&!1===Ut)if("object"==typeof L){for(let e=0;e<L.length;e++)n(25,L[e]=L[e].replace("_pct",""),L);n(125,Ut=!1)}else n(25,L=L.replace("_pct","")),n(125,Ut=!1);if(C&&Gt.push(C),L)if("object"==typeof L)for(n(128,Mt=0);Mt<L.length;n(128,Mt++,Mt))Gt.push(L[Mt]);else Gt.push(L);if(N)if("object"==typeof N)for(n(128,Mt=0);Mt<N.length;n(128,Mt++,Mt))Gt.push(N[Mt]);else Gt.push(N);if(A&&Gt.push(A),O&&Bt.push(O),I&&Bt.push(I),kn(w,Gt,Bt),!0===Ae){if(n(0,w=Rc(w,C,L)),"object"==typeof L){for(let e=0;e<L.length;e++)n(25,L[e]=L[e]+"_pct",L);n(125,Ut=!1)}else n(25,L+="_pct"),n(125,Ut=!1);n(80,_e=si(w))}switch(n(86,He=_e[C].type),He){case"number":n(86,He="value");break;case"string":n(86,He="category");break;case"date":n(86,He="time")}if(n(26,U="category"===U?"category":He),n(1,S=S?"true"===S||!0===S:"category"===U),M&&"category"!==U)throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(M&&N)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(M&&n(26,U="category"),n(87,Qe="value"===He&&"category"===U),n(0,w=W?"category"===He?Dl(w,L,!1):Dl(w,C,!0):w),"time"===He&&n(0,w=Dl(w,C,!0)),n(129,qt=si(w,"array")),n(130,jt=qt.filter((e=>"date"===e.type))),n(130,jt=jt.map((e=>e.id))),jt.length>0)for(let e=0;e<jt.length;e++)n(0,w=bo(w,jt[e]));n(88,Xe=Y?It(Y,null==(t=_e[C].format)?void 0:t.valueType):_e[C].format),n(89,We=L?de?It(de,"object"==typeof L?null==(u=_e[L[0]].format)?void 0:u.valueType:null==(h=_e[L].format)?void 0:h.valueType):"object"==typeof L?_e[L[0]].format:_e[L].format:"str"),N&&n(90,Ye=Ee?It(Ee,"object"==typeof N?null==(y=_e[N[0]].format)?void 0:y.valueType:null==(v=_e[N].format)?void 0:v.valueType):"object"==typeof N?_e[N[0]].format:_e[N].format),A&&n(91,Ke=Te?It(Te,null==(D=_e[A].format)?void 0:D.valueType):_e[A].format),n(92,Je=_e[C].columnUnitSummary),L&&n(93,Ze="object"==typeof L?_e[L[0]].columnUnitSummary:_e[L].columnUnitSummary),N&&n(94,et="object"==typeof N?_e[N[0]].columnUnitSummary:_e[N].columnUnitSummary),n(27,G="true"===G?vt(C,Xe):"false"===G?"":G),n(34,ne="true"===ne?"object"==typeof L?"":vt(L,We):"false"===ne?"":ne),n(40,pe="true"===pe?"object"==typeof N?"":vt(N,Ye):"false"===pe?"":pe);let e,s,l="object"==typeof L?L.length:1,i=O?Mc(w,O):1,o=l*i,p="object"==typeof N?N.length:N?1:0,m=o+p;if(void 0!==De&&n(46,De="true"===De||!0===De),n(46,De=De??m>1),!0===Ae&&!0===Z)throw Error("Log axis cannot be used in a 100% stacked chart");if("stacked"===Oe&&m>1&&!0===Z)throw Error("Log axis cannot be used in a stacked chart");if("object"==typeof L){e=_e[L[0]].columnUnitSummary.min;for(let t=0;t<L.length;t++)_e[L[t]].columnUnitSummary.min<e&&(e=_e[L[t]].columnUnitSummary.min)}else L&&(e=_e[L].columnUnitSummary.min);if(!0===Z&&e<=0&&null!==e)throw Error("Log axis cannot display values less than or equal to zero");if($.update((e=>({...e,data:w,x:C,y:L,y2:N,series:O,swapXY:M,sort:W,xType:U,xFormat:Xe,yFormat:We,y2Format:Ye,sizeFormat:Ke,xMismatch:Qe,size:A,yMin:ae,y2Min:ge,columnSummary:_e,xAxisTitle:G,yAxisTitle:ne,y2AxisTitle:pe,tooltipTitle:I,chartAreaHeight:Ie,chartType:j,yCount:l,y2Count:p}))),n(95,tt=Bl(w,C)),n(96,nt=M?{type:ee,logBase:te,position:"top",axisLabel:{show:oe,hideOverlap:!0,showMaxLabel:!0,formatter:e=>ii(e,We,Ze),margin:4},min:ae,max:re,scale:ce,splitLine:{show:ie},axisLine:{show:se,onZero:!1},axisTick:{show:le},boundaryGap:!1,z:2}:{type:U,min:K,max:J,tooltip:{show:!0,position:"inside",formatter(e){if(e.isTruncated())return e.name}},splitLine:{show:Q},axisLine:{show:B},axisTick:{show:H},axisLabel:{show:X,hideOverlap:!0,showMaxLabel:"category"===U||"value"===U,formatter:"time"!==U&&"category"!==U&&function(e){return ii(e,Xe,Je)},margin:6},scale:!0,z:2}),M?n(97,st={type:U,inverse:"true",splitLine:{show:Q},axisLine:{show:B},axisTick:{show:H},axisLabel:{show:X,hideOverlap:!0},scale:!0,min:K,max:J,z:2}):(n(97,st={type:ee,logBase:te,splitLine:{show:ie},axisLine:{show:se,onZero:!1},axisTick:{show:le},axisLabel:{show:oe,hideOverlap:!0,margin:4,formatter:e=>ii(e,We,Ze),color:N?"true"===d?a[0]:"false"!==d?d:void 0:void 0},name:ne,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:N?"true"===d?a[0]:"false"!==d?d:void 0:void 0},nameGap:6,min:ae,max:re,scale:ce,boundaryGap:["0%","1%"],z:2}),s={type:"value",show:!1,alignTicks:!0,splitLine:{show:ye},axisLine:{show:fe,onZero:!1},axisTick:{show:he},axisLabel:{show:$e,hideOverlap:!0,margin:4,formatter:e=>ii(e,Ye,et),color:"true"===c?a[o]:"false"!==c?c:void 0},name:pe,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:"true"===c?a[o]:"false"!==c?c:void 0},nameGap:6,min:ge,max:ve,scale:be,boundaryGap:["0%","1%"],z:2},n(97,st=[st,s])),Ie){if(n(47,Ie=Number(Ie)),isNaN(Ie))throw Error("chartAreaHeight must be a number");if(Ie<=0)throw Error("chartAreaHeight must be a positive number")}else n(47,Ie=180);n(100,ot=!!R),n(101,at=!!q),n(102,rt=De*(null!==O||"object"==typeof L&&L.length>1)),n(103,dt=""!==ne&&M),n(104,ut=""!==G&&!M),n(105,pt=15),n(106,mt=13),n(107,ft=6*at),n(108,ht=ot*pt+at*mt+ft*Math.max(ot,at)),n(109,yt=10),n(110,$t=10),n(111,bt=14),n(112,Et=14),n(113,xt=15),n(113,xt*=rt),n(114,Tt=7),n(114,Tt*=Math.max(ot,at)),n(115,wt=ht+Tt),n(116,Dt=wt+xt+Et*dt+yt),n(117,Ct=ut*bt+$t),n(121,At=8),n(123,kt=1),M&&(n(122,St=tt.length),n(123,kt=Math.max(1,St/At))),n(118,Lt=Ie*kt+Dt+Ct),n(119,Nt=wt+xt+7),n(15,Pt=Lt+"px"),n(13,Vt="100%"),n(120,Ot=M?ne:G),""!==Ot&&n(120,Ot+=" →"),n(98,lt={id:"horiz-axis-title",type:"text",style:{text:Ot,textAlign:"right",fill:r.colors["base-content-muted"]},cursor:"auto",right:M?"2%":"3%",top:M?Nt:null,bottom:M?null:"2%"}),n(99,it={title:{text:R,subtext:q,subtextStyle:{width:Vt}},tooltip:{trigger:"axis",show:!0,formatter(e){let t,n,s,i;if(m>1){n=e[0].value[M?1:0],t=`<span id="tooltip" style='font-weight: 600;'>${ct(n,Xe)}</span>`;for(let n=e.length-1;n>=0;n--)"stackTotal"!==e[n].seriesName&&(s=e[n].value[M?0:1],t+=`<br> <span style='font-size: 11px;'>${e[n].marker} ${e[n].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${ct(s,0===br(e[n].componentIndex,l,p)?We:Ye)}</span>`)}else"value"===U?(n=e[0].value[M?1:0],s=e[0].value[M?0:1],i=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${vt(C,Xe)}: </span><span style='float:right; margin-left: 10px;'>${ct(n,Xe)}</span><br/><span style='font-weight: 600;'>${vt(i,We)}: </span><span style='float:right; margin-left: 10px;'>${ct(s,We)}</span>`):(n=e[0].value[M?1:0],s=e[0].value[M?0:1],i=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${ct(n,Xe)}</span><br/><span>${vt(i,We)}: </span><span style='float:right; margin-left: 10px;'>${ct(s,We)}</span>`);return t},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:De,type:"scroll",top:wt,padding:[0,0,0,0],data:[]},grid:{left:Me??(M?"1%":"0.8%"),right:Re??(M?"4%":"3%"),bottom:Ct,top:Dt,containLabel:!0},xAxis:nt,yAxis:st,series:[],animation:!0,graphic:lt,color:a}),g.update((()=>it))}catch(e){if(n(14,Rt=e.message),console.error("[31m%s[0m",`Error in ${j}: ${e.message}`),yo)throw Rt;$.update((e=>({...e,error:Rt})))}e.$$.dirty[0]},Nl(lr,$),Nl(ir,g),[w,S,k,M,ke,Pe,D,R,j,Ce,Le,Se,Ve,Vt,Rt,Pt,o,i,l,s,u,g,v,ze,C,L,U,G,B,H,Q,X,W,Z,ne,se,le,ie,oe,ce,pe,fe,he,ye,$e,be,De,Ie,qe,N,O,A,I,q,z,_,F,Y,K,J,ee,te,ae,re,de,ue,ge,ve,Ee,xe,Te,we,Ne,Oe,Ae,Me,Re,P,V,T,_e,Fe,zt,_t,Ue,Be,He,Qe,Xe,We,Ye,Ke,Je,Ze,et,tt,nt,st,lt,it,ot,at,rt,dt,ut,pt,mt,ft,ht,yt,$t,bt,Et,xt,Tt,wt,Dt,Ct,Lt,Nt,Ot,At,St,kt,Ft,Ut,Gt,Bt,Mt,qt,jt,a,r,c,d,y,h]}class Uc extends ve{constructor(e){super(),Te(this,e,Bc,Fc,ke,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Vc(e){let t;const n=e[7].default,s=we(n,e,e[8],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||256&l)&&Le(s,n,e,e[8],t?De(n,e[8],l,null):Ie(e[8]),null)},i(e){t||(y(s,e),t=!0)},o(e){v(s,e),t=!1},d(e){s&&s.d(e)}}}function Hc(e){let t,n;const s=[e[5],{data:zl.isQuery(e[11])?Array.from(e[11]):e[11]},{queryID:e[6]}];let l={$$slots:{default:[Vc]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=re(l,s[e]);return t=new Uc({props:l}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const l=2144&n?et(s,[32&n&&Nt(e[5]),2048&n&&{data:zl.isQuery(e[11])?Array.from(e[11]):e[11]},64&n&&{queryID:e[6]}]):{};256&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Gc(e){let t,n;return t=new Co({props:{slot:"empty",emptyMessage:e[2],emptySet:e[1],chartType:e[5].chartType,isInitial:e[4]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};4&n&&(s.emptyMessage=e[2]),2&n&&(s.emptySet=e[1]),32&n&&(s.chartType=e[5].chartType),16&n&&(s.isInitial=e[4]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function qc(e){let t,n;return t=new nr({props:{slot:"error",title:e[5].chartType,error:e[11].error.message}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};32&n&&(s.title=e[5].chartType),2048&n&&(s.error=e[11].error.message),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function jc(e){let t,n;return t=new ko({props:{data:e[0],height:e[3],$$slots:{error:[qc,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0],empty:[Gc],default:[Hc,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,[n]){const s={};1&n&&(s.data=e[0]),8&n&&(s.height=e[3]),2358&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Wc(e,t,n){let s,{$$slots:l={},$$scope:i}=t,{data:o}=t;const a=zl.isQuery(o)?o.hash:void 0;let r=(null==o?void 0:o.hash)===a,{emptySet:c}=t,{emptyMessage:d}=t,{height:u=200}=t,p=null==o?void 0:o.id;return e.$$set=e=>{n(10,t=re(re({},t),nt(e))),"data"in e&&n(0,o=e.data),"emptySet"in e&&n(1,c=e.emptySet),"emptyMessage"in e&&n(2,d=e.emptyMessage),"height"in e&&n(3,u=e.height),"$$scope"in e&&n(8,i=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(4,r=(null==o?void 0:o.hash)===a),n(5,s={...Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e)))})},t=nt(t),[o,c,d,u,r,s,p,l,i]}class Xc extends ve{constructor(e){super(),Te(this,e,Wc,jc,ke,{data:0,emptySet:1,emptyMessage:2,height:3})}}function Yc(e,t,n,s,l,i,o,a,r,c,d=void 0,u=void 0,p=void 0,m=void 0){function f(e,t,n,s){let l={name:t,data:e,yAxisIndex:n};return l={...s,...l},l}let h,y,$,g,v,b,E,x,T=[],w=function(e,t){const n=[];function s(e,t){(function(e){return typeof e>"u"})(e)||(Array.isArray(e)?e.forEach((e=>n.push([e,t]))):n.push([e,t]))}return s(e,0),s(t,1),n}(n,p);if(null!=s&&1===w.length)for(E=Bl(e,s),h=0;h<E.length;h++){if(v=e.filter((e=>e[s]===E[h])),g=l?v.map((e=>[e[w[0][0]],a?e[t].toString():e[t]])):v.map((e=>[a?e[t].toString():e[t],e[w[0][0]]])),d){let e=v.map((e=>e[d]));g.forEach(((t,n)=>t.push(e[n])))}if(u){let e=v.map((e=>e[u]));g.forEach(((t,n)=>t.push(e[n])))}b=E[h]??"null",x=w[0][1],$=f(g,b,x,i),T.push($)}if(null!=s&&w.length>1)for(E=Bl(e,s),h=0;h<E.length;h++)for(v=e.filter((e=>e[s]===E[h])),y=0;y<w.length;y++){if(g=l?v.map((e=>[e[w[y][0]],a?e[t].toString():e[t]])):v.map((e=>[a?e[t].toString():e[t],e[w[y][0]]])),d){let e=v.map((e=>e[d]));g.forEach(((t,n)=>t.push(e[n])))}if(u){let e=v.map((e=>e[u]));g.forEach(((t,n)=>t.push(e[n])))}b=(E[h]??"null")+" - "+r[w[y][0]].title,x=w[y][1],$=f(g,b,x,i),T.push($)}if(null==s&&w.length>1)for(h=0;h<w.length;h++){if(g=l?e.map((e=>[e[w[h][0]],a?e[t].toString():e[t]])):e.map((e=>[a?e[t].toString():e[t],e[w[h][0]]])),d){let t=e.map((e=>e[d]));g.forEach(((e,n)=>e.push(t[n])))}if(u){let t=e.map((e=>e[u]));g.forEach(((e,n)=>e.push(t[n])))}b=r[w[h][0]].title,x=w[h][1],$=f(g,b,x,i),T.push($)}if(null==s&&1===w.length){if(g=l?e.map((e=>[e[w[0][0]],a?e[t].toString():e[t]])):e.map((e=>[a?e[t].toString():e[t],e[w[0][0]]])),d){let t=e.map((e=>e[d]));g.forEach(((e,n)=>e.push(t[n])))}if(u){let t=e.map((e=>e[u]));g.forEach(((e,n)=>e.push(t[n])))}b=r[w[0][0]].title,x=w[0][1],$=f(g,b,x,i),T.push($)}return c&&T.sort(((e,t)=>c.indexOf(e.name)-c.indexOf(t.name))),m&&T.forEach((e=>{e.name=Eo(e.name,m)})),T}function Kc(e){let t=[];for(let n=1;n<e.length;n++)t.push(e[n]-e[n-1]);return t}function yr(e,t){return("number"!=typeof e||isNaN(e))&&(e=0),("number"!=typeof t||isNaN(t))&&(t=0),e=Math.abs(e),(t=Math.abs(t))<=.01?e:yr(t,e%t)}function Qc(e,t){if(!Array.isArray(e))throw new TypeError("Cannot calculate extent of non-array value.");let n,s;for(const t of e)"number"==typeof t&&(void 0===n?t>=t&&(n=s=t):(n>t&&(n=t),s<t&&(s=t)));return[n,s]}function Zc(e,t){let[n,s]=Qc(e);const l=[];let i=n;for(;i<=s;)l.push(Math.round(1e8*(i+Number.EPSILON))/1e8),i+=t;return l}function Jc(e){if(e.length<=1)return;e.sort((function(e,t){return e-t}));let t=(e=Kc(e=e.map((function(e){return 1e8*e})))).reduce(((e,t)=>yr(e,t)))/1e8;return t=Math.round(1e8*(t+Number.EPSILON))/1e8,t}function Gi(e,t,n,s,l=!1,i=!1){var o;let a=!1;const r=e.map((e=>Object.assign({},e,{[t]:e[t]instanceof Date?(a=!0,e[t].toISOString()):e[t]}))).filter((e=>void 0!==e[t]&&null!==e[t])),c=Array.from(r).reduce(((e,n)=>(n[t]instanceof Date&&(n[t]=n[t].toISOString(),a=!0),s?(e[n[s]??"null"]||(e[n[s]??"null"]=[]),e[n[s]??"null"].push(n)):(e.default||(e.default=[]),e.default.push(n)),e)),{}),d={};let u;const p=(null==(o=r.find((e=>e&&null!==e[t]&&void 0!==e[t])))?void 0:o[t])??null;switch(typeof p){case"object":throw null===p?new Error(`Column '${t}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(u=Bl(r,t),i){const e=Jc(u);d[t]=Zc(u,e)}break;case"string":u=Bl(r,t),d[t]=u}const m=[];for(const e of Object.values(c)){const i=s?{[s]:null}:{};if(l)if(n instanceof Array)for(let e=0;e<n.length;e++)i[n[e]]=0;else i[n]=0;else if(n instanceof Array)for(let e=0;e<n.length;e++)i[n[e]]=null;else i[n]=null;s&&(d[s]=s);const o=[];0===Object.keys(d).length?o.push(Zn([t],i)):o.push(Zn(d,i)),m.push(Ht(e,...o))}return a?m.flat().map((e=>({...e,[t]:new Date(e[t])}))):m.flat()}function Fs(e,t,n){let s=Ht(e,Wi(t,[vo(n,Xi)]));if("object"==typeof n)for(let e=0;e<s.length;e++){s[e].stackTotal=0;for(let t=0;t<n.length;t++)s[e].stackTotal=s[e].stackTotal+s[e][n[t]]}return s}let xc=60;function $c(e,t,n){let s,l,i,o,a,r,c,d,u,p,m,f,h,y,$,g,v,b,E,x,T,w,D,C=me,L=me,N=me,O=me;e.$$.on_destroy.push((()=>C())),e.$$.on_destroy.push((()=>L())),e.$$.on_destroy.push((()=>N())),e.$$.on_destroy.push((()=>O()));const{resolveColor:A}=Yt();let{y:I}=t;const S=!!I;let{y2:k}=t;const P=!!k;let{series:V}=t;const M=!!V;let R,{options:q}=t,{name:j}=t,{type:z="stacked"}=t,{stackName:_}=t,{fillColor:F}=t,{fillOpacity:U}=t,{outlineColor:G}=t,{outlineWidth:B}=t,{labels:H=!1}=t,{seriesLabels:Q=!0}=t,{labelSize:X=11}=t,{labelPosition:W}=t,{labelColor:Y}=t,{labelFmt:K}=t;K&&(R=It(K));let J,{yLabelFmt:Z}=t;Z&&(J=It(Z));let ee,{y2LabelFmt:te}=t;te&&(ee=It(te));let ne,se,le,ie,{y2SeriesType:oe="bar"}=t,{stackTotalLabel:ae=!0}=t,{showAllLabels:re=!1}=t,{seriesOrder:ce}=t;const de={outside:"top",inside:"inside"},ue={outside:"right",inside:"inside"};let{seriesLabelFmt:pe}=t;return Or((()=>{q&&l.update((e=>({...e,...q}))),E&&l.update((e=>{if(z.includes("stacked")?e.tooltip={...e.tooltip,order:"seriesDesc"}:e.tooltip={...e.tooltip,order:"seriesAsc"},"stacked100"===z&&(f?e.xAxis={...e.xAxis,max:1}:e.yAxis[0]={...e.yAxis[0],max:1}),f)e.yAxis={...e.yAxis,...E.xAxis},e.xAxis={...e.xAxis,...E.yAxis};else if(e.yAxis[0]={...e.yAxis[0],...E.yAxis},e.xAxis={...e.xAxis,...E.xAxis},k&&(e.yAxis[1]={...e.yAxis[1],show:!0},["line","bar","scatter"].includes(oe)))for(let t=0;t<m;t++)e.series[p+t].type=oe,e.series[p+t].stack=void 0;return e}))})),e.$$set=e=>{"y"in e&&n(4,I=e.y),"y2"in e&&n(5,k=e.y2),"series"in e&&n(6,V=e.series),"options"in e&&n(13,q=e.options),"name"in e&&n(7,j=e.name),"type"in e&&n(14,z=e.type),"stackName"in e&&n(8,_=e.stackName),"fillColor"in e&&n(15,F=e.fillColor),"fillOpacity"in e&&n(16,U=e.fillOpacity),"outlineColor"in e&&n(17,G=e.outlineColor),"outlineWidth"in e&&n(18,B=e.outlineWidth),"labels"in e&&n(9,H=e.labels),"seriesLabels"in e&&n(10,Q=e.seriesLabels),"labelSize"in e&&n(19,X=e.labelSize),"labelPosition"in e&&n(11,W=e.labelPosition),"labelColor"in e&&n(20,Y=e.labelColor),"labelFmt"in e&&n(21,K=e.labelFmt),"yLabelFmt"in e&&n(22,Z=e.yLabelFmt),"y2LabelFmt"in e&&n(23,te=e.y2LabelFmt),"y2SeriesType"in e&&n(24,oe=e.y2SeriesType),"stackTotalLabel"in e&&n(12,ae=e.stackTotalLabel),"showAllLabels"in e&&n(25,re=e.showAllLabels),"seriesOrder"in e&&n(26,ce=e.seriesOrder),"seriesLabelFmt"in e&&n(27,pe=e.seriesLabelFmt)},e.$$.update=()=>{32768&e.$$.dirty[0]&&(n(2,i=A(F)),L(),L=gt(i,(e=>n(50,T=e)))),131072&e.$$.dirty[0]&&(n(1,o=A(G)),C(),C=gt(o,(e=>n(49,x=e)))),512&e.$$.dirty[0]&&n(9,H="true"===H||!0===H),1024&e.$$.dirty[0]&&n(10,Q="true"===Q||!0===Q),1048576&e.$$.dirty[0]&&(n(0,a=A(Y)),N(),N=gt(a,(e=>n(51,w=e)))),4096&e.$$.dirty[0]&&n(12,ae="true"===ae||!0===ae),2097152&e.$$.dirty[1]&&n(46,r=D.data),2097152&e.$$.dirty[1]&&n(42,c=D.x),16&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&n(4,I=S?I:D.y),32&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&n(5,k=P?k:D.y2),2097152&e.$$.dirty[1]&&n(40,d=D.yFormat),2097152&e.$$.dirty[1]&&n(47,u=D.y2Format),2097152&e.$$.dirty[1]&&n(35,p=D.yCount),2097152&e.$$.dirty[1]&&n(36,m=D.y2Count),2097152&e.$$.dirty[1]&&n(37,f=D.swapXY),2097152&e.$$.dirty[1]&&n(39,h=D.xType),2097152&e.$$.dirty[1]&&n(43,y=D.xMismatch),2097152&e.$$.dirty[1]&&n(44,$=D.columnSummary),2097152&e.$$.dirty[1]&&n(48,g=D.sort),64&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&n(6,V=M?V:D.series),16848&e.$$.dirty[0]|174403&e.$$.dirty[1]&&(V||"object"==typeof I?(!0===g&&"category"===h&&(n(31,ne=Fs(r,c,I)),n(31,ne=Dl(ne,"object"==typeof I?"stackTotal":I,!1)),n(32,se=ne.map((e=>e[c]))),n(46,r=[...r].sort((function(e,t){return se.indexOf(e[c])-se.indexOf(t[c])})))),f||("value"===h||"category"===h)&&z.includes("stacked")?(n(46,r=Gi(r,c,I,V,!0,"value"===h)),n(39,h="category")):"time"===h&&z.includes("stacked")&&n(46,r=Gi(r,c,I,V,!0,!0)),z.includes("stacked")?(n(8,_=_??"stack1"),n(33,le="inside")):(n(8,_=void 0),n(33,le=f?"right":"top"))):(n(7,j=j??vt(I,$[I].title)),f&&"category"!==h&&(n(46,r=Gi(r,c,I,V,!0,"time"!==h)),n(39,h="category")),n(8,_="stack1"),n(33,le=f?"right":"top"))),16400&e.$$.dirty[0]|34816&e.$$.dirty[1]&&"stacked"===z&&n(34,ie=Fs(r,c,I)),2048&e.$$.dirty[0]|68&e.$$.dirty[1]&&n(11,W=(f?ue[W]:de[W])??le),1913458432&e.$$.dirty[0]|1901168&e.$$.dirty[1]&&n(45,v={type:"bar",stack:_,label:{show:H&&Q,formatter:e=>0===e.value[f?0:1]?"":ct(e.value[f?0:1],[J??R??d,ee??R??u][br(e.componentIndex,p,m)]),position:W,fontSize:X,color:w},labelLayout:{hideOverlap:!re},emphasis:{focus:"series"},barMaxWidth:xc,itemStyle:{color:T,opacity:U,borderColor:x,borderWidth:B}}),201326832&e.$$.dirty[0]|63552&e.$$.dirty[1]&&n(41,b=Yc(r,c,I,V,f,v,j,y,$,ce,void 0,void 0,k,pe)),268981072&e.$$.dirty[0]|7880&e.$$.dirty[1]&&l.update((e=>(e.series.push(...b),e.legend.data.push(...b.map((e=>e.name.toString()))),!0===H&&"stacked"===z&&"object"==typeof I|void 0!==V&&!0===ae&&V!==c&&(e.series.push({type:"bar",stack:_,name:"stackTotal",color:"none",data:ie.map((e=>[f?0:y?e[c].toString():e[c],f?y?e[c].toString():e[c]:0])),label:{show:!0,position:f?"right":"top",formatter(e){let t=0;return b.forEach((n=>{t+=n.data[e.dataIndex][f?0:1]})),0===t?"":ct(t,R??d)},fontWeight:"bold",fontSize:X,padding:f?[0,0,0,5]:void 0}}),e.legend.selectedMode=!1),e))),256&e.$$.dirty[1]&&(E={xAxis:{boundaryGap:["1%","2%"],type:h}})},n(3,s=Ml(lr)),O(),O=gt(s,(e=>n(52,D=e))),n(38,l=Ml(ir)),[a,o,i,s,I,k,V,j,_,H,Q,W,ae,q,z,F,U,G,B,X,Y,K,Z,te,oe,re,ce,pe,R,J,ee,ne,se,le,ie,p,m,f,l,h,d,b,c,y,$,v,r,u,g,x,T,w,D]}class ed extends ve{constructor(e){super(),Te(this,e,$c,null,ke,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function td(e){let t,n,s;t=new ed({props:{type:e[38],fillColor:e[72],fillOpacity:e[39],outlineColor:e[71],outlineWidth:e[40],labels:e[43],labelSize:e[44],labelPosition:e[45],labelColor:e[69],labelFmt:e[46],yLabelFmt:e[47],y2LabelFmt:e[48],stackTotalLabel:e[49],seriesLabels:e[50],showAllLabels:e[51],y2SeriesType:e[9],seriesOrder:e[60],seriesLabelFmt:e[62]}});const l=e[81].default,i=we(l,e,e[82],null);return{c(){ne(t.$$.fragment),n=j(),i&&i.c()},l(e){ie(t.$$.fragment,e),n=q(e),i&&i.l(e)},m(e,l){le(t,e,l),E(e,n,l),i&&i.m(e,l),s=!0},p(e,n){const o={};128&n[1]&&(o.type=e[38]),1024&n[2]&&(o.fillColor=e[72]),256&n[1]&&(o.fillOpacity=e[39]),512&n[2]&&(o.outlineColor=e[71]),512&n[1]&&(o.outlineWidth=e[40]),4096&n[1]&&(o.labels=e[43]),8192&n[1]&&(o.labelSize=e[44]),16384&n[1]&&(o.labelPosition=e[45]),128&n[2]&&(o.labelColor=e[69]),32768&n[1]&&(o.labelFmt=e[46]),65536&n[1]&&(o.yLabelFmt=e[47]),131072&n[1]&&(o.y2LabelFmt=e[48]),262144&n[1]&&(o.stackTotalLabel=e[49]),524288&n[1]&&(o.seriesLabels=e[50]),1048576&n[1]&&(o.showAllLabels=e[51]),512&n[0]&&(o.y2SeriesType=e[9]),536870912&n[1]&&(o.seriesOrder=e[60]),1&n[2]&&(o.seriesLabelFmt=e[62]),t.$set(o),i&&i.p&&(!s||1048576&n[2])&&Le(i,l,e,e[82],s?De(l,e[82],n,null):Ie(e[82]),null)},i(e){s||(y(t.$$.fragment,e),y(i,e),s=!0)},o(e){v(t.$$.fragment,e),v(i,e),s=!1},d(e){e&&m(n),te(t,e),i&&i.d(e)}}}function ld(e){let t,n;return t=new Xc({props:{data:e[1],x:e[2],y:e[3],y2:e[4],xFmt:e[12],yFmt:e[10],y2Fmt:e[11],series:e[5],xType:e[6],yLog:e[7],yLogBase:e[8],legend:e[15],xAxisTitle:e[16],yAxisTitle:e[17],y2AxisTitle:e[18],xGridlines:e[19],yGridlines:e[20],y2Gridlines:e[21],xAxisLabels:e[22],yAxisLabels:e[23],y2AxisLabels:e[24],xBaseline:e[25],yBaseline:e[26],y2Baseline:e[27],xTickMarks:e[28],yTickMarks:e[29],y2TickMarks:e[30],yAxisColor:e[68],y2AxisColor:e[67],yMin:e[31],yMax:e[32],yScale:e[33],y2Min:e[34],y2Max:e[35],y2Scale:e[36],swapXY:e[0],title:e[13],subtitle:e[14],chartType:"Bar Chart",stackType:e[38],sort:e[42],stacked100:e[73],chartAreaHeight:e[41],showAllXAxisLabels:e[37],colorPalette:e[70],echartsOptions:e[52],seriesOptions:e[53],printEchartsConfig:e[54],emptySet:e[55],emptyMessage:e[56],renderer:e[57],downloadableData:e[58],downloadableImage:e[59],connectGroup:e[61],xLabelWrap:e[65],seriesColors:e[66],leftPadding:e[63],rightPadding:e[64],$$slots:{default:[td]},$$scope:{ctx:e}}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};2&n[0]&&(s.data=e[1]),4&n[0]&&(s.x=e[2]),8&n[0]&&(s.y=e[3]),16&n[0]&&(s.y2=e[4]),4096&n[0]&&(s.xFmt=e[12]),1024&n[0]&&(s.yFmt=e[10]),2048&n[0]&&(s.y2Fmt=e[11]),32&n[0]&&(s.series=e[5]),64&n[0]&&(s.xType=e[6]),128&n[0]&&(s.yLog=e[7]),256&n[0]&&(s.yLogBase=e[8]),32768&n[0]&&(s.legend=e[15]),65536&n[0]&&(s.xAxisTitle=e[16]),131072&n[0]&&(s.yAxisTitle=e[17]),262144&n[0]&&(s.y2AxisTitle=e[18]),524288&n[0]&&(s.xGridlines=e[19]),1048576&n[0]&&(s.yGridlines=e[20]),2097152&n[0]&&(s.y2Gridlines=e[21]),4194304&n[0]&&(s.xAxisLabels=e[22]),8388608&n[0]&&(s.yAxisLabels=e[23]),16777216&n[0]&&(s.y2AxisLabels=e[24]),33554432&n[0]&&(s.xBaseline=e[25]),67108864&n[0]&&(s.yBaseline=e[26]),134217728&n[0]&&(s.y2Baseline=e[27]),268435456&n[0]&&(s.xTickMarks=e[28]),536870912&n[0]&&(s.yTickMarks=e[29]),1073741824&n[0]&&(s.y2TickMarks=e[30]),64&n[2]&&(s.yAxisColor=e[68]),32&n[2]&&(s.y2AxisColor=e[67]),1&n[1]&&(s.yMin=e[31]),2&n[1]&&(s.yMax=e[32]),4&n[1]&&(s.yScale=e[33]),8&n[1]&&(s.y2Min=e[34]),16&n[1]&&(s.y2Max=e[35]),32&n[1]&&(s.y2Scale=e[36]),1&n[0]&&(s.swapXY=e[0]),8192&n[0]&&(s.title=e[13]),16384&n[0]&&(s.subtitle=e[14]),128&n[1]&&(s.stackType=e[38]),2048&n[1]&&(s.sort=e[42]),1024&n[1]&&(s.chartAreaHeight=e[41]),64&n[1]&&(s.showAllXAxisLabels=e[37]),256&n[2]&&(s.colorPalette=e[70]),2097152&n[1]&&(s.echartsOptions=e[52]),4194304&n[1]&&(s.seriesOptions=e[53]),8388608&n[1]&&(s.printEchartsConfig=e[54]),16777216&n[1]&&(s.emptySet=e[55]),33554432&n[1]&&(s.emptyMessage=e[56]),67108864&n[1]&&(s.renderer=e[57]),134217728&n[1]&&(s.downloadableData=e[58]),268435456&n[1]&&(s.downloadableImage=e[59]),1073741824&n[1]&&(s.connectGroup=e[61]),8&n[2]&&(s.xLabelWrap=e[65]),16&n[2]&&(s.seriesColors=e[66]),2&n[2]&&(s.leftPadding=e[63]),4&n[2]&&(s.rightPadding=e[64]),512&n[0]|538964864&n[1]|1050241&n[2]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function id(e,t,n){let s,l,i,o,a,r,c,{$$slots:d={},$$scope:u}=t;const{resolveColor:p,resolveColorsObject:m,resolveColorPalette:f}=Yt();let{data:h}=t,{x:y}=t,{y:$}=t,{y2:g}=t,{series:v}=t,{xType:b}=t,{yLog:E}=t,{yLogBase:x}=t,{y2SeriesType:T}=t,{yFmt:w}=t,{y2Fmt:D}=t,{xFmt:C}=t,{title:L}=t,{subtitle:N}=t,{legend:O}=t,{xAxisTitle:A}=t,{yAxisTitle:I=(g?"true":void 0)}=t,{y2AxisTitle:S=(g?"true":void 0)}=t,{xGridlines:k}=t,{yGridlines:P}=t,{y2Gridlines:V}=t,{xAxisLabels:M}=t,{yAxisLabels:R}=t,{y2AxisLabels:q}=t,{xBaseline:j}=t,{yBaseline:z}=t,{y2Baseline:_}=t,{xTickMarks:F}=t,{yTickMarks:U}=t,{y2TickMarks:G}=t,{yMin:B}=t,{yMax:H}=t,{yScale:Q}=t,{y2Min:X}=t,{y2Max:W}=t,{y2Scale:Y}=t,{swapXY:K=!1}=t,{showAllXAxisLabels:J}=t,{type:Z="stacked"}=t,ee="stacked100"===Z,{fillColor:te}=t,{fillOpacity:ne}=t,{outlineColor:se}=t,{outlineWidth:le}=t,{chartAreaHeight:ie}=t,{sort:oe}=t,{colorPalette:ae="default"}=t,{labels:re}=t,{labelSize:ce}=t,{labelPosition:de}=t,{labelColor:ue}=t,{labelFmt:pe}=t,{yLabelFmt:me}=t,{y2LabelFmt:fe}=t,{stackTotalLabel:he}=t,{seriesLabels:ye}=t,{showAllLabels:$e}=t,{yAxisColor:ge}=t,{y2AxisColor:ve}=t,{echartsOptions:be}=t,{seriesOptions:Ee}=t,{printEchartsConfig:xe=!1}=t,{emptySet:Te}=t,{emptyMessage:we}=t,{renderer:De}=t,{downloadableData:Ce}=t,{downloadableImage:Le}=t,{seriesColors:Ne}=t,{seriesOrder:Oe}=t,{connectGroup:Ae}=t,{seriesLabelFmt:Ie}=t,{leftPadding:Se}=t,{rightPadding:ke}=t,{xLabelWrap:Pe}=t;return e.$$set=e=>{"data"in e&&n(1,h=e.data),"x"in e&&n(2,y=e.x),"y"in e&&n(3,$=e.y),"y2"in e&&n(4,g=e.y2),"series"in e&&n(5,v=e.series),"xType"in e&&n(6,b=e.xType),"yLog"in e&&n(7,E=e.yLog),"yLogBase"in e&&n(8,x=e.yLogBase),"y2SeriesType"in e&&n(9,T=e.y2SeriesType),"yFmt"in e&&n(10,w=e.yFmt),"y2Fmt"in e&&n(11,D=e.y2Fmt),"xFmt"in e&&n(12,C=e.xFmt),"title"in e&&n(13,L=e.title),"subtitle"in e&&n(14,N=e.subtitle),"legend"in e&&n(15,O=e.legend),"xAxisTitle"in e&&n(16,A=e.xAxisTitle),"yAxisTitle"in e&&n(17,I=e.yAxisTitle),"y2AxisTitle"in e&&n(18,S=e.y2AxisTitle),"xGridlines"in e&&n(19,k=e.xGridlines),"yGridlines"in e&&n(20,P=e.yGridlines),"y2Gridlines"in e&&n(21,V=e.y2Gridlines),"xAxisLabels"in e&&n(22,M=e.xAxisLabels),"yAxisLabels"in e&&n(23,R=e.yAxisLabels),"y2AxisLabels"in e&&n(24,q=e.y2AxisLabels),"xBaseline"in e&&n(25,j=e.xBaseline),"yBaseline"in e&&n(26,z=e.yBaseline),"y2Baseline"in e&&n(27,_=e.y2Baseline),"xTickMarks"in e&&n(28,F=e.xTickMarks),"yTickMarks"in e&&n(29,U=e.yTickMarks),"y2TickMarks"in e&&n(30,G=e.y2TickMarks),"yMin"in e&&n(31,B=e.yMin),"yMax"in e&&n(32,H=e.yMax),"yScale"in e&&n(33,Q=e.yScale),"y2Min"in e&&n(34,X=e.y2Min),"y2Max"in e&&n(35,W=e.y2Max),"y2Scale"in e&&n(36,Y=e.y2Scale),"swapXY"in e&&n(0,K=e.swapXY),"showAllXAxisLabels"in e&&n(37,J=e.showAllXAxisLabels),"type"in e&&n(38,Z=e.type),"fillColor"in e&&n(74,te=e.fillColor),"fillOpacity"in e&&n(39,ne=e.fillOpacity),"outlineColor"in e&&n(75,se=e.outlineColor),"outlineWidth"in e&&n(40,le=e.outlineWidth),"chartAreaHeight"in e&&n(41,ie=e.chartAreaHeight),"sort"in e&&n(42,oe=e.sort),"colorPalette"in e&&n(76,ae=e.colorPalette),"labels"in e&&n(43,re=e.labels),"labelSize"in e&&n(44,ce=e.labelSize),"labelPosition"in e&&n(45,de=e.labelPosition),"labelColor"in e&&n(77,ue=e.labelColor),"labelFmt"in e&&n(46,pe=e.labelFmt),"yLabelFmt"in e&&n(47,me=e.yLabelFmt),"y2LabelFmt"in e&&n(48,fe=e.y2LabelFmt),"stackTotalLabel"in e&&n(49,he=e.stackTotalLabel),"seriesLabels"in e&&n(50,ye=e.seriesLabels),"showAllLabels"in e&&n(51,$e=e.showAllLabels),"yAxisColor"in e&&n(78,ge=e.yAxisColor),"y2AxisColor"in e&&n(79,ve=e.y2AxisColor),"echartsOptions"in e&&n(52,be=e.echartsOptions),"seriesOptions"in e&&n(53,Ee=e.seriesOptions),"printEchartsConfig"in e&&n(54,xe=e.printEchartsConfig),"emptySet"in e&&n(55,Te=e.emptySet),"emptyMessage"in e&&n(56,we=e.emptyMessage),"renderer"in e&&n(57,De=e.renderer),"downloadableData"in e&&n(58,Ce=e.downloadableData),"downloadableImage"in e&&n(59,Le=e.downloadableImage),"seriesColors"in e&&n(80,Ne=e.seriesColors),"seriesOrder"in e&&n(60,Oe=e.seriesOrder),"connectGroup"in e&&n(61,Ae=e.connectGroup),"seriesLabelFmt"in e&&n(62,Ie=e.seriesLabelFmt),"leftPadding"in e&&n(63,Se=e.leftPadding),"rightPadding"in e&&n(64,ke=e.rightPadding),"xLabelWrap"in e&&n(65,Pe=e.xLabelWrap),"$$scope"in e&&n(82,u=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty[0]&&n(0,K="true"===K||!0===K),4096&e.$$.dirty[2]&&n(72,s=p(te)),8192&e.$$.dirty[2]&&n(71,l=p(se)),16384&e.$$.dirty[2]&&n(70,i=f(ae)),32768&e.$$.dirty[2]&&n(69,o=p(ue)),65536&e.$$.dirty[2]&&n(68,a=p(ge)),131072&e.$$.dirty[2]&&n(67,r=p(ve)),262144&e.$$.dirty[2]&&n(66,c=m(Ne))},[K,h,y,$,g,v,b,E,x,T,w,D,C,L,N,O,A,I,S,k,P,V,M,R,q,j,z,_,F,U,G,B,H,Q,X,W,Y,J,Z,ne,le,ie,oe,re,ce,de,pe,me,fe,he,ye,$e,be,Ee,xe,Te,we,De,Ce,Le,Oe,Ae,Ie,Se,ke,Pe,c,r,a,o,i,l,s,ee,te,se,ae,ue,ge,ve,Ne,d,u]}class nd extends ve{constructor(e){super(),Te(this,e,id,ld,ke,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}const{document:Dt}=sr;function Bs(e,t,n){const s=e.slice();return s[30]=t[n],s}function sd(e){let t,n,s=Ue.title+"";return{c(){t=N("h1"),n=de(s),this.h()},l(e){t=D(e,"H1",{class:!0});var l=V(t);n=ce(l,s),l.forEach(m),this.h()},h(){T(t,"class","title")},m(e,s){E(e,t,s),P(t,n)},p:me,d(e){e&&m(t)}}}function rd(e){return{c(){this.h()},l(e){this.h()},h(){Dt.title="Evidence"},m:me,p:me,d:me}}function od(e){let t,n,s,l,i;return Dt.title=t=Ue.title,{c(){n=j(),s=N("meta"),l=j(),i=N("meta"),this.h()},l(e){n=q(e),s=D(e,"META",{property:!0,content:!0}),l=q(e),i=D(e,"META",{name:!0,content:!0}),this.h()},h(){var e,t;T(s,"property","og:title"),T(s,"content",(null==(e=Ue.og)?void 0:e.title)??Ue.title),T(i,"name","twitter:title"),T(i,"content",(null==(t=Ue.og)?void 0:t.title)??Ue.title)},m(e,t){E(e,n,t),E(e,s,t),E(e,l,t),E(e,i,t)},p(e,n){0&n&&t!==(t=Ue.title)&&(Dt.title=t)},d(e){e&&(m(n),m(s),m(l),m(i))}}}function ad(e){var t,n;let s,l,i=(Ue.description||(null==(t=Ue.og)?void 0:t.description))&&fd(),o=(null==(n=Ue.og)?void 0:n.image)&&ud();return{c(){i&&i.c(),s=j(),o&&o.c(),l=fe()},l(e){i&&i.l(e),s=q(e),o&&o.l(e),l=fe()},m(e,t){i&&i.m(e,t),E(e,s,t),o&&o.m(e,t),E(e,l,t)},p(e,t){var n,s;(Ue.description||null!=(n=Ue.og)&&n.description)&&i.p(e,t),null!=(s=Ue.og)&&s.image&&o.p(e,t)},d(e){e&&(m(s),m(l)),i&&i.d(e),o&&o.d(e)}}}function fd(e){let t,n,s,l,i;return{c(){t=N("meta"),n=j(),s=N("meta"),l=j(),i=N("meta"),this.h()},l(e){t=D(e,"META",{name:!0,content:!0}),n=q(e),s=D(e,"META",{property:!0,content:!0}),l=q(e),i=D(e,"META",{name:!0,content:!0}),this.h()},h(){var e,n,l;T(t,"name","description"),T(t,"content",Ue.description??(null==(e=Ue.og)?void 0:e.description)),T(s,"property","og:description"),T(s,"content",(null==(n=Ue.og)?void 0:n.description)??Ue.description),T(i,"name","twitter:description"),T(i,"content",(null==(l=Ue.og)?void 0:l.description)??Ue.description)},m(e,o){E(e,t,o),E(e,n,o),E(e,s,o),E(e,l,o),E(e,i,o)},p:me,d(e){e&&(m(t),m(n),m(s),m(l),m(i))}}}function ud(e){let t,n,s;return{c(){t=N("meta"),n=j(),s=N("meta"),this.h()},l(e){t=D(e,"META",{property:!0,content:!0}),n=q(e),s=D(e,"META",{name:!0,content:!0}),this.h()},h(){var e,n;T(t,"property","og:image"),T(t,"content",Cn(null==(e=Ue.og)?void 0:e.image)),T(s,"name","twitter:image"),T(s,"content",Cn(null==(n=Ue.og)?void 0:n.image))},m(e,l){E(e,t,l),E(e,n,l),E(e,s,l)},p:me,d(e){e&&(m(t),m(n),m(s))}}}function cd(e){let t,n='<h3 class="svelte-16omzez">🛠️ Development Mode</h3> <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p>';return{c(){t=N("div"),t.innerHTML=n,this.h()},l(e){t=D(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1b532x0"!==We(t)&&(t.innerHTML=n),this.h()},h(){T(t,"class","dev-banner svelte-16omzez")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function dd(e){let t,n='<h3 class="svelte-16omzez">🚀 Running in Domo DDX Environment</h3> <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p>';return{c(){t=N("div"),t.innerHTML=n,this.h()},l(e){t=D(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1uzwljv"!==We(t)&&(t.innerHTML=n),this.h()},h(){T(t,"class","domo-banner svelte-16omzez")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function md(e){let t,n,s,l="📈 View Analysis";return{c(){t=N("button"),t.textContent=l,this.h()},l(e){t=D(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-1myai4g"!==We(t)&&(t.textContent=l),this.h()},h(){T(t,"class","btn-secondary svelte-16omzez")},m(l,i){E(l,t,i),n||(s=qe(t,"click",e[8]),n=!0)},p:me,d(e){e&&m(t),n=!1,s()}}}function Us(e){let t,n,s,l,i,o,a,r,c,d,u,p,f,h,y,$,g,v,b,x,w,C,L,O,A,I,S,k,M,R,z="📊 Load Domo Dataset",_="Select and load Domo datasets into DuckDB for analysis",F="Select Dataset:",U="Choose a dataset...",G='<h4>Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info svelte-16omzez"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div>',B="Loading Configuration",H='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/>',X="Refresh Mode:",W="Replace existing data",Y="Append to existing data",K='<button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button>',J='<div class="loading-spinner svelte-16omzez"></div> <p class="svelte-16omzez">Loading...</p>';return{c(){t=N("div"),n=N("h3"),n.textContent=z,s=j(),l=N("p"),l.textContent=_,i=j(),o=N("div"),a=N("div"),r=N("label"),r.textContent=F,c=j(),d=N("select"),u=N("option"),u.textContent=U,p=j(),f=N("div"),f.innerHTML=G,h=j(),y=N("div"),$=N("h4"),$.textContent=B,g=j(),v=N("div"),b=N("div"),b.innerHTML=H,x=j(),w=N("div"),C=N("label"),C.textContent=X,L=j(),O=N("select"),A=N("option"),A.textContent=W,I=N("option"),I.textContent=Y,S=j(),k=N("div"),k.innerHTML=K,M=j(),R=N("div"),R.innerHTML=J,this.h()},l(e){t=D(e,"DIV",{class:!0});var E=V(t);n=D(E,"H3",{class:!0,"data-svelte-h":!0}),"svelte-19dew8q"!==We(n)&&(n.textContent=z),s=q(E),l=D(E,"P",{class:!0,"data-svelte-h":!0}),"svelte-s0e84h"!==We(l)&&(l.textContent=_),i=q(E),o=D(E,"DIV",{id:!0,class:!0});var T=V(o);a=D(T,"DIV",{class:!0});var N=V(a);r=D(N,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-1fci9ty"!==We(r)&&(r.textContent=F),c=q(N),d=D(N,"SELECT",{id:!0,class:!0});var P=V(d);u=D(P,"OPTION",{"data-svelte-h":!0}),"svelte-59d9xk"!==We(u)&&(u.textContent=U),P.forEach(m),N.forEach(m),p=q(T),f=D(T,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-4n19su"!==We(f)&&(f.innerHTML=G),h=q(T),y=D(T,"DIV",{id:!0,class:!0,style:!0});var j=V(y);$=D(j,"H4",{"data-svelte-h":!0}),"svelte-1foy07w"!==We($)&&($.textContent=B),g=q(j),v=D(j,"DIV",{class:!0});var Q=V(v);b=D(Q,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-shu5b1"!==We(b)&&(b.innerHTML=H),x=q(Q),w=D(Q,"DIV",{class:!0});var Z=V(w);C=D(Z,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-p1qydn"!==We(C)&&(C.textContent=X),L=q(Z),O=D(Z,"SELECT",{id:!0,class:!0});var ee=V(O);A=D(ee,"OPTION",{"data-svelte-h":!0}),"svelte-qvzdub"!==We(A)&&(A.textContent=W),I=D(ee,"OPTION",{"data-svelte-h":!0}),"svelte-idsvi6"!==We(I)&&(I.textContent=Y),ee.forEach(m),Z.forEach(m),Q.forEach(m),j.forEach(m),S=q(T),k=D(T,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-1hy0ddx"!==We(k)&&(k.innerHTML=K),M=q(T),R=D(T,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-nqwiq1"!==We(R)&&(R.innerHTML=J),T.forEach(m),E.forEach(m),this.h()},h(){T(n,"class","svelte-16omzez"),T(l,"class","svelte-16omzez"),T(r,"for","dataset-selector"),T(r,"class","svelte-16omzez"),u.__value="",qt(u,u.__value),T(d,"id","dataset-selector"),T(d,"class","dataset-dropdown svelte-16omzez"),T(a,"class","workflow-step svelte-16omzez"),T(f,"id","dataset-preview"),T(f,"class","dataset-preview svelte-16omzez"),Q(f,"display","none"),T(b,"class","config-item"),T(C,"for","refresh-mode"),T(C,"class","svelte-16omzez"),A.__value="replace",qt(A,A.__value),I.__value="append",qt(I,I.__value),T(O,"id","refresh-mode"),T(O,"class","svelte-16omzez"),T(w,"class","config-item"),T(v,"class","config-grid svelte-16omzez"),T(y,"id","loading-config"),T(y,"class","workflow-step svelte-16omzez"),Q(y,"display","none"),T(k,"id","workflow-actions"),T(k,"class","workflow-actions svelte-16omzez"),Q(k,"display","none"),T(R,"id","loading-status"),T(R,"class","loading-status svelte-16omzez"),Q(R,"display","none"),T(o,"id","domo-workflow-picker"),T(o,"class","workflow-picker svelte-16omzez"),T(t,"class","workflow-picker-section svelte-16omzez")},m(e,m){E(e,t,m),P(t,n),P(t,s),P(t,l),P(t,i),P(t,o),P(o,a),P(a,r),P(a,c),P(a,d),P(d,u),P(o,p),P(o,f),P(o,h),P(o,y),P(y,$),P(y,g),P(y,v),P(v,b),P(v,x),P(v,w),P(w,C),P(w,L),P(w,O),P(O,A),P(O,I),P(o,S),P(o,k),P(o,M),P(o,R)},d(e){e&&m(t)}}}function hd(e){let t;return{c(){t=de("This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.")},l(e){t=ce(e,"This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.")},m(e,n){E(e,t,n)},d(e){e&&m(t)}}}function Vs(e){let t,n;return t=new gr({props:{queryID:"categories",queryResult:e[1]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};2&n[0]&&(s.queryResult=e[1]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function _d(e){let t,n;return t=new hl({props:{value:"%",valueLabel:"All Categories"}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p:me,i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function gd(e){let t,n,s,l,i,o,a,r;return t=new hl({props:{value:"%",valueLabel:"All Years"}}),s=new hl({props:{value:"2019"}}),i=new hl({props:{value:"2020"}}),a=new hl({props:{value:"2021"}}),{c(){ne(t.$$.fragment),n=j(),ne(s.$$.fragment),l=j(),ne(i.$$.fragment),o=j(),ne(a.$$.fragment)},l(e){ie(t.$$.fragment,e),n=q(e),ie(s.$$.fragment,e),l=q(e),ie(i.$$.fragment,e),o=q(e),ie(a.$$.fragment,e)},m(e,c){le(t,e,c),E(e,n,c),le(s,e,c),E(e,l,c),le(i,e,c),E(e,o,c),le(a,e,c),r=!0},p:me,i(e){r||(y(t.$$.fragment,e),y(s.$$.fragment,e),y(i.$$.fragment,e),y(a.$$.fragment,e),r=!0)},o(e){v(t.$$.fragment,e),v(s.$$.fragment,e),v(i.$$.fragment,e),v(a.$$.fragment,e),r=!1},d(e){e&&(m(n),m(l),m(o)),te(t,e),te(s,e),te(i,e),te(a,e)}}}function Hs(e){let t,n;return t=new gr({props:{queryID:"orders_by_category",queryResult:e[2]}}),{c(){ne(t.$$.fragment)},l(e){ie(t.$$.fragment,e)},m(e,s){le(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.queryResult=e[2]),t.$set(s)},i(e){n||(y(t.$$.fragment,e),n=!0)},o(e){v(t.$$.fragment,e),n=!1},d(e){te(t,e)}}}function Gs(e){let t,n,s,l,i,o,a,r="📈 Data Analysis",c='<h3 class="svelte-16omzez">Example Queries</h3> <p>Here are some example queries you can run on your loaded data. Replace <code>your_table_name</code> with the actual table name from your loaded datasets.</p> <div class="query-example svelte-16omzez"><h4 class="svelte-16omzez">Basic Data Exploration</h4> <pre class="svelte-16omzez"><code class="svelte-16omzez">-- Get row count and basic stats\nSELECT\n  COUNT(*) as total_rows,\n  COUNT(DISTINCT column_name) as unique_values\nFROM your_table_name;</code></pre></div> <div class="query-example svelte-16omzez"><h4 class="svelte-16omzez">Time Series Analysis</h4> <pre class="svelte-16omzez"><code class="svelte-16omzez">-- Aggregate by date (if you have date columns)\nSELECT\n  DATE_TRUNC(&#39;month&#39;, date_column) as month,\n  SUM(numeric_column) as total\nFROM your_table_name\nGROUP BY month\nORDER BY month;</code></pre></div> <div class="query-example svelte-16omzez"><h4 class="svelte-16omzez">Category Analysis</h4> <pre class="svelte-16omzez"><code class="svelte-16omzez">-- Group by categorical columns\nSELECT\n  category_column,\n  COUNT(*) as count,\n  AVG(numeric_column) as average\nFROM your_table_name\nGROUP BY category_column\nORDER BY count DESC;</code></pre></div>';return l=new hr({props:{title:"Loaded Datasets",$$slots:{default:[kd]},$$scope:{ctx:e}}}),{c(){t=N("div"),n=N("h2"),n.textContent=r,s=j(),ne(l.$$.fragment),i=j(),o=N("div"),o.innerHTML=c,this.h()},l(e){t=D(e,"DIV",{id:!0,class:!0});var a=V(t);n=D(a,"H2",{class:!0,"data-svelte-h":!0}),"svelte-1vpr9t5"!==We(n)&&(n.textContent=r),s=q(a),ie(l.$$.fragment,a),i=q(a),o=D(a,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1pkrpz0"!==We(o)&&(o.innerHTML=c),a.forEach(m),this.h()},h(){T(n,"class","svelte-16omzez"),T(o,"class","analysis-examples svelte-16omzez"),T(t,"id","analysis-section"),T(t,"class","svelte-16omzez")},m(e,r){E(e,t,r),P(t,n),P(t,s),le(l,t,null),P(t,i),P(t,o),a=!0},p(e,t){const n={};4&t[1]&&(n.$$scope={dirty:t,ctx:e}),l.$set(n)},i(e){a||(y(l.$$.fragment,e),a=!0)},o(e){v(l.$$.fragment,e),a=!1},d(e){e&&m(t),te(l)}}}function bd(e){let t,n="No datasets loaded yet. Use the workflow picker above to load data.";return{c(){t=N("p"),t.textContent=n},l(e){t=D(e,"P",{"data-svelte-h":!0}),"svelte-12hk0ka"!==We(t)&&(t.textContent=n)},m(e,n){E(e,t,n)},p:me,d(e){e&&m(t)}}}function yd(e){let t,n,s,l,i,o,a=e[6].length+"",r=at(e[6]),c=[];for(let t=0;t<r.length;t+=1)c[t]=qs(Bs(e,r,t));return{c(){t=N("p"),n=de("You have loaded "),s=de(a),l=de(" dataset(s) for analysis:"),i=j(),o=N("ul");for(let e=0;e<c.length;e+=1)c[e].c();this.h()},l(e){t=D(e,"P",{});var r=V(t);n=ce(r,"You have loaded "),s=ce(r,a),l=ce(r," dataset(s) for analysis:"),r.forEach(m),i=q(e),o=D(e,"UL",{class:!0});var d=V(o);for(let e=0;e<c.length;e+=1)c[e].l(d);d.forEach(m),this.h()},h(){T(o,"class","svelte-16omzez")},m(e,a){E(e,t,a),P(t,n),P(t,s),P(t,l),E(e,i,a),E(e,o,a);for(let e=0;e<c.length;e+=1)c[e]&&c[e].m(o,null)},p(e,t){if(64&t[0]){let n;for(r=at(e[6]),n=0;n<r.length;n+=1){const s=Bs(e,r,n);c[n]?c[n].p(s,t):(c[n]=qs(s),c[n].c(),c[n].m(o,null))}for(;n<c.length;n+=1)c[n].d(1);c.length=r.length}},d(e){e&&(m(t),m(i),m(o)),jt(c,e)}}}function qs(e){let t,n,s,l,i,o,a,r,c=e[30].tableName+"",d=e[30].datasetName+"",u=e[30].rowCount.toLocaleString()+"";return{c(){t=N("li"),n=N("strong"),s=de(c),l=de(" - "),i=de(d),o=de(" ("),a=de(u),r=de(" rows)"),this.h()},l(e){t=D(e,"LI",{class:!0});var p=V(t);n=D(p,"STRONG",{});var f=V(n);s=ce(f,c),f.forEach(m),l=ce(p," - "),i=ce(p,d),o=ce(p," ("),a=ce(p,u),r=ce(p," rows)"),p.forEach(m),this.h()},h(){T(t,"class","svelte-16omzez")},m(e,c){E(e,t,c),P(t,n),P(n,s),P(t,l),P(t,i),P(t,o),P(t,a),P(t,r)},p:me,d(e){e&&m(t)}}}function kd(e){let t,n=(e[6].length>0?yd:bd)(e);return{c(){t=N("div"),n.c(),this.h()},l(e){t=D(e,"DIV",{class:!0});var s=V(t);n.l(s),s.forEach(m),this.h()},h(){T(t,"class","loaded-datasets-summary svelte-16omzez")},m(e,s){E(e,t,s),n.m(t,null)},p(e,t){n.p(e,t)},d(e){e&&m(t),n.d()}}}function Cd(e){let t,n,s,l,i,o,a,r,c,d,u,p,f,h,$,g,b,x,w,C,L,O,A,I,S,k,M,R,z,_,F,U,G,B,H,Q,X,W,Y,K,J='<a href="#evidence-dashboard-for-domo-ddx">Evidence Dashboard for Domo DDX</a>',Z="Welcome to your Evidence dashboard! This application combines the power of Evidence's analytics framework with Domo's data platform, allowing you to create interactive dashboards and reports using your Domo datasets.",ee='<a href="#domo-dataset-workflow--analysis">Domo Dataset Workflow &amp; Analysis</a>',se=e[4]?"📊 Hide":"📊 Load",oe='<a href="#sample-analysis">Sample Analysis</a>',ae='<a href="#whats-next">What&#39;s Next?</a>',re='<div class="step svelte-16omzez"><h4 class="svelte-16omzez">1. Load Your Data</h4> <p class="svelte-16omzez">Use the workflow picker above to select and load Domo datasets into DuckDB</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">2. Create Queries</h4> <p class="svelte-16omzez">Write SQL queries against your loaded data using Evidence&#39;s query blocks</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">3. Build Visualizations</h4> <p class="svelte-16omzez">Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p></div> <div class="step svelte-16omzez"><h4 class="svelte-16omzez">4. Deploy to Domo</h4> <p class="svelte-16omzez">Package your Evidence app and deploy it to Domo DDX for your team to use</p></div>',ue=typeof Ue<"u"&&Ue.title&&!0!==Ue.hide_title&&sd(),pe=(typeof Ue<"u"&&Ue.title?od:rd)(e),me="object"==typeof Ue&&ad();function he(e,t){return e[3]?dd:cd}let ye=he(e),$e=ye(e),ve=e[6].length>0&&md(e),Ee=e[4]&&Us();I=new hr({props:{title:"How Evidence Works with Your Data",$$slots:{default:[hd]},$$scope:{ctx:e}}});let xe=e[1]&&Vs(e);M=new _s({props:{data:e[1],name:"category",value:"category",$$slots:{default:[_d]},$$scope:{ctx:e}}}),z=new _s({props:{name:"year",$$slots:{default:[gd]},$$scope:{ctx:e}}});let Te=e[2]&&Hs(e);U=new nd({props:{data:e[2],title:"Sales by Month, "+e[0].category.label,x:"month",y:"sales_usd",series:"category"}});let we=e[5]&&Gs(e);return{c(){ue&&ue.c(),t=j(),pe.c(),n=N("meta"),s=N("meta"),me&&me.c(),l=fe(),i=j(),o=N("h1"),o.innerHTML=J,a=j(),$e.c(),r=j(),c=N("p"),c.textContent=Z,d=j(),u=N("h2"),u.innerHTML=ee,p=j(),f=N("div"),h=N("button"),$=de(se),g=de(" Domo Dataset"),b=j(),ve&&ve.c(),x=j(),Ee&&Ee.c(),w=j(),C=N("h2"),C.innerHTML=oe,L=j(),O=N("div"),A=j(),ne(I.$$.fragment),S=j(),xe&&xe.c(),k=j(),ne(M.$$.fragment),R=j(),ne(z.$$.fragment),_=j(),Te&&Te.c(),F=j(),ne(U.$$.fragment),G=j(),we&&we.c(),B=j(),H=N("h2"),H.innerHTML=ae,Q=j(),X=N("div"),X.innerHTML=re,this.h()},l(e){ue&&ue.l(e),t=q(e);const y=wr("svelte-2igo1p",Dt.head);pe.l(y),n=D(y,"META",{name:!0,content:!0}),s=D(y,"META",{name:!0,content:!0}),me&&me.l(y),l=fe(),y.forEach(m),i=q(e),o=D(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-dwxo2v"!==We(o)&&(o.innerHTML=J),a=q(e),$e.l(e),r=q(e),c=D(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-17kjwqn"!==We(c)&&(c.textContent=Z),d=q(e),u=D(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-d64fj6"!==We(u)&&(u.innerHTML=ee),p=q(e),f=D(e,"DIV",{class:!0});var v=V(f);h=D(v,"BUTTON",{class:!0});var E=V(h);$=ce(E,se),g=ce(E," Domo Dataset"),E.forEach(m),b=q(v),ve&&ve.l(v),v.forEach(m),x=q(e),Ee&&Ee.l(e),w=q(e),C=D(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1941s2m"!==We(C)&&(C.innerHTML=oe),L=q(e),O=D(e,"DIV",{id:!0}),V(O).forEach(m),A=q(e),ie(I.$$.fragment,e),S=q(e),xe&&xe.l(e),k=q(e),ie(M.$$.fragment,e),R=q(e),ie(z.$$.fragment,e),_=q(e),Te&&Te.l(e),F=q(e),ie(U.$$.fragment,e),G=q(e),we&&we.l(e),B=q(e),H=D(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-fy128a"!==We(H)&&(H.innerHTML=ae),Q=q(e),X=D(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-pm10gv"!==We(X)&&(X.innerHTML=re),this.h()},h(){T(n,"name","twitter:card"),T(n,"content","summary_large_image"),T(s,"name","twitter:site"),T(s,"content","@evidence_dev"),T(o,"class","markdown"),T(o,"id","evidence-dashboard-for-domo-ddx"),T(c,"class","markdown"),T(u,"class","markdown"),T(u,"id","domo-dataset-workflow--analysis"),T(h,"class","btn-primary svelte-16omzez"),T(f,"class","workflow-toggle svelte-16omzez"),T(C,"class","markdown"),T(C,"id","sample-analysis"),T(O,"id","sample-analysis"),T(H,"class","markdown"),T(H,"id","whats-next"),T(X,"class","next-steps svelte-16omzez")},m(m,y){ue&&ue.m(m,y),E(m,t,y),pe.m(Dt.head,null),P(Dt.head,n),P(Dt.head,s),me&&me.m(Dt.head,null),P(Dt.head,l),E(m,i,y),E(m,o,y),E(m,a,y),$e.m(m,y),E(m,r,y),E(m,c,y),E(m,d,y),E(m,u,y),E(m,p,y),E(m,f,y),P(f,h),P(h,$),P(h,g),P(f,b),ve&&ve.m(f,null),E(m,x,y),Ee&&Ee.m(m,y),E(m,w,y),E(m,C,y),E(m,L,y),E(m,O,y),E(m,A,y),le(I,m,y),E(m,S,y),xe&&xe.m(m,y),E(m,k,y),le(M,m,y),E(m,R,y),le(z,m,y),E(m,_,y),Te&&Te.m(m,y),E(m,F,y),le(U,m,y),E(m,G,y),we&&we.m(m,y),E(m,B,y),E(m,H,y),E(m,Q,y),E(m,X,y),W=!0,Y||(K=qe(h,"click",e[7]),Y=!0)},p(e,t){typeof Ue<"u"&&Ue.title&&!0!==Ue.hide_title&&ue.p(e,t),pe.p(e,t),"object"==typeof Ue&&me.p(e,t),ye!==(ye=he(e))&&($e.d(1),$e=ye(e),$e&&($e.c(),$e.m(r.parentNode,r))),(!W||16&t[0])&&se!==(se=e[4]?"📊 Hide":"📊 Load")&&Xe($,se),e[6].length>0&&ve.p(e,t),e[4]?Ee||(Ee=Us(),Ee.c(),Ee.m(w.parentNode,w)):Ee&&(Ee.d(1),Ee=null);const n={};4&t[1]&&(n.$$scope={dirty:t,ctx:e}),I.$set(n),e[1]?xe?(xe.p(e,t),2&t[0]&&y(xe,1)):(xe=Vs(e),xe.c(),y(xe,1),xe.m(k.parentNode,k)):xe&&(ge(),v(xe,1,1,(()=>{xe=null})),be());const s={};2&t[0]&&(s.data=e[1]),4&t[1]&&(s.$$scope={dirty:t,ctx:e}),M.$set(s);const l={};4&t[1]&&(l.$$scope={dirty:t,ctx:e}),z.$set(l),e[2]?Te?(Te.p(e,t),4&t[0]&&y(Te,1)):(Te=Hs(e),Te.c(),y(Te,1),Te.m(F.parentNode,F)):Te&&(ge(),v(Te,1,1,(()=>{Te=null})),be());const i={};4&t[0]&&(i.data=e[2]),1&t[0]&&(i.title="Sales by Month, "+e[0].category.label),U.$set(i),e[5]?we?(we.p(e,t),32&t[0]&&y(we,1)):(we=Gs(e),we.c(),y(we,1),we.m(B.parentNode,B)):we&&(ge(),v(we,1,1,(()=>{we=null})),be())},i(e){W||(y(I.$$.fragment,e),y(xe),y(M.$$.fragment,e),y(z.$$.fragment,e),y(Te),y(U.$$.fragment,e),y(we),W=!0)},o(e){v(I.$$.fragment,e),v(xe),v(M.$$.fragment,e),v(z.$$.fragment,e),v(Te),v(U.$$.fragment,e),v(we),W=!1},d(e){e&&(m(t),m(i),m(o),m(a),m(r),m(c),m(d),m(u),m(p),m(f),m(x),m(w),m(C),m(L),m(O),m(A),m(S),m(k),m(R),m(_),m(F),m(G),m(B),m(H),m(Q),m(X)),ue&&ue.d(e),pe.d(e),m(n),m(s),me&&me.d(e),m(l),$e.d(e),ve&&ve.d(),Ee&&Ee.d(e),te(I,e),xe&&xe.d(e),te(M,e),te(z,e),Te&&Te.d(e),te(U,e),we&&we.d(e),Y=!1,K()}}}const Ue={title:"Evidence Dashboard for Domo"};function Ed(){if(!window.domoDuckDBIntegration){const e=document.createElement("script");e.src="/static/domo-duckdb-integration.js",e.onload=function(){console.log("Domo integration script loaded")},document.head.appendChild(e)}}function vd(e,t,n){let s,l;je(e,Ji,(e=>n(19,s=e))),je(e,En,(e=>n(24,l=e)));let{data:i}=t,{data:o={},customFormattingSettings:a,__db:r,inputs:c}=i;Rl(En,l="6666cd76f96956469e7be39d750cc7d9",l);let d=To(oi(c));qi(d.subscribe((e=>n(0,c=e)))),Nl(Ao,{getCustomFormats:()=>a.customFormats||[]});const u=(e,t)=>Lo(r.query,e,{query_name:t});So(u),s.params,gl((()=>!0));let p={initialData:void 0,initialError:void 0},m=ni`select
      category
  from needful_things.orders
  group by category`,f="select\n      category\n  from needful_things.orders\n  group by category";o.categories_data&&(o.categories_data instanceof Error?p.initialError=o.categories_data:p.initialData=o.categories_data,o.categories_columns&&(p.knownColumns=o.categories_columns));let h,y=!1;const $=zl.createReactive({callback:e=>{n(1,h=e)},execFn:u},{id:"categories",...p});$(f,{noResolve:m,...p}),globalThis[Symbol.for("categories")]={get value(){return h}};let g={initialData:void 0,initialError:void 0},v=ni`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`,b=`select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '${c.category.value}'\n  and date_part('year', order_datetime) like '${c.year.value}'\n  group by all\n  order by sales_usd desc`;o.orders_by_category_data&&(o.orders_by_category_data instanceof Error?g.initialError=o.orders_by_category_data:g.initialData=o.orders_by_category_data,o.orders_by_category_columns&&(g.knownColumns=o.orders_by_category_columns));let E,x=!1;const T=zl.createReactive({callback:e=>{n(2,E=e)},execFn:u},{id:"orders_by_category",...g});T(b,{noResolve:v,...g}),globalThis[Symbol.for("orders_by_category")]={get value(){return E}};let w=!1,D=!1,C=!1;return typeof window<"u"&&(w=typeof window.domo<"u"),e.$$set=e=>{"data"in e&&n(9,i=e.data)},e.$$.update=()=>{512&e.$$.dirty[0]&&n(10,({data:o={},customFormattingSettings:a,__db:r}=i),o),1024&e.$$.dirty[0]&&po.set(Object.keys(o).length>0),524288&e.$$.dirty[0]&&s.params,30720&e.$$.dirty[0]&&(m||!y?m||($(f,{noResolve:m,...p}),n(14,y=!0)):$(f,{noResolve:m})),1&e.$$.dirty[0]&&n(16,v=ni`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`),1&e.$$.dirty[0]&&n(17,b=`select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '${c.category.value}'\n  and date_part('year', order_datetime) like '${c.year.value}'\n  group by all\n  order by sales_usd desc`),491520&e.$$.dirty[0]&&(v||!x?v||(T(b,{noResolve:v,...g}),n(18,x=!0)):T(b,{noResolve:v}))},n(12,m=ni`select
      category
  from needful_things.orders
  group by category`),n(13,f="select\n      category\n  from needful_things.orders\n  group by category"),[c,h,E,w,D,C,[],function(){n(4,D=!D),D&&setTimeout((()=>{Ed()}),100)},function(){n(5,C=!0),setTimeout((()=>{var e;null==(e=document.getElementById("analysis-section"))||e.scrollIntoView({behavior:"smooth"})}),100)},i,o,p,m,f,y,g,v,b,x,s]}class Rd extends ve{constructor(e){super(),Te(this,e,vd,Cd,ke,{data:9},null,[-1,-1])}}export{Rd as component};